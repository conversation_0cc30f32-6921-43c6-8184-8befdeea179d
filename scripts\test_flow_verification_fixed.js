/**
 * Test Flow Verification Script (Fixed Version)
 * 
 * This script verifies the complete test flow:
 * 1. Triggers a test run via API call
 * 2. Connects to database directly (avoiding SSH tunnel issues)
 * 3. Monitors test status until completion
 * 4. Retrieves test suite results from database
 * 5. Gets details of each test case/step in the test suite
 */

const axios = require('axios');
const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');

// Process command line arguments to get environment and test ID
const args = process.argv.slice(2);
const selectedEnvironment = args[0] || 'qa02'; // Default to qa02 if not specified
const providedTsnId = args[1] ? parseInt(args[1], 10) : null; // Check if a specific tsn_id was provided
const timeout = args[2] ? parseInt(args[2], 10) * 1000 : 1800000; // Optional timeout in seconds, default 30 minutes

// Configuration
const config = {
  // Environment selection
  environment: selectedEnvironment, // 'qa01', 'qa02', or 'qa03'
  
  // Environment-specific settings
  environments: {
    qa01: {
      baseUrl: 'http://mprts-qa01.lab.wagerworks.com:9080/AutoRun/',
      dbHost: 'mprts-qa01.lab.wagerworks.com',
      dbUser: 'rgs_rw',
      dbPassword: 'rgs_rw',
      dbName: 'rgs_test',
      dbPort: 3306
    },
    qa02: {
      baseUrl: 'http://mprts-qa02.lab.wagerworks.com:5080/AutoRun/',
      dbHost: 'mprts-qa02.lab.wagerworks.com',
      dbUser: 'rgs_rw',
      dbPassword: 'rgs_rw',
      dbName: 'rgs_test',
      dbPort: 3306
    },
    qa03: {
      baseUrl: 'http://mprts-qa03.lab.wagerworks.com:5080/AutoRun/',
      dbHost: 'mprts-qa03.lab.wagerworks.com',
      dbUser: 'rgs_rw',
      dbPassword: 'rgs_rw',
      dbName: 'rgs_test',
      dbPort: 3306
    }
  },
  
  // API Configuration
  api: {
    credentials: {
      uid: '<EMAIL>', // Replace with your email if needed
      password: 'test'              // Replace with your actual password
    }
  },
  
  // User Information (for Active Tests panel)
  user: {
    user_id: 'Iakov.Volfkovich', // User ID - typically part of the email
    username: 'Iakov.Volfkovich' // Username
  },
  
  // Test Configuration
  test: {
    // Use either tc_id for a single test case or ts_id for a test suite
    tc_id: 3180,       // Example test case ID
    // ts_id: 312,     // Example test suite ID (uncomment to use)
    
    // Additional parameters will be set based on environment
    envir: null, // Will be set from environment
    shell_host: 'jps-qa10-app01',
    file_path: '/home/<USER>/',
    operatorConfigs: 'operatorNameConfigs',
    kafka_server: 'kafka-qa-a0.lab.wagerworks.com',
    dataCenter: 'GU',
    rgs_env: null, // Will be set from environment
    old_version: '0',
    networkType1: 'multi-site',
    networkType2: 'multi-site',
    sign: '-',
    rate_src: 'local'
  },
  
  // Script Configuration
  polling: {
    intervalMs: 5000,  // Check status every 5 seconds
    timeoutMs: timeout // Updated with the provided value
  }
};

// Database connection
let dbConnection = null;

// Initialize the environment settings
function initializeEnvironment() {
  if (!config.environments[config.environment]) {
    throw new Error(`Environment ${config.environment} not found in configuration. Available environments: ${Object.keys(config.environments).join(', ')}`);
  }
  
  // Get environment configuration
  const envConfig = config.environments[config.environment];
  
  // Update the API base URL from environment
  config.api.baseUrl = envConfig.baseUrl;
  
  // Update test environment to match selected environment
  config.test.envir = config.environment;
  config.test.rgs_env = config.environment;
  
  console.log(`Using ${config.environment} environment`);
  console.log(`API Base URL: ${config.api.baseUrl}`);
  
  return envConfig;
}

// Initialize the database connection
async function initializeDatabase(envConfig) {
  try {
    console.log(`Connecting directly to ${envConfig.dbHost}...`);
    
    // Create direct database connection
    dbConnection = await mysql.createConnection({
      host: envConfig.dbHost,
      user: envConfig.dbUser,
      password: envConfig.dbPassword,
      database: envConfig.dbName,
      port: envConfig.dbPort
    });
    
    // Test the connection
    const [result] = await dbConnection.query('SELECT 1 as test');
    if (result[0].test === 1) {
      console.log('Database connection successful');
      return true;
    }
    
    throw new Error('Database connection test failed');
  } catch (error) {
    console.error('Database connection error:', error.message);
    throw error;
  }
}

// Close the database connection
async function closeDatabase() {
  if (dbConnection) {
    console.log('Closing database connection...');
    await dbConnection.end();
    dbConnection = null;
  }
}

// Execute a database query
async function query(sql, params = []) {
  if (!dbConnection) {
    throw new Error('Database not connected');
  }
  
  try {
    const [rows] = await dbConnection.query(sql, params);
    return rows;
  } catch (error) {
    console.error('Query error:', error.message);
    console.error('SQL:', sql);
    console.error('Parameters:', params);
    throw error;
  }
}

// Trigger test run via API
async function triggerTestRun() {
  try {
    console.log('Starting test run...');
    
    // Prepare request parameters
    const params = new URLSearchParams();
    
    // Add credentials
    Object.entries(config.api.credentials).forEach(([key, value]) => {
      params.append(key, value);
    });
    
    // Add user information for test initiator tracking
    // The API passes this to the database through uid parameter
    Object.entries(config.user).forEach(([key, value]) => {
      params.append(key, value);
    });
    
    // Add test configuration
    Object.entries(config.test).forEach(([key, value]) => {
      if (value !== null && value !== undefined) {
        params.append(key, value);
      }
    });
    
    // Make the API request
    console.log(`Sending API request to ${config.api.baseUrl}CaseRunner`);
    const response = await axios.post(
      `${config.api.baseUrl}CaseRunner`,
      params.toString(),
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      }
    );
    
    // Log response status
    console.log(`API response status: ${response.status}`);
    
    // Extract tsn_id from response
    const tsnId = extractTsnId(response.data);
    
    if (!tsnId) {
      console.log('Could not extract tsn_id from response, getting latest from database...');
      const latestTsnId = await getLatestTsnId();
      if (latestTsnId) {
        console.log(`Using latest tsn_id from database: ${latestTsnId}`);
        return latestTsnId;
      }
      throw new Error('Failed to extract tsn_id from response and could not get latest from database');
    }
    
    console.log(`Test run started with tsn_id: ${tsnId}`);
    return tsnId;
    
  } catch (error) {
    console.error('Error triggering test run:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      if (typeof error.response.data === 'string') {
        console.error('Response data (truncated):', error.response.data.substring(0, 200));
      } else {
        console.error('Response data:', error.response.data);
      }
    }
    throw error;
  }
}

// Extract tsn_id from API response (improved version)
function extractTsnId(responseData) {
  console.log('Extracting tsn_id from response...');
  
  // If the response is a string (likely HTML)
  if (typeof responseData === 'string') {
    // Method 1: Check for ReportSummary?tsn_id=number format
    const reportMatch = responseData.match(/ReportSummary\?tsn_id=(\d+)/);
    if (reportMatch && reportMatch[1]) {
      console.log(`Found tsn_id using ReportSummary URL: ${reportMatch[1]}`);
      return reportMatch[1];
    }
    
    // Method 2: Look for CaseEditor?tsn_id=number format
    const editorMatch = responseData.match(/CaseEditor\?tsn_id=(\d+)/);
    if (editorMatch && editorMatch[1]) {
      console.log(`Found tsn_id using CaseEditor URL: ${editorMatch[1]}`);
      return editorMatch[1];
    }
    
    // Method 3: Look for general tsn_id=number format
    const generalMatch = responseData.match(/tsn_id=(\d+)/);
    if (generalMatch && generalMatch[1]) {
      console.log(`Found tsn_id using general pattern: ${generalMatch[1]}`);
      return generalMatch[1];
    }
    
    // Method 4: Look for any number after tsn_id
    const anyTsnIdMatch = responseData.match(/tsn_id.*?(\d+)/);
    if (anyTsnIdMatch && anyTsnIdMatch[1]) {
      console.log(`Found tsn_id using loose pattern: ${anyTsnIdMatch[1]}`);
      return anyTsnIdMatch[1];
    }
    
    console.log('Could not extract tsn_id from response string');
  } else if (responseData && typeof responseData === 'object') {
    // If the response is an object, check for tsn_id property
    if (responseData.tsn_id) {
      console.log(`Found tsn_id in response object: ${responseData.tsn_id}`);
      return responseData.tsn_id;
    }
    console.log('Response is an object, but no tsn_id found');
  } else {
    console.log(`Response data type is: ${typeof responseData}`);
  }
  
  console.log('No tsn_id found in response');
  return null;
}

// Get latest tsn_id from database (fallback if API doesn't return it)
async function getLatestTsnId() {
  try {
    const rows = await query('SELECT MAX(tsn_id) AS latest_tsn_id FROM test_result');
    
    if (rows && rows.length > 0 && rows[0].latest_tsn_id) {
      return rows[0].latest_tsn_id;
    }
    
    throw new Error('Failed to retrieve latest tsn_id from database');
  } catch (error) {
    console.error('Error getting latest tsn_id:', error.message);
    throw error;
  }
}

// Check test run status
async function checkTestStatus(tsnId) {
  try {
    // Query to check test status (joining with test_session for user info)
    const rows = await query(`
      SELECT r.tsn_id, r.tc_id, r.outcome, s.uid as initiator_user, COUNT(*) as count
      FROM test_result r
      JOIN output i ON i.cnt = r.cnt
      LEFT JOIN test_session s ON r.tsn_id = s.tsn_id
      WHERE r.tsn_id = ?
      GROUP BY r.tc_id, r.outcome, r.tsn_id, s.uid
      ORDER BY r.creation_time ASC
    `, [tsnId]);
    
    console.log(`\nCurrent test status for tsn_id ${tsnId}:`);
    if (rows.length === 0) {
      console.log('No test results found yet. Test might still be initializing...');
      return { isComplete: false };
    }
    
    // Display current status
    const testCases = new Map();
    let allPassed = true;
    
    rows.forEach(row => {
      const { tc_id, outcome, count, initiator_user } = row;
      
      if (!testCases.has(tc_id)) {
        testCases.set(tc_id, { 
          outcomes: {},
          initiator: initiator_user || 'Unknown'
        });
      }
      
      testCases.get(tc_id).outcomes[outcome] = count;
      
      // If any outcome is not 'P' (Passed), the test is not all passed
      if (outcome !== 'P') {
        allPassed = false;
      }
    });
    
    // Print status summary
    for (const [tcId, data] of testCases.entries()) {
      const outcomes = Object.entries(data.outcomes)
        .map(([outcome, count]) => `${outcome}: ${count}`)
        .join(', ');
      
      // Include initiator information
      const currentUser = data.initiator === config.user.username ? '(Current User)' : '';
      console.log(`Test Case ${tcId}: ${outcomes} | Initiated by: ${data.initiator} ${currentUser}`);
    }
    
    // Check if test is complete by checking test_session.end_ts
    const sessionRows = await query(`
      SELECT end_ts 
      FROM test_session
      WHERE tsn_id = ?
    `, [tsnId]);
    
    // If end_ts is null, the test is still running
    const isRunning = sessionRows.length === 0 || sessionRows[0].end_ts === null;
    
    return { 
      isComplete: !isRunning,
      allPassed
    };
  } catch (error) {
    console.error('Error checking test status:', error.message);
    throw error;
  }
}

// Get test run summary
async function getTestSummary(tsnId) {
  try {
    // Query to get test summary
    const rows = await query(`
      SELECT 
        tsn_id, 
        SUM(CASE WHEN outcome='P' THEN 1 ELSE 0 END) AS passed_cases,
        SUM(CASE WHEN outcome='F' THEN 1 ELSE 0 END) AS failed_cases,
        TIMEDIFF(MAX(creation_time), MIN(creation_time)) AS duration
      FROM test_result
      WHERE tsn_id = ?
      GROUP BY tsn_id
    `, [tsnId]);
    
    if (rows.length === 0) {
      throw new Error(`No summary found for tsn_id ${tsnId}`);
    }
    
    console.log('\n====== TEST SUMMARY ======');
    console.log(`Test Suite Run ID: ${rows[0].tsn_id}`);
    console.log(`Passed Cases: ${rows[0].passed_cases}`);
    console.log(`Failed Cases: ${rows[0].failed_cases}`);
    console.log(`Duration: ${rows[0].duration}`);
    console.log('==========================\n');
    
    return rows[0];
  } catch (error) {
    console.error('Error getting test summary:', error.message);
    throw error;
  }
}

// Get detailed failure information
async function getFailureDetails(tsnId) {
  try {
    // Query to get failure details
    const rows = await query(`
      SELECT r.cnt, seq_index, tsn_id, tc_id, outcome, txt
      FROM test_result r, output i
      WHERE i.cnt = r.cnt
        AND r.tsn_id = ?
        AND outcome = 'F'
    `, [tsnId]);
    
    console.log('\n====== FAILURE DETAILS ======');
    
    if (rows.length === 0) {
      console.log('No failures detected.');
      return [];
    }
    
    // Group failures by test case
    const failuresByTestCase = new Map();
    
    rows.forEach(row => {
      const { tc_id, cnt, seq_index, txt } = row;
      
      if (!failuresByTestCase.has(tc_id)) {
        failuresByTestCase.set(tc_id, []);
      }
      
      failuresByTestCase.get(tc_id).push({
        cnt,
        seq_index,
        output: txt
      });
    });
    
    // Print failures by test case
    for (const [tcId, failures] of failuresByTestCase.entries()) {
      console.log(`\nTest Case ${tcId} Failures:`);
      failures.forEach((failure, index) => {
        console.log(`  Failure #${index + 1}:`);
        console.log(`  - Sequence Index: ${failure.seq_index}`);
        console.log(`  - Output: ${failure.output.substring(0, 150)}${failure.output.length > 150 ? '...' : ''}`);
      });
    }
    
    console.log('=============================\n');
    
    return rows;
  } catch (error) {
    console.error('Error getting failure details:', error.message);
    throw error;
  }
}

// Get detailed test case information
async function getTestCaseDetails(tsnId) {
  try {
    // Query to get test case details
    const rows = await query(`
      SELECT DISTINCT r.tc_id, t.name
      FROM test_result r
      LEFT JOIN test_case t ON r.tc_id = t.tc_id
      WHERE r.tsn_id = ?
    `, [tsnId]);
    
    console.log('\n====== TEST CASE DETAILS ======');
    
    if (rows.length === 0) {
      console.log('No test case details found.');
      return [];
    }
    
    // Print test case details
    rows.forEach(row => {
      console.log(`Test Case ID: ${row.tc_id}, Name: ${row.name || 'Unnamed'}`);
    });
    
    console.log('===============================\n');
    
    return rows;
  } catch (error) {
    console.error('Error getting test case details:', error.message);
    throw error;
  }
}

// Get active tests for all users
async function getActiveTests() {
  try {
    // Query to get active test sessions
    const rows = await query(`
      SELECT s.tsn_id, 
             s.tc_id, 
             s.uid as initiator_user, 
             s.start_ts as creation_time,
             (s.uid = ?) as is_current_user
      FROM test_session s
      WHERE s.end_ts IS NULL
      ORDER BY s.start_ts DESC
    `, [config.user.username]);
    
    console.log('\n====== ACTIVE TESTS ======');
    
    if (rows.length === 0) {
      console.log('No active tests currently running.');
      return [];
    }
    
    // Group by initiator
    const testsByInitiator = new Map();
    
    rows.forEach(row => {
      const { initiator_user, tc_id, tsn_id, creation_time } = row;
      // Handle null values properly
      const initiator = initiator_user || 'Unknown';
      const testCaseId = tc_id || 'Unknown';
      const testRunId = tsn_id || 'Unknown';
      const startTime = creation_time ? new Date(creation_time).toLocaleString() : 'Unknown';
      
      if (!testsByInitiator.has(initiator)) {
        testsByInitiator.set(initiator, []);
      }
      
      testsByInitiator.get(initiator).push({
        tc_id: testCaseId,
        tsn_id: testRunId,
        creation_time: startTime
      });
    });
    
    // Display by initiator
    let currentUserTestsCount = 0;
    let otherUsersTestsCount = 0;
    
    console.log('Current User Tests:');
    if (testsByInitiator.has(config.user.username)) {
      const userTests = testsByInitiator.get(config.user.username);
      currentUserTestsCount = userTests.length;
      
      userTests.forEach(test => {
        console.log(`  Test Case ${test.tc_id} (Run ID: ${test.tsn_id}) - Started at: ${test.creation_time}`);
      });
    } else {
      console.log('  No tests running by current user.');
    }
    
    console.log('\nOther Users\' Tests:');
    for (const [initiator, tests] of testsByInitiator.entries()) {
      if (initiator !== config.user.username) {
        console.log(`  ${initiator}:`);
        tests.forEach(test => {
          console.log(`    Test Case ${test.tc_id} (Run ID: ${test.tsn_id}) - Started at: ${test.creation_time}`);
        });
        otherUsersTestsCount += tests.length;
      }
    }
    
    console.log('\nSummary:');
    console.log(`  Current User: ${currentUserTestsCount} active tests`);
    console.log(`  Other Users: ${otherUsersTestsCount} active tests`);
    console.log('=========================\n');
    
    return rows;
  } catch (error) {
    console.error('Error getting active tests:', error.message);
    throw error;
  }
}

// Main function to run the test verification flow
async function runTestVerification() {
  try {
    console.log('Starting test flow verification...');
    
    // Initialize configuration with environment settings
    const envConfig = initializeEnvironment();
    
    // Initialize the database connection (direct connection)
    await initializeDatabase(envConfig);
    
    // Check for active tests before starting a new one
    console.log('Checking for currently active tests...');
    await getActiveTests();
    
    let tsnId;
    
    // If a specific tsn_id was provided, use it instead of starting a new test
    if (providedTsnId) {
      console.log(`Using provided tsn_id: ${providedTsnId}`);
      tsnId = providedTsnId;
      
      // Verify the test exists
      const testExists = await verifyTestExists(tsnId);
      if (!testExists) {
        throw new Error(`Test with tsn_id ${tsnId} does not exist`);
      }
    } else {
      // Otherwise, trigger a new test run via API call
      console.log('No tsn_id provided, initiating a new test run...');
      tsnId = await triggerTestRun();
    }
    
    // Monitor test status until completion
    console.log(`Monitoring test status for tsn_id: ${tsnId}`);
    
    let isComplete = false;
    let allPassed = false;
    const startTime = Date.now();
    
    while (!isComplete && (Date.now() - startTime < config.polling.timeoutMs)) {
      const status = await checkTestStatus(tsnId);
      isComplete = status.isComplete;
      allPassed = status.allPassed;
      
      if (isComplete) {
        console.log('\nTest run completed!');
        break;
      }
      
      console.log(`Waiting ${config.polling.intervalMs / 1000} seconds before next check...`);
      await new Promise(resolve => setTimeout(resolve, config.polling.intervalMs));
    }
    
    if (!isComplete) {
      throw new Error(`Test run timed out after ${config.polling.timeoutMs / 60000} minutes`);
    }
    
    // Get test summary
    const summary = await getTestSummary(tsnId);
    
    // Get failure details if any
    if (summary.failed_cases > 0) {
      await getFailureDetails(tsnId);
    }
    
    // Get test case details
    await getTestCaseDetails(tsnId);
    
    // After test completion, check for active tests again
    console.log('Test completed. Checking for currently active tests...');
    await getActiveTests();
    
    console.log('Test flow verification completed successfully');
    
  } catch (error) {
    console.error('Test flow verification failed:', error.message);
  } finally {
    // Clean up resources
    await closeDatabase();
  }
}

// Verify that a test with the given tsn_id exists
async function verifyTestExists(tsnId) {
  try {
    const rows = await query(`
      SELECT tsn_id FROM test_session WHERE tsn_id = ?
    `, [tsnId]);
    
    return rows.length > 0;
  } catch (error) {
    console.error('Error verifying test existence:', error.message);
    return false;
  }
}

// Run the test verification
runTestVerification().catch(error => {
  console.error('Fatal error:', error);
  process.exit(1);
}); 
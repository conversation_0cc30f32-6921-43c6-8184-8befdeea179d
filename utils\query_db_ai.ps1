<#
.SYNOPSIS
Connects to the rgs_test database via SSH, executes a SQL query, and returns results as JSON. Designed for AI/programmatic use.

.DESCRIPTION
This script uses SSH key authentication (with legacy algorithm support) to connect to the specified server, 
executes a given SQL query using the mysql client remotely, parses the tab-separated output, and 
returns a JSON object containing the execution status, original query, and resulting data or error message.

.PARAMETER Query
The SQL query string to execute on the remote database. This parameter is mandatory.

.EXAMPLE
powershell.exe -ExecutionPolicy Bypass -File .\\utils\\query_db_ai.ps1 -Query "SELECT COUNT(*) FROM test_result;"
Outputs JSON: {"success":true,"query":"SELECT COUNT(*) FROM test_result;","data":[{"COUNT(*)":"15244447"}],"rowCount":1,"errorMessage":null}

.EXAMPLE
powershell.exe -ExecutionPolicy Bypass -File .\\utils\\query_db_ai.ps1 -Query "SELECT * FROM non_existent_table;"
Outputs JSON: {"success":false,"query":"SELECT * FROM non_existent_table;","data":null,"rowCount":0,"errorMessage":"MySQL Error: ERROR 1146 (42S02) at line 1: Table 'rgs_test.non_existent_table' doesn't exist","sshError":null,"mysqlError":"ERROR 1146 (42S02) at line 1: Table 'rgs_test.non_existent_table' doesn't exist"}

.OUTPUTS
System.String - A JSON formatted string representing the query result.
#>
param(
    [Parameter(Mandatory=$true)]
    [string]$Query
)

# --- Function to parse .env file ---
function Get-EnvVariable {
    param(
        [string]$FilePath,
        [string]$VariableName
    )
    if (Test-Path $FilePath) {
        $content = Get-Content $FilePath
        foreach ($line in $content) {
            if ($line -match "^\s*$VariableName\s*=\s*(.+?)\s*$") {
                return $Matches[1].Trim('"', "'")
            }
        }
    }
    return $null
}

# --- Configuration ---
$server = "mprts-qa02.lab.wagerworks.com"
$database = "rgs_test"
$envFilePath = Join-Path $PSScriptRoot "..\frontend\server\.env"

# Read DB credentials from .env file
$dbUser = Get-EnvVariable -FilePath $envFilePath -VariableName "DB_USER"
$dbPassword = Get-EnvVariable -FilePath $envFilePath -VariableName "DB_PASSWORD"

# Check if credentials were found
if ([string]::IsNullOrEmpty($dbUser) -or [string]::IsNullOrEmpty($dbPassword)) {
    Write-Error "DB_USER or DB_PASSWORD not found in $envFilePath"
    exit 1 # Exit if credentials cannot be read
} 

# $sshUser = "volfkoi" # Removed explicit SSH user
$sshKeyPath = Join-Path $env:USERPROFILE ".ssh\id_rsa_dbserver"
$sshLegacyOptions = "-o HostKeyAlgorithms=+ssh-rsa -o PubkeyAcceptedAlgorithms=+ssh-rsa -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null" # Added options to bypass host key checks for automation

# --- Define mysql path ---
$mysqlFullPath = "/usr/bin/mysql" # Using full path found via 'command -v mysql'

# Output object template
$result = @{
    success      = $false
    query        = $Query
    data         = $null
    rowCount     = 0
    errorMessage = $null
    sshError     = $null
    mysqlError   = $null
}

# --- Validate SSH Key ---
if (-not (Test-Path $sshKeyPath)) {
    $result.errorMessage = "SSH private key not found at $sshKeyPath"
    Write-Output ($result | ConvertTo-Json -Depth 5)
    exit 1
}

# --- Prepare Commands ---
# Escape ONLY single quotes within the SQL query for use within mysql -e \'...\'
# Standard SQL escaping is doubling the single quote.
$escapedQuery = $Query.Replace("'", "''")

# Construct the full SSH command arguments for Start-Process
# Passing mysql command and args separately
$sshArgumentList = @(
    $sshLegacyOptions.Split(' ') # Split legacy options string into individual arguments
    # "-i", # Commented out -i as we might rely on password or agent
    # "$sshKeyPath" # Use standard PowerShell quoting for the path
    # "${sshUser}@${server}" # Modified to remove explicit user
    $server, # Target server directly
    $mysqlFullPath,
    ("--user={0}" -f $dbUser),
    ("--password={0}" -f $dbPassword),
    ("--database={0}" -f $database),
    "--batch",
    "-N",
    "-e",
    "$escapedQuery" # Pass the escaped query directly as the last argument for -e
)

# --- Execute Query via SSH ---
Write-Verbose "Executing SSH command via Start-Process with arguments: $($sshArgumentList -join ' ')"
$stdOut = ""
$stdErr = ""
$exitCode = 0

try {
    # Execute the command using Start-Process for better argument handling
    $process = Start-Process ssh -ArgumentList $sshArgumentList -NoNewWindow -PassThru -RedirectStandardOutput (Join-Path $env:TEMP "stdout.tmp") -RedirectStandardError (Join-Path $env:TEMP "stderr.tmp")
    $process | Wait-Process
    $exitCode = $process.ExitCode
    $stdOut = Get-Content (Join-Path $env:TEMP "stdout.tmp") -Raw -ErrorAction SilentlyContinue
    $stdErr = Get-Content (Join-Path $env:TEMP "stderr.tmp") -Raw -ErrorAction SilentlyContinue
    
    # Clean up temp files
    Remove-Item (Join-Path $env:TEMP "stdout.tmp") -ErrorAction SilentlyContinue
    Remove-Item (Join-Path $env:TEMP "stderr.tmp") -ErrorAction SilentlyContinue

    Write-Verbose "SSH Exit Code: $exitCode"
    Write-Verbose "Raw Standard Output: $stdOut"
    Write-Verbose "Raw Standard Error: $stdErr"

    # --- Process Results ---
    if ($exitCode -ne 0) {
        # SSH command itself failed
        $result.success = $false
        $result.errorMessage = "SSH command failed with exit code $exitCode."
        $result.sshError = $stdErr.Trim()
        if ([string]::IsNullOrWhiteSpace($result.sshError)) {
             $result.sshError = $stdOut.Trim() # Sometimes errors go to stdout
        }
    } else { # SSH exit code was 0
        # Check stderr first for MySQL errors even if SSH succeeded
        if ($stdErr -match 'ERROR\s+\d+\s+\(.+?\):(.+)') {
             # MySQL returned an error (captured typically in stderr via SSH)
            $result.success = $false
            $result.mysqlError = $stdErr.Trim()
            $result.errorMessage = "MySQL Error: $($result.mysqlError)"
            # Keep any non-fatal SSH warnings (like password exposure) if present
            if ($stdErr -notmatch 'ERROR\s+\d+') {
                $result.sshError = $stdErr.Trim()
            }
        } elseif ($stdOut -match 'ERROR\s+\d+\s+\(.+?\):(.+)') {
            # Check stdout too, just in case mysql errors end up there
            $result.success = $false
            $result.mysqlError = $stdOut -split '\r?\n' | Where-Object { $_ -match 'ERROR\s+\d+\s+\(.+?\):' } | Select-Object -First 1
            $result.errorMessage = "MySQL Error: $($result.mysqlError)"
        } else {
            # SSH succeeded AND stderr/stdout look clean of errors, proceed with processing stdout
            $result.success = $true
            $lines = $stdOut -split '\r?\n' | Where-Object { -not [string]::IsNullOrWhiteSpace($_) }
            if ($lines.Length -gt 0) {
                # Fetch column names separately as -N skips them
                # $headerQuery = "SELECT GROUP_CONCAT(COLUMN_NAME ORDER BY ORDINAL_POSITION) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = '$database' AND TABLE_NAME = (SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA='$database' LIMIT 1) AND ($Query -match CONCAT('.*',TABLE_NAME,'.*')) LIMIT 1;" # Commenting out unused variable
                # A more robust way requires parsing the query, which is complex.
                # For now, let's just use numerical headers if we can't guess
                
                # Assuming first line is headers if present
                $headers = @()
                if ($lines.Length -gt 1) {
                    $headers = $lines[0] -split '\t'
                } else {
                    # If no headers, use numerical headers
                    $headers = @(1..($lines[0] -split '\t').Count)
                }
                
                # Process data rows
                $dataArray = @()
                for ($i = 1; $i -lt $lines.Length; $i++) {
                    $dataRow = [ordered]@{}
                    $values = $lines[$i] -split '\t'
                    for ($j = 0; $j -lt $headers.Count; $j++) {
                        $dataRow[$headers[$j]] = $values[$j]
                    }
                    $dataArray += [PSCustomObject]$dataRow
                }
                $result.data = $dataArray
                $result.rowCount = $dataArray.Count
            } else {
                # Query succeeded but returned no rows
                $result.data = @()
                $result.rowCount = 0
            }
        }
    }

} catch {
    $result.success = $false
    $result.errorMessage = "Script error: $($_.Exception.Message)"
    $result.sshError = $stdErr # Include stderr potentially captured before the exception
}

# --- Output JSON ---
# Use Depth 5 to handle nested data structures if any arise
Write-Output ($result | ConvertTo-Json -Depth 5 -Compress)

# Set exit code based on success
if ($result.success) {
    exit 0
} else {
    exit 1
} 
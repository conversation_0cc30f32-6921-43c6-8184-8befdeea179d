# Dashboard Polling Architecture

## Overview
The SmartTest Dashboard uses multiple overlapping polling mechanisms to maintain real-time visibility into test execution status. This document outlines the current polling architecture, identified issues, and performance optimization opportunities.

## Current Polling Mechanisms

### 1. Main Dashboard Refresh (`refreshDashboard`)
- **Interval**: 30 seconds
- **Location**: `dashboard.js` line 362
- **Purpose**: Complete dashboard data refresh
- **Triggers**: 
  - `setInterval(refreshDashboard, config.refreshInterval)`
  - Manual refresh actions
- **Data Sources**: All dashboard API endpoints
- **Impact**: Full DOM update, high resource usage

### 2. Recent Runs Polling (`pollRecentRuns`)
- **Interval**: 10 seconds  
- **Location**: `api-integration.js` line 767
- **Purpose**: Fetch new test runs since last known TSN ID
- **API Endpoint**: `/local/recent-runs`
- **Functionality**:
  - Maintains `recentRunsCache` (max 50 items)
  - Updates `highestRecentTsnId` tracking
  - Triggers dashboard counter updates
  - Updates recent runs table display

### 3. Test Status Polling (`updateTestStatuses`)
- **Interval**: 5 seconds
- **Location**: `api-integration.js` line 1135
- **Purpose**: Monitor active test execution status
- **API Endpoint**: `/api/test-status`
- **Functionality**:
  - Checks status of all tests in `activeTests` Map
  - Updates test progress, passed/failed counts
  - Manages test completion notifications
  - Triggers Active Tests section re-rendering

## Polling Data Flow

```
Dashboard Initialization
         ↓
   Start All Polling
         ↓
┌─────────────────┬─────────────────┬─────────────────┐
│  refreshDashboard │  pollRecentRuns   │ updateTestStatuses │
│    (30s)         │     (10s)        │      (5s)         │
└─────────────────┴─────────────────┴─────────────────┘
         ↓                ↓                 ↓
    Full Refresh    Recent Runs Cache   Active Tests Map
         ↓                ↓                 ↓
    DOM Updates     Counter Updates    Status Updates
         ↓                ↓                 ↓
      All Sections   Recent Runs Table  Active Tests Cards
```

## Identified Issues

### 1. Overlapping DOM Updates
- **Problem**: Multiple polling functions trigger simultaneous DOM updates
- **Symptoms**: Flickering Active Tests section, "container not found" warnings
- **Root Cause**: Race conditions between polling intervals
- **Impact**: Poor user experience, unnecessary DOM manipulation

### 2. Redundant API Calls
- **Problem**: Same data fetched by multiple polling mechanisms
- **Example**: Recent runs data used by both `pollRecentRuns` and `refreshDashboard`
- **Impact**: Increased server load, network overhead

### 3. Inefficient Active Tests Rendering
- **Problem**: Complete DOM clearing (`container.innerHTML = ''`) on every update
- **Frequency**: Every 5 seconds via `updateTestStatuses`
- **Impact**: Visual flickering, lost scroll position, poor performance

### 4. Inconsistent Status Logic
- **Problem**: Conflicting logic for determining test active/completed state
- **Impact**: Incorrect Stop button display, confusing status indicators

## Recent Fixes Implemented

### 1. Efficient DOM Updates for Active Tests
**Location**: `api-integration.js` renderActiveTests()
**Changes**:
- Implemented card reuse instead of full DOM clearing
- Added existence checking before DOM manipulation
- Reduced unnecessary re-rendering

```javascript
// Before: container.innerHTML = ''; (clears all)
// After: Selective card updates and reuse
const existingCards = new Map();
const existingElements = container.querySelectorAll('.test-card[data-tsn-id]');
// ... reuse existing cards, only update content
```

### 2. Fixed Active State Logic
**Problem**: Tests with `endTime` still showing as "Pending"
**Solution**: Improved status determination logic

```javascript
// A test is completed if it has an endTime OR explicit completion status
const isCompleted = testInfo.endTime || 
                   ['passed', 'failed', 'completed', 'stopped'].includes(testInfo.status?.toLowerCase());

// A test is active (can be stopped) if it's running AND not completed
const isActive = running > 0 && !isCompleted;
```

### 3. Corrected Stop Button Logic
- **Before**: Stop button shown for completed tests with "Pending" display
- **After**: Stop button only shown for actively running tests
- **Logic**: `isActive = running > 0 && !isCompleted`

## Performance Optimization Opportunities

### 1. Coordinated Polling Strategy
- **Recommendation**: Implement single polling coordinator
- **Benefits**: Eliminate redundant API calls, synchronized updates
- **Implementation**: Central polling service with event-driven updates

### 2. Intelligent Polling Intervals
- **Current**: Fixed intervals regardless of activity
- **Recommended**: Adaptive intervals based on test activity
  - Active tests: 2-5 second intervals
  - No active tests: 30-60 second intervals
  - Background monitoring: 5-10 minutes

### 3. Delta Updates Instead of Full Refresh
- **Current**: Full data refresh every 30 seconds
- **Recommended**: Incremental updates based on changes
- **Implementation**: Use timestamps, change detection, WebSocket consideration

### 4. Caching and State Management
- **Current**: Multiple overlapping caches
- **Recommended**: Centralized state management
- **Benefits**: Consistent data, reduced memory usage, easier debugging

## Next Steps for Performance Improvements

1. **Audit Current API Usage**: Map all polling endpoints and their data overlap
2. **Implement Polling Coordinator**: Single service managing all polling activities
3. **Add Activity-Based Intervals**: Adjust polling frequency based on test activity
4. **Optimize Data Structures**: Consolidate caches and state management
5. **Add Performance Monitoring**: Track API call frequency and response times
6. **Consider WebSocket Integration**: For real-time updates without polling

## Technical Debt Identified

1. **Multiple State Sources**: activeTests Map, recentRunsCache, dashboard counters
2. **Inconsistent Error Handling**: Different approaches across polling functions
3. **Mixed Status Vocabularies**: 'running', 'RUNNING', 'pending', etc.
4. **Hard-coded Intervals**: No configuration management for polling frequencies
5. **No Polling Lifecycle Management**: Difficult to pause/resume polling

---
*Document created: 2025-07-02*
*Related to: Active Tests flickering bug fix and dashboard performance optimization*

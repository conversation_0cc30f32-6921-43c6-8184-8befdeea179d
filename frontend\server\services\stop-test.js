/**
 * Stop Test Service
 * Handles stopping test runs via the external RemoveSession API
 */
const fetch = require('node-fetch');
const { getJsessionId, getFreshJsessionId } = require('./cookie-auth');

/**
 * Stops a test run via the external RemoveSession API
 * @param {string} tsnId - Test session ID
 * @param {string} uid - User ID
 * @param {string} password - Password
 * @param {boolean} forceFreshLogin - Force fresh login instead of using cached session (default: true)
 * @returns {Promise<Object>} - API response
 */
async function stopTest(tsnId, uid, password, forceFreshLogin = true) {
  const logPrefix = `[stopTest Service][TSN:${tsnId}]`;

  try {
    console.log(`${logPrefix} ===== STOP TEST REQUEST STARTED =====`);
    console.log(`${logPrefix} Input parameters:`, { tsnId, uid: uid ? uid.substring(0, 10) + '...' : 'N/A', password: password ? '[PROVIDED]' : '[MISSING]' });

    // Input validation
    if (!tsnId) {
      console.error(`${logPrefix} VALIDATION ERROR: Missing tsn_id parameter`);
      throw new Error('Missing or invalid tsn_id (test session ID) parameter.');
    }

    // Get JSESSIONID cookie (fresh or cached based on parameter)
    let jsessionId;
    try {
      if (forceFreshLogin) {
        console.log(`${logPrefix} Attempting to get FRESH JSESSIONID cookie for user: ${uid}`);
        console.log(`${logPrefix} 🔄 FORCING FRESH LOGIN - bypassing cache for stop operation`);
        jsessionId = await getFreshJsessionId(uid, password);
        console.log(`${logPrefix} Successfully obtained FRESH JSESSIONID: ${jsessionId.substring(0, 10)}...`);
      } else {
        console.log(`${logPrefix} Attempting to get JSESSIONID cookie for user: ${uid} (cached allowed)`);
        jsessionId = await getJsessionId(uid, password);
        console.log(`${logPrefix} Successfully obtained JSESSIONID: ${jsessionId.substring(0, 10)}...`);
      }

      if (!jsessionId) {
        console.error(`${logPrefix} AUTHENTICATION ERROR: Failed to obtain JSESSIONID cookie`);
        throw new Error('Failed to obtain valid JSESSIONID cookie');
      }
    } catch (authError) {
      console.error(`${logPrefix} AUTHENTICATION ERROR:`, authError);
      throw new Error(`Authentication error: ${authError.message}`);
    }

    // Build the URL for the RemoveSession endpoint
    const url = 'http://mprts-qa02.lab.wagerworks.com:9080/AutoRun/RemoveSession';

    // Prepare the form data
    const formData = new URLSearchParams();
    formData.append('tsn_id', tsnId);

    console.log(`${logPrefix} Prepared request:`, {
      url,
      method: 'POST',
      formData: formData.toString(),
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
        'Cookie': `JSESSIONID=${jsessionId.substring(0, 10)}...`
      }
    });

    // Make the request to the external API with timeout
    let response;
    const requestStartTime = Date.now();

    try {
      console.log(`${logPrefix} ===== SENDING HTTP REQUEST =====`);
      const controller = new AbortController();
      const timeout = setTimeout(() => {
        console.error(`${logPrefix} REQUEST TIMEOUT: External API did not respond within 10 seconds`);
        controller.abort();
      }, 10000); // 10 second timeout

      response = await fetch(url, {
        method: 'POST',
        body: formData,
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
          'Origin': 'http://mprts-qa02.lab.wagerworks.com:9080',
          'Referer': `http://mprts-qa02.lab.wagerworks.com:9080/AutoRun/ReportSummary?tsn_id=${tsnId}`,
          'X-Requested-With': 'XMLHttpRequest',
          'Cookie': `JSESSIONID=${jsessionId}`
        },
        signal: controller.signal
      });

      clearTimeout(timeout);
      const requestDuration = Date.now() - requestStartTime;

      console.log(`${logPrefix} ===== HTTP RESPONSE RECEIVED =====`);
      console.log(`${logPrefix} Request duration: ${requestDuration}ms`);
      console.log(`${logPrefix} Response status: ${response.status} ${response.statusText}`);
      console.log(`${logPrefix} Response headers:`, Object.fromEntries(response.headers.entries()));

    } catch (fetchError) {
      const requestDuration = Date.now() - requestStartTime;
      console.error(`${logPrefix} ===== HTTP REQUEST FAILED =====`);
      console.error(`${logPrefix} Request duration: ${requestDuration}ms`);
      console.error(`${logPrefix} Fetch error:`, fetchError);

      if (fetchError.name === 'AbortError') {
        throw new Error('Request timed out when connecting to external API');
      }
      throw new Error(`Network error: ${fetchError.message}`);
    }
    
    // Process the response
    let responseText;
    try {
      responseText = await response.text();
      console.log(`${logPrefix} ===== RESPONSE BODY ANALYSIS =====`);
      console.log(`${logPrefix} Raw response text: '${responseText}'`);
      console.log(`${logPrefix} Response length: ${responseText.length} characters`);
      console.log(`${logPrefix} Response trimmed: '${responseText.trim()}'`);
      console.log(`${logPrefix} Response type: ${typeof responseText}`);
      console.log(`${logPrefix} Response is empty: ${responseText.length === 0}`);
    } catch (textError) {
      console.error(`${logPrefix} ERROR READING RESPONSE TEXT:`, textError);
      throw new Error(`Failed to read API response: ${textError.message}`);
    }

    // Check if the response is OK
    if (!response.ok) {
      console.error(`${logPrefix} ===== HTTP ERROR RESPONSE =====`);
      console.error(`${logPrefix} HTTP Status: ${response.status} ${response.statusText}`);
      console.error(`${logPrefix} Response Body: '${responseText}'`);
      throw new Error(`External API returned error status: ${response.status} ${response.statusText}`);
    }

    console.log(`${logPrefix} ===== RESPONSE ANALYSIS =====`);

    // Check if the response indicates success
    if (responseText && responseText.trim() === 'Removed') {
      console.log(`${logPrefix} ✅ SUCCESS: External API confirmed test was removed`);
      console.log(`${logPrefix} Returning success response to client`);
      return {
        success: true,
        message: `Test session ${tsnId} stopped successfully`,
        actualResult: 'REMOVED',
        originalResponse: 'Removed'
      };
    }
    // Handle the common 'Error' response which could mean various things
    else if (responseText && responseText.trim() === 'Error') {
      console.warn(`${logPrefix} ⚠️  AMBIGUOUS RESPONSE: External API returned 'Error'`);
      console.warn(`${logPrefix} This could mean:`);
      console.warn(`${logPrefix}   - Test was already stopped`);
      console.warn(`${logPrefix}   - Test doesn't exist`);
      console.warn(`${logPrefix}   - Test is in a state that can't be stopped`);
      console.warn(`${logPrefix}   - Authentication/permission issue`);
      console.warn(`${logPrefix}   - Internal error in external API`);

      // Try to check the actual test status to determine what really happened
      console.log(`${logPrefix} Attempting to verify actual test state...`);
      let actualStatus = 'unknown';
      let statusCheckError = null;

      try {
        const { getTestStatus } = require('./test-status');
        const statusResult = await getTestStatus(tsnId, uid, password);
        actualStatus = statusResult.status || 'unknown';
        console.log(`${logPrefix} Status check result:`, statusResult);

        if (actualStatus === 'completed' || actualStatus === 'stopped' || actualStatus === 'failed') {
          console.log(`${logPrefix} ✅ VERIFIED: Test is actually stopped/completed (status: ${actualStatus})`);
          return {
            success: true,
            status: actualStatus,
            message: `Test run ${tsnId} was successfully stopped. External API returned 'Error' but status verification confirms the test is ${actualStatus}.`,
            actualResult: 'VERIFIED_STOPPED',
            originalResponse: 'Error',
            verifiedStatus: actualStatus
          };
        } else if (actualStatus === 'running') {
          console.warn(`${logPrefix} ⚠️  FAILED: Test is still running (status: ${actualStatus})`);
          return {
            success: false,
            status: actualStatus,
            message: `Failed to stop test run ${tsnId}. External API returned 'Error' and status verification shows the test is still ${actualStatus}.`,
            actualResult: 'FAILED_TO_STOP',
            originalResponse: 'Error',
            verifiedStatus: actualStatus
          };
        }
      } catch (statusError) {
        console.error(`${logPrefix} Failed to verify test status:`, statusError);
        statusCheckError = statusError.message;
      }

      console.warn(`${logPrefix} Returning 'partial success' to prevent UI errors`);
      console.warn(`${logPrefix} ⚠️  WARNING: This may not reflect the actual test state!`);

      return {
        success: true, // We're returning success to prevent UI errors
        status: actualStatus,
        message: `Test run ${tsnId} may have already been stopped or doesn't exist. The system attempted to stop it, but the external API returned 'Error'.${statusCheckError ? ` Status verification failed: ${statusCheckError}` : ''}`,
        warning: true,
        actualResult: 'ERROR_RESPONSE',
        originalResponse: 'Error',
        verifiedStatus: actualStatus,
        statusCheckError: statusCheckError,
        recommendation: 'Check test status manually to verify actual state'
      };
    }
    // Handle any other unexpected responses
    else {
      console.error(`${logPrefix} ===== UNEXPECTED RESPONSE =====`);
      console.error(`${logPrefix} ❌ UNEXPECTED: External API returned unexpected response`);
      console.error(`${logPrefix} Response: '${responseText}'`);
      console.error(`${logPrefix} Response length: ${responseText.length}`);
      console.error(`${logPrefix} Response type: ${typeof responseText}`);

      throw new Error(`Unexpected API response: ${responseText || 'Empty response'}`);
    }
  } catch (error) {
    // Catch all errors and provide a consistent error structure
    console.error(`${logPrefix} ===== STOP TEST FAILED =====`);
    console.error(`${logPrefix} Final error:`, error);
    console.error(`${logPrefix} Error type: ${error.constructor.name}`);
    console.error(`${logPrefix} Error message: ${error.message}`);

    throw error; // Re-throw the error with improved details
  } finally {
    console.log(`${logPrefix} ===== STOP TEST REQUEST COMPLETED =====`);
  }
}

module.exports = {
  stopTest
};

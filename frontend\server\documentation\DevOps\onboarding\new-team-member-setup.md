# New Team Member Setup Guide

Welcome to the SmartTest DevOps team! This guide will get you up and running with our GitHub automation and DevOps processes.

## 🎯 Prerequisites

Before starting, ensure you have:
- [ ] GitHub account with access to the SmartTest repository
- [ ] GitHub Premium features enabled on your account
- [ ] GitHub Copilot subscription (included with Premium)
- [ ] Basic understanding of Git, Node.js, and CI/CD concepts

## 🚀 Initial Setup (30 minutes)

### Step 1: Repository Access
1. **Clone the repository**:
   ```bash
   git clone https://github.com/yacov/smarttest.git
   cd smarttest
   ```

2. **Check your branch access**:
   ```bash
   git branch -a
   # You should see: main, develop, and feature branches
   ```

3. **Verify GitHub CLI access** (optional but recommended):
   ```bash
   gh auth login
   gh repo view yacov/smarttest
   ```

### Step 2: GitHub Settings Configuration

#### Enable Repository Notifications
1. Go to **Repository Settings** → **Notifications**
2. Enable notifications for:
   - [ ] **Issues and PRs**: Get notified of new issues and pull requests
   - [ ] **Actions**: Get notified when workflows fail
   - [ ] **Security alerts**: Critical for DevOps monitoring

#### Configure Personal Access Token
1. Go to **GitHub Settings** → **Developer settings** → **Personal access tokens**
2. Create a new token with these scopes:
   - [ ] `repo` (Full repository access)
   - [ ] `workflow` (Update GitHub Action workflows)
   - [ ] `write:packages` (Upload packages to GitHub Package Registry)
   - [ ] `read:org` (Read organization membership)

### Step 3: Local Development Environment

#### Option A: GitHub Codespaces (Recommended)
1. **Open in Codespaces**:
   - Go to repository → **Code** → **Codespaces** → **Create codespace**
   - Pre-configured environment loads automatically
   - All tools and extensions pre-installed

2. **Test the setup**:
   ```bash
   npm install
   npm test
   npm run build
   ```

#### Option B: Local Setup
1. **Install Node.js**:
   ```bash
   # Install Node.js 18.x (recommended)
   node --version  # Should be 18.x or higher
   npm --version   # Should be 9.x or higher
   ```

2. **Install dependencies**:
   ```bash
   npm install
   ```

3. **Run initial tests**:
   ```bash
   npm test
   npm run build
   ```

## 🔧 Understanding Our Automation

### GitHub Actions Workflows Overview

| Workflow File | Purpose | When It Runs |
|---------------|---------|--------------|
| `.github/workflows/ci.yml` | Continuous Integration | Every push/PR |
| `.github/workflows/release.yml` | Automated releases | Push to main |
| `.github/workflows/security-scan.yml` | Security scanning | Daily + PR |
| `.github/workflows/dependency-update.yml` | Dependency updates | Weekly |
| `.github/workflows/deploy-environments.yml` | Deploy to environments | main branch + tags |

### Key GitHub Features We Use

#### 🤖 **GitHub Actions**
- **Purpose**: Automated CI/CD, testing, deployments
- **Your role**: Monitor workflow status, troubleshoot failures
- **Location**: Repository → **Actions** tab

#### 🏗️ **GitHub Environments**
- **Purpose**: Staging and production deployment controls
- **Your role**: Approve production deployments, manage environment secrets
- **Location**: Repository → **Settings** → **Environments**

#### 🔒 **GitHub Security**
- **Purpose**: Vulnerability scanning, dependency alerts
- **Your role**: Review and address security alerts
- **Location**: Repository → **Security** tab

#### 📋 **GitHub Projects**
- **Purpose**: Automated issue and PR management
- **Your role**: Monitor project boards, manage issue labels
- **Location**: Repository → **Projects** tab

## 📚 Essential Knowledge

### Semantic Versioning (SemVer)
We follow strict semantic versioning:
- **MAJOR.MINOR.PATCH** (e.g., 1.2.0)
- **Major**: Breaking changes (1.x.x → 2.0.0)
- **Minor**: New features (1.2.x → 1.3.0)
- **Patch**: Bug fixes (1.2.0 → 1.2.1)

### Conventional Commits
All commits must follow this format:
```
<type>(<scope>): <description>

[optional body]

[optional footer(s)]
```

**Examples**:
```bash
feat(dashboard): add test suite selection UI
fix(api): resolve authentication timeout issue
docs(devops): update deployment procedures
chore(deps): update node-fetch to v2.7.0
```

**Types**:
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation
- `style`: Formatting
- `refactor`: Code restructuring
- `test`: Adding tests
- `chore`: Build/dependency updates

## 🎯 Your First Week Tasks

### Day 1: Environment Setup
- [ ] Complete this setup guide
- [ ] Test local development environment
- [ ] Run your first GitHub Codespace
- [ ] Introduce yourself in team chat

### Day 2: Workflow Familiarization  
- [ ] Review all GitHub Actions workflows
- [ ] Understand the CI/CD pipeline
- [ ] Practice with a test commit following conventional commits
- [ ] Review current GitHub Issues and PRs

### Day 3: Security & Quality
- [ ] Review security scanning results
- [ ] Understand dependency management workflow
- [ ] Check current vulnerability alerts
- [ ] Review code quality metrics

### Day 4: Release Process
- [ ] Study the automated release workflow
- [ ] Practice version bumping with npm scripts
- [ ] Understand changelog generation process
- [ ] Review deployment environments

### Day 5: Documentation & Planning
- [ ] Update any documentation gaps you noticed
- [ ] Plan improvements to existing workflows
- [ ] Shadow a production deployment
- [ ] Provide feedback on DevOps processes

## 🚨 Emergency Contacts & Escalation

### When Things Go Wrong
1. **CI/CD Pipeline Failure**:
   - Check GitHub Actions logs
   - Look for common issues in [troubleshooting guide](../operations/troubleshooting.md)
   - Escalate to senior DevOps engineer if unresolved in 30 minutes

2. **Production Deployment Issues**:
   - Immediate rollback using GitHub Environments
   - Notify team in emergency chat
   - Document incident for post-mortem

3. **Security Alerts**:
   - Review alert severity in GitHub Security tab
   - For HIGH/CRITICAL: Immediate action required
   - Create hotfix branch if needed

## 📖 Next Steps

Once you've completed this setup:

1. **Read**: [GitHub Features Overview](github-features-overview.md)
2. **Study**: [CI Pipeline Documentation](../workflows/ci-pipeline.md)
3. **Practice**: [Daily Routines](../operations/daily-routines.md)
4. **Master**: [Release Procedures](../versioning/release-procedures.md)

## ✅ Setup Verification Checklist

Before moving on, verify you can:
- [ ] Access GitHub repository with appropriate permissions
- [ ] Run SmartTest locally or in Codespaces
- [ ] View and understand GitHub Actions workflow runs
- [ ] Create a test commit with conventional commit format
- [ ] Access GitHub Security and Environment tabs
- [ ] Trigger a test workflow (on a feature branch)

---

**Welcome to the team! 🎉** 

If you have any questions during setup, don't hesitate to ask in the team chat or create an issue with the `question` label.

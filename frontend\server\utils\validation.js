/**
 * Input Validation and Sanitization Utilities
 * Provides secure validation for authentication and API parameters
 */

const validator = require('validator');

class ValidationUtils {
  /**
   * Validate and sanitize email address
   * @param {string} email - Email to validate
   * @returns {Object} Validation result
   */
  static validateEmail(email) {
    if (!email || typeof email !== 'string') {
      return { isValid: false, error: 'Email is required and must be a string' };
    }

    // Trim whitespace
    const trimmed = email.trim();
    
    // Check length
    if (trimmed.length === 0) {
      return { isValid: false, error: 'Email cannot be empty' };
    }
    
    if (trimmed.length > 254) {
      return { isValid: false, error: 'Email is too long (max 254 characters)' };
    }

    // Validate email format
    if (!validator.isEmail(trimmed)) {
      return { isValid: false, error: 'Invalid email format' };
    }

    // Additional security checks
    if (this.containsSqlInjection(trimmed) || this.containsXss(trimmed)) {
      return { isValid: false, error: 'Email contains invalid characters' };
    }

    return { isValid: true, sanitized: trimmed.toLowerCase() };
  }

  /**
   * Validate and sanitize password
   * @param {string} password - Password to validate
   * @param {Object} policy - Password policy
   * @returns {Object} Validation result
   */
  static validatePassword(password, policy = {}) {
    if (!password || typeof password !== 'string') {
      return { isValid: false, error: 'Password is required and must be a string' };
    }

    const minLength = policy.minLength || 4;
    const maxLength = policy.maxLength || 128;
    const requireSpecialChars = policy.requireSpecialChars || false;

    // Check length
    if (password.length < minLength) {
      return { isValid: false, error: `Password must be at least ${minLength} characters long` };
    }
    
    if (password.length > maxLength) {
      return { isValid: false, error: `Password is too long (max ${maxLength} characters)` };
    }

    // Check for special characters if required
    if (requireSpecialChars && !/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      return { isValid: false, error: 'Password must contain at least one special character' };
    }

    // Security checks
    if (this.containsSqlInjection(password)) {
      return { isValid: false, error: 'Password contains invalid characters' };
    }

    return { isValid: true, sanitized: password };
  }

  /**
   * Validate string parameter
   * @param {string} value - Value to validate
   * @param {Object} options - Validation options
   * @returns {Object} Validation result
   */
  static validateString(value, options = {}) {
    const {
      required = false,
      minLength = 0,
      maxLength = 255,
      allowEmpty = false,
      pattern = null,
      name = 'parameter'
    } = options;

    if (!value) {
      if (required) {
        return { isValid: false, error: `${name} is required` };
      }
      if (!allowEmpty) {
        return { isValid: true, sanitized: '' };
      }
    }

    if (typeof value !== 'string') {
      return { isValid: false, error: `${name} must be a string` };
    }

    // Trim whitespace
    const trimmed = value.trim();

    // Check length
    if (trimmed.length < minLength) {
      return { isValid: false, error: `${name} must be at least ${minLength} characters long` };
    }
    
    if (trimmed.length > maxLength) {
      return { isValid: false, error: `${name} is too long (max ${maxLength} characters)` };
    }

    // Check pattern if provided
    if (pattern && !pattern.test(trimmed)) {
      return { isValid: false, error: `${name} format is invalid` };
    }

    // Security checks
    if (this.containsSqlInjection(trimmed) || this.containsXss(trimmed)) {
      return { isValid: false, error: `${name} contains invalid characters` };
    }

    return { isValid: true, sanitized: trimmed };
  }

  /**
   * Validate integer parameter
   * @param {any} value - Value to validate
   * @param {Object} options - Validation options
   * @returns {Object} Validation result
   */
  static validateInteger(value, options = {}) {
    const {
      required = false,
      min = Number.MIN_SAFE_INTEGER,
      max = Number.MAX_SAFE_INTEGER,
      name = 'parameter'
    } = options;

    if (value === null || value === undefined || value === '') {
      if (required) {
        return { isValid: false, error: `${name} is required` };
      }
      return { isValid: true, sanitized: null };
    }

    // Convert to number
    const num = parseInt(value, 10);
    
    if (isNaN(num)) {
      return { isValid: false, error: `${name} must be a valid integer` };
    }

    // Check range
    if (num < min) {
      return { isValid: false, error: `${name} must be at least ${min}` };
    }
    
    if (num > max) {
      return { isValid: false, error: `${name} must be at most ${max}` };
    }

    return { isValid: true, sanitized: num };
  }

  /**
   * Check for SQL injection patterns
   * @param {string} input - Input to check
   * @returns {boolean} True if suspicious patterns found
   */
  static containsSqlInjection(input) {
    const sqlPatterns = [
      /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)/i,
      /(--|\/\*|\*\/|;|'|"|`)/,
      /(\bOR\b|\bAND\b).*(\b=\b|\b<\b|\b>\b)/i,
      /(INFORMATION_SCHEMA|SYSOBJECTS|SYSCOLUMNS)/i
    ];

    return sqlPatterns.some(pattern => pattern.test(input));
  }

  /**
   * Check for XSS patterns
   * @param {string} input - Input to check
   * @returns {boolean} True if suspicious patterns found
   */
  static containsXss(input) {
    const xssPatterns = [
      /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
      /<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi,
      /javascript:/i,
      /on\w+\s*=/i,
      /<[^>]*\s(on\w+|href|src)\s*=\s*["']?[^"'>]*["']?[^>]*>/i
    ];

    return xssPatterns.some(pattern => pattern.test(input));
  }

  /**
   * Sanitize HTML content
   * @param {string} input - Input to sanitize
   * @returns {string} Sanitized input
   */
  static sanitizeHtml(input) {
    if (!input || typeof input !== 'string') {
      return '';
    }

    return input
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#x27;')
      .replace(/\//g, '&#x2F;');
  }

  /**
   * Validate authentication credentials
   * @param {Object} credentials - Credentials to validate
   * @returns {Object} Validation result
   */
  static validateCredentials(credentials) {
    const errors = [];

    // Validate email
    const emailResult = this.validateEmail(credentials.uid);
    if (!emailResult.isValid) {
      errors.push(`Username: ${emailResult.error}`);
    }

    // Validate password
    const passwordResult = this.validatePassword(credentials.password);
    if (!passwordResult.isValid) {
      errors.push(`Password: ${passwordResult.error}`);
    }

    if (errors.length > 0) {
      return { isValid: false, errors };
    }

    return {
      isValid: true,
      sanitized: {
        uid: emailResult.sanitized,
        password: passwordResult.sanitized
      }
    };
  }

  /**
   * Rate limiting check
   * @param {string} identifier - IP or user identifier
   * @param {number} maxAttempts - Maximum attempts allowed
   * @param {number} windowMs - Time window in milliseconds
   * @returns {Object} Rate limit result
   */
  static checkRateLimit(identifier, maxAttempts = 5, windowMs = 900000) {
    // This would typically use Redis or a database
    // For now, using in-memory storage (not suitable for production)
    if (!this.rateLimitStore) {
      this.rateLimitStore = new Map();
    }

    const now = Date.now();
    const key = `rate_limit_${identifier}`;
    const attempts = this.rateLimitStore.get(key) || [];

    // Remove old attempts outside the window
    const validAttempts = attempts.filter(timestamp => now - timestamp < windowMs);

    if (validAttempts.length >= maxAttempts) {
      return {
        allowed: false,
        retryAfter: Math.ceil((validAttempts[0] + windowMs - now) / 1000)
      };
    }

    // Add current attempt
    validAttempts.push(now);
    this.rateLimitStore.set(key, validAttempts);

    return { allowed: true, remaining: maxAttempts - validAttempts.length };
  }
}

module.exports = ValidationUtils;

#!/bin/bash

# Test External CaseRunner API with All Configuration Parameters
# This script tests the external API with all 12 configuration parameters
# to verify that the enhanced parameter support will work

echo "Testing External CaseRunner API with All Configuration Parameters"
echo "================================================================="

# Configuration - REPLACE THESE VALUES WITH YOUR ACTUAL CREDENTIALS
TEST_CASE_ID="3180"  # Replace with a valid test case ID
USERNAME="<EMAIL>"  # Replace with your username
PASSWORD="test"  # Replace with your password
ENVIRONMENT="qa02"  # Environment to test

echo "Test Case ID: $TEST_CASE_ID"
echo "Username: $USERNAME"
echo "Environment: $ENVIRONMENT"
echo ""

# Create the curl command with all parameters
echo "Executing curl command..."
echo ""

curl -X POST "http://mprts-qa02.lab.wagerworks.com:5080/AutoRun/CaseRunner" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "uid=${USERNAME}" \
  -d "password=${PASSWORD}" \
  -d "tc_id=${TEST_CASE_ID}" \
  -d "envir=${ENVIRONMENT}" \
  -d "shell_host=jps-qa10-app01" \
  -d "file_path=/home/<USER>/" \
  -d "operatorConfigs=operatorNameConfigs" \
  -d "kafka_server=kafka-qa-a0.lab.wagerworks.com" \
  -d "dataCenter=GU" \
  -d "rgs_env=${ENVIRONMENT}" \
  -d "old_version=0" \
  -d "networkType1=multi-site" \
  -d "networkType2=multi-site" \
  -d "sign=-" \
  -d "rate_src=local" \
  --verbose

echo ""
echo "================================================================="
echo "Expected Response:"
echo "- Success: Should return text containing 'Your test session id: [NUMBER]'"
echo "- Failure: Check credentials, test case ID, and network connectivity"
echo ""
echo "Parameters Used (from app-config.js DEFAULT_PARAMS):"
echo "1. envir: $ENVIRONMENT (currently configurable)"
echo "2. shell_host: jps-qa10-app01 (currently configurable)"
echo "3. file_path: /home/<USER>/ (NEW)"
echo "4. operatorConfigs: operatorNameConfigs (NEW)"
echo "5. kafka_server: kafka-qa-a0.lab.wagerworks.com (NEW)"
echo "6. dataCenter: GU (NEW)"
echo "7. rgs_env: $ENVIRONMENT (NEW - matches envir)"
echo "8. old_version: 0 (NEW)"
echo "9. networkType1: multi-site (NEW)"
echo "10. networkType2: multi-site (NEW)"
echo "11. sign: - (NEW)"
echo "12. rate_src: local (NEW)"
echo ""
echo "If this test succeeds, it confirms that the external API supports"
echo "all the additional parameters we want to expose in the UI."

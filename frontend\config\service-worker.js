/**
 * SmartTest - Custom Test Runner Service Worker
 * 
 * This service worker provides offline capabilities
 * and improves performance through caching.
 */

const CACHE_NAME = 'smarttest-runner-v1';
const ASSETS_TO_CACHE = [
  './',
  './index.html',
  './styles.css',
  './config.js'
];

// Install event - cache static assets
self.addEventListener('install', (event) => {
  console.log('[Service Worker] Installing service worker...');
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => {
        console.log('[Service Worker] Caching app assets');
        return cache.addAll(ASSETS_TO_CACHE);
      })
      .catch((error) => {
        console.error('[Service Worker] Cache failure:', error);
      })
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('[Service Worker] Activating service worker...');
  event.waitUntil(
    caches.keys().then((keyList) => {
      return Promise.all(keyList.map((key) => {
        if (key !== CACHE_NAME) {
          console.log('[Service Worker] Removing old cache', key);
          return caches.delete(key);
        }
      }));
    })
  );
  return self.clients.claim();
});

// Fetch event - serve from cache or network
self.addEventListener('fetch', event => {
  // Skip caching for POST requests and other non-GET methods
  if (event.request.method !== 'GET') {
    return;
  }

  // Skip caching for API requests
  if (event.request.url.includes('/api/') ||
      event.request.url.includes('/local/')) {
    return;
  }

  // Skip caching for chrome-extension URLs and external resources
  const url = new URL(event.request.url);
  if (url.protocol === 'chrome-extension:' ||
      url.hostname === 'fonts.googleapis.com' ||
      url.hostname === 'fonts.gstatic.com' ||
      !url.hostname.includes('localhost')) {
    // Let these requests pass through without caching
    return;
  }

  event.respondWith(
    caches.match(event.request)
      .then(response => {
        // Return cached version or fetch from network
        return response || fetch(event.request)
          .then(response => {
            // Only cache successful GET responses from same origin
            if (response.status === 200 &&
                event.request.method === 'GET' &&
                url.hostname.includes('localhost')) {
              const responseClone = response.clone();
              caches.open(CACHE_NAME)
                .then(cache => {
                  cache.put(event.request, responseClone);
                })
                .catch(error => {
                  console.warn('[Service Worker] Cache put failed:', error);
                });
            }
            return response;
          })
          .catch(error => {
            console.warn('[Service Worker] Fetch failed:', error);
            // Return a fallback response or let the error propagate
            throw error;
          });
      })
  );
});


# Rerunning Test Cases with Original Parent Suite Parameters

This document outlines the mechanism for extracting parameters from an original parent test suite run and passing them to the `caseRunner` API when an individual test case is rerun from the reports page. The primary goal is to ensure that a rerun test case executes with the same contextual parameters as its original parent suite, overriding any system defaults where applicable.

## 1. Objective

When rerunning a specific test case, it's often critical that it executes under the same environmental and configuration parameters as its parent test suite's original run. This ensures consistency and allows for accurate reproduction or debugging of specific test scenarios. This mechanism overrides default parameters with those extracted from the original test suite.

## 2. Parameter Source

The original parameters for a test suite run are primarily sourced from the HTML reports generated by the external system. These parameters are typically found in a "Variables" section within the:
*   **Report Summary HTML**: Provides a general overview and often a list of initial variables.
*   **Report Details HTML**: Can contain a more detailed list of parameters, potentially including those specific to individual test steps or configurations at runtime.

These HTML reports are fetched when a user views the details of a specific test run (`tsn_id`) on the reports page.

## 3. Parameter Extraction Process (`frontend/reports/reports.js`)

The extraction of parameters occurs primarily within the `loadTestDetailsFromExternalApi` function when fetching details for a test run:

1.  **Fetching HTML Reports**:
    *   The function makes calls to `/api/external/ReportSummary?tsn_id=<ID>` and `/api/external/ReportDetails?tsn_id=<ID>` to get the respective HTML content.

2.  **Parsing HTML for Variables**:
    *   `parseReportSummaryHtml(summaryHtml, testId)`: Parses the Report Summary HTML. It looks for a section typically starting with "Variables:" and extracts key-value pairs. These are stored in `summaryData.variables`.
    *   `parseReportDetailsHtml(detailsHtml, testId)`: Parses the Report Details HTML. It also looks for a "Variables:" section and extracts key-value pairs. These are stored in `detailsData.originalParameters`.
    *   Both parsing functions are designed to handle variables presented as `key=value` lines, separated by `<br>` tags in the HTML. They attempt to robustly parse these lines.

3.  **Populating `testDetails.originalParameters`**:
    *   When constructing the `testDetails` object within `loadTestDetailsFromExternalApi`, the `originalParameters` property is populated using the following priority:
        ```javascript
        testDetails.originalParameters = detailsData.originalParameters || summaryData.variables || {};
        ```
        This means parameters from the `ReportDetails` HTML are prioritized. If they are not found, it falls back to parameters from the `ReportSummary` HTML. If neither is found, it defaults to an empty object.
    *   All parameter values extracted through HTML parsing are stored as **strings**.

4.  **Storing in Current State**:
    *   The assembled `testDetails` object, including `testDetails.originalParameters`, is then stored in `currentState.currentTestDetails`.

## 4. Parameter Passing During Rerun (`frontend/reports/reports.js`)

When a user initiates a rerun of selected test cases:

1.  **Retrieving Parameters (`handleRerunSelectedClick`)**:
    *   The `handleRerunSelectedClick` function retrieves the stored parameters for the parent suite:
        ```javascript
        const originalParametersFromSuite = currentState.currentTestDetails?.originalParameters || {};
        ```

2.  **Constructing the Payload (`triggerTestCaseRerun`)**:
    *   The `originalParametersFromSuite` are passed to the `triggerTestCaseRerun(tcId, tsnId, uid, password, originalParameters)` function.
    *   Inside `triggerTestCaseRerun`, the `payload` for the `caseRunner` API is constructed:
        ```javascript
        const payload = { ...originalParameters }; // Start with parameters from the parent suite
        ```
    *   **Critical ID Handling**:
        *   `if (payload.ts_id) { delete payload.ts_id; }`: The test suite ID (`ts_id`) from the original parameters is deleted because a specific test case (`tc_id`) is being rerun, not the entire suite.
        *   `if (payload.tsn_id) { delete payload.tsn_id; }`: Any `tsn_id` (test session ID) present in the `originalParameters` is deleted. This is crucial because the new rerun must generate its own unique `tsn_id`. The old `tsn_id` is not relevant for identifying the new run.
    *   **Essential Rerun Information**:
        *   `payload.tc_id = tcId;`: The specific test case ID for the rerun.
        *   `payload.uid = uid;`: Current user's ID.
        *   `payload.password = password;`: Current user's password.
        *   The `tsnId` argument (parent's test session ID) passed to `triggerTestCaseRerun` is **not** assigned to `payload.tsn_id`. This ensures the new run gets a new ID. If the backend requires the parent `tsn_id` for other contextual reasons, it should be passed under a different parameter name.

## 5. Parameter Handling in API Service (`frontend/shared/services/unified-api-service.js`)

The `UnifiedApiService` handles the actual API call to `/api/case-runner`:

1.  **`runTestCase(tcId, params)` Method**:
    *   The `params` argument received by this method is the `payload` constructed in `triggerTestCaseRerun`.
    *   Default parameters for the service are defined in `this.defaultTestParams` (all values are strings).
    *   The final `testParams` sent to the API are constructed as follows for the 'reports' module context:
        ```javascript
        testParams = {
          tc_id: tcId,                 // Specific tc_id for the new run
          ...this.defaultTestParams,  // Default parameters (all strings)
          ...params                  // Parameters from parent suite (all strings), overrides defaults
        };
        ```
2.  **Override Mechanism**:
    *   The spread order (`...this.defaultTestParams, ...params`) is critical. It ensures that any parameter present in `params` (i.e., sourced from `originalParametersFromSuite`) will override the corresponding parameter in `this.defaultTestParams`.
    *   Since both `defaultTestParams` and the parsed `originalParameters` store values as strings, the override process is type-consistent at this stage.

## 6. Final Payload to `/api/case-runner`

The `testParams` object, which is a result of merging default parameters with the overriding original suite parameters (and essential rerun info like `tc_id`, `uid`), is then JSON stringified and sent as the body of the POST request to the `/api/case-runner` endpoint. The backend is then responsible for interpreting these parameters and initiating the new test case run, generating a new unique `tsn_id` for it.

## 7. Key Console Logs for Debugging

To trace and verify the flow of these parameters, the following `console.log` statements have been strategically placed:

*   **In `frontend/reports/reports.js` (`loadTestDetailsFromExternalApi`):**
    ```javascript
    console.log('[DEBUG] loadTestDetailsFromExternalApi: Assembled testDetails.originalParameters:', JSON.stringify(testDetails.originalParameters, null, 2));
    ```
    *Purpose: Shows the `originalParameters` object as it's assembled from parsed HTML and stored in `currentState.currentTestDetails`.*

*   **In `frontend/reports/reports.js` (`handleRerunSelectedClick`):**
    ```javascript
    console.log('[DEBUG] handleRerunSelectedClick: Using original parameters for bulk rerun:', originalParametersFromSuite);
    ```
    *Purpose: Shows the parameters being retrieved from the state just before initiating a rerun.*

*   **In `frontend/reports/reports.js` (`triggerTestCaseRerun`):**
    ```javascript
    console.log(`[DEBUG] triggerTestCaseRerun: Received TC_ID: ${tcId}, Parent_TSN_ID: ${tsnId}`);
    console.log('[DEBUG] triggerTestCaseRerun: Received originalParameters:', JSON.stringify(originalParameters, null, 2));
    console.log('[DEBUG] triggerTestCaseRerun: Payload after spreading originalParameters:', JSON.stringify(payload, null, 2));
    // ... (after tc_id, uid, password are set, and ts_id/tsn_id are deleted)
    console.log('[DEBUG] triggerTestCaseRerun: Final payload for /api/case-runner:', JSON.stringify(payload, null, 2));
    ```
    *Purpose: Provides a step-by-step view of how the `payload` is constructed within this function.*

*   **In `frontend/shared/services/unified-api-service.js` (`UnifiedApiService.runTestCase`):**
    ```javascript
    console.log('[DEBUG] UnifiedApiService.runTestCase: Received tcId:', tcId, 'Received params (payload from frontend):', JSON.stringify(params, null, 2));
    // ... (after testParams is constructed by merging defaults and params)
    console.log('[DEBUG] UnifiedApiService.runTestCase: Final testParams before API call:', JSON.stringify(testParams, null, 2));
    ```
    *Purpose: Shows the parameters as received by the API service and the final set of parameters after merging with system defaults, just before the actual call to `/api/case-runner`.*

These logs allow developers to verify that parameters are correctly extracted, propagated, and merged at each critical step of the rerun process. 
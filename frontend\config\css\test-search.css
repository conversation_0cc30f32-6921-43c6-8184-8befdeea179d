/**
 * Test Case Search & Filter Styles
 */

/* Search results table */
.teams-search-results {
    max-height: 400px;
    overflow-y: auto;
}

/* Recent runs table */
.ms-card-body:has(#recent-runs-table) {
    height: 400px;
    min-height: 400px;
    overflow-y: auto;
}

.teams-search-results table {
    width: 100%;
    border-collapse: collapse;
}

.teams-search-results th,
.teams-search-results td {
    padding: 8px;
    text-align: left;
    border-bottom: 1px solid #e0e0e0;
}

.teams-search-results th {
    background-color: #f3f3f3;
    font-weight: 600;
}

.teams-search-results tr:hover {
    background-color: #f9f9f9;
}

/* Toast notifications */
.ms-toast {
    position: fixed;
    bottom: 20px;
    right: 20px;
    padding: 12px 16px;
    background: white;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border-radius: 4px;
    display: flex;
    align-items: center;
    transform: translateY(100px);
    opacity: 0;
    transition: transform 0.3s, opacity 0.3s;
    z-index: 1000;
    max-width: 400px;
}

.ms-toast.show {
    transform: translateY(0);
    opacity: 1;
}

.ms-toast-success {
    border-left: 4px solid #107c10;
}

.ms-toast-error {
    border-left: 4px solid #e81123;
}

.ms-toast-icon {
    margin-right: 10px;
    font-weight: bold;
}

.ms-toast-success .ms-toast-icon {
    color: #107c10;
}

.ms-toast-error .ms-toast-icon {
    color: #e81123;
}

/* Highlighting effect for selected test case */
.ms-DocumentCard.highlighted {
    box-shadow: 0 0 0 2px #0078d4;
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0% { box-shadow: 0 0 0 2px rgba(0, 120, 212, 0.5); }
    50% { box-shadow: 0 0 0 4px rgba(0, 120, 212, 1); }
    100% { box-shadow: 0 0 0 2px rgba(0, 120, 212, 0.5); }
}

/* Responsive design for small screens */
@media (max-width: 768px) {
    .teams-search-results td:nth-child(4),
    .teams-search-results th:nth-child(4) {
        display: none;
    }
    
    .teams-search-results td:nth-child(5),
    .teams-search-results th:nth-child(5) {
        max-width: 100px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
}

/**
 * Tests for API endpoints
 */

const request = require('supertest');
const db = require('../db');

// Mock the database module
jest.mock('../db', () => ({
  init: jest.fn().mockResolvedValue(),
  close: jest.fn().mockResolvedValue(),
  query: jest.fn().mockImplementation(async (sql, params = []) => {
    // Mock different query responses based on the SQL
    if (sql.includes('INSERT INTO test_session')) {
      return [{ insertId: 1, affectedRows: 1 }];
    } else if (sql.includes('SELECT * FROM test_session WHERE session_id =')) {
      if (params[0] === 'test-session-123') {
        return [{ 
          session_id: 'test-session-123', 
          test_type: 'Regression', 
          environment: 'qa',
          description: 'Test session',
          created_by: 'testuser',
          created_at: '2023-01-01T00:00:00Z',
          status: 'running',
          progress: 50
        }];
      }
      return [];
    } else if (sql.includes('SELECT * FROM test_session')) {
      return [
        { 
          session_id: 'test-session-123', 
          test_type: 'Regression', 
          environment: 'qa',
          description: 'Test session 1',
          created_by: 'testuser',
          created_at: '2023-01-01T00:00:00Z',
          status: 'running',
          progress: 50
        },
        { 
          session_id: 'test-session-456', 
          test_type: 'Smoke', 
          environment: 'dev',
          description: 'Test session 2',
          created_by: 'testuser',
          created_at: '2023-01-02T00:00:00Z',
          status: 'completed',
          progress: 100
        }
      ];
    } else if (sql.includes('UPDATE test_session SET status')) {
      return [{ affectedRows: 1 }];
    } else if (sql.includes('INSERT INTO input_queries')) {
      return [{ insertId: 789, affectedRows: 1 }];
    } else if (sql.includes('SELECT * FROM input_queries WHERE session_id =')) {
      return [
        { 
          id: 789, 
          session_id: 'test-session-123', 
          query: 'Test query 1', 
          execution_time: 250,
          status: 'success',
          created_at: '2023-01-01T01:00:00Z' 
        },
        { 
          id: 790, 
          session_id: 'test-session-123', 
          query: 'Test query 2', 
          execution_time: 300,
          status: 'failed',
          created_at: '2023-01-01T02:00:00Z' 
        }
      ];
    } else if (sql.includes('SELECT AVG(execution_time)')) {
      return [{ avg_execution_time: 275, total_queries: 2, success_rate: 50 }];
    }
    return [];
  })
}));

// Load the API after the mock is set up
const app = require('../api');

describe('API Endpoints', () => {
  // Test user credentials from environment or default for testing
  const TEST_USER = process.env.TEST_USER || '<EMAIL>';
  const TEST_PASSWORD = process.env.TEST_PASSWORD || 'test';

  beforeEach(() => {
    jest.clearAllMocks();
  });

  // Session Endpoints
  describe('Session Endpoints', () => {
    test('POST /AutoRun/TestSession creates a new test session', async () => {
      const response = await request(app)
        .post('/AutoRun/TestSession')
        .send({
          uid: TEST_USER,
          password: TEST_PASSWORD,
          test_type: 'Regression',
          environment: 'qa',
          description: 'Test session'
        });
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('session_id');
      expect(db.query).toHaveBeenCalledWith(
        expect.stringContaining('INSERT INTO test_session'),
        expect.arrayContaining([expect.any(String), 'Regression', 'qa', 'Test session', TEST_USER, expect.any(String), 'created'])
      );
    });

    test('GET /AutoRun/TestSession/:id gets a specific test session', async () => {
      const response = await request(app)
        .get('/AutoRun/TestSession/test-session-123')
        .query({
          uid: TEST_USER,
          password: TEST_PASSWORD
        });
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('session');
      expect(response.body.session).toHaveProperty('session_id', 'test-session-123');
      expect(db.query).toHaveBeenCalledWith(
        expect.stringContaining('SELECT * FROM test_session WHERE session_id ='),
        ['test-session-123']
      );
    });

    test('GET /AutoRun/TestSession gets all test sessions', async () => {
      const response = await request(app)
        .get('/AutoRun/TestSession')
        .query({
          uid: TEST_USER,
          password: TEST_PASSWORD,
          limit: 10,
          offset: 0
        });
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('sessions');
      expect(Array.isArray(response.body.sessions)).toBe(true);
      expect(response.body.sessions.length).toBe(2);
      expect(response.body.sessions[0]).toHaveProperty('session_id', 'test-session-123');
      expect(db.query).toHaveBeenCalledWith(
        expect.stringContaining('SELECT * FROM test_session'),
        expect.arrayContaining([10, 0])
      );
    });

    test('POST /AutoRun/TestSession/:id/status updates a test session status', async () => {
      const response = await request(app)
        .post('/AutoRun/TestSession/test-session-123/status')
        .send({
          uid: TEST_USER,
          password: TEST_PASSWORD,
          status: 'completed',
          progress: 100
        });
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('session_id', 'test-session-123');
      expect(response.body).toHaveProperty('status', 'completed');
      expect(db.query).toHaveBeenCalledWith(
        expect.stringContaining('UPDATE test_session SET status = ?, progress = ?, updated_at = ? WHERE session_id = ?'),
        expect.arrayContaining(['completed', 100, expect.any(String), 'test-session-123'])
      );
    });

    test('GET /AutoRun/TestSession/:id/Report gets a test session report', async () => {
      // Mock the query responses for the report
      db.query
        // First query for session
        .mockImplementationOnce(() => ([{ 
          session_id: 'test-session-123', 
          test_type: 'Regression', 
          environment: 'qa',
          description: 'Test session report',
          created_by: 'testuser',
          created_at: '2023-01-01T00:00:00Z',
          status: 'completed',
          progress: 100
        }]))
        // Second query for test cases
        .mockImplementationOnce(() => ([
          { test_id: 1, name: 'Test 1', status: 'passed', duration: 200 },
          { test_id: 2, name: 'Test 2', status: 'failed', duration: 150 }
        ]));

      const response = await request(app)
        .get('/AutoRun/TestSession/test-session-123/Report')
        .query({
          uid: TEST_USER,
          password: TEST_PASSWORD
        });
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('report');
      expect(response.body.report).toHaveProperty('session_id', 'test-session-123');
      expect(db.query).toHaveBeenCalledWith(
        expect.stringContaining('SELECT * FROM test_session WHERE session_id ='),
        ['test-session-123']
      );
    });
  });

  // Input Query Endpoints
  describe('Input Query Endpoints', () => {
    test('POST /AutoRun/InputQuery logs a new input query', async () => {
      const response = await request(app)
        .post('/AutoRun/InputQuery')
        .send({
          uid: TEST_USER,
          password: TEST_PASSWORD,
          session_id: 'test-session-123',
          query: 'Test query',
          execution_time: 250,
          status: 'success'
        });
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(db.query).toHaveBeenCalledWith(
        expect.stringContaining('INSERT INTO input_queries'),
        expect.arrayContaining(['test-session-123', 'Test query', 250, 'success', expect.any(String)])
      );
    });

    test('GET /AutoRun/InputQuery/:sessionId gets input queries for a session', async () => {
      const response = await request(app)
        .get('/AutoRun/InputQuery/test-session-123')
        .query({
          uid: TEST_USER,
          password: TEST_PASSWORD
        });
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('queries');
      expect(Array.isArray(response.body.queries)).toBe(true);
      expect(response.body.queries.length).toBe(2);
      expect(response.body.queries[0]).toHaveProperty('id', 789);
      expect(db.query).toHaveBeenCalledWith(
        expect.stringContaining('SELECT * FROM input_queries WHERE session_id ='),
        ['test-session-123']
      );
    });

    test('GET /AutoRun/InputQuery/:sessionId/Stats gets query stats for a session', async () => {
      // Mock the stats query response
      db.query.mockImplementationOnce(() => ([{
        avg_execution_time: 275, 
        total_queries: 2, 
        success_rate: 50
      }]));

      const response = await request(app)
        .get('/AutoRun/InputQuery/test-session-123/Stats')
        .query({
          uid: TEST_USER,
          password: TEST_PASSWORD
        });
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('stats');
      expect(response.body.stats).toHaveProperty('avg_execution_time', 275);
      expect(response.body.stats).toHaveProperty('total_queries', 2);
      expect(response.body.stats).toHaveProperty('success_rate', 50);
      expect(db.query).toHaveBeenCalledWith(
        expect.stringContaining('SELECT AVG(execution_time)'),
        ['test-session-123']
      );
    });
  });

  // Test Runner Endpoint
  describe('CaseRunner Endpoint', () => {
    test('POST /AutoRun/CaseRunner runs a test case or suite', async () => {
      // Mock the query response for CaseRunner
      db.query.mockImplementationOnce(() => ([{ tsn_id: 123 }]));

      const response = await request(app)
        .post('/AutoRun/CaseRunner')
        .send({
          uid: TEST_USER,
          password: TEST_PASSWORD,
          tc_id: 101,
          environment: 'qa'
        });
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('tsn_id');
      expect(response.body).toHaveProperty('session_id');
      expect(response.body).toHaveProperty('message');
    });
  });

  // Recent Runs Endpoint
  describe('Recent Runs Endpoint', () => {
    test('GET /api/local/recent-runs returns latest test session results', async () => {
      // Mock getRecentSessionIds and external API call
      const mockSessions = [
        { tsn_id: 'session-001', type: 'Regression', envir: 'qa', start_time: '2025-04-15T10:00:00Z' },
        { tsn_id: 'session-002', type: 'Smoke', envir: 'dev', start_time: '2025-04-15T11:00:00Z' }
      ];
      const mockReport = {
        tsn_id: 'session-001',
        summary_status: 'completed',
        total_cases: 10,
        passed_cases: 9,
        failed_cases: 1,
        skipped_cases: 0,
        start_time: '2025-04-15T10:00:00Z',
        envir: 'qa',
        type: 'Regression'
      };
      // Mock db-direct and external API
      jest.spyOn(require('../db-direct'), 'getRecentSessionIds').mockResolvedValue(mockSessions);
      jest.spyOn(global, 'fetch').mockImplementation(async url => {
        return {
          ok: true,
          json: async () => mockReport
        };
      });
      const response = await request(app).get('/api/local/recent-runs');
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(Array.isArray(response.body.recentRuns)).toBe(true);
      expect(response.body.recentRuns[0]).toHaveProperty('tsn_id', 'session-001');
      expect(response.body.recentRuns[0]).toHaveProperty('summary_status', 'completed');
      // Restore mocks
      require('../db-direct').getRecentSessionIds.mockRestore();
      global.fetch.mockRestore();
    });
  });

  // Error Handling
  describe('Error Handling', () => {
    test('Returns 401 for invalid credentials', async () => {
      const response = await request(app)
        .get('/AutoRun/TestSession')
        .query({
          uid: '<EMAIL>',
          password: 'wrongpass'
        });
      
      expect(response.status).toBe(401);
      expect(response.body).toHaveProperty('success', false);
    });

    test('Returns 400 for missing required parameters', async () => {
      const response = await request(app)
        .post('/AutoRun/TestSession')
        .send({
          uid: TEST_USER,
          password: TEST_PASSWORD,
          // Missing test_type field
          environment: 'qa'
        });
      
      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('success', false);
    });

    test('Returns 404 for non-existent session', async () => {
      // Mock to return empty array (no sessions found)
      db.query.mockImplementationOnce(() => []);
      
      const response = await request(app)
        .get('/AutoRun/TestSession/999')
        .query({
          uid: TEST_USER,
          password: TEST_PASSWORD
        });
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('success', false);
    });
  });
}); 
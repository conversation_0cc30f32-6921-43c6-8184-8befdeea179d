/**
 * Request Manager - <PERSON><PERSON> request deduplication, caching, and performance optimization
 * Prevents multiple concurrent requests to the same endpoint and implements intelligent caching
 */

class RequestManager {
    constructor() {
        this.pendingRequests = new Map(); // Track ongoing requests
        this.cache = new Map(); // Response cache with TTL
        this.cacheConfig = {
            recentRuns: { ttl: 3000, maxSize: 5 }, // 3 seconds TTL, max 5 cached responses
            activeTests: { ttl: 2000, maxSize: 3 }, // 2 seconds TTL for active tests
            testDetails: { ttl: 30000, maxSize: 50 } // 30 seconds TTL, cache up to 50 test details
        };
        
        // Performance metrics
        this.metrics = {
            requestsSaved: 0,
            cacheHits: 0,
            cacheMisses: 0,
            averageResponseTime: 0
        };
        
        console.log('RequestManager initialized with caching and deduplication');
    }

    /**
     * Execute a request with deduplication and caching
     * @param {string} key - Unique key for the request
     * @param {Function} requestFn - Function that returns a Promise for the actual request
     * @param {string} cacheType - Type of cache to use (recentRuns, activeTests, testDetails)
     * @returns {Promise} - The request result
     */
    async executeRequest(key, requestFn, cacheType = 'recentRuns') {
        const startTime = Date.now();
        
        // Check cache first
        const cachedResult = this.getCachedResult(key, cacheType);
        if (cachedResult) {
            this.metrics.cacheHits++;
            console.log(`Cache HIT for ${key} (${cacheType})`);
            return cachedResult;
        }
        
        this.metrics.cacheMisses++;
        
        // Check if request is already pending
        if (this.pendingRequests.has(key)) {
            console.log(`Request deduplication for ${key} - waiting for existing request`);
            this.metrics.requestsSaved++;
            return await this.pendingRequests.get(key);
        }

        // Execute new request
        console.log(`Executing new request for ${key}`);
        const requestPromise = this.executeNewRequest(key, requestFn, cacheType, startTime);
        this.pendingRequests.set(key, requestPromise);
        
        return requestPromise;
    }

    async executeNewRequest(key, requestFn, cacheType, startTime) {
        try {
            const result = await requestFn();
            const responseTime = Date.now() - startTime;
            
            // Update performance metrics
            this.updateMetrics(responseTime);
            
            // Cache the result
            this.cacheResult(key, result, cacheType);
            
            console.log(`Request completed for ${key} in ${responseTime}ms`);
            return result;
            
        } catch (error) {
            console.error(`Request failed for ${key}:`, error);
            throw error;
        } finally {
            // Remove from pending requests
            this.pendingRequests.delete(key);
        }
    }

    getCachedResult(key, cacheType) {
        const cacheEntry = this.cache.get(key);
        if (!cacheEntry) return null;
        
        const config = this.cacheConfig[cacheType];
        const isExpired = Date.now() - cacheEntry.timestamp > config.ttl;
        
        if (isExpired) {
            this.cache.delete(key);
            return null;
        }
        
        return cacheEntry.data;
    }

    cacheResult(key, data, cacheType) {
        const config = this.cacheConfig[cacheType];
        
        // Implement LRU eviction if cache is full
        if (this.cache.size >= config.maxSize) {
            const oldestKey = this.cache.keys().next().value;
            this.cache.delete(oldestKey);
        }
        
        this.cache.set(key, {
            data: data,
            timestamp: Date.now(),
            type: cacheType
        });
    }

    updateMetrics(responseTime) {
        const currentAvg = this.metrics.averageResponseTime;
        const totalRequests = this.metrics.cacheHits + this.metrics.cacheMisses;
        this.metrics.averageResponseTime = (currentAvg * (totalRequests - 1) + responseTime) / totalRequests;
    }

    /**
     * Clear cache for specific type or all
     * @param {string} cacheType - Optional cache type to clear
     */
    clearCache(cacheType = null) {
        if (cacheType) {
            for (const [key, entry] of this.cache.entries()) {
                if (entry.type === cacheType) {
                    this.cache.delete(key);
                }
            }
            console.log(`Cleared ${cacheType} cache`);
        } else {
            this.cache.clear();
            console.log('Cleared all cache');
        }
    }

    /**
     * Get performance metrics
     */
    getMetrics() {
        return {
            ...this.metrics,
            cacheSize: this.cache.size,
            pendingRequests: this.pendingRequests.size,
            cacheHitRatio: this.metrics.cacheHits / (this.metrics.cacheHits + this.metrics.cacheMisses) || 0
        };
    }

    /**
     * Preload test details for visible test runs
     * @param {Array} testRuns - Array of test runs to preload details for
     */
    async preloadTestDetails(testRuns) {
        const preloadPromises = testRuns.slice(0, 10).map(run => {
            const key = `testDetails_${run.tsn_id}`;
            return this.executeRequest(key, 
                () => window.apiService.getTestDetails(run.tsn_id),
                'testDetails'
            ).catch(error => {
                console.warn(`Failed to preload details for ${run.tsn_id}:`, error);
            });
        });
        
        await Promise.allSettled(preloadPromises);
        console.log(`Preloaded test details for ${testRuns.length} runs`);
    }
}

// Create global instance
window.requestManager = new RequestManager();

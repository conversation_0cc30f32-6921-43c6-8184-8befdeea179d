/**
 * dashboard-charts.js
 *
 * This module handles the initialization and updating of all charts
 * on the dashboard, such as test results and duration trends.
 */

import { config } from './dashboard-config.js';

/**
 * Initializes all dashboard charts.
 * This should be called once when the dashboard is loaded.
 */
export function initializeCharts() {
    const { resultsChart, durationChart } = config.elements;

    if (resultsChart && window.Chart) {
        const ctx = resultsChart.getContext('2d');
        config.charts.resultsChartInstance = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['Successful', 'Failed', 'Running'],
                datasets: [{
                    data: [0, 0, 0],
                    backgroundColor: ['#107c10', '#d13438', '#0078d4'],
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                legend: { position: 'bottom' },
            }
        });
    }

    if (durationChart && window.Chart) {
        const ctx = durationChart.getContext('2d');
        config.charts.durationChartInstance = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [], // Timestamps
                datasets: [{
                    label: 'Test Duration (s)',
                    data: [], // Durations
                    borderColor: '#0078d4',
                    fill: false,
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    x: { title: { display: true, text: 'Time' } },
                    y: { title: { display: true, text: 'Duration (s)' } }
                }
            }
        });
    }
}

/**
 * Updates the charts with new data from recent test runs.
 * @param {Array} recentRuns - An array of recent test run objects.
 * @param {Object} summary - An object containing summary counts.
 */
export function updateCharts(recentRuns, summary) {
    // Update Results Doughnut Chart
    if (config.charts.resultsChartInstance) {
        config.charts.resultsChartInstance.data.datasets[0].data = [
            summary.successful,
            summary.failed,
            summary.running
        ];
        config.charts.resultsChartInstance.update();
    }

    // Update Duration Line Chart
    if (config.charts.durationChartInstance) {
        const labels = recentRuns.map(run => new Date(run.start_time).toLocaleTimeString());
        const data = recentRuns.map(run => run.duration_seconds);

        config.charts.durationChartInstance.data.labels = labels.reverse();
        config.charts.durationChartInstance.data.datasets[0].data = data.reverse();
        config.charts.durationChartInstance.update();
    }
}

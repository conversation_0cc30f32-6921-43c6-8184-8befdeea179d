/**
 * Centralized Authentication Service
 * Provides comprehensive authentication, validation, and session management
 */

const userManager = require('./user-manager');
const sessionManager = require('./session-manager');
const accountLockout = require('./account-lockout');
const auditLogger = require('../utils/audit-logger');
const ValidationUtils = require('../utils/validation');
const devConfig = require('../config/development');
const { AUTH_CONFIG } = require('../config/app-config');

class AuthService {
  constructor() {
    this.activeSessions = new Map(); // In production, use Redis or database
    this.failedAttempts = new Map(); // Track failed login attempts
    this.sessionTimeout = AUTH_CONFIG.SESSION_TIMEOUT;
  }

  /**
   * Authenticate user with comprehensive validation
   * @param {string} uid - User ID (email)
   * @param {string} password - User password
   * @param {string} clientIp - Client IP address
   * @returns {Object} Authentication result
   */
  async authenticate(uid, password, clientIp = 'unknown') {
    try {
      // Input validation
      const credentialsValidation = ValidationUtils.validateCredentials({ uid, password });
      if (!credentialsValidation.isValid) {
        return {
          success: false,
          error: 'Invalid input: ' + credentialsValidation.errors.join(', '),
          code: 'INVALID_INPUT'
        };
      }

      const sanitizedCredentials = credentialsValidation.sanitized;

      // Check account lockout status
      const accountLockStatus = accountLockout.isAccountLocked(sanitizedCredentials.uid);
      if (accountLockStatus.locked) {
        return {
          success: false,
          error: `Account temporarily locked due to multiple failed attempts. Try again in ${accountLockStatus.retryAfter} seconds.`,
          code: 'ACCOUNT_LOCKED',
          retryAfter: accountLockStatus.retryAfter
        };
      }

      // Check IP lockout status
      const ipLockStatus = accountLockout.isIPLocked(clientIp);
      if (ipLockStatus.locked) {
        return {
          success: false,
          error: `IP address temporarily blocked due to suspicious activity. Try again in ${ipLockStatus.retryAfter} seconds.`,
          code: 'IP_LOCKED',
          retryAfter: ipLockStatus.retryAfter
        };
      }

      // Validate user credentials
      const user = userManager.validateUser(sanitizedCredentials.uid, sanitizedCredentials.password);
      
      if (!user) {
        // Record failed attempt with enhanced lockout tracking
        const lockoutResult = accountLockout.recordFailedAttempt(sanitizedCredentials.uid, clientIp);

        // Log authentication failure
        auditLogger.logAuthFailure({
          uid: sanitizedCredentials.uid,
          ip: clientIp,
          userAgent: 'SmartTest-Client',
          reason: 'Invalid credentials',
          failedAttempts: lockoutResult.accountLocked ? 'Account locked' : 'Failed attempt'
        });

        // Log account lockout if it occurred
        if (lockoutResult.accountLocked) {
          auditLogger.logAccountLockout({
            uid: sanitizedCredentials.uid,
            ip: clientIp,
            lockoutDuration: lockoutResult.accountRetryAfter,
            failedAttempts: 'Multiple failed attempts',
            lockoutCount: 1
          });
        }

        // Log suspicious activity if IP is flagged
        if (lockoutResult.suspicious) {
          auditLogger.logSuspiciousActivity({
            ip: clientIp,
            activityType: 'Multiple failed login attempts',
            details: `Suspicious login attempts for user: ${sanitizedCredentials.uid}`,
            userAgent: 'SmartTest-Client',
            attempts: 'Multiple'
          });
        }

        // Return appropriate error based on lockout status
        if (lockoutResult.accountLocked) {
          return {
            success: false,
            error: `Account locked due to multiple failed attempts. Try again in ${lockoutResult.accountRetryAfter} seconds.`,
            code: 'ACCOUNT_LOCKED',
            retryAfter: lockoutResult.accountRetryAfter
          };
        } else if (lockoutResult.ipLocked) {
          return {
            success: false,
            error: `IP address blocked due to suspicious activity. Try again in ${lockoutResult.ipRetryAfter} seconds.`,
            code: 'IP_LOCKED',
            retryAfter: lockoutResult.ipRetryAfter
          };
        } else {
          return {
            success: false,
            error: 'Authentication failed. Invalid credentials or user not authorized.',
            code: 'INVALID_CREDENTIALS'
          };
        }
      }

      // Clear failed attempts on successful login
      accountLockout.clearFailedAttempts(sanitizedCredentials.uid);

      // Create secure session with JWT tokens
      const sessionData = sessionManager.createSession(user, clientIp, 'SmartTest-Client');

      // Log successful authentication
      auditLogger.logAuthSuccess({
        uid: user.uid,
        ip: clientIp,
        userAgent: 'SmartTest-Client',
        sessionId: sessionData.sessionId,
        role: user.role,
        permissions: this.getUserPermissions(user)
      });

      return {
        success: true,
        user: user,
        sessionId: sessionData.sessionId,
        accessToken: sessionData.accessToken,
        refreshToken: sessionData.refreshToken,
        permissions: this.getUserPermissions(user),
        expiresIn: sessionData.expiresIn,
        tokenType: sessionData.tokenType
      };

    } catch (error) {
      console.error('Authentication error:', error);
      return {
        success: false,
        error: 'Internal authentication error.',
        code: 'INTERNAL_ERROR'
      };
    }
  }

  /**
   * Validate access token
   * @param {string} accessToken - JWT access token
   * @returns {Object} Token validation result
   */
  validateAccessToken(accessToken) {
    return sessionManager.validateAccessToken(accessToken);
  }

  /**
   * Validate existing session (legacy method for backward compatibility)
   * @param {string} sessionId - Session ID
   * @returns {Object} Session validation result
   */
  validateSession(sessionId) {
    const session = sessionManager.getSession(sessionId);
    if (!session) {
      return { valid: false, error: 'Session not found' };
    }

    return {
      valid: true,
      session: session,
      user: session.user,
      permissions: this.getUserPermissions(session.user)
    };
  }

  /**
   * Refresh access token using refresh token
   * @param {string} refreshToken - Refresh token
   * @returns {Object} Refresh result
   */
  refreshAccessToken(refreshToken) {
    return sessionManager.refreshAccessToken(refreshToken);
  }

  /**
   * Refresh session (legacy method for backward compatibility)
   * @param {string} sessionId - Session ID
   * @returns {Object} Refresh result
   */
  refreshSession(sessionId) {
    const session = sessionManager.getSession(sessionId);
    if (!session) {
      return { success: false, error: 'Session not found' };
    }

    return {
      success: true,
      session: session
    };
  }

  /**
   * Logout user and invalidate session
   * @param {string} sessionId - Session ID
   * @param {string} accessToken - Access token to blacklist
   * @returns {Object} Logout result
   */
  logout(sessionId, accessToken = null) {
    if (!sessionId) {
      return { success: false, error: 'Session ID required' };
    }

    const session = sessionManager.getSession(sessionId);
    const success = sessionManager.revokeSession(sessionId, accessToken);

    if (success && session) {
      // Log session termination
      auditLogger.logSessionEvent({
        event: 'SESSION_LOGOUT',
        sessionId: sessionId,
        uid: session.user.uid,
        ip: session.clientIp,
        details: 'User initiated logout'
      });

      console.log(`✅ User logged out: ${sessionId}`);
    }

    return { success: success };
  }

  /**
   * Create new session
   * @param {Object} user - User object
   * @param {string} clientIp - Client IP address
   * @returns {Object} Session object
   */
  createSession(user, clientIp) {
    const sessionId = this.generateSessionId();
    const now = Date.now();
    
    const session = {
      id: sessionId,
      user: user,
      createdAt: now,
      lastActivity: now,
      expiresAt: now + (this.sessionTimeout * 1000),
      clientIp: clientIp,
      userAgent: 'SmartTest-Client' // Could be enhanced to capture actual user agent
    };

    this.activeSessions.set(sessionId, session);
    
    console.log(`✅ Session created for user: ${user.uid} (${sessionId})`);
    
    return session;
  }

  /**
   * Get user permissions
   * @param {Object} user - User object
   * @returns {Array} Array of permissions
   */
  getUserPermissions(user) {
    const roles = userManager.getRoles();
    const userRole = roles[user.role];
    return userRole ? userRole.permissions : [];
  }

  /**
   * Check if user has specific permission
   * @param {Object} user - User object
   * @param {string} permission - Permission to check
   * @returns {boolean} True if user has permission
   */
  hasPermission(user, permission) {
    const permissions = this.getUserPermissions(user);
    return permissions.includes(permission);
  }

  /**
   * Get account lockout information (admin function)
   * @param {string} uid - User ID
   * @returns {Object} Account lockout info
   */
  getAccountLockoutInfo(uid) {
    return accountLockout.getAccountInfo(uid);
  }

  /**
   * Manually unlock account (admin function)
   * @param {string} uid - User ID
   * @returns {boolean} Success status
   */
  unlockAccount(uid) {
    return accountLockout.unlockAccount(uid);
  }

  /**
   * Get lockout statistics (admin function)
   * @returns {Object} Lockout statistics
   */
  getLockoutStats() {
    return accountLockout.getStats();
  }

  /**
   * Generate secure session ID
   * @returns {string} Session ID
   */
  generateSessionId() {
    const crypto = require('crypto');
    return crypto.randomBytes(32).toString('hex');
  }

  /**
   * Get active sessions count
   * @returns {number} Number of active sessions
   */
  getActiveSessionsCount() {
    return this.activeSessions.size;
  }

  /**
   * Get sessions for user
   * @param {string} uid - User ID
   * @returns {Array} Array of user sessions
   */
  getUserSessions(uid) {
    const sessions = [];
    for (const session of this.activeSessions.values()) {
      if (session.user.uid === uid) {
        sessions.push({
          id: session.id,
          createdAt: session.createdAt,
          lastActivity: session.lastActivity,
          expiresAt: session.expiresAt,
          clientIp: session.clientIp
        });
      }
    }
    return sessions;
  }

  /**
   * Cleanup expired sessions
   */
  cleanupExpiredSessions() {
    const now = Date.now();
    let cleanedCount = 0;

    for (const [sessionId, session] of this.activeSessions.entries()) {
      if (now > session.expiresAt) {
        this.activeSessions.delete(sessionId);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      console.log(`🧹 Cleaned up ${cleanedCount} expired sessions`);
    }

    return cleanedCount;
  }

  /**
   * Get authentication statistics
   * @returns {Object} Authentication statistics
   */
  getStats() {
    return {
      activeSessions: this.activeSessions.size,
      failedAttempts: this.failedAttempts.size,
      sessionTimeout: this.sessionTimeout
    };
  }
}

// Export singleton instance
module.exports = new AuthService();

/**
 * dashboard-config.js
 *
 * This file centralizes all configuration for the dashboard, including:
 * - DOM element selectors
 * - Chart instances.
 * - Shared state variables
 * - Constants
 *
 * By centralizing configuration, we avoid hardcoded values scattered across
 * different modules and provide a single source of truth for dashboard state.
 */

// Export a shared configuration object to be used by all dashboard modules.
export const charts = {
    resultsChartInstance: null,
    durationChartInstance: null,
};

export const config = {
    // --- Constants ---
    MAX_API_CHECK_ATTEMPTS: 10,
    RECENT_RUNS_POLL_INTERVAL_MS: 5000, // Poll every 5 seconds
    TEST_COMPLETION_GRACE_PERIOD_MS: 10000, // 10 seconds
    
    // Grouped constants for easier access
    constants: {
        ACTIVE_TESTS_POLLING_INTERVAL: 3000, // Poll every 3 seconds for active tests
        RECENT_RUNS_POLLING_INTERVAL: 15000, // Poll every 5 seconds for recent runs
        TEST_COMPLETION_GRACE_PERIOD_MS: 10000, // 10 seconds
        MAX_API_CHECK_ATTEMPTS: 10
    },

    // --- Shared State Variables ---
    state: {
        activeTests: new Map(), // Map of active test runs (tsnId -> test data)
        recentRunsCache: [],
        highestRecentTsnId: null,
        testDetailsCache: {},
        runningTestSuites: new Set(),
        predefinedSuites: {},
        availableTestCases: [],
        isUpdatingStatuses: false, // Flag to prevent concurrent status updates
        statusPollingInterval: null,
        recentRunsPollingInterval: null,
        currentUser: null, // To be populated with the current user's ID/email
    },

    // --- DOM Element Selectors ---
    elements: {
        // Counters
        totalTestsCounter: document.getElementById('total-tests'),
        successfulTestsCounter: document.getElementById('successful-tests'),
        failedTestsCounter: document.getElementById('failed-tests'),
        runningTestsCounter: document.getElementById('running-tests'),

        // Notifications
        notificationContainer: document.getElementById('notification-container'),

        // Loading Indicator
        loadingIndicator: document.getElementById('loading-indicator'),

        // Authentication
        loginModal: document.getElementById('login-modal'),
        loginForm: document.getElementById('login-form'),
        loginButton: document.getElementById('login-button'),
        logoutButton: document.getElementById('logout-button'),
        userDisplay: document.getElementById('user-display'),
        usernameInput: document.getElementById('username'),
        passwordInput: document.getElementById('password'),
        loginStatus: document.getElementById('login-status'),

        // Active Tests Panel
        activeTestsContainer: document.getElementById('active-tests-container'),
        activeTestsFilterButtons: document.querySelectorAll('.active-tests-filter-btn'),

        // Recent Runs Table
        recentRunsTableBody: document.getElementById('recent-runs-table')?.querySelector('tbody'),
        recentRunsContainer: document.getElementById('recent-runs-container'),

        // Test Suite Selection
        predefinedSuitesContainer: document.getElementById('predefined-suites-container'),
        suiteProjectFilter: document.getElementById('suite-project-filter'),
        suiteLevelFilter: document.getElementById('suite-level-filter'),
        suiteVersionFilter: document.getElementById('suite-version-filter'),
        filteredSuitesContainer: document.getElementById('filtered-suites-container'),
        runFilteredSuitesBtn: document.getElementById('run-filtered-suites-btn'),



        // Test Details Modal
        detailsModal: document.getElementById('details-modal'),
        detailsModalTitle: document.getElementById('test-details-modal-title'),
        detailsModalBody: document.getElementById('test-details-modal-body'),
        detailsModalCloseBtn: document.querySelector('#test-details-modal .btn-close'),

        // Charts
        resultsChart: document.getElementById('results-chart'),
        durationChart: document.getElementById('duration-chart'),
    },
    charts,
};
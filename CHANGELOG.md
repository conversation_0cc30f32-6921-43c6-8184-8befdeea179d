# Changelog

All notable changes to SmartTest will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Added
- Consolidated package.json dependencies for simplified dependency management
- Enhanced versioning and release management documentation

### Changed
- Downgraded node-fetch to v2.7.0 for CommonJS compatibility
- Merged multiple package.json files into single root configuration

### Fixed
- Resolved ERR_REQUIRE_ESM error with node-fetch v3+ compatibility

## [1.2.0] - 2024-XX-XX (UI Improvements)

### Added
- Enhanced dashboard UI/UX with improved config module
- Better table readability and responsive design
- Microsoft Fabric CSS integration for modern styling

### Fixed
- Config module UI/UX issues resolved
- Dashboard styling improvements with CSP policy updates
- jQuery and DataTables CDN resource loading

## [1.1.0] - Previous Features (Estimated)

### Added
- Test suite selection functionality
- Multi-select test suite cards
- Architecture quick wins and refactoring
- Admin panel and debug tools for test runner configuration

### Fixed
- Single test case filtering improvements
- Test runner polling system enhancements
- Stop test functionality improvements

## [1.0.0] - Initial Release (Estimated)

### Added
- Core SmartTest MVP functionality
- Frontend dashboard for test management
- Backend API server with Express.js
- MySQL database integration
- External testing framework API integration
- Real-time test monitoring and status updates
- Test report generation and viewing
- User authentication and session management

# QA01 Database Connection Test Script (PowerShell)
# This script uses query_db_ai.ps1 to test connectivity to the QA01 database

# Set script path variables
$scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Path
$queryToolPath = Join-Path -Path $scriptPath -ChildPath "..\..\utils\query_db_ai.ps1"

# Ensure the query_db_ai.ps1 script exists
if (-not (Test-Path $queryToolPath)) {
    Write-Error "Query tool not found at: $queryToolPath"
    exit 1
}

# Function to execute a query and check the result
function Execute-Query {
    param (
        [string]$Description,
        [string]$Query
    )
    
    Write-Host "`n--- $Description ---" -ForegroundColor Cyan
    Write-Host "Executing query: $Query"
    
    try {
        # Note: -UseReadWriteUser is added to allow write operations if needed
        $result = powershell.exe -ExecutionPolicy Bypass -File $queryToolPath -Query $Query -UseReadWriteUser
        
        # Parse the JSON result
        $parsedResult = $result | ConvertFrom-Json
        
        if ($parsedResult.success -eq $true) {
            Write-Host "✅ Query executed successfully!" -ForegroundColor Green
            
            # Display number of rows returned
            Write-Host "Rows returned: $($parsedResult.rowCount)"
            
            # Display results (limited to first 3 rows for readability)
            if ($parsedResult.rowCount -gt 0) {
                Write-Host "Sample data (up to 3 rows):"
                $displayRows = [Math]::Min(3, $parsedResult.rowCount)
                for ($i = 0; $i -lt $displayRows; $i++) {
                    Write-Host ($parsedResult.data[$i] | ConvertTo-Json -Compress)
                }
                
                if ($parsedResult.rowCount -gt 3) {
                    Write-Host "...and $($parsedResult.rowCount - 3) more rows"
                }
            } else {
                Write-Host "No data returned"
            }
            
            return $true
        } else {
            Write-Host "❌ Query execution failed: $($parsedResult.errorMessage)" -ForegroundColor Red
            if ($parsedResult.mysqlError) {
                Write-Host "MySQL Error: $($parsedResult.mysqlError)" -ForegroundColor Red
            }
            if ($parsedResult.sshError) {
                Write-Host "SSH Error: $($parsedResult.sshError)" -ForegroundColor Red
            }
            return $false
        }
    } catch {
        Write-Host "❌ Exception occurred: $_" -ForegroundColor Red
        return $false
    }
}

# Main script execution
Write-Host "=== Testing Database Connection to QA01 Server ===`n" -ForegroundColor Yellow

# Test 1: Simple connectivity test
$test1 = Execute-Query -Description "Basic Connectivity Test" -Query "SELECT 1 AS test_value;"

# Test 2: Test case information
$test2 = Execute-Query -Description "Test Case Query" -Query @"
SELECT tc.tc_id, r.tsn_id, r.outcome, r.creation_time
FROM test_case tc
LEFT JOIN test_result r ON tc.tc_id = r.tc_id
WHERE tc.tc_id = 1279
ORDER BY r.creation_time DESC
LIMIT 5;
"@

# Test 3: Test suite information
$test3 = Execute-Query -Description "Test Suite Query" -Query @"
SELECT tg.ts_id, tg.tc_id, COUNT(r.cnt) AS execution_count
FROM test_case_group tg
LEFT JOIN test_result r ON tg.tc_id = r.tc_id
WHERE tg.ts_id = 82
GROUP BY tg.tc_id
ORDER BY tg.seq_index;
"@

# Summary
Write-Host "`n=== Test Summary ===" -ForegroundColor Yellow
Write-Host "Basic Connectivity Test: $(if ($test1) { "PASSED ✅" } else { "FAILED ❌" })"
Write-Host "Test Case Query: $(if ($test2) { "PASSED ✅" } else { "FAILED ❌" })"
Write-Host "Test Suite Query: $(if ($test3) { "PASSED ✅" } else { "FAILED ❌" })"

if ($test1 -and $test2 -and $test3) {
    Write-Host "`nAll tests PASSED! Database connection to QA01 is working properly." -ForegroundColor Green
    exit 0
} else {
    Write-Host "`nSome tests FAILED. Please check the error messages above." -ForegroundColor Red
    exit 1
} 
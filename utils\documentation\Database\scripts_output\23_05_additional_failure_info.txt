==== Executing Query on qa2 (mprts-qa02.lab.wagerworks.com) ====
Query: SELECT  tc_id, seq_index,tsn_id,outcome,creation_time, i.txt "input_data", o.txt "output_data" FROM rgs_test.test_result r, rgs_test.input i , rgs_test.output o where i.cnt=r.cnt and o.cnt=i.cnt  and r.tsn_id in (14813) and i.cnt between ((select cnt from rgs_test.test_result where tsn_id in (14813) and outcome = 'F' order by creation_time asc limit 1)-10) and (select cnt from rgs_test.test_result where tsn_id in (14813) and outcome = 'F' order by creation_time asc limit 1) order by i.cnt asc
VERBOSE: SSH command base: ssh -i "C:\Users\<USER>\.ssh\id_rsa_dbserver" -o 
HostKeyAlgorithms=+ssh-rsa
VERBOSE: Complete SSH command: ssh -i "C:\Users\<USER>\.ssh\id_rsa_dbserver" -o 
HostKeyAlgorithms=+ssh-rsa <EMAIL>
VERBOSE: SQL query saved to: C:\Users\<USER>\AppData\Local\Temp\temp_query.sql
Executing command...
Copying query file to server...
Warning: Using a password on the command line interface can be insecure.
Exit code: 0
Query executed successfully!
Result:
tc_id   seq_index       tsn_id  outcome creation_time   input_data      output_data
5       2       14813   P       2025-05-22 22:58:00.821 INPUT: 10       Sleep for 10000 milliseconds
5       3       14813   P       2025-05-22 22:58:10.834 INPUT: 10       Sleep for 10000 milliseconds
5       4       14813   P       2025-05-22 22:58:20.847 INPUT: 10       Sleep for 10000 milliseconds
5       5       14813   P       2025-05-22 22:58:20.861 select  template_id,
\n
\njp_scale_granularity, bet_size_active, bet_size,
\ncase when 1 is null then 100000
\nelse
\n
\ncase when
\nceil(truncate(1*100.00/CAST(700000 AS DECIMAL(12,2))*1*CAST(100000 AS DECIMAL(12,2)),2)) > 1 then
\n
\nceil(truncate(1*100.00/CAST(700000 AS DECIMAL(12,2))*1*CAST(100000 AS DECIMAL(12,2)),2))    
\nelse 1
\nend
\n
\n end "expected_odds"
\nfrom jn_progressive_t_lvl where template_id =  678    template_id1=678&jp_scale_granularity1=second&bet_size_active1=1&bet_size1=1.00&expected_odds1=15
5       6       14813   P       2025-05-22 22:58:26.255 ssh  nje-qa05-app01  a=$(grep "checkHit"  /wworks/nje/tomcat/qa05/logs/nje-qa05-app01.lab.wagerworks.com/nje.log | grep 174795463417479546341747954634 | grep level_runtime_id=1495 | tail -1 | awk 'NF>1{print $NF}'); echo  '<val>'${a#*=}'</val>'      <val>15</val>\n
5       7       14813   P       2025-05-22 22:58:26.269 echo val 15 expected_odds1 15   val 15 expected_odds1 15\n
3076    9       14813   P       2025-05-22 22:58:26.297 select count(*) as "winNum", 3 as "feed_number", 700000 as "amount" from jn_jackpot_wins where engine_id='1495';      winNum=4&feed_number=3&amount=700000
3076    10      14813   P       2025-05-22 22:58:26.306 NULL
3076    11      14813   F       2025-05-22 22:58:26.321 select *, current_seed, current_prize 
from jn_engine_runtime where template_id =  678 id1=1495&template_id1=678&level_id1=835&type1=0&licensee_id1=-1&operator_id1=-1&user_licensee_id1=-1&user_operator_id1=-1&status_id1=1&current_seed1=839.**********&current_prize1=53100.**********&currency1=EUR&date_created1=2025-05-21 18:14:59&date_modified1=2025-05-22 22:56:17&is_deleted1=0&deleted_by1=0&block_until_funded1=0&auto_restart1=1&is_transferred1=0&auto_pay_win1=0&date_to_activate1=1970-01-01 00:00:00&active_players1=1&last_bet_date1=2025-05-22 22:56:26&active_tier1=null&level_stage1=1&current_seed1=839.**********&current_prize1=53100.**********

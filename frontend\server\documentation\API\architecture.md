# SmartTest Unified API Architecture

This document provides an overview of the SmartTest unified API server architecture, which consolidates functionality from dashboard, reports, and config modules into a single, cohesive system.

## Unified Architectural Overview

The SmartTest API server now implements a **unified architecture** that provides:

### Core Benefits
- **Consolidated Services**: Single service handling all module contexts (dashboard, reports, config)
- **Shared Resources**: Database connections, authentication, and configuration shared across modules
- **Module Context Awareness**: Automatic behavior adaptation based on calling module
- **Backward Compatibility**: Preserves existing functionality while providing enhanced capabilities

### Architecture Layers
1. **Unified API Layer**: Handles HTTP requests with module context awareness
2. **Unified Service Layer**: Contains consolidated business logic for all modules
3. **Shared Data Access Layer**: Centralized database operations with connection pooling
4. **External Integration Layer**: Unified external API communication

## Unified Directory Structure

The unified server architecture is organized into the following structure:

```
frontend/
├── shared/                 # 🆕 UNIFIED SERVICES LAYER
│   └── services/           # Consolidated services for all modules
│       ├── unified-api-service.js      # Main unified service
│       ├── external-api-service.js     # External API integration
│       ├── base-api-service.js         # Shared HTTP client
│       └── api-config.js               # Unified configuration
├── server/                 # Server-side components
│   ├── api.js              # Main entry point
│   ├── config/             # Configuration files
│   │   ├── app-config.js   # Application configuration
│   │   └── env-config.js   # Environment variables
│   ├── middleware/         # Middleware functions
│   │   ├── auth.js         # Authentication middleware
│   │   ├── error-handler.js # Error handling middleware
│   │   ├── logging.js      # Logging middleware
│   │   └── proxy.js        # Proxy middleware
│   ├── routes/             # Route handlers (unified)
│   │   ├── index.js        # Route registration
│   │   ├── test-cases.js   # Test case routes
│   │   ├── test-suites.js  # Test suite routes
│   │   ├── test-reports.js # Report routes
│   │   ├── active-tests.js # Active tests routes
│   │   ├── recent-runs.js  # Recent runs routes
│   │   ├── case-runner.js  # Case runner routes
│   │   ├── proxy-routes.js # External API proxy routes
│   │   ├── test-sessions.js # Test session routes
│   │   └── input-queries.js # Input query routes
│   ├── services/           # Server-side business logic
│   │   ├── case-runner.js  # Case runner service
│   │   ├── cookie-auth.js  # Cookie authentication service
│   │   ├── stop-test.js    # Stop test service
│   │   ├── test-reports.js # Report generation service
│   │   └── test-status.js  # Test status service
│   └── database/           # 🔄 ENHANCED DATABASE LAYER
│       ├── index.js        # Main entry point with unified connection
│       ├── config/         # Database configuration
│       ├── connections/    # Database connection implementations
│       ├── queries/        # Pre-defined database queries
│       └── utils/          # Database utilities
├── dashboard/              # 🔄 MIGRATED TO UNIFIED SERVICES
│   ├── api-service.js      # Unified service wrapper
│   └── [other dashboard files]
├── reports/                # 🔄 MIGRATED TO UNIFIED SERVICES
│   ├── api-service.js      # Unified service wrapper
│   └── [other reports files]
└── tests/                  # 🆕 COMPREHENSIVE TESTING
    └── unified/            # Unified testing suite
        ├── unit/           # Unit tests for services
        ├── integration/    # Integration tests
        ├── mocks/          # Shared mock data
        └── [test configuration files]
```

## Unified Component Descriptions

### 🆕 Unified Services Layer (frontend/shared/services/)

The unified services layer consolidates all API functionality:

#### **Unified API Service** (`unified-api-service.js`)
- **Purpose**: Central service handling all module contexts (dashboard, reports, config)
- **Features**:
  - Module context awareness and automatic behavior adaptation
  - Consistent interface across all modules
  - Shared configuration and error handling
  - Performance monitoring and logging

#### **External API Service** (`external-api-service.js`)
- **Purpose**: Consolidated external API integration
- **Features**:
  - Unified authentication handling
  - Consistent response transformation
  - Error handling with module context
  - Connection management and retry logic

#### **Base API Service** (`base-api-service.js`)
- **Purpose**: Shared HTTP client functionality
- **Features**:
  - Common HTTP operations (GET, POST, PUT, DELETE)
  - Request/response interceptors
  - Error handling and retry mechanisms
  - Performance tracking

### Main Entry Point (api.js)

The `api.js` file remains the main entry point but now supports unified architecture:

- Creates the Express application with unified middleware
- Configures module-aware middleware
- Registers unified routes with context detection
- Initializes shared database connection
- Starts the HTTP server with enhanced monitoring

### Configuration (config/)

Enhanced configuration supporting unified architecture:

- `app-config.js`: Application configuration with module context support
- `env-config.js`: Environment-specific configuration for all modules

### Middleware (middleware/)

Enhanced middleware with unified support:

- `auth.js`: Authentication middleware with module context awareness
- `error-handler.js`: Enhanced error handling with detailed context
- `logging.js`: Request logging with module identification
- `proxy.js`: Unified proxy middleware for external API forwarding

### Unified Routes (routes/)

The `routes/` directory contains unified route handlers that serve all modules:

- `index.js`: Registers all unified routes with module context detection
- `test-cases.js`: Handles test case endpoints for all modules
- `test-suites.js`: Handles test suite endpoints for all modules
- `test-reports.js`: Handles test report endpoints for all modules
- `active-tests.js`: Handles active test endpoints for all modules
- `recent-runs.js`: Handles recent runs endpoints for all modules
- `test-details.js`: Handles test details endpoints for all modules
- `case-runner.js`: Handles test execution endpoints with module awareness
- `proxy-routes.js`: Handles unified proxy routes to external APIs
- `test-sessions.js`: Handles test session endpoints for all modules
- `input-queries.js`: Handles input query endpoints for all modules

Each unified route file follows an enhanced pattern:
1. Import unified services and middleware
2. Detect module context from request headers or parameters
3. Use unified database abstraction with module-aware behavior
4. Format responses with module context and enhanced error information
5. Export the unified router

### Enhanced Services (services/)

The server-side `services/` directory contains enhanced business logic:

- `case-runner.js`: Enhanced test case execution with module context
- `cookie-auth.js`: Unified cookie-based authentication for external APIs
- `stop-test.js`: Enhanced test stopping with module awareness
- `test-reports.js`: Unified test report generation for all modules
- `test-status.js`: Enhanced test status retrieval with module context

### 🔄 Enhanced Database Layer (database/)

The `database/` directory contains the enhanced database layer:

- `index.js`: Main entry point with unified connection management
- `config/`: Enhanced database configuration for all modules
- `connections/`: Improved connection implementations with pooling
- `queries/`: Unified pre-defined database queries
- `utils/`: Enhanced database utilities with performance monitoring

#### Database Enhancements:
- **Shared Connection Pooling**: Single connection pool for all modules
- **Performance Monitoring**: Built-in query performance tracking
- **Error Recovery**: Automatic connection recovery and retry logic
- **Module Context**: Query optimization based on calling module

## Unified Request Flow

### Server-Side Unified Flow

1. **Client Request**: Client sends HTTP request to the unified server
2. **Middleware Processing**: Request passes through enhanced middleware:
   - Logging middleware identifies module context
   - Authentication middleware validates credentials
   - Module context detection middleware determines calling module
3. **Unified Routing**: Request is routed to unified route handler
4. **Module Context Adaptation**: Route handler adapts behavior based on module context
5. **Unified Service Layer**: Route handler calls unified service methods
6. **Shared Database Layer**: Service interacts with shared database layer
7. **Performance Monitoring**: Database operations are monitored and logged
8. **Response Formatting**: Response is formatted with module context and enhanced information
9. **Client Response**: Standardized response sent back to client

### Client-Side Unified Flow (Using Unified ApiService)

1. **Module Initialization**: Frontend module initializes unified ApiService with module context
2. **Request Construction**: Unified ApiService constructs request with module-specific parameters
3. **Authentication**: Unified authentication parameters added automatically
4. **HTTP Request**: Unified service makes HTTP request to server
5. **Module Context**: Server detects module context and adapts behavior
6. **Response Processing**: Unified service processes response with module-aware logic
7. **Data Standardization**: Response data standardized for module consumption
8. **UI Update**: Frontend component updates UI with processed data

### Enhanced Flow Features

- **Module Context Awareness**: Automatic behavior adaptation based on calling module
- **Performance Monitoring**: Built-in performance tracking throughout the flow
- **Error Handling**: Enhanced error handling with detailed context
- **Backward Compatibility**: Existing client code works without modification

## Unified Database Access

The unified SmartTest API server uses an enhanced database layer that serves all modules:

### Enhanced Database Features
- **Shared Connection Pooling**: Single connection pool shared across all modules
- **Real SSH Connectivity**: Direct SSH connections with automatic retry and recovery
- **Performance Monitoring**: Built-in query performance tracking and optimization
- **Module Context Awareness**: Query optimization based on calling module

### Connection Methods
- **Direct SSH Commands**: Primary connection method with enhanced reliability
- **SSH Tunnel**: Fallback connection method for network restrictions
- **Connection Recovery**: Automatic reconnection and error recovery

### Database Layer Benefits
- **Unified Interface**: Consistent database operations across all modules
- **Performance Optimization**: Shared connection pooling improves performance
- **Error Recovery**: Automatic connection recovery and retry logic
- **Comprehensive Testing**: Real database integration tests ensure reliability

## Unified External API Integration

The unified SmartTest API server consolidates external API integration:

### Unified Integration Mechanisms

1. **Unified External API Service**: Centralized service handling all external API communication
   - Consistent authentication across all modules
   - Unified error handling and retry logic
   - Performance monitoring and optimization
   - Module context awareness

2. **Enhanced Proxy Middleware**: Improved proxy functionality
   - Forwards requests to external APIs on port 9080
   - Module-aware request routing
   - Enhanced error handling and logging

3. **Unified Cookie Authentication**: Consolidated authentication service
   - Manages JSESSIONID cookies for all modules
   - Automatic session renewal and recovery
   - Module context preservation

## Enhanced Error Handling

The unified API server implements comprehensive error handling:

### Error Handling Features
- **Module Context**: Errors include module context information
- **Detailed Logging**: Enhanced error logging with stack traces
- **Error Recovery**: Automatic retry and recovery mechanisms
- **Standardized Responses**: Consistent error response format across all modules

### Error Response Format
```json
{
  "success": false,
  "message": "Error message",
  "error": "Detailed error information",
  "moduleContext": "dashboard|reports|config",
  "requestId": "unique-request-identifier",
  "timestamp": "2023-01-01T00:00:00Z"
}
```

## Unified Authentication

The unified API server provides enhanced authentication:

### Authentication Mechanisms

1. **Unified Local Authentication**: Enhanced username/password authentication
   - Module context preservation
   - Enhanced security features
   - Session management across modules

2. **Unified External Authentication**: Consolidated external API authentication
   - Shared cookie management across modules
   - Automatic session renewal
   - Module-aware authentication state

### Authentication Benefits
- **Single Sign-On**: Authentication state shared across modules
- **Enhanced Security**: Improved security features and monitoring
- **Module Context**: Authentication preserves module context

## Enhanced Logging

The unified API server implements comprehensive logging:

### Logging Features
- **Module Identification**: All logs include module context
- **Performance Monitoring**: Request/response timing and performance metrics
- **Error Tracking**: Detailed error logging with stack traces
- **Database Query Logging**: SQL query execution and performance tracking

### Log Format
```
[TIMESTAMP] [MODULE_CONTEXT] [REQUEST_ID] METHOD URL - STATUS_CODE (DURATION_MS)
```

## Testing and Quality Assurance

The unified architecture includes comprehensive testing:

### Testing Components
- **Unit Tests**: Individual service and component testing
- **Integration Tests**: API-database flow testing with real connections
- **Database Tests**: Real database connectivity and query testing
- **Performance Tests**: Response time and optimization testing

### Quality Metrics
- **Test Coverage**: 90%+ coverage for critical components
- **Performance Benchmarks**: Response time thresholds and monitoring
- **Error Recovery**: Comprehensive error scenario testing

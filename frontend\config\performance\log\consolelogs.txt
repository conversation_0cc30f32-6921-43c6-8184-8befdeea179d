simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":50,"type":"single_case"}
15:17:34.486 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
15:17:36.485 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
15:17:38.261 simple-optimizations.js:47 Request completed: recentRuns_{"limit":100,"type":"single_case"}
15:17:38.261 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:17:38.261 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:17:38.261 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:17:38.261 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:17:38.261 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:17:38.261 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:17:38.261 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:17:38.261 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:17:38.261 config.js:484 Received 0 active tests update
15:17:38.261 config.js:538 Updating active tests display with 0 tests
15:17:38.261 config.js:683 🔄 renderActiveTests called, active tests count: 1
15:17:38.261 config.js:723 📋 Found 2 existing cards in DOM
15:17:38.261 config.js:729 ⚠️ Found duplicate card for test 18070, removing it
(anonymous) @ config.js:729
renderActiveTests @ config.js:725
updateActiveTestsDisplay @ config.js:576
(anonymous) @ config.js:485
(anonymous) @ simple-optimizations.js:158
notifySubscribers @ simple-optimizations.js:156
poll @ simple-optimizations.js:147
await in poll
(anonymous) @ simple-optimizations.js:136
setInterval
startPolling @ simple-optimizations.js:135
setupConfigPolling @ simple-optimizations.js:262
setTimeout
(anonymous) @ simple-optimizations.js:307
(anonymous) @ simple-optimizations.js:309
15:17:38.261 config.js:736 📋 After cleanup: 1 unique cards
15:17:38.261 config.js:743 Processing active test: 18070
15:17:38.261 config.js:748 Replacing existing card for test 18070
15:17:38.262 config.js:772 ✅ renderActiveTests completed
15:17:38.262 config.js:484 Received 0 active tests update
15:17:38.262 config.js:538 Updating active tests display with 0 tests
15:17:38.262 config.js:683 🔄 renderActiveTests called, active tests count: 1
15:17:38.262 config.js:723 📋 Found 2 existing cards in DOM
15:17:38.262 config.js:729 ⚠️ Found duplicate card for test 18070, removing it
(anonymous) @ config.js:729
renderActiveTests @ config.js:725
updateActiveTestsDisplay @ config.js:576
(anonymous) @ config.js:485
(anonymous) @ simple-optimizations.js:158
notifySubscribers @ simple-optimizations.js:156
poll @ simple-optimizations.js:147
await in poll
(anonymous) @ simple-optimizations.js:136
setInterval
startPolling @ simple-optimizations.js:135
setupConfigPolling @ simple-optimizations.js:262
setTimeout
(anonymous) @ simple-optimizations.js:307
(anonymous) @ simple-optimizations.js:309
15:17:38.262 config.js:736 📋 After cleanup: 1 unique cards
15:17:38.262 config.js:743 Processing active test: 18070
15:17:38.262 config.js:748 Replacing existing card for test 18070
15:17:38.262 config.js:772 ✅ renderActiveTests completed
15:17:38.262 config.js:484 Received 0 active tests update
15:17:38.262 config.js:538 Updating active tests display with 0 tests
15:17:38.262 config.js:683 🔄 renderActiveTests called, active tests count: 1
15:17:38.262 config.js:723 📋 Found 2 existing cards in DOM
15:17:38.262 config.js:729 ⚠️ Found duplicate card for test 18070, removing it
(anonymous) @ config.js:729
renderActiveTests @ config.js:725
updateActiveTestsDisplay @ config.js:576
(anonymous) @ config.js:485
(anonymous) @ simple-optimizations.js:158
notifySubscribers @ simple-optimizations.js:156
poll @ simple-optimizations.js:147
await in poll
(anonymous) @ simple-optimizations.js:136
setInterval
startPolling @ simple-optimizations.js:135
setupConfigPolling @ simple-optimizations.js:262
setTimeout
(anonymous) @ simple-optimizations.js:307
(anonymous) @ simple-optimizations.js:309
15:17:38.262 config.js:736 📋 After cleanup: 1 unique cards
15:17:38.262 config.js:743 Processing active test: 18070
15:17:38.262 config.js:748 Replacing existing card for test 18070
15:17:38.262 config.js:772 ✅ renderActiveTests completed
15:17:38.262 config.js:484 Received 0 active tests update
15:17:38.262 config.js:538 Updating active tests display with 0 tests
15:17:38.262 config.js:683 🔄 renderActiveTests called, active tests count: 1
15:17:38.263 config.js:723 📋 Found 2 existing cards in DOM
15:17:38.263 config.js:729 ⚠️ Found duplicate card for test 18070, removing it
(anonymous) @ config.js:729
renderActiveTests @ config.js:725
updateActiveTestsDisplay @ config.js:576
(anonymous) @ config.js:485
(anonymous) @ simple-optimizations.js:158
notifySubscribers @ simple-optimizations.js:156
poll @ simple-optimizations.js:147
await in poll
(anonymous) @ simple-optimizations.js:136
setInterval
startPolling @ simple-optimizations.js:135
setupConfigPolling @ simple-optimizations.js:262
setTimeout
(anonymous) @ simple-optimizations.js:307
(anonymous) @ simple-optimizations.js:309
15:17:38.263 config.js:736 📋 After cleanup: 1 unique cards
15:17:38.263 config.js:743 Processing active test: 18070
15:17:38.263 config.js:748 Replacing existing card for test 18070
15:17:38.263 config.js:772 ✅ renderActiveTests completed
15:17:38.263 config.js:484 Received 0 active tests update
15:17:38.263 config.js:538 Updating active tests display with 0 tests
15:17:38.263 config.js:683 🔄 renderActiveTests called, active tests count: 1
15:17:38.263 config.js:723 📋 Found 2 existing cards in DOM
15:17:38.264 config.js:729 ⚠️ Found duplicate card for test 18070, removing it
(anonymous) @ config.js:729
renderActiveTests @ config.js:725
updateActiveTestsDisplay @ config.js:576
(anonymous) @ config.js:485
(anonymous) @ simple-optimizations.js:158
notifySubscribers @ simple-optimizations.js:156
poll @ simple-optimizations.js:147
await in poll
(anonymous) @ simple-optimizations.js:136
setInterval
startPolling @ simple-optimizations.js:135
setupConfigPolling @ simple-optimizations.js:262
setTimeout
(anonymous) @ simple-optimizations.js:307
(anonymous) @ simple-optimizations.js:309
15:17:38.264 config.js:736 📋 After cleanup: 1 unique cards
15:17:38.264 config.js:743 Processing active test: 18070
15:17:38.264 config.js:748 Replacing existing card for test 18070
15:17:38.264 config.js:772 ✅ renderActiveTests completed
15:17:38.264 config.js:484 Received 0 active tests update
15:17:38.264 config.js:538 Updating active tests display with 0 tests
15:17:38.264 config.js:683 🔄 renderActiveTests called, active tests count: 1
15:17:38.264 config.js:723 📋 Found 2 existing cards in DOM
15:17:38.264 config.js:729 ⚠️ Found duplicate card for test 18070, removing it
(anonymous) @ config.js:729
renderActiveTests @ config.js:725
updateActiveTestsDisplay @ config.js:576
(anonymous) @ config.js:485
(anonymous) @ simple-optimizations.js:158
notifySubscribers @ simple-optimizations.js:156
poll @ simple-optimizations.js:147
await in poll
(anonymous) @ simple-optimizations.js:136
setInterval
startPolling @ simple-optimizations.js:135
setupConfigPolling @ simple-optimizations.js:262
setTimeout
(anonymous) @ simple-optimizations.js:307
(anonymous) @ simple-optimizations.js:309
15:17:38.265 config.js:736 📋 After cleanup: 1 unique cards
15:17:38.265 config.js:743 Processing active test: 18070
15:17:38.265 config.js:748 Replacing existing card for test 18070
15:17:38.265 config.js:772 ✅ renderActiveTests completed
15:17:38.265 config.js:484 Received 0 active tests update
15:17:38.265 config.js:538 Updating active tests display with 0 tests
15:17:38.265 config.js:683 🔄 renderActiveTests called, active tests count: 1
15:17:38.265 config.js:723 📋 Found 2 existing cards in DOM
15:17:38.265 config.js:729 ⚠️ Found duplicate card for test 18070, removing it
(anonymous) @ config.js:729
renderActiveTests @ config.js:725
updateActiveTestsDisplay @ config.js:576
(anonymous) @ config.js:485
(anonymous) @ simple-optimizations.js:158
notifySubscribers @ simple-optimizations.js:156
poll @ simple-optimizations.js:147
await in poll
(anonymous) @ simple-optimizations.js:136
setInterval
startPolling @ simple-optimizations.js:135
setupConfigPolling @ simple-optimizations.js:262
setTimeout
(anonymous) @ simple-optimizations.js:307
(anonymous) @ simple-optimizations.js:309
15:17:38.266 config.js:736 📋 After cleanup: 1 unique cards
15:17:38.266 config.js:743 Processing active test: 18070
15:17:38.266 config.js:748 Replacing existing card for test 18070
15:17:38.266 config.js:772 ✅ renderActiveTests completed
15:17:38.266 config.js:484 Received 0 active tests update
15:17:38.266 config.js:538 Updating active tests display with 0 tests
15:17:38.266 config.js:683 🔄 renderActiveTests called, active tests count: 1
15:17:38.266 config.js:723 📋 Found 2 existing cards in DOM
15:17:38.266 config.js:729 ⚠️ Found duplicate card for test 18070, removing it
(anonymous) @ config.js:729
renderActiveTests @ config.js:725
updateActiveTestsDisplay @ config.js:576
(anonymous) @ config.js:485
(anonymous) @ simple-optimizations.js:158
notifySubscribers @ simple-optimizations.js:156
poll @ simple-optimizations.js:147
await in poll
(anonymous) @ simple-optimizations.js:136
setInterval
startPolling @ simple-optimizations.js:135
setupConfigPolling @ simple-optimizations.js:262
setTimeout
(anonymous) @ simple-optimizations.js:307
(anonymous) @ simple-optimizations.js:309
15:17:38.267 config.js:736 📋 After cleanup: 1 unique cards
15:17:38.267 config.js:743 Processing active test: 18070
15:17:38.267 config.js:748 Replacing existing card for test 18070
15:17:38.267 config.js:772 ✅ renderActiveTests completed
15:17:38.483 simple-optimizations.js:26 Cache HIT: recentRuns_{"limit":100,"type":"single_case"}
15:17:38.483 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:17:38.483 config.js:484 Received 0 active tests update
15:17:38.484 config.js:538 Updating active tests display with 0 tests
15:17:38.484 config.js:683 🔄 renderActiveTests called, active tests count: 1
15:17:38.484 config.js:723 📋 Found 2 existing cards in DOM
15:17:38.484 config.js:729 ⚠️ Found duplicate card for test 18070, removing it
(anonymous) @ config.js:729
renderActiveTests @ config.js:725
updateActiveTestsDisplay @ config.js:576
(anonymous) @ config.js:485
(anonymous) @ simple-optimizations.js:158
notifySubscribers @ simple-optimizations.js:156
poll @ simple-optimizations.js:147
await in poll
(anonymous) @ simple-optimizations.js:136
setInterval
startPolling @ simple-optimizations.js:135
setupConfigPolling @ simple-optimizations.js:262
setTimeout
(anonymous) @ simple-optimizations.js:307
(anonymous) @ simple-optimizations.js:309
15:17:38.484 config.js:736 📋 After cleanup: 1 unique cards
15:17:38.484 config.js:743 Processing active test: 18070
15:17:38.484 config.js:748 Replacing existing card for test 18070
15:17:38.485 config.js:772 ✅ renderActiveTests completed
15:17:40.094 simple-optimizations.js:47 Request completed: recentRuns_{"limit":50,"type":"single_case"}
15:17:40.094 config.js:476 Received 50 recent runs update
15:17:40.094 config.js:585 processRecentRunsData called with 50 runs
15:17:40.095 config.js:611 Processing active test 18070, found in recent runs: true
15:17:40.095 config.js:627 Checking completion for TSN 18070: status="passed", hasEndTime=true, isCompleted=true
15:17:40.095 config.js:632 Test 18070 completed with status: passed
15:17:40.096 config.js:683 🔄 renderActiveTests called, active tests count: 1
15:17:40.096 config.js:723 📋 Found 2 existing cards in DOM
15:17:40.096 config.js:729 ⚠️ Found duplicate card for test 18070, removing it
(anonymous) @ config.js:729
renderActiveTests @ config.js:725
processRecentRunsData @ config.js:658
(anonymous) @ config.js:478
(anonymous) @ simple-optimizations.js:158
notifySubscribers @ simple-optimizations.js:156
poll @ simple-optimizations.js:147
await in poll
(anonymous) @ simple-optimizations.js:136
setInterval
startPolling @ simple-optimizations.js:135
setupConfigPolling @ simple-optimizations.js:248
setTimeout
(anonymous) @ simple-optimizations.js:307
(anonymous) @ simple-optimizations.js:309
15:17:40.097 config.js:736 📋 After cleanup: 1 unique cards
15:17:40.097 config.js:743 Processing active test: 18070
15:17:40.097 config.js:748 Replacing existing card for test 18070
15:17:40.097 config.js:772 ✅ renderActiveTests completed
15:17:40.098 config.js:868 renderRecentRuns called with data: (50) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
15:17:40.098 config.js:869 recentRunsBody element: <tbody id=​"recent-runs-body">​…​</tbody>​
15:17:40.101 config.js:887 Rendering 50 recent runs
15:17:40.107 config.js:476 Received 50 recent runs update
15:17:40.108 config.js:585 processRecentRunsData called with 50 runs
15:17:40.108 config.js:611 Processing active test 18070, found in recent runs: true
15:17:40.108 config.js:627 Checking completion for TSN 18070: status="passed", hasEndTime=true, isCompleted=true
15:17:40.108 config.js:683 🔄 renderActiveTests called, active tests count: 1
15:17:40.108 config.js:723 📋 Found 2 existing cards in DOM
15:17:40.109 config.js:729 ⚠️ Found duplicate card for test 18070, removing it
(anonymous) @ config.js:729
renderActiveTests @ config.js:725
processRecentRunsData @ config.js:658
(anonymous) @ config.js:478
(anonymous) @ simple-optimizations.js:158
notifySubscribers @ simple-optimizations.js:156
poll @ simple-optimizations.js:147
await in poll
(anonymous) @ simple-optimizations.js:136
setInterval
startPolling @ simple-optimizations.js:135
setupConfigPolling @ simple-optimizations.js:248
setTimeout
(anonymous) @ simple-optimizations.js:307
(anonymous) @ simple-optimizations.js:309
15:17:40.109 config.js:736 📋 After cleanup: 1 unique cards
15:17:40.109 config.js:743 Processing active test: 18070
15:17:40.109 config.js:748 Replacing existing card for test 18070
15:17:40.109 config.js:772 ✅ renderActiveTests completed
15:17:40.109 config.js:868 renderRecentRuns called with data: (50) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
15:17:40.110 config.js:869 recentRunsBody element: <tbody id=​"recent-runs-body">​…​</tbody>​
15:17:40.111 config.js:887 Rendering 50 recent runs
15:17:40.476 simple-optimizations.js:26 Cache HIT: recentRuns_{"limit":100,"type":"single_case"}
15:17:40.476 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:17:40.477 config.js:484 Received 0 active tests update
15:17:40.477 config.js:538 Updating active tests display with 0 tests
15:17:40.477 config.js:683 🔄 renderActiveTests called, active tests count: 1
15:17:40.477 config.js:723 📋 Found 2 existing cards in DOM
15:17:40.478 config.js:729 ⚠️ Found duplicate card for test 18070, removing it
(anonymous) @ config.js:729
renderActiveTests @ config.js:725
updateActiveTestsDisplay @ config.js:576
(anonymous) @ config.js:485
(anonymous) @ simple-optimizations.js:158
notifySubscribers @ simple-optimizations.js:156
poll @ simple-optimizations.js:147
await in poll
(anonymous) @ simple-optimizations.js:136
setInterval
startPolling @ simple-optimizations.js:135
setupConfigPolling @ simple-optimizations.js:262
setTimeout
(anonymous) @ simple-optimizations.js:307
(anonymous) @ simple-optimizations.js:309
15:17:40.479 config.js:736 📋 After cleanup: 1 unique cards
15:17:40.479 config.js:743 Processing active test: 18070
15:17:40.479 config.js:748 Replacing existing card for test 18070
15:17:40.480 config.js:772 ✅ renderActiveTests completed
15:17:42.478 simple-optimizations.js:26 Cache HIT: recentRuns_{"limit":50,"type":"single_case"}
15:17:42.478 config.js:476 Received 50 recent runs update
15:17:42.479 config.js:585 processRecentRunsData called with 50 runs
15:17:42.479 config.js:611 Processing active test 18070, found in recent runs: true
15:17:42.479 config.js:627 Checking completion for TSN 18070: status="passed", hasEndTime=true, isCompleted=true
15:17:42.479 config.js:683 🔄 renderActiveTests called, active tests count: 1
15:17:42.479 config.js:723 📋 Found 2 existing cards in DOM
15:17:42.479 config.js:729 ⚠️ Found duplicate card for test 18070, removing it
(anonymous) @ config.js:729
renderActiveTests @ config.js:725
processRecentRunsData @ config.js:658
(anonymous) @ config.js:478
(anonymous) @ simple-optimizations.js:158
notifySubscribers @ simple-optimizations.js:156
poll @ simple-optimizations.js:147
await in poll
(anonymous) @ simple-optimizations.js:136
setInterval
startPolling @ simple-optimizations.js:135
setupConfigPolling @ simple-optimizations.js:248
setTimeout
(anonymous) @ simple-optimizations.js:307
(anonymous) @ simple-optimizations.js:309
15:17:42.479 config.js:736 📋 After cleanup: 1 unique cards
15:17:42.480 config.js:743 Processing active test: 18070
15:17:42.480 config.js:748 Replacing existing card for test 18070
15:17:42.480 config.js:772 ✅ renderActiveTests completed
15:17:42.480 config.js:868 renderRecentRuns called with data: (50) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
15:17:42.481 config.js:869 recentRunsBody element: <tbody id=​"recent-runs-body">​…​</tbody>​
15:17:42.483 config.js:887 Rendering 50 recent runs
15:17:42.494 unified-api-service.js:316 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
15:17:42.494 unified-api-service.js:321 Making GET request to: http://localhost:3000/local/recent-runs
15:17:44.477 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
15:17:46.476 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
15:17:48.487 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
15:17:50.482 unified-api-service.js:316 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
15:17:50.482 unified-api-service.js:321 Making GET request to: http://localhost:3000/local/recent-runs
15:17:50.483 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
15:17:52.476 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
15:17:54.483 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
15:17:55.681 config.js:954 viewTestDetails called for TSN: 18070
15:17:55.682 test-details-modal.js:62 Using JWT credentials from unified auth client: <EMAIL>
15:17:55.682 test-details-modal.js:206 Attempting to load test details from external API first...
15:17:55.683 reports.details-external.js:3 Loading test details from external API for 18070...
15:17:55.683 reports.details-external.js:7 🔧 External API Service found: true
15:17:55.683 reports.details-external.js:8 🔧 Service type: externalApiService
15:17:55.683 external-api-service.js:56 [ExternalApiService] 🔍 Session invalid: jsessionId=false, jsessionExpiry=false
15:17:55.683 reports.details-external.js:16 🔐 Session valid: false
15:17:55.683 reports.details-external.js:20 🔑 Logging in to external API...
15:17:55.683 reports.details-external.js:54 🔐 Attempting login with credentials: <EMAIL>
15:17:55.684 external-api-service.js:75 [ExternalApiService] 🔑 Attempting login to external <NAME_EMAIL>...
15:17:55.684 external-api-service.js:81 [ExternalApiService] 🔗 Using external login endpoint: /api/external/login
15:17:55.684 external-api-service.js:83 [ExternalApiService] 📡 Making login request to /api/external/login
15:17:55.684 config.js:1063 View Details clicked for TSN ID: 18070
15:17:55.685 test-details-modal.js:62 Using JWT credentials from unified auth client: <EMAIL>
15:17:55.685 test-details-modal.js:206 Attempting to load test details from external API first...
15:17:55.685 reports.details-external.js:3 Loading test details from external API for 18070...
15:17:55.685 reports.details-external.js:7 🔧 External API Service found: true
15:17:55.685 reports.details-external.js:8 🔧 Service type: externalApiService
15:17:55.685 external-api-service.js:56 [ExternalApiService] 🔍 Session invalid: jsessionId=false, jsessionExpiry=false
15:17:55.685 reports.details-external.js:16 🔐 Session valid: false
15:17:55.685 reports.details-external.js:20 🔑 Logging in to external API...
15:17:55.685 reports.details-external.js:54 🔐 Attempting login with credentials: <EMAIL>
15:17:55.685 external-api-service.js:75 [ExternalApiService] 🔑 Attempting login to external <NAME_EMAIL>...
15:17:55.685 external-api-service.js:81 [ExternalApiService] 🔗 Using external login endpoint: /api/external/login
15:17:55.685 external-api-service.js:83 [ExternalApiService] 📡 Making login request to /api/external/login
15:17:56.486 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
15:17:57.807 external-api-service.js:92 [ExternalApiService] 📡 Login response status: 200
15:17:57.808 external-api-service.js:93 [ExternalApiService] 📡 Login response headers: {access-control-allow-origin: '*', connection: 'keep-alive', content-length: '5516', content-security-policy: "default-src 'self'; script-src 'self' 'unsafe-inli…'; form-action 'self'; upgrade-insecure-requests;", content-type: 'text/html; charset=utf-8', …}
15:17:57.808 external-api-service.js:100 [ExternalApiService] 🍪 Checking for JSESSIONID cookie...
15:17:57.809 external-api-service.js:104 [ExternalApiService] 🍪 Document cookies: sessionId=ea185bd690eb92271601a4c24b09ad7d4f2beac2cc711a7d53cb69864e23e845; JSESSIONID=D94AC70BDA30ABB8E130DA0D32DE1D7B
15:17:57.809 external-api-service.js:109 [ExternalApiService] ✅ Found JSESSIONID in document.cookie: D94AC70BDA30ABB8E130DA0D32DE1D7B
15:17:57.810 external-api-service.js:132 [ExternalApiService] 📄 Response text length: 5516
15:17:57.810 external-api-service.js:149 [ExternalApiService] 🔍 Login success indicators: {hasValidJsessionId: true, jsessionId: 'D94AC70B...', includesUid: true, includesWelcome: false, includesLogout: false, …}
15:17:57.810 external-api-service.js:162 [ExternalApiService] ✅ Login successful
15:17:57.810 reports.details-external.js:58 ✅ Login successful
15:17:57.810 reports.details-external.js:80 📡 Fetching report summary...
15:17:57.810 reports.details-external.js:95 🔑 External API session available: Explicit ID
15:17:57.810 reports.details-external.js:100 📡 Making request with credentials: include to ensure cookies are sent
15:17:57.865 external-api-service.js:92 [ExternalApiService] 📡 Login response status: 200
15:17:57.865 external-api-service.js:93 [ExternalApiService] 📡 Login response headers: {access-control-allow-origin: '*', connection: 'keep-alive', content-length: '5516', content-security-policy: "default-src 'self'; script-src 'self' 'unsafe-inli…'; form-action 'self'; upgrade-insecure-requests;", content-type: 'text/html; charset=utf-8', …}
15:17:57.865 external-api-service.js:100 [ExternalApiService] 🍪 Checking for JSESSIONID cookie...
15:17:57.865 external-api-service.js:104 [ExternalApiService] 🍪 Document cookies: sessionId=ea185bd690eb92271601a4c24b09ad7d4f2beac2cc711a7d53cb69864e23e845; JSESSIONID=B8AF002792BE3FD32B25C03D51C49C5A
15:17:57.865 external-api-service.js:109 [ExternalApiService] ✅ Found JSESSIONID in document.cookie: B8AF002792BE3FD32B25C03D51C49C5A
15:17:57.866 external-api-service.js:132 [ExternalApiService] 📄 Response text length: 5516
15:17:57.866 external-api-service.js:149 [ExternalApiService] 🔍 Login success indicators: {hasValidJsessionId: true, jsessionId: 'B8AF0027...', includesUid: true, includesWelcome: false, includesLogout: false, …}
15:17:57.866 external-api-service.js:162 [ExternalApiService] ✅ Login successful
15:17:57.866 reports.details-external.js:58 ✅ Login successful
15:17:57.866 reports.details-external.js:80 📡 Fetching report summary...
15:17:57.866 reports.details-external.js:95 🔑 External API session available: Explicit ID
15:17:57.866 reports.details-external.js:100 📡 Making request with credentials: include to ensure cookies are sent
15:17:57.970 simple-optimizations.js:47 Request completed: recentRuns_{"limit":100,"type":"single_case"}
15:17:57.970 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:17:57.970 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:17:57.970 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:17:57.970 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:17:57.970 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:17:57.970 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:17:57.970 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:17:57.970 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:17:57.970 config.js:484 Received 0 active tests update
15:17:57.971 config.js:538 Updating active tests display with 0 tests
15:17:57.971 config.js:683 🔄 renderActiveTests called, active tests count: 1
15:17:57.971 config.js:723 📋 Found 2 existing cards in DOM
15:17:57.971 config.js:729 ⚠️ Found duplicate card for test 18070, removing it
(anonymous) @ config.js:729
renderActiveTests @ config.js:725
updateActiveTestsDisplay @ config.js:576
(anonymous) @ config.js:485
(anonymous) @ simple-optimizations.js:158
notifySubscribers @ simple-optimizations.js:156
poll @ simple-optimizations.js:147
await in poll
(anonymous) @ simple-optimizations.js:136
setInterval
startPolling @ simple-optimizations.js:135
setupConfigPolling @ simple-optimizations.js:262
setTimeout
(anonymous) @ simple-optimizations.js:307
(anonymous) @ simple-optimizations.js:309
15:17:57.971 config.js:736 📋 After cleanup: 1 unique cards
15:17:57.971 config.js:743 Processing active test: 18070
15:17:57.971 config.js:748 Replacing existing card for test 18070
15:17:57.971 config.js:772 ✅ renderActiveTests completed
15:17:57.971 config.js:484 Received 0 active tests update
15:17:57.971 config.js:538 Updating active tests display with 0 tests
15:17:57.971 config.js:683 🔄 renderActiveTests called, active tests count: 1
15:17:57.971 config.js:723 📋 Found 2 existing cards in DOM
15:17:57.972 config.js:729 ⚠️ Found duplicate card for test 18070, removing it
(anonymous) @ config.js:729
renderActiveTests @ config.js:725
updateActiveTestsDisplay @ config.js:576
(anonymous) @ config.js:485
(anonymous) @ simple-optimizations.js:158
notifySubscribers @ simple-optimizations.js:156
poll @ simple-optimizations.js:147
await in poll
(anonymous) @ simple-optimizations.js:136
setInterval
startPolling @ simple-optimizations.js:135
setupConfigPolling @ simple-optimizations.js:262
setTimeout
(anonymous) @ simple-optimizations.js:307
(anonymous) @ simple-optimizations.js:309
15:17:57.972 config.js:736 📋 After cleanup: 1 unique cards
15:17:57.972 config.js:743 Processing active test: 18070
15:17:57.972 config.js:748 Replacing existing card for test 18070
15:17:57.972 config.js:772 ✅ renderActiveTests completed
15:17:57.972 config.js:484 Received 0 active tests update
15:17:57.972 config.js:538 Updating active tests display with 0 tests
15:17:57.972 config.js:683 🔄 renderActiveTests called, active tests count: 1
15:17:57.972 config.js:723 📋 Found 2 existing cards in DOM
15:17:57.972 config.js:729 ⚠️ Found duplicate card for test 18070, removing it
(anonymous) @ config.js:729
renderActiveTests @ config.js:725
updateActiveTestsDisplay @ config.js:576
(anonymous) @ config.js:485
(anonymous) @ simple-optimizations.js:158
notifySubscribers @ simple-optimizations.js:156
poll @ simple-optimizations.js:147
await in poll
(anonymous) @ simple-optimizations.js:136
setInterval
startPolling @ simple-optimizations.js:135
setupConfigPolling @ simple-optimizations.js:262
setTimeout
(anonymous) @ simple-optimizations.js:307
(anonymous) @ simple-optimizations.js:309
15:17:57.972 config.js:736 📋 After cleanup: 1 unique cards
15:17:57.972 config.js:743 Processing active test: 18070
15:17:57.972 config.js:748 Replacing existing card for test 18070
15:17:57.972 config.js:772 ✅ renderActiveTests completed
15:17:57.972 config.js:484 Received 0 active tests update
15:17:57.972 config.js:538 Updating active tests display with 0 tests
15:17:57.972 config.js:683 🔄 renderActiveTests called, active tests count: 1
15:17:57.973 config.js:723 📋 Found 2 existing cards in DOM
15:17:57.973 config.js:729 ⚠️ Found duplicate card for test 18070, removing it
(anonymous) @ config.js:729
renderActiveTests @ config.js:725
updateActiveTestsDisplay @ config.js:576
(anonymous) @ config.js:485
(anonymous) @ simple-optimizations.js:158
notifySubscribers @ simple-optimizations.js:156
poll @ simple-optimizations.js:147
await in poll
(anonymous) @ simple-optimizations.js:136
setInterval
startPolling @ simple-optimizations.js:135
setupConfigPolling @ simple-optimizations.js:262
setTimeout
(anonymous) @ simple-optimizations.js:307
(anonymous) @ simple-optimizations.js:309
15:17:57.973 config.js:736 📋 After cleanup: 1 unique cards
15:17:57.973 config.js:743 Processing active test: 18070
15:17:57.973 config.js:748 Replacing existing card for test 18070
15:17:57.973 config.js:772 ✅ renderActiveTests completed
15:17:57.973 config.js:484 Received 0 active tests update
15:17:57.973 config.js:538 Updating active tests display with 0 tests
15:17:57.973 config.js:683 🔄 renderActiveTests called, active tests count: 1
15:17:57.973 config.js:723 📋 Found 2 existing cards in DOM
15:17:57.973 config.js:729 ⚠️ Found duplicate card for test 18070, removing it
(anonymous) @ config.js:729
renderActiveTests @ config.js:725
updateActiveTestsDisplay @ config.js:576
(anonymous) @ config.js:485
(anonymous) @ simple-optimizations.js:158
notifySubscribers @ simple-optimizations.js:156
poll @ simple-optimizations.js:147
await in poll
(anonymous) @ simple-optimizations.js:136
setInterval
startPolling @ simple-optimizations.js:135
setupConfigPolling @ simple-optimizations.js:262
setTimeout
(anonymous) @ simple-optimizations.js:307
(anonymous) @ simple-optimizations.js:309
15:17:57.973 config.js:736 📋 After cleanup: 1 unique cards
15:17:57.973 config.js:743 Processing active test: 18070
15:17:57.973 config.js:748 Replacing existing card for test 18070
15:17:57.973 config.js:772 ✅ renderActiveTests completed
15:17:57.973 config.js:484 Received 0 active tests update
15:17:57.973 config.js:538 Updating active tests display with 0 tests
15:17:57.973 config.js:683 🔄 renderActiveTests called, active tests count: 1
15:17:57.974 config.js:723 📋 Found 2 existing cards in DOM
15:17:57.974 config.js:729 ⚠️ Found duplicate card for test 18070, removing it
(anonymous) @ config.js:729
renderActiveTests @ config.js:725
updateActiveTestsDisplay @ config.js:576
(anonymous) @ config.js:485
(anonymous) @ simple-optimizations.js:158
notifySubscribers @ simple-optimizations.js:156
poll @ simple-optimizations.js:147
await in poll
(anonymous) @ simple-optimizations.js:136
setInterval
startPolling @ simple-optimizations.js:135
setupConfigPolling @ simple-optimizations.js:262
setTimeout
(anonymous) @ simple-optimizations.js:307
(anonymous) @ simple-optimizations.js:309
15:17:57.974 config.js:736 📋 After cleanup: 1 unique cards
15:17:57.974 config.js:743 Processing active test: 18070
15:17:57.974 config.js:748 Replacing existing card for test 18070
15:17:57.974 config.js:772 ✅ renderActiveTests completed
15:17:57.974 config.js:484 Received 0 active tests update
15:17:57.974 config.js:538 Updating active tests display with 0 tests
15:17:57.974 config.js:683 🔄 renderActiveTests called, active tests count: 1
15:17:57.974 config.js:723 📋 Found 2 existing cards in DOM
15:17:57.974 config.js:729 ⚠️ Found duplicate card for test 18070, removing it
(anonymous) @ config.js:729
renderActiveTests @ config.js:725
updateActiveTestsDisplay @ config.js:576
(anonymous) @ config.js:485
(anonymous) @ simple-optimizations.js:158
notifySubscribers @ simple-optimizations.js:156
poll @ simple-optimizations.js:147
await in poll
(anonymous) @ simple-optimizations.js:136
setInterval
startPolling @ simple-optimizations.js:135
setupConfigPolling @ simple-optimizations.js:262
setTimeout
(anonymous) @ simple-optimizations.js:307
(anonymous) @ simple-optimizations.js:309
15:17:57.974 config.js:736 📋 After cleanup: 1 unique cards
15:17:57.974 config.js:743 Processing active test: 18070
15:17:57.974 config.js:748 Replacing existing card for test 18070
15:17:57.974 config.js:772 ✅ renderActiveTests completed
15:17:57.974 config.js:484 Received 0 active tests update
15:17:57.974 config.js:538 Updating active tests display with 0 tests
15:17:57.974 config.js:683 🔄 renderActiveTests called, active tests count: 1
15:17:57.975 config.js:723 📋 Found 2 existing cards in DOM
15:17:57.975 config.js:729 ⚠️ Found duplicate card for test 18070, removing it
(anonymous) @ config.js:729
renderActiveTests @ config.js:725
updateActiveTestsDisplay @ config.js:576
(anonymous) @ config.js:485
(anonymous) @ simple-optimizations.js:158
notifySubscribers @ simple-optimizations.js:156
poll @ simple-optimizations.js:147
await in poll
(anonymous) @ simple-optimizations.js:136
setInterval
startPolling @ simple-optimizations.js:135
setupConfigPolling @ simple-optimizations.js:262
setTimeout
(anonymous) @ simple-optimizations.js:307
(anonymous) @ simple-optimizations.js:309
15:17:57.975 config.js:736 📋 After cleanup: 1 unique cards
15:17:57.975 config.js:743 Processing active test: 18070
15:17:57.975 config.js:748 Replacing existing card for test 18070
15:17:57.975 config.js:772 ✅ renderActiveTests completed
15:17:58.095 reports.details-external.js:103 📡 Summary response status: 200
15:17:58.095 reports.details-external.js:104 📡 Summary response headers: {access-control-allow-origin: '*', connection: 'keep-alive', content-length: '1749', content-security-policy: "default-src 'self'; script-src 'self' 'unsafe-inli…'; form-action 'self'; upgrade-insecure-requests;", content-type: 'text/html; charset=utf-8', …}
15:17:58.095 reports.details-external.js:174 📄 Summary HTML length: 1749
15:17:58.096 reports.summary-parser.js:24 Parsing summary HTML for test ID: 18070
15:17:58.096 reports.summary-parser.js:30 Found session ID: 18070
15:17:58.096 reports.summary-parser.js:37 Found owner: <EMAIL>
15:17:58.096 reports.summary-parser.js:78 Found start time: 2025-07-29 12:17:06
15:17:58.096 reports.summary-parser.js:84 Found end time: 2025-07-29 12:17:22
15:17:58.096 reports.summary-parser.js:91 Found error info: Report PASS Case: 3180  check NO pending feeds
15:17:58.096 reports.summary-parser.js:104 Determined status: Success
15:17:58.096 reports.summary-parser.js:125 Found and parsed variables: {uid: '<EMAIL>', envir: 'qa02', shell_host: 'jps-qa10-app01', file_path: '/home/<USER>/', operatorConfigs: 'operatorNameConfigs', …}
15:17:58.096 reports.summary-parser.js:134 Found passed cases: 1
15:17:58.097 reports.summary-parser.js:140 Found failed cases: 0
15:17:58.097 reports.summary-parser.js:148 Found 1 test cases in HTML
15:17:58.097 reports.summary-parser.js:168 Extracted test case: ID 3180, Status: PASS, Description: &nbsp;check NO pending feeds...
15:17:58.097 reports.summary-parser.js:185 Found environment from envir variable: qa02shell_host=jps-qa10-app01file_path=/home/<USER>/operatorConfigs=operatorNameConfigskafka_server=kafka-qa-a0.lab.wagerworks.comdataCenter=GUrgs_env=qa02old_version=0networkType1=multi-sitenetworkType2=multi-sitesign=-rate_src=local
15:17:58.097 reports.summary-parser.js:191 Total cases: 1
15:17:58.097 reports.details-external.js:228 📊 Parsed summary data: {tsn_id: '18070', status: 'Success', start_time: '2025-07-29 12:17:06', end_time: '2025-07-29 12:17:22', owner: '<EMAIL>', …}
15:17:58.097 reports.details-external.js:231 📡 Fetching report details...
15:17:58.097 reports.details-external.js:234 📡 Making details request with credentials: include to ensure cookies are sent
15:17:58.146 reports.details-external.js:103 📡 Summary response status: 200
15:17:58.146 reports.details-external.js:104 📡 Summary response headers: {access-control-allow-origin: '*', connection: 'keep-alive', content-length: '1749', content-security-policy: "default-src 'self'; script-src 'self' 'unsafe-inli…'; form-action 'self'; upgrade-insecure-requests;", content-type: 'text/html; charset=utf-8', …}
15:17:58.146 reports.details-external.js:174 📄 Summary HTML length: 1749
15:17:58.147 reports.summary-parser.js:24 Parsing summary HTML for test ID: 18070
15:17:58.147 reports.summary-parser.js:30 Found session ID: 18070
15:17:58.147 reports.summary-parser.js:37 Found owner: <EMAIL>
15:17:58.147 reports.summary-parser.js:78 Found start time: 2025-07-29 12:17:06
15:17:58.147 reports.summary-parser.js:84 Found end time: 2025-07-29 12:17:22
15:17:58.147 reports.summary-parser.js:91 Found error info: Report PASS Case: 3180  check NO pending feeds
15:17:58.147 reports.summary-parser.js:104 Determined status: Success
15:17:58.147 reports.summary-parser.js:125 Found and parsed variables: {uid: '<EMAIL>', envir: 'qa02', shell_host: 'jps-qa10-app01', file_path: '/home/<USER>/', operatorConfigs: 'operatorNameConfigs', …}
15:17:58.147 reports.summary-parser.js:134 Found passed cases: 1
15:17:58.147 reports.summary-parser.js:140 Found failed cases: 0
15:17:58.147 reports.summary-parser.js:148 Found 1 test cases in HTML
15:17:58.147 reports.summary-parser.js:168 Extracted test case: ID 3180, Status: PASS, Description: &nbsp;check NO pending feeds...
15:17:58.147 reports.summary-parser.js:185 Found environment from envir variable: qa02shell_host=jps-qa10-app01file_path=/home/<USER>/operatorConfigs=operatorNameConfigskafka_server=kafka-qa-a0.lab.wagerworks.comdataCenter=GUrgs_env=qa02old_version=0networkType1=multi-sitenetworkType2=multi-sitesign=-rate_src=local
15:17:58.147 reports.summary-parser.js:191 Total cases: 1
15:17:58.147 reports.details-external.js:228 📊 Parsed summary data: {tsn_id: '18070', status: 'Success', start_time: '2025-07-29 12:17:06', end_time: '2025-07-29 12:17:22', owner: '<EMAIL>', …}
15:17:58.147 reports.details-external.js:231 📡 Fetching report details...
15:17:58.147 reports.details-external.js:234 📡 Making details request with credentials: include to ensure cookies are sent
15:17:58.381 reports.details-external.js:237 📡 Details response status: 200
15:17:58.381 reports.details-external.js:243 📄 Details HTML length: 5010
15:17:58.382 reports.details-parser.js:17 [DEBUG] parseReportDetailsHtml: Parsing details HTML for test ID: 18070
15:17:58.382 reports.details-parser.js:23 [DEBUG] parseReportDetailsHtml: Found session ID in details HTML: 18070
15:17:58.383 reports.details-parser.js:35 [DEBUG] parseReportDetailsHtml: Pagination: Page 1 of 1
15:17:58.383 reports.details-parser.js:43 [DEBUG] parseReportDetailsHtml: Found 3 rows in the details table (table#table tbody tr)
15:17:58.383 reports.details-parser.js:103 [DEBUG] parseReportDetailsHtml: Extracted 1 unique test cases from 3 detail rows.
15:17:58.383 reports.details-external.js:291 📊 Parsed details data: {tsn_id: '18070', test_cases: Array(1), report_html: '\n\n\n\n\n<html>\n<head>\n<title>Test Result Details</tit…<EMAIL></a><p/>\n\n</body>\n</html>\n', pagination: {…}, originalParameters: {…}}
15:17:58.384 reports.details-external.js:319 Processing 1 test cases from detailsData (which includes steps).
15:17:58.384 reports.details-external.js:326 Merging TC ID 3180: Summary status='Success', Detail steps count=3
15:17:58.384 reports.details-external.js:349 Final merged test_cases count: 1
15:17:58.384 reports.details-external.js:352   TC ID 3180, Status: Success, Steps: 3, HasFailures: false, Name: 3180
15:17:58.384 reports.details-external.js:371 [DEBUG] loadTestDetailsFromExternalApi: Assembled testDetails.originalParameters: {
  "uid": "<EMAIL>=qa02shell_host=jps-qa10-app01file_path=/home/<USER>/operatorConfigs=operatorNameConfigskafka_server=kafka-qa-a0.lab.wagerworks.comdataCenter=GUrgs_env=qa02old_version=0networkType1=multi-sitenetworkType2=multi-sitesign=-rate_src=local",
  "envir": "qa02",
  "shell_host": "jps-qa10-app01",
  "file_path": "/home/<USER>/",
  "operatorConfigs": "operatorNameConfigs",
  "kafka_server": "kafka-qa-a0.lab.wagerworks.com",
  "dataCenter": "GU",
  "rgs_env": "qa02",
  "old_version": "0",
  "networkType1": "multi-site",
  "networkType2": "multi-site",
  "sign": "-",
  "rate_src": "local"
}
15:17:58.384 reports.details-external.js:373 Test details loaded from external API: {id: '18070', tsn_id: '18070', test_id: '', test_name: 'Unknown Test', name: 'Unknown Test', …}
15:17:58.384 test-details-modal.js:210 Successfully loaded from external API: {id: '18070', tsn_id: '18070', test_id: '', test_name: 'Unknown Test', name: 'Unknown Test', …}
15:17:58.434 reports.details-external.js:237 📡 Details response status: 200
15:17:58.435 reports.details-external.js:243 📄 Details HTML length: 5010
15:17:58.435 reports.details-parser.js:17 [DEBUG] parseReportDetailsHtml: Parsing details HTML for test ID: 18070
15:17:58.436 reports.details-parser.js:23 [DEBUG] parseReportDetailsHtml: Found session ID in details HTML: 18070
15:17:58.436 reports.details-parser.js:35 [DEBUG] parseReportDetailsHtml: Pagination: Page 1 of 1
15:17:58.436 reports.details-parser.js:43 [DEBUG] parseReportDetailsHtml: Found 3 rows in the details table (table#table tbody tr)
15:17:58.436 reports.details-parser.js:103 [DEBUG] parseReportDetailsHtml: Extracted 1 unique test cases from 3 detail rows.
15:17:58.436 reports.details-external.js:291 📊 Parsed details data: {tsn_id: '18070', test_cases: Array(1), report_html: '\n\n\n\n\n<html>\n<head>\n<title>Test Result Details</tit…<EMAIL></a><p/>\n\n</body>\n</html>\n', pagination: {…}, originalParameters: {…}}
15:17:58.436 reports.details-external.js:319 Processing 1 test cases from detailsData (which includes steps).
15:17:58.436 reports.details-external.js:326 Merging TC ID 3180: Summary status='Success', Detail steps count=3
15:17:58.436 reports.details-external.js:349 Final merged test_cases count: 1
15:17:58.436 reports.details-external.js:352   TC ID 3180, Status: Success, Steps: 3, HasFailures: false, Name: 3180
15:17:58.436 reports.details-external.js:371 [DEBUG] loadTestDetailsFromExternalApi: Assembled testDetails.originalParameters: {
  "uid": "<EMAIL>=qa02shell_host=jps-qa10-app01file_path=/home/<USER>/operatorConfigs=operatorNameConfigskafka_server=kafka-qa-a0.lab.wagerworks.comdataCenter=GUrgs_env=qa02old_version=0networkType1=multi-sitenetworkType2=multi-sitesign=-rate_src=local",
  "envir": "qa02",
  "shell_host": "jps-qa10-app01",
  "file_path": "/home/<USER>/",
  "operatorConfigs": "operatorNameConfigs",
  "kafka_server": "kafka-qa-a0.lab.wagerworks.com",
  "dataCenter": "GU",
  "rgs_env": "qa02",
  "old_version": "0",
  "networkType1": "multi-site",
  "networkType2": "multi-site",
  "sign": "-",
  "rate_src": "local"
}
15:17:58.436 reports.details-external.js:373 Test details loaded from external API: {id: '18070', tsn_id: '18070', test_id: '', test_name: 'Unknown Test', name: 'Unknown Test', …}
15:17:58.436 test-details-modal.js:210 Successfully loaded from external API: {id: '18070', tsn_id: '18070', test_id: '', test_name: 'Unknown Test', name: 'Unknown Test', …}
15:17:58.484 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":50,"type":"single_case"}
15:17:58.485 simple-optimizations.js:26 Cache HIT: recentRuns_{"limit":100,"type":"single_case"}
15:17:58.485 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:17:58.485 config.js:484 Received 0 active tests update
15:17:58.485 config.js:538 Updating active tests display with 0 tests
15:17:58.485 config.js:683 🔄 renderActiveTests called, active tests count: 1
15:17:58.485 config.js:723 📋 Found 2 existing cards in DOM
15:17:58.485 config.js:729 ⚠️ Found duplicate card for test 18070, removing it
(anonymous) @ config.js:729
renderActiveTests @ config.js:725
updateActiveTestsDisplay @ config.js:576
(anonymous) @ config.js:485
(anonymous) @ simple-optimizations.js:158
notifySubscribers @ simple-optimizations.js:156
poll @ simple-optimizations.js:147
await in poll
(anonymous) @ simple-optimizations.js:136
setInterval
startPolling @ simple-optimizations.js:135
setupConfigPolling @ simple-optimizations.js:262
setTimeout
(anonymous) @ simple-optimizations.js:307
(anonymous) @ simple-optimizations.js:309
15:17:58.485 config.js:736 📋 After cleanup: 1 unique cards
15:17:58.485 config.js:743 Processing active test: 18070
15:17:58.485 config.js:748 Replacing existing card for test 18070
15:17:58.485 config.js:772 ✅ renderActiveTests completed
15:18:00.485 simple-optimizations.js:26 Cache HIT: recentRuns_{"limit":100,"type":"single_case"}
15:18:00.486 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:18:00.486 config.js:484 Received 0 active tests update
15:18:00.486 config.js:538 Updating active tests display with 0 tests
15:18:00.486 config.js:683 🔄 renderActiveTests called, active tests count: 1
15:18:00.486 config.js:723 📋 Found 2 existing cards in DOM
15:18:00.486 config.js:729 ⚠️ Found duplicate card for test 18070, removing it
(anonymous) @ config.js:729
renderActiveTests @ config.js:725
updateActiveTestsDisplay @ config.js:576
(anonymous) @ config.js:485
(anonymous) @ simple-optimizations.js:158
notifySubscribers @ simple-optimizations.js:156
poll @ simple-optimizations.js:147
await in poll
(anonymous) @ simple-optimizations.js:136
setInterval
startPolling @ simple-optimizations.js:135
setupConfigPolling @ simple-optimizations.js:262
setTimeout
(anonymous) @ simple-optimizations.js:307
(anonymous) @ simple-optimizations.js:309
15:18:00.486 config.js:736 📋 After cleanup: 1 unique cards
15:18:00.487 config.js:743 Processing active test: 18070
15:18:00.487 config.js:748 Replacing existing card for test 18070
15:18:00.487 config.js:772 ✅ renderActiveTests completed
15:18:02.486 unified-api-service.js:316 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
15:18:02.487 unified-api-service.js:321 Making GET request to: http://localhost:3000/local/recent-runs
15:18:03.636 simple-optimizations.js:47 Request completed: recentRuns_{"limit":50,"type":"single_case"}
15:18:03.636 config.js:476 Received 50 recent runs update
15:18:03.636 config.js:585 processRecentRunsData called with 50 runs
15:18:03.636 config.js:611 Processing active test 18070, found in recent runs: true
15:18:03.636 config.js:627 Checking completion for TSN 18070: status="passed", hasEndTime=true, isCompleted=true
15:18:03.636 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:18:03.636 config.js:868 renderRecentRuns called with data: (50) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
15:18:03.637 config.js:869 recentRunsBody element: <tbody id=​"recent-runs-body">​…​</tbody>​
15:18:03.638 config.js:887 Rendering 50 recent runs
15:18:03.641 config.js:476 Received 50 recent runs update
15:18:03.641 config.js:585 processRecentRunsData called with 50 runs
15:18:03.641 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:18:03.641 config.js:868 renderRecentRuns called with data: (50) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
15:18:03.641 config.js:869 recentRunsBody element: <tbody id=​"recent-runs-body">​…​</tbody>​
15:18:03.642 config.js:887 Rendering 50 recent runs
15:18:04.484 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
15:18:06.486 simple-optimizations.js:26 Cache HIT: recentRuns_{"limit":50,"type":"single_case"}
15:18:06.486 config.js:476 Received 50 recent runs update
15:18:06.486 config.js:585 processRecentRunsData called with 50 runs
15:18:06.486 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:18:06.487 config.js:868 renderRecentRuns called with data: (50) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
15:18:06.487 config.js:869 recentRunsBody element: <tbody id=​"recent-runs-body">​…​</tbody>​
15:18:06.489 config.js:887 Rendering 50 recent runs
15:18:06.501 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
15:18:08.482 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
15:18:10.491 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
15:18:12.476 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
15:18:14.486 unified-api-service.js:316 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
15:18:14.486 unified-api-service.js:321 Making GET request to: http://localhost:3000/local/recent-runs
15:18:14.487 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
15:18:16.486 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
15:18:18.025 simple-optimizations.js:47 Request completed: recentRuns_{"limit":100,"type":"single_case"}
15:18:18.026 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:18:18.026 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:18:18.026 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:18:18.026 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:18:18.026 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:18:18.026 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:18:18.026 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:18:18.026 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:18:18.026 config.js:484 Received 0 active tests update
15:18:18.026 config.js:538 Updating active tests display with 0 tests
15:18:18.026 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:18:18.026 config.js:484 Received 0 active tests update
15:18:18.026 config.js:538 Updating active tests display with 0 tests
15:18:18.026 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:18:18.027 config.js:484 Received 0 active tests update
15:18:18.027 config.js:538 Updating active tests display with 0 tests
15:18:18.027 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:18:18.027 config.js:484 Received 0 active tests update
15:18:18.027 config.js:538 Updating active tests display with 0 tests
15:18:18.027 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:18:18.027 config.js:484 Received 0 active tests update
15:18:18.027 config.js:538 Updating active tests display with 0 tests
15:18:18.027 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:18:18.027 config.js:484 Received 0 active tests update
15:18:18.027 config.js:538 Updating active tests display with 0 tests
15:18:18.027 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:18:18.027 config.js:484 Received 0 active tests update
15:18:18.027 config.js:538 Updating active tests display with 0 tests
15:18:18.027 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:18:18.027 config.js:484 Received 0 active tests update
15:18:18.027 config.js:538 Updating active tests display with 0 tests
15:18:18.028 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:18:18.485 simple-optimizations.js:26 Cache HIT: recentRuns_{"limit":100,"type":"single_case"}
15:18:18.485 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:18:18.485 config.js:484 Received 0 active tests update
15:18:18.485 config.js:538 Updating active tests display with 0 tests
15:18:18.485 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:18:20.487 simple-optimizations.js:26 Cache HIT: recentRuns_{"limit":100,"type":"single_case"}
15:18:20.487 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:18:20.487 config.js:484 Received 0 active tests update
15:18:20.488 config.js:538 Updating active tests display with 0 tests
15:18:20.488 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:18:22.484 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":50,"type":"single_case"}
15:18:22.485 unified-api-service.js:316 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
15:18:22.485 unified-api-service.js:321 Making GET request to: http://localhost:3000/local/recent-runs
15:18:24.485 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
15:18:26.485 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
15:18:27.785 simple-optimizations.js:47 Request completed: recentRuns_{"limit":50,"type":"single_case"}
15:18:27.785 config.js:476 Received 50 recent runs update
15:18:27.785 config.js:585 processRecentRunsData called with 50 runs
15:18:27.785 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:18:27.785 config.js:868 renderRecentRuns called with data: (50) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
15:18:27.785 config.js:869 recentRunsBody element: <tbody id=​"recent-runs-body">​…​</tbody>​
15:18:27.787 config.js:887 Rendering 50 recent runs
15:18:27.789 config.js:476 Received 50 recent runs update
15:18:27.789 config.js:585 processRecentRunsData called with 50 runs
15:18:27.789 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:18:27.790 config.js:868 renderRecentRuns called with data: (50) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
15:18:27.790 config.js:869 recentRunsBody element: <tbody id=​"recent-runs-body">​…​</tbody>​
15:18:27.790 config.js:887 Rendering 50 recent runs
15:18:28.484 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
15:18:30.475 simple-optimizations.js:26 Cache HIT: recentRuns_{"limit":50,"type":"single_case"}
15:18:30.476 config.js:476 Received 50 recent runs update
15:18:30.476 config.js:585 processRecentRunsData called with 50 runs
15:18:30.476 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:18:30.477 config.js:868 renderRecentRuns called with data: (50) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
15:18:30.478 config.js:869 recentRunsBody element: <tbody id=​"recent-runs-body">​…​</tbody>​
15:18:30.482 config.js:887 Rendering 50 recent runs
15:18:30.502 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
15:18:32.485 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
15:18:34.475 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
15:18:36.485 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
15:18:38.028 simple-optimizations.js:47 Request completed: recentRuns_{"limit":100,"type":"single_case"}
15:18:38.028 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:18:38.028 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:18:38.028 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:18:38.028 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:18:38.029 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:18:38.029 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:18:38.029 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:18:38.029 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:18:38.029 config.js:484 Received 0 active tests update
15:18:38.029 config.js:538 Updating active tests display with 0 tests
15:18:38.029 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:18:38.029 config.js:484 Received 0 active tests update
15:18:38.029 config.js:538 Updating active tests display with 0 tests
15:18:38.029 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:18:38.029 config.js:484 Received 0 active tests update
15:18:38.029 config.js:538 Updating active tests display with 0 tests
15:18:38.029 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:18:38.029 config.js:484 Received 0 active tests update
15:18:38.029 config.js:538 Updating active tests display with 0 tests
15:18:38.029 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:18:38.030 config.js:484 Received 0 active tests update
15:18:38.030 config.js:538 Updating active tests display with 0 tests
15:18:38.030 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:18:38.030 config.js:484 Received 0 active tests update
15:18:38.030 config.js:538 Updating active tests display with 0 tests
15:18:38.030 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:18:38.030 config.js:484 Received 0 active tests update
15:18:38.030 config.js:538 Updating active tests display with 0 tests
15:18:38.030 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:18:38.030 config.js:484 Received 0 active tests update
15:18:38.030 config.js:538 Updating active tests display with 0 tests
15:18:38.030 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:18:38.485 unified-api-service.js:316 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
15:18:38.485 unified-api-service.js:321 Making GET request to: http://localhost:3000/local/recent-runs
15:18:38.486 simple-optimizations.js:26 Cache HIT: recentRuns_{"limit":100,"type":"single_case"}
15:18:38.486 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:18:38.486 config.js:484 Received 0 active tests update
15:18:38.486 config.js:538 Updating active tests display with 0 tests
15:18:38.486 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:18:40.485 simple-optimizations.js:26 Cache HIT: recentRuns_{"limit":100,"type":"single_case"}
15:18:40.485 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:18:40.485 config.js:484 Received 0 active tests update
15:18:40.485 config.js:538 Updating active tests display with 0 tests
15:18:40.485 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:18:42.483 unified-api-service.js:316 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
15:18:42.484 unified-api-service.js:321 Making GET request to: http://localhost:3000/local/recent-runs
15:18:44.484 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
15:18:46.480 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":50,"type":"single_case"}
15:18:46.480 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
15:18:48.482 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
15:18:50.477 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
15:18:52.285 simple-optimizations.js:47 Request completed: recentRuns_{"limit":50,"type":"single_case"}
15:18:52.285 config.js:476 Received 50 recent runs update
15:18:52.285 config.js:585 processRecentRunsData called with 50 runs
15:18:52.285 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:18:52.285 config.js:868 renderRecentRuns called with data: (50) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
15:18:52.285 config.js:869 recentRunsBody element: <tbody id=​"recent-runs-body">​…​</tbody>​
15:18:52.288 config.js:887 Rendering 50 recent runs
15:18:52.294 config.js:476 Received 50 recent runs update
15:18:52.294 config.js:585 processRecentRunsData called with 50 runs
15:18:52.294 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:18:52.295 config.js:868 renderRecentRuns called with data: (50) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
15:18:52.295 config.js:869 recentRunsBody element: <tbody id=​"recent-runs-body">​…​</tbody>​
15:18:52.295 config.js:887 Rendering 50 recent runs
15:18:52.477 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
15:18:54.480 simple-optimizations.js:26 Cache HIT: recentRuns_{"limit":50,"type":"single_case"}
15:18:54.481 config.js:476 Received 50 recent runs update
15:18:54.481 config.js:585 processRecentRunsData called with 50 runs
15:18:54.481 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:18:54.481 config.js:868 renderRecentRuns called with data: (50) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
15:18:54.483 config.js:869 recentRunsBody element: <tbody id=​"recent-runs-body">​…​</tbody>​
15:18:54.487 config.js:887 Rendering 50 recent runs
15:18:54.509 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
15:18:56.486 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
15:18:58.484 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
15:18:58.877 simple-optimizations.js:47 Request completed: recentRuns_{"limit":100,"type":"single_case"}
15:18:58.878 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:18:58.878 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:18:58.878 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:18:58.878 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:18:58.878 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:18:58.878 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:18:58.878 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:18:58.878 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:18:58.878 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:18:58.878 config.js:484 Received 0 active tests update
15:18:58.878 config.js:538 Updating active tests display with 0 tests
15:18:58.879 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:18:58.879 config.js:484 Received 0 active tests update
15:18:58.879 config.js:538 Updating active tests display with 0 tests
15:18:58.879 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:18:58.879 config.js:484 Received 0 active tests update
15:18:58.880 config.js:538 Updating active tests display with 0 tests
15:18:58.880 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:18:58.880 config.js:484 Received 0 active tests update
15:18:58.880 config.js:538 Updating active tests display with 0 tests
15:18:58.880 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:18:58.880 config.js:484 Received 0 active tests update
15:18:58.881 config.js:538 Updating active tests display with 0 tests
15:18:58.881 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:18:58.881 config.js:484 Received 0 active tests update
15:18:58.881 config.js:538 Updating active tests display with 0 tests
15:18:58.881 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:18:58.881 config.js:484 Received 0 active tests update
15:18:58.881 config.js:538 Updating active tests display with 0 tests
15:18:58.881 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:18:58.882 config.js:484 Received 0 active tests update
15:18:58.882 config.js:538 Updating active tests display with 0 tests
15:18:58.882 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:18:58.882 config.js:484 Received 0 active tests update
15:18:58.882 config.js:538 Updating active tests display with 0 tests
15:18:58.882 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:19:00.476 simple-optimizations.js:26 Cache HIT: recentRuns_{"limit":100,"type":"single_case"}
15:19:00.476 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:19:00.477 config.js:484 Received 0 active tests update
15:19:00.477 config.js:538 Updating active tests display with 0 tests
15:19:00.477 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:19:02.487 unified-api-service.js:316 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
15:19:02.487 unified-api-service.js:321 Making GET request to: http://localhost:3000/local/recent-runs
15:19:02.488 unified-api-service.js:316 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
15:19:02.488 unified-api-service.js:321 Making GET request to: http://localhost:3000/local/recent-runs
15:19:04.485 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
15:19:06.481 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
15:19:08.475 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
15:19:10.481 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":50,"type":"single_case"}
15:19:10.482 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
15:19:12.476 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
15:19:14.483 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
15:19:16.476 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
15:19:17.689 simple-optimizations.js:47 Request completed: recentRuns_{"limit":50,"type":"single_case"}
15:19:17.689 config.js:476 Received 50 recent runs update
15:19:17.689 config.js:585 processRecentRunsData called with 50 runs
15:19:17.690 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:19:17.690 config.js:868 renderRecentRuns called with data: (50) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
15:19:17.690 config.js:869 recentRunsBody element: <tbody id=​"recent-runs-body">​…​</tbody>​
15:19:17.694 config.js:887 Rendering 50 recent runs
15:19:17.700 config.js:476 Received 50 recent runs update
15:19:17.700 config.js:585 processRecentRunsData called with 50 runs
15:19:17.701 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:19:17.701 config.js:868 renderRecentRuns called with data: (50) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
15:19:17.701 config.js:869 recentRunsBody element: <tbody id=​"recent-runs-body">​…​</tbody>​
15:19:17.702 config.js:887 Rendering 50 recent runs
15:19:18.485 simple-optimizations.js:26 Cache HIT: recentRuns_{"limit":50,"type":"single_case"}
15:19:18.485 config.js:476 Received 50 recent runs update
15:19:18.485 config.js:585 processRecentRunsData called with 50 runs
15:19:18.485 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:19:18.485 config.js:868 renderRecentRuns called with data: (50) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
15:19:18.486 config.js:869 recentRunsBody element: <tbody id=​"recent-runs-body">​…​</tbody>​
15:19:18.487 config.js:887 Rendering 50 recent runs
15:19:18.499 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
15:19:19.972 simple-optimizations.js:47 Request completed: recentRuns_{"limit":100,"type":"single_case"}
15:19:19.972 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:19:19.972 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:19:19.972 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:19:19.972 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:19:19.972 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:19:19.972 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:19:19.972 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:19:19.972 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:19:19.972 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:19:19.972 config.js:484 Received 0 active tests update
15:19:19.972 config.js:538 Updating active tests display with 0 tests
15:19:19.972 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:19:19.972 config.js:484 Received 0 active tests update
15:19:19.972 config.js:538 Updating active tests display with 0 tests
15:19:19.972 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:19:19.972 config.js:484 Received 0 active tests update
15:19:19.972 config.js:538 Updating active tests display with 0 tests
15:19:19.972 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:19:19.972 config.js:484 Received 0 active tests update
15:19:19.973 config.js:538 Updating active tests display with 0 tests
15:19:19.973 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:19:19.973 config.js:484 Received 0 active tests update
15:19:19.973 config.js:538 Updating active tests display with 0 tests
15:19:19.973 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:19:19.973 config.js:484 Received 0 active tests update
15:19:19.973 config.js:538 Updating active tests display with 0 tests
15:19:19.973 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:19:19.973 config.js:484 Received 0 active tests update
15:19:19.973 config.js:538 Updating active tests display with 0 tests
15:19:19.973 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:19:19.973 config.js:484 Received 0 active tests update
15:19:19.973 config.js:538 Updating active tests display with 0 tests
15:19:19.973 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:19:19.973 config.js:484 Received 0 active tests update
15:19:19.973 config.js:538 Updating active tests display with 0 tests
15:19:19.973 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:19:20.484 simple-optimizations.js:26 Cache HIT: recentRuns_{"limit":100,"type":"single_case"}
15:19:20.484 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:19:20.484 config.js:484 Received 0 active tests update
15:19:20.484 config.js:538 Updating active tests display with 0 tests
15:19:20.484 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:19:22.484 simple-optimizations.js:26 Cache HIT: recentRuns_{"limit":100,"type":"single_case"}
15:19:22.485 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:19:22.485 config.js:484 Received 0 active tests update
15:19:22.485 config.js:538 Updating active tests display with 0 tests
15:19:22.485 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:19:24.482 unified-api-service.js:316 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
15:19:24.483 unified-api-service.js:321 Making GET request to: http://localhost:3000/local/recent-runs
15:19:26.482 unified-api-service.js:316 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
15:19:26.483 unified-api-service.js:321 Making GET request to: http://localhost:3000/local/recent-runs
15:19:26.484 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
15:19:28.482 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
15:19:30.478 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
15:19:32.483 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
15:19:34.482 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":50,"type":"single_case"}
15:19:34.482 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
15:19:36.475 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
15:19:38.485 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
15:19:40.484 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
15:19:41.683 simple-optimizations.js:47 Request completed: recentRuns_{"limit":50,"type":"single_case"}
15:19:41.683 config.js:476 Received 50 recent runs update
15:19:41.683 config.js:585 processRecentRunsData called with 50 runs
15:19:41.683 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:19:41.683 config.js:868 renderRecentRuns called with data: (50) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
15:19:41.684 config.js:869 recentRunsBody element: <tbody id=​"recent-runs-body">​…​</tbody>​
15:19:41.685 config.js:887 Rendering 50 recent runs
15:19:41.688 config.js:476 Received 50 recent runs update
15:19:41.688 config.js:585 processRecentRunsData called with 50 runs
15:19:41.688 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:19:41.688 config.js:868 renderRecentRuns called with data: (50) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
15:19:41.688 config.js:869 recentRunsBody element: <tbody id=​"recent-runs-body">​…​</tbody>​
15:19:41.689 config.js:887 Rendering 50 recent runs
15:19:41.758 simple-optimizations.js:47 Request completed: recentRuns_{"limit":100,"type":"single_case"}
15:19:41.758 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:19:41.758 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:19:41.758 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:19:41.758 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:19:41.758 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:19:41.758 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:19:41.758 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:19:41.758 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:19:41.758 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:19:41.758 config.js:484 Received 0 active tests update
15:19:41.758 config.js:538 Updating active tests display with 0 tests
15:19:41.759 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:19:41.759 config.js:484 Received 0 active tests update
15:19:41.759 config.js:538 Updating active tests display with 0 tests
15:19:41.759 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:19:41.759 config.js:484 Received 0 active tests update
15:19:41.759 config.js:538 Updating active tests display with 0 tests
15:19:41.759 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:19:41.759 config.js:484 Received 0 active tests update
15:19:41.759 config.js:538 Updating active tests display with 0 tests
15:19:41.759 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:19:41.759 config.js:484 Received 0 active tests update
15:19:41.759 config.js:538 Updating active tests display with 0 tests
15:19:41.759 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:19:41.759 config.js:484 Received 0 active tests update
15:19:41.759 config.js:538 Updating active tests display with 0 tests
15:19:41.759 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:19:41.759 config.js:484 Received 0 active tests update
15:19:41.759 config.js:538 Updating active tests display with 0 tests
15:19:41.759 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:19:41.759 config.js:484 Received 0 active tests update
15:19:41.759 config.js:538 Updating active tests display with 0 tests
15:19:41.759 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:19:41.759 config.js:484 Received 0 active tests update
15:19:41.759 config.js:538 Updating active tests display with 0 tests
15:19:41.759 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:19:42.475 simple-optimizations.js:26 Cache HIT: recentRuns_{"limit":50,"type":"single_case"}
15:19:42.476 config.js:476 Received 50 recent runs update
15:19:42.476 config.js:585 processRecentRunsData called with 50 runs
15:19:42.476 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:19:42.477 config.js:868 renderRecentRuns called with data: (50) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
15:19:42.478 config.js:869 recentRunsBody element: <tbody id=​"recent-runs-body">​…​</tbody>​
15:19:42.483 config.js:887 Rendering 50 recent runs
15:19:42.503 simple-optimizations.js:26 Cache HIT: recentRuns_{"limit":100,"type":"single_case"}
15:19:42.503 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:19:42.504 config.js:484 Received 0 active tests update
15:19:42.504 config.js:538 Updating active tests display with 0 tests
15:19:42.504 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:19:44.481 simple-optimizations.js:26 Cache HIT: recentRuns_{"limit":100,"type":"single_case"}
15:19:44.482 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:19:44.482 config.js:484 Received 0 active tests update
15:19:44.482 config.js:538 Updating active tests display with 0 tests
15:19:44.482 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:19:46.480 unified-api-service.js:316 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
15:19:46.480 unified-api-service.js:321 Making GET request to: http://localhost:3000/local/recent-runs
15:19:48.485 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
15:19:50.485 unified-api-service.js:316 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
15:19:50.485 unified-api-service.js:321 Making GET request to: http://localhost:3000/local/recent-runs
15:19:50.485 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
15:19:52.477 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
15:19:54.479 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
15:19:56.475 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
15:19:58.485 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":50,"type":"single_case"}
15:19:58.485 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
15:20:00.485 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
15:20:02.477 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
15:20:02.721 simple-optimizations.js:47 Request completed: recentRuns_{"limit":100,"type":"single_case"}
15:20:02.721 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:20:02.721 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:20:02.721 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:20:02.721 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:20:02.721 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:20:02.721 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:20:02.721 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:20:02.721 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:20:02.721 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:20:02.721 config.js:484 Received 0 active tests update
15:20:02.721 config.js:538 Updating active tests display with 0 tests
15:20:02.721 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:20:02.722 config.js:484 Received 0 active tests update
15:20:02.722 config.js:538 Updating active tests display with 0 tests
15:20:02.722 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:20:02.722 config.js:484 Received 0 active tests update
15:20:02.722 config.js:538 Updating active tests display with 0 tests
15:20:02.722 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:20:02.722 config.js:484 Received 0 active tests update
15:20:02.722 config.js:538 Updating active tests display with 0 tests
15:20:02.722 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:20:02.722 config.js:484 Received 0 active tests update
15:20:02.722 config.js:538 Updating active tests display with 0 tests
15:20:02.722 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:20:02.722 config.js:484 Received 0 active tests update
15:20:02.722 config.js:538 Updating active tests display with 0 tests
15:20:02.722 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:20:02.722 config.js:484 Received 0 active tests update
15:20:02.722 config.js:538 Updating active tests display with 0 tests
15:20:02.722 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:20:02.722 config.js:484 Received 0 active tests update
15:20:02.722 config.js:538 Updating active tests display with 0 tests
15:20:02.722 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:20:02.722 config.js:484 Received 0 active tests update
15:20:02.722 config.js:538 Updating active tests display with 0 tests
15:20:02.722 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:20:04.482 simple-optimizations.js:26 Cache HIT: recentRuns_{"limit":100,"type":"single_case"}
15:20:04.482 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:20:04.482 config.js:484 Received 0 active tests update
15:20:04.482 config.js:538 Updating active tests display with 0 tests
15:20:04.482 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:20:04.957 simple-optimizations.js:47 Request completed: recentRuns_{"limit":50,"type":"single_case"}
15:20:04.957 config.js:476 Received 50 recent runs update
15:20:04.957 config.js:585 processRecentRunsData called with 50 runs
15:20:04.957 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:20:04.957 config.js:868 renderRecentRuns called with data: (50) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
15:20:04.957 config.js:869 recentRunsBody element: <tbody id=​"recent-runs-body">​…​</tbody>​
15:20:04.958 config.js:887 Rendering 50 recent runs
15:20:04.961 config.js:476 Received 50 recent runs update
15:20:04.961 config.js:585 processRecentRunsData called with 50 runs
15:20:04.961 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:20:04.961 config.js:868 renderRecentRuns called with data: (50) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
15:20:04.961 config.js:869 recentRunsBody element: <tbody id=​"recent-runs-body">​…​</tbody>​
15:20:04.961 config.js:887 Rendering 50 recent runs
15:20:06.482 simple-optimizations.js:26 Cache HIT: recentRuns_{"limit":50,"type":"single_case"}
15:20:06.483 config.js:476 Received 50 recent runs update
15:20:06.483 config.js:585 processRecentRunsData called with 50 runs
15:20:06.483 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:20:06.483 config.js:868 renderRecentRuns called with data: (50) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
15:20:06.483 config.js:869 recentRunsBody element: <tbody id=​"recent-runs-body">​…​</tbody>​
15:20:06.486 config.js:887 Rendering 50 recent runs
15:20:06.490 unified-api-service.js:316 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
15:20:06.490 unified-api-service.js:321 Making GET request to: http://localhost:3000/local/recent-runs
15:20:08.483 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
15:20:10.489 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
15:20:12.478 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
15:20:14.486 unified-api-service.js:316 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
15:20:14.486 unified-api-service.js:321 Making GET request to: http://localhost:3000/local/recent-runs
15:20:14.487 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
15:20:16.478 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
15:20:18.482 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
15:20:20.485 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
15:20:22.476 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":50,"type":"single_case"}
15:20:22.476 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
15:20:22.715 simple-optimizations.js:47 Request completed: recentRuns_{"limit":100,"type":"single_case"}
15:20:22.715 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:20:22.715 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:20:22.715 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:20:22.715 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:20:22.715 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:20:22.715 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:20:22.715 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:20:22.715 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:20:22.716 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:20:22.716 config.js:484 Received 0 active tests update
15:20:22.716 config.js:538 Updating active tests display with 0 tests
15:20:22.716 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:20:22.716 config.js:484 Received 0 active tests update
15:20:22.717 config.js:538 Updating active tests display with 0 tests
15:20:22.717 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:20:22.717 config.js:484 Received 0 active tests update
15:20:22.717 config.js:538 Updating active tests display with 0 tests
15:20:22.717 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:20:22.717 config.js:484 Received 0 active tests update
15:20:22.718 config.js:538 Updating active tests display with 0 tests
15:20:22.718 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:20:22.718 config.js:484 Received 0 active tests update
15:20:22.718 config.js:538 Updating active tests display with 0 tests
15:20:22.718 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:20:22.719 config.js:484 Received 0 active tests update
15:20:22.719 config.js:538 Updating active tests display with 0 tests
15:20:22.719 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:20:22.719 config.js:484 Received 0 active tests update
15:20:22.719 config.js:538 Updating active tests display with 0 tests
15:20:22.719 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:20:22.720 config.js:484 Received 0 active tests update
15:20:22.720 config.js:538 Updating active tests display with 0 tests
15:20:22.720 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:20:22.720 config.js:484 Received 0 active tests update
15:20:22.720 config.js:538 Updating active tests display with 0 tests
15:20:22.720 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:20:24.489 simple-optimizations.js:26 Cache HIT: recentRuns_{"limit":100,"type":"single_case"}
15:20:24.489 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:20:24.489 config.js:484 Received 0 active tests update
15:20:24.489 config.js:538 Updating active tests display with 0 tests
15:20:24.489 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:20:26.475 unified-api-service.js:316 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
15:20:26.475 unified-api-service.js:321 Making GET request to: http://localhost:3000/local/recent-runs
15:20:27.849 simple-optimizations.js:47 Request completed: recentRuns_{"limit":50,"type":"single_case"}
15:20:27.849 config.js:476 Received 50 recent runs update
15:20:27.850 config.js:585 processRecentRunsData called with 50 runs
15:20:27.850 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:20:27.850 config.js:868 renderRecentRuns called with data: (50) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
15:20:27.850 config.js:869 recentRunsBody element: <tbody id=​"recent-runs-body">​…​</tbody>​
15:20:27.852 config.js:887 Rendering 50 recent runs
15:20:27.854 config.js:476 Received 50 recent runs update
15:20:27.854 config.js:585 processRecentRunsData called with 50 runs
15:20:27.855 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:20:27.855 config.js:868 renderRecentRuns called with data: (50) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
15:20:27.855 config.js:869 recentRunsBody element: <tbody id=​"recent-runs-body">​…​</tbody>​
15:20:27.856 config.js:887 Rendering 50 recent runs
15:20:28.485 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
15:20:30.486 simple-optimizations.js:26 Cache HIT: recentRuns_{"limit":50,"type":"single_case"}
15:20:30.486 config.js:476 Received 50 recent runs update
15:20:30.486 config.js:585 processRecentRunsData called with 50 runs
15:20:30.486 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:20:30.486 config.js:868 renderRecentRuns called with data: (50) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
15:20:30.487 config.js:869 recentRunsBody element: <tbody id=​"recent-runs-body">​…​</tbody>​
15:20:30.489 config.js:887 Rendering 50 recent runs
15:20:30.499 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
15:20:32.475 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
15:20:34.486 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
15:20:36.480 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
15:20:38.480 unified-api-service.js:316 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
15:20:38.481 unified-api-service.js:321 Making GET request to: http://localhost:3000/local/recent-runs
15:20:38.481 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
15:20:40.485 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
15:20:42.046 simple-optimizations.js:47 Request completed: recentRuns_{"limit":100,"type":"single_case"}
15:20:42.046 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:20:42.046 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:20:42.046 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:20:42.046 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:20:42.046 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:20:42.046 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:20:42.046 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:20:42.046 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:20:42.046 config.js:484 Received 0 active tests update
15:20:42.046 config.js:538 Updating active tests display with 0 tests
15:20:42.046 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:20:42.046 config.js:484 Received 0 active tests update
15:20:42.046 config.js:538 Updating active tests display with 0 tests
15:20:42.046 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:20:42.046 config.js:484 Received 0 active tests update
15:20:42.046 config.js:538 Updating active tests display with 0 tests
15:20:42.046 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:20:42.047 config.js:484 Received 0 active tests update
15:20:42.047 config.js:538 Updating active tests display with 0 tests
15:20:42.047 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:20:42.047 config.js:484 Received 0 active tests update
15:20:42.047 config.js:538 Updating active tests display with 0 tests
15:20:42.047 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:20:42.047 config.js:484 Received 0 active tests update
15:20:42.047 config.js:538 Updating active tests display with 0 tests
15:20:42.047 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:20:42.047 config.js:484 Received 0 active tests update
15:20:42.047 config.js:538 Updating active tests display with 0 tests
15:20:42.047 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:20:42.047 config.js:484 Received 0 active tests update
15:20:42.047 config.js:538 Updating active tests display with 0 tests
15:20:42.047 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:20:42.485 simple-optimizations.js:26 Cache HIT: recentRuns_{"limit":100,"type":"single_case"}
15:20:42.485 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:20:42.486 config.js:484 Received 0 active tests update
15:20:42.486 config.js:538 Updating active tests display with 0 tests
15:20:42.486 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:20:44.485 simple-optimizations.js:26 Cache HIT: recentRuns_{"limit":100,"type":"single_case"}
15:20:44.485 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:20:44.485 config.js:484 Received 0 active tests update
15:20:44.485 config.js:538 Updating active tests display with 0 tests
15:20:44.485 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:20:46.489 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":50,"type":"single_case"}
15:20:46.489 unified-api-service.js:316 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
15:20:46.489 unified-api-service.js:321 Making GET request to: http://localhost:3000/local/recent-runs
15:20:48.485 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
15:20:50.479 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
15:20:51.857 simple-optimizations.js:47 Request completed: recentRuns_{"limit":50,"type":"single_case"}
15:20:51.857 config.js:476 Received 50 recent runs update
15:20:51.857 config.js:585 processRecentRunsData called with 50 runs
15:20:51.857 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:20:51.857 config.js:868 renderRecentRuns called with data: (50) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
15:20:51.858 config.js:869 recentRunsBody element: <tbody id=​"recent-runs-body">​…​</tbody>​
15:20:51.859 config.js:887 Rendering 50 recent runs
15:20:51.862 config.js:476 Received 50 recent runs update
15:20:51.862 config.js:585 processRecentRunsData called with 50 runs
15:20:51.862 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:20:51.863 config.js:868 renderRecentRuns called with data: (50) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
15:20:51.863 config.js:869 recentRunsBody element: <tbody id=​"recent-runs-body">​…​</tbody>​
15:20:51.863 config.js:887 Rendering 50 recent runs
15:20:52.485 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
15:20:54.483 simple-optimizations.js:26 Cache HIT: recentRuns_{"limit":50,"type":"single_case"}
15:20:54.483 config.js:476 Received 50 recent runs update
15:20:54.483 config.js:585 processRecentRunsData called with 50 runs
15:20:54.483 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:20:54.483 config.js:868 renderRecentRuns called with data: (50) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
15:20:54.484 config.js:869 recentRunsBody element: <tbody id=​"recent-runs-body">​…​</tbody>​
15:20:54.486 config.js:887 Rendering 50 recent runs
15:20:54.516 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
15:20:56.482 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
15:20:58.481 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
15:21:00.485 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
15:21:02.169 simple-optimizations.js:47 Request completed: recentRuns_{"limit":100,"type":"single_case"}
15:21:02.169 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:21:02.169 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:21:02.169 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:21:02.169 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:21:02.169 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:21:02.169 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:21:02.169 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:21:02.169 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:21:02.169 config.js:484 Received 0 active tests update
15:21:02.169 config.js:538 Updating active tests display with 0 tests
15:21:02.169 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:21:02.170 config.js:484 Received 0 active tests update
15:21:02.170 config.js:538 Updating active tests display with 0 tests
15:21:02.170 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:21:02.170 config.js:484 Received 0 active tests update
15:21:02.170 config.js:538 Updating active tests display with 0 tests
15:21:02.170 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:21:02.170 config.js:484 Received 0 active tests update
15:21:02.170 config.js:538 Updating active tests display with 0 tests
15:21:02.170 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:21:02.170 config.js:484 Received 0 active tests update
15:21:02.170 config.js:538 Updating active tests display with 0 tests
15:21:02.170 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:21:02.170 config.js:484 Received 0 active tests update
15:21:02.170 config.js:538 Updating active tests display with 0 tests
15:21:02.170 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:21:02.170 config.js:484 Received 0 active tests update
15:21:02.170 config.js:538 Updating active tests display with 0 tests
15:21:02.170 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:21:02.170 config.js:484 Received 0 active tests update
15:21:02.170 config.js:538 Updating active tests display with 0 tests
15:21:02.170 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:21:02.481 unified-api-service.js:316 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
15:21:02.481 unified-api-service.js:321 Making GET request to: http://localhost:3000/local/recent-runs
15:21:02.482 simple-optimizations.js:26 Cache HIT: recentRuns_{"limit":100,"type":"single_case"}
15:21:02.482 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:21:02.482 config.js:484 Received 0 active tests update
15:21:02.482 config.js:538 Updating active tests display with 0 tests
15:21:02.482 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:21:04.477 simple-optimizations.js:26 Cache HIT: recentRuns_{"limit":100,"type":"single_case"}
15:21:04.477 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:21:04.477 config.js:484 Received 0 active tests update
15:21:04.478 config.js:538 Updating active tests display with 0 tests
15:21:04.478 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:21:06.481 unified-api-service.js:316 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
15:21:06.481 unified-api-service.js:321 Making GET request to: http://localhost:3000/local/recent-runs
15:21:08.480 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
15:21:10.490 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":50,"type":"single_case"}
15:21:10.490 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
15:21:12.488 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
15:21:14.485 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
15:21:16.483 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
15:21:17.883 simple-optimizations.js:47 Request completed: recentRuns_{"limit":50,"type":"single_case"}
15:21:17.883 config.js:476 Received 50 recent runs update
15:21:17.883 config.js:585 processRecentRunsData called with 50 runs
15:21:17.883 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:21:17.883 config.js:868 renderRecentRuns called with data: (50) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
15:21:17.884 config.js:869 recentRunsBody element: <tbody id=​"recent-runs-body">​…​</tbody>​
15:21:17.885 config.js:887 Rendering 50 recent runs
15:21:17.888 config.js:476 Received 50 recent runs update
15:21:17.888 config.js:585 processRecentRunsData called with 50 runs
15:21:17.888 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:21:17.888 config.js:868 renderRecentRuns called with data: (50) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
15:21:17.888 config.js:869 recentRunsBody element: <tbody id=​"recent-runs-body">​…​</tbody>​
15:21:17.889 config.js:887 Rendering 50 recent runs
15:21:18.482 simple-optimizations.js:26 Cache HIT: recentRuns_{"limit":50,"type":"single_case"}
15:21:18.482 config.js:476 Received 50 recent runs update
15:21:18.482 config.js:585 processRecentRunsData called with 50 runs
15:21:18.482 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:21:18.482 config.js:868 renderRecentRuns called with data: (50) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
15:21:18.483 config.js:869 recentRunsBody element: <tbody id=​"recent-runs-body">​…​</tbody>​
15:21:18.485 config.js:887 Rendering 50 recent runs
15:21:18.500 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
15:21:20.481 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
15:21:22.480 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
15:21:23.767 simple-optimizations.js:47 Request completed: recentRuns_{"limit":100,"type":"single_case"}
15:21:23.767 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:21:23.767 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:21:23.767 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:21:23.767 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:21:23.768 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:21:23.768 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:21:23.768 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:21:23.768 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:21:23.768 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
15:21:23.768 config.js:484 Received 0 active tests update
15:21:23.768 config.js:538 Updating active tests display with 0 tests
15:21:23.768 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:21:23.769 config.js:484 Received 0 active tests update
15:21:23.769 config.js:538 Updating active tests display with 0 tests
15:21:23.769 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:21:23.769 config.js:484 Received 0 active tests update
15:21:23.769 config.js:538 Updating active tests display with 0 tests
15:21:23.769 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:21:23.769 config.js:484 Received 0 active tests update
15:21:23.769 config.js:538 Updating active tests display with 0 tests
15:21:23.769 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:21:23.769 config.js:484 Received 0 active tests update
15:21:23.770 config.js:538 Updating active tests display with 0 tests
15:21:23.770 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:21:23.770 config.js:484 Received 0 active tests update
15:21:23.770 config.js:538 Updating active tests display with 0 tests
15:21:23.770 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:21:23.770 config.js:484 Received 0 active tests update
15:21:23.770 config.js:538 Updating active tests display with 0 tests
15:21:23.770 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:21:23.770 config.js:484 Received 0 active tests update
15:21:23.770 config.js:538 Updating active tests display with 0 tests
15:21:23.770 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:21:23.770 config.js:484 Received 0 active tests update
15:21:23.770 config.js:538 Updating active tests display with 0 tests
15:21:23.770 config.js:683 🔄 renderActiveTests called, active tests count: 0
15:21:24.488 simple-optimizations.js:26 Cache HIT: recentRuns_{"limit":100,"type":"single_case"}
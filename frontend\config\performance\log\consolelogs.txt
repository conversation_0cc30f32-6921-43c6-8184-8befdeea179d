api-integration.js:307 No test suites to render
16:49:34.714 api-integration.js:244 [DashboardApiIntegration] loadDashboardData: window.initialRecentRuns not found, fetching from API.
16:49:34.714 api-integration.js:245 Fetching recent runs from API...
16:49:34.715 api-integration.js:246 [DashboardApiIntegration] loadDashboardData: Calling apiService.getRecentRuns().
16:49:34.715 unified-api-service.js:316 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
16:49:34.715 unified-api-service.js:321 Making GET request to: http://localhost:3000/local/recent-runs
16:49:38.555 api-integration.js:451 Starting poll request #44
16:49:38.556 api-integration.js:461 Using since_id=18014 for incremental polling (highestRecentTsnId=18015)
16:49:38.556 unified-api-service.js:316 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
16:49:38.556 unified-api-service.js:321 Making GET request to: http://localhost:3000/local/recent-runs
16:49:41.239 api-integration.js:248 [DashboardApiIntegration] loadDashboardData: Received recentRunsResponse from API: (20) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
16:49:41.239 api-integration.js:260 [DashboardApiIntegration] loadDashboardData: recentRunsCache populated from API response: (20) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
16:49:41.239 api-integration.js:1499 Updating dashboard counters from recent runs data
16:49:41.239 api-integration.js:1510 Found 2 runs for current user: <EMAIL>
16:49:41.239 api-integration.js:1530 Dashboard counters updated: Total=2, Passed=2, Failed=0, Running=0
16:49:41.239 api-integration.js:371 renderRecentTests called but is deprecated - Recent Test Executions UI has been removed
16:49:41.239 api-integration.js:2938 Smart merge: 0 raw tests merged to 0 unique tests
16:49:41.239 api-integration.js:1643 Deduplicated 0 raw tests to 0 processed tests
16:49:41.239 api-integration.js:1665 Found 0 active tests that will always be shown with Stop buttons
16:49:41.239 api-integration.js:2316 [getCurrentUser] Got user from apiService credentials: <EMAIL>
16:49:41.239 api-integration.js:1669 Current filter: undefined Current user: <EMAIL>
16:49:41.240 api-integration.js:1570 Applied 'undefined' filter: 0/0 tests included
16:49:41.240 api-integration.js:451 Starting poll request #1
16:49:41.240 api-integration.js:461 Using since_id=18014 for incremental polling (highestRecentTsnId=18015)
16:49:41.240 unified-api-service.js:316 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
16:49:41.240 unified-api-service.js:321 Making GET request to: http://localhost:3000/local/recent-runs
16:49:44.011 api-integration.js:2496 Active tests filter set to: mine
16:49:44.011 api-integration.js:2938 Smart merge: 0 raw tests merged to 0 unique tests
16:49:44.011 api-integration.js:1643 Deduplicated 0 raw tests to 0 processed tests
16:49:44.011 api-integration.js:1665 Found 0 active tests that will always be shown with Stop buttons
16:49:44.012 api-integration.js:2316 [getCurrentUser] Got user from apiService credentials: <EMAIL>
16:49:44.012 api-integration.js:1669 Current filter: mine Current user: <EMAIL>
16:49:44.012 api-integration.js:1570 Applied 'mine' filter: 0/0 tests included
16:49:44.012 api-integration.js:2439 [API Integration] Active tests filter set to: mine
16:49:44.013 api-integration.js:2446 [API Integration] Re-rendering active tests due to filter change.
16:49:44.013 api-integration.js:2938 Smart merge: 0 raw tests merged to 0 unique tests
16:49:44.013 api-integration.js:1643 Deduplicated 0 raw tests to 0 processed tests
16:49:44.013 api-integration.js:1665 Found 0 active tests that will always be shown with Stop buttons
16:49:44.013 api-integration.js:2316 [getCurrentUser] Got user from apiService credentials: <EMAIL>
16:49:44.013 api-integration.js:1669 Current filter: mine Current user: <EMAIL>
16:49:44.014 api-integration.js:1570 Applied 'mine' filter: 0/0 tests included
16:49:44.232 api-integration.js:466 Discarding stale response from request #44 as newer request #1 has been made
16:49:44.232 api-integration.js:558 Completed stale poll request #44, not releasing lock as newer request #1 is in progress
16:49:45.659 api-integration.js:2496 Active tests filter set to: all
16:49:45.660 api-integration.js:2938 Smart merge: 0 raw tests merged to 0 unique tests
16:49:45.660 api-integration.js:1643 Deduplicated 0 raw tests to 0 processed tests
16:49:45.660 api-integration.js:1665 Found 0 active tests that will always be shown with Stop buttons
16:49:45.660 api-integration.js:2316 [getCurrentUser] Got user from apiService credentials: <EMAIL>
16:49:45.660 api-integration.js:1669 Current filter: all Current user: <EMAIL>
16:49:45.660 api-integration.js:1545 Applying 'all' filter, returning 0 tests
16:49:45.660 api-integration.js:2439 [API Integration] Active tests filter set to: all
16:49:45.660 api-integration.js:2446 [API Integration] Re-rendering active tests due to filter change.
16:49:45.661 api-integration.js:2938 Smart merge: 0 raw tests merged to 0 unique tests
16:49:45.661 api-integration.js:1643 Deduplicated 0 raw tests to 0 processed tests
16:49:45.661 api-integration.js:1665 Found 0 active tests that will always be shown with Stop buttons
16:49:45.661 api-integration.js:2316 [getCurrentUser] Got user from apiService credentials: <EMAIL>
16:49:45.661 api-integration.js:1669 Current filter: all Current user: <EMAIL>
16:49:45.661 api-integration.js:1545 Applying 'all' filter, returning 0 tests
16:49:49.906 api-integration.js:677 Skipping test 18015 as it was previously marked as completed
16:49:49.906 api-integration.js:1499 Updating dashboard counters from recent runs data
16:49:49.906 api-integration.js:1510 Found 2 runs for current user: <EMAIL>
16:49:49.906 api-integration.js:1530 Dashboard counters updated: Total=2, Passed=2, Failed=0, Running=0
16:49:49.906 api-integration.js:2938 Smart merge: 0 raw tests merged to 0 unique tests
16:49:49.906 api-integration.js:1643 Deduplicated 0 raw tests to 0 processed tests
16:49:49.906 api-integration.js:1665 Found 0 active tests that will always be shown with Stop buttons
16:49:49.906 api-integration.js:2316 [getCurrentUser] Got user from apiService credentials: <EMAIL>
16:49:49.907 api-integration.js:1669 Current filter: all Current user: <EMAIL>
16:49:49.907 api-integration.js:1545 Applying 'all' filter, returning 0 tests
16:49:49.907 api-integration.js:555 Completed poll request #1, releasing lock
16:49:51.031 api-integration.js:2475 Run button clicked for suite: DEMO PE2.1 Sanity Test (312)
16:49:51.032 api-integration.js:2218 Loading: Running test suite 312...
16:49:51.034 api-service.js:109 Running test suite with ID: 312
16:49:51.034 unified-api-service.js:364 Making POST request to: http://localhost:3000/api/suite-runner
16:49:51.046 unified-api-service.js:403 Request parameters: {ts_id: '312'}
16:49:51.241 api-integration.js:451 Starting poll request #2
16:49:51.241 api-integration.js:461 Using since_id=18014 for incremental polling (highestRecentTsnId=18015)
16:49:51.241 unified-api-service.js:316 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
16:49:51.241 unified-api-service.js:321 Making GET request to: http://localhost:3000/local/recent-runs
16:49:51.888 api-integration.js:101 TSN ID is an object, attempting to extract ID: {success: true, testId: undefined, message: 'Test suite 312 started successfully with session ID 18016'}
16:49:51.888 api-integration.js:109 Extracted session ID from message: 18016
16:49:51.888 api-integration.js:2251 Success: Test suite 312 started successfully. Run ID: 18016
16:49:51.889 api-integration.js:2938 Smart merge: 1 raw tests merged to 1 unique tests
16:49:51.889 api-integration.js:3075 Normalized test data for actual TSN_ID 18016 (original map key: 18016): {status: 'running', type: 'Test Suite', id: '312', ts_id: '312', startTime: Mon Jul 28 2025 16:49:51 GMT+0300 (Eastern European Summer Time), …}
16:49:51.889 api-integration.js:1643 Deduplicated 1 raw tests to 1 processed tests
16:49:51.889 api-integration.js:1665 Found 1 active tests that will always be shown with Stop buttons
16:49:51.889 api-integration.js:2316 [getCurrentUser] Got user from apiService credentials: <EMAIL>
16:49:51.889 api-integration.js:1669 Current filter: all Current user: <EMAIL>
16:49:51.889 api-integration.js:1545 Applying 'all' filter, returning 1 tests
16:49:51.889 api-integration.js:1734 Test 18016 - Using test data from: activeTests map, status: running
16:49:51.890 api-integration.js:1877 Test status for 18016: {statusClass: 'running', statusText: 'Running', hasFailedTests: false, passed: 0, failed: 0, …}
16:49:51.890 api-integration.js:1903 Test info for rendering: {testName: 'DEMO PE2.1 Sanity Test', testId: '312', tsn_id: '18016', isTestSuite: true, status: 'running', …}
16:49:51.890 api-integration.js:1958 Rendering card for session 18016: {testName: 'DEMO PE2.1 Sanity Test', testId: '312', sessionId: '18016', status: 'running', statusText: 'Running', …}
16:49:51.890 api-integration.js:2236 Loading complete
16:49:56.992 api-integration.js:628 Test 18016 has end_time, keeping active for grace period (status: Running)
16:49:56.992 api-integration.js:709 Test 18016 completion data: {passed_cases: 0, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
16:49:56.992 api-integration.js:753 Test 18016 - No pass/fail data available (passed_cases=0, failed_cases=0, error="undefined")
16:49:56.992 api-integration.js:757 Test 18016 - Raw end time value: "2025-07-28 13:49:52" (type: string)
16:49:56.993 api-integration.js:772 Test 18016 - Timezone fix applied: "2025-07-28 13:49:52" -> corrected for 3h offset -> 2025-07-28T13:49:52.000Z
16:49:56.993 api-integration.js:779 Test 18016 - Parsed end time: 2025-07-28T13:49:52.000Z, Current time: 2025-07-28T13:49:56.993Z
16:49:56.993 api-integration.js:780 Test 18016 - Parsed end time (local): Mon Jul 28 2025 16:49:52 GMT+0300 (Eastern European Summer Time), Current time (local): Mon Jul 28 2025 16:49:56 GMT+0300 (Eastern European Summer Time)
16:49:56.993 api-integration.js:781 Test 18016 - End time timestamp: 1753710592000, Current time timestamp: 1753710596993
16:49:56.993 api-integration.js:787 Test 18016 - Recent completion (5.0s ago), keeping as running to allow test results to be written
16:49:56.993 api-integration.js:677 Skipping test 18015 as it was previously marked as completed
16:49:56.993 api-integration.js:1499 Updating dashboard counters from recent runs data
16:49:56.993 api-integration.js:1510 Found 3 runs for current user: <EMAIL>
16:49:56.993 api-integration.js:1530 Dashboard counters updated: Total=3, Passed=2, Failed=0, Running=1
16:49:56.993 api-integration.js:2928 Test 18016 is older (10805.0s old), using standard smart merge
16:49:56.993 api-integration.js:2938 Smart merge: 2 raw tests merged to 1 unique tests
16:49:56.993 api-integration.js:3075 Normalized test data for actual TSN_ID 18016 (original map key: 18016): {status: 'running', type: 'Test Suite', id: '312', ts_id: 332, startTime: '2025-07-28 13:49:52', …}
16:49:56.993 api-integration.js:1643 Deduplicated 2 raw tests to 1 processed tests
16:49:56.993 api-integration.js:1665 Found 1 active tests that will always be shown with Stop buttons
16:49:56.993 api-integration.js:2316 [getCurrentUser] Got user from apiService credentials: <EMAIL>
16:49:56.993 api-integration.js:1669 Current filter: all Current user: <EMAIL>
16:49:56.993 api-integration.js:1545 Applying 'all' filter, returning 1 tests
16:49:56.993 api-integration.js:1734 Test 18016 - Using test data from: activeTests map, status: running
16:49:56.994 api-integration.js:1776 Test 18016 status calculation - hasEndTime: true, testInfo.status: "running", passed: 0, failed: 0
16:49:56.994 api-integration.js:1809 Test 18016 - Respecting timing-based running status (recent completion)
16:49:56.994 api-integration.js:1877 Test status for 18016: {statusClass: 'running', statusText: 'Running', hasFailedTests: false, passed: 0, failed: 0, …}
16:49:56.994 api-integration.js:1903 Test info for rendering: {testName: 'PE2.1 Sanity Test', testId: 332, tsn_id: '18016', isTestSuite: true, status: 'running', …}
16:49:56.994 api-integration.js:1958 Rendering card for session 18016: {testName: 'PE2.1 Sanity Test', testId: 332, sessionId: '18016', status: 'running', statusText: 'Running', …}
16:49:56.994 api-integration.js:555 Completed poll request #2, releasing lock
16:50:01.241 api-integration.js:451 Starting poll request #3
16:50:01.241 api-integration.js:461 Using since_id=18015 for incremental polling (highestRecentTsnId=18016)
16:50:01.241 unified-api-service.js:316 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
16:50:01.242 unified-api-service.js:321 Making GET request to: http://localhost:3000/local/recent-runs
16:50:06.940 api-integration.js:628 Test 18016 has end_time, keeping active for grace period (status: Running)
16:50:06.940 api-integration.js:709 Test 18016 completion data: {passed_cases: 0, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
16:50:06.940 api-integration.js:753 Test 18016 - No pass/fail data available (passed_cases=0, failed_cases=0, error="undefined")
16:50:06.940 api-integration.js:757 Test 18016 - Raw end time value: "2025-07-28 13:49:52" (type: string)
16:50:06.940 api-integration.js:772 Test 18016 - Timezone fix applied: "2025-07-28 13:49:52" -> corrected for 3h offset -> 2025-07-28T13:49:52.000Z
16:50:06.940 api-integration.js:779 Test 18016 - Parsed end time: 2025-07-28T13:49:52.000Z, Current time: 2025-07-28T13:50:06.940Z
16:50:06.940 api-integration.js:780 Test 18016 - Parsed end time (local): Mon Jul 28 2025 16:49:52 GMT+0300 (Eastern European Summer Time), Current time (local): Mon Jul 28 2025 16:50:06 GMT+0300 (Eastern European Summer Time)
16:50:06.940 api-integration.js:781 Test 18016 - End time timestamp: 1753710592000, Current time timestamp: 1753710606940
16:50:06.941 api-integration.js:787 Test 18016 - Recent completion (14.9s ago), keeping as running to allow test results to be written
16:50:06.941 api-integration.js:1499 Updating dashboard counters from recent runs data
16:50:06.941 api-integration.js:1510 Found 3 runs for current user: <EMAIL>
16:50:06.941 api-integration.js:1530 Dashboard counters updated: Total=3, Passed=2, Failed=0, Running=1
16:50:06.941 api-integration.js:2928 Test 18016 is older (10814.9s old), using standard smart merge
16:50:06.941 api-integration.js:2938 Smart merge: 2 raw tests merged to 1 unique tests
16:50:06.941 api-integration.js:3075 Normalized test data for actual TSN_ID 18016 (original map key: 18016): {status: 'running', type: 'Test Suite', id: '312', ts_id: 332, startTime: '2025-07-28 13:49:52', …}
16:50:06.941 api-integration.js:1643 Deduplicated 2 raw tests to 1 processed tests
16:50:06.941 api-integration.js:1665 Found 1 active tests that will always be shown with Stop buttons
16:50:06.941 api-integration.js:2316 [getCurrentUser] Got user from apiService credentials: <EMAIL>
16:50:06.942 api-integration.js:1669 Current filter: all Current user: <EMAIL>
16:50:06.942 api-integration.js:1545 Applying 'all' filter, returning 1 tests
16:50:06.942 api-integration.js:1734 Test 18016 - Using test data from: activeTests map, status: running
16:50:06.942 api-integration.js:1776 Test 18016 status calculation - hasEndTime: true, testInfo.status: "running", passed: 0, failed: 0
16:50:06.942 api-integration.js:1809 Test 18016 - Respecting timing-based running status (recent completion)
16:50:06.942 api-integration.js:1877 Test status for 18016: {statusClass: 'running', statusText: 'Running', hasFailedTests: false, passed: 0, failed: 0, …}
16:50:06.942 api-integration.js:1903 Test info for rendering: {testName: 'PE2.1 Sanity Test', testId: 332, tsn_id: '18016', isTestSuite: true, status: 'running', …}
16:50:06.943 api-integration.js:1958 Rendering card for session 18016: {testName: 'PE2.1 Sanity Test', testId: 332, sessionId: '18016', status: 'running', statusText: 'Running', …}
16:50:06.943 api-integration.js:555 Completed poll request #3, releasing lock
16:50:11.247 api-integration.js:451 Starting poll request #4
16:50:11.247 api-integration.js:461 Using since_id=18015 for incremental polling (highestRecentTsnId=18016)
16:50:11.247 unified-api-service.js:316 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
16:50:11.247 unified-api-service.js:321 Making GET request to: http://localhost:3000/local/recent-runs
16:50:16.954 api-integration.js:628 Test 18016 has end_time, keeping active for grace period (status: Running)
16:50:16.954 api-integration.js:709 Test 18016 completion data: {passed_cases: 0, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
16:50:16.955 api-integration.js:753 Test 18016 - No pass/fail data available (passed_cases=0, failed_cases=0, error="undefined")
16:50:16.955 api-integration.js:757 Test 18016 - Raw end time value: "2025-07-28 13:50:08" (type: string)
16:50:16.955 api-integration.js:772 Test 18016 - Timezone fix applied: "2025-07-28 13:50:08" -> corrected for 3h offset -> 2025-07-28T13:50:08.000Z
16:50:16.955 api-integration.js:779 Test 18016 - Parsed end time: 2025-07-28T13:50:08.000Z, Current time: 2025-07-28T13:50:16.955Z
16:50:16.955 api-integration.js:780 Test 18016 - Parsed end time (local): Mon Jul 28 2025 16:50:08 GMT+0300 (Eastern European Summer Time), Current time (local): Mon Jul 28 2025 16:50:16 GMT+0300 (Eastern European Summer Time)
16:50:16.955 api-integration.js:781 Test 18016 - End time timestamp: 1753710608000, Current time timestamp: 1753710616955
16:50:16.955 api-integration.js:787 Test 18016 - Recent completion (9.0s ago), keeping as running to allow test results to be written
16:50:16.956 api-integration.js:1499 Updating dashboard counters from recent runs data
16:50:16.956 api-integration.js:1510 Found 3 runs for current user: <EMAIL>
16:50:16.956 api-integration.js:1530 Dashboard counters updated: Total=3, Passed=2, Failed=0, Running=1
16:50:16.956 api-integration.js:2928 Test 18016 is older (10825.0s old), using standard smart merge
16:50:16.956 api-integration.js:2938 Smart merge: 2 raw tests merged to 1 unique tests
16:50:16.957 api-integration.js:3075 Normalized test data for actual TSN_ID 18016 (original map key: 18016): {status: 'running', type: 'Test Suite', id: '312', ts_id: 332, startTime: '2025-07-28 13:49:52', …}
16:50:16.957 api-integration.js:1643 Deduplicated 2 raw tests to 1 processed tests
16:50:16.957 api-integration.js:1665 Found 1 active tests that will always be shown with Stop buttons
16:50:16.957 api-integration.js:2316 [getCurrentUser] Got user from apiService credentials: <EMAIL>
16:50:16.957 api-integration.js:1669 Current filter: all Current user: <EMAIL>
16:50:16.958 api-integration.js:1545 Applying 'all' filter, returning 1 tests
16:50:16.958 api-integration.js:1734 Test 18016 - Using test data from: activeTests map, status: running
16:50:16.958 api-integration.js:1776 Test 18016 status calculation - hasEndTime: true, testInfo.status: "running", passed: 0, failed: 0
16:50:16.958 api-integration.js:1809 Test 18016 - Respecting timing-based running status (recent completion)
16:50:16.958 api-integration.js:1877 Test status for 18016: {statusClass: 'running', statusText: 'Running', hasFailedTests: false, passed: 0, failed: 0, …}
16:50:16.958 api-integration.js:1903 Test info for rendering: {testName: 'PE2.1 Sanity Test', testId: 332, tsn_id: '18016', isTestSuite: true, status: 'running', …}
16:50:16.958 api-integration.js:1958 Rendering card for session 18016: {testName: 'PE2.1 Sanity Test', testId: 332, sessionId: '18016', status: 'running', statusText: 'Running', …}
16:50:16.959 api-integration.js:555 Completed poll request #4, releasing lock
16:50:21.241 api-integration.js:451 Starting poll request #5
16:50:21.241 api-integration.js:461 Using since_id=18015 for incremental polling (highestRecentTsnId=18016)
16:50:21.241 unified-api-service.js:316 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
16:50:21.241 unified-api-service.js:321 Making GET request to: http://localhost:3000/local/recent-runs
16:50:26.985 api-integration.js:628 Test 18016 has end_time, keeping active for grace period (status: Running)
16:50:26.986 api-integration.js:709 Test 18016 completion data: {passed_cases: 0, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
16:50:26.986 api-integration.js:753 Test 18016 - No pass/fail data available (passed_cases=0, failed_cases=0, error="undefined")
16:50:26.986 api-integration.js:757 Test 18016 - Raw end time value: "2025-07-28 13:50:24" (type: string)
16:50:26.986 api-integration.js:772 Test 18016 - Timezone fix applied: "2025-07-28 13:50:24" -> corrected for 3h offset -> 2025-07-28T13:50:24.000Z
16:50:26.986 api-integration.js:779 Test 18016 - Parsed end time: 2025-07-28T13:50:24.000Z, Current time: 2025-07-28T13:50:26.986Z
16:50:26.986 api-integration.js:780 Test 18016 - Parsed end time (local): Mon Jul 28 2025 16:50:24 GMT+0300 (Eastern European Summer Time), Current time (local): Mon Jul 28 2025 16:50:26 GMT+0300 (Eastern European Summer Time)
16:50:26.987 api-integration.js:781 Test 18016 - End time timestamp: 1753710624000, Current time timestamp: 1753710626986
16:50:26.987 api-integration.js:787 Test 18016 - Recent completion (3.0s ago), keeping as running to allow test results to be written
16:50:26.987 api-integration.js:1499 Updating dashboard counters from recent runs data
16:50:26.987 api-integration.js:1510 Found 3 runs for current user: <EMAIL>
16:50:26.987 api-integration.js:1530 Dashboard counters updated: Total=3, Passed=2, Failed=0, Running=1
16:50:26.987 api-integration.js:2928 Test 18016 is older (10835.0s old), using standard smart merge
16:50:26.988 api-integration.js:2938 Smart merge: 2 raw tests merged to 1 unique tests
16:50:26.988 api-integration.js:3075 Normalized test data for actual TSN_ID 18016 (original map key: 18016): {status: 'running', type: 'Test Suite', id: '312', ts_id: 332, startTime: '2025-07-28 13:49:52', …}
16:50:26.988 api-integration.js:1643 Deduplicated 2 raw tests to 1 processed tests
16:50:26.988 api-integration.js:1665 Found 1 active tests that will always be shown with Stop buttons
16:50:26.988 api-integration.js:2316 [getCurrentUser] Got user from apiService credentials: <EMAIL>
16:50:26.988 api-integration.js:1669 Current filter: all Current user: <EMAIL>
16:50:26.989 api-integration.js:1545 Applying 'all' filter, returning 1 tests
16:50:26.989 api-integration.js:1734 Test 18016 - Using test data from: activeTests map, status: running
16:50:26.989 api-integration.js:1776 Test 18016 status calculation - hasEndTime: true, testInfo.status: "running", passed: 0, failed: 0
16:50:26.990 api-integration.js:1809 Test 18016 - Respecting timing-based running status (recent completion)
16:50:26.990 api-integration.js:1877 Test status for 18016: {statusClass: 'running', statusText: 'Running', hasFailedTests: false, passed: 0, failed: 0, …}
16:50:26.990 api-integration.js:1903 Test info for rendering: {testName: 'PE2.1 Sanity Test', testId: 332, tsn_id: '18016', isTestSuite: true, status: 'running', …}
16:50:26.991 api-integration.js:1958 Rendering card for session 18016: {testName: 'PE2.1 Sanity Test', testId: 332, sessionId: '18016', status: 'running', statusText: 'Running', …}
16:50:26.991 api-integration.js:510 Performing full recent runs refresh
16:50:26.991 api-integration.js:514 Starting full refresh as part of request #5
16:50:26.991 unified-api-service.js:316 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
16:50:26.991 unified-api-service.js:321 Making GET request to: http://localhost:3000/local/recent-runs
16:50:31.248 api-integration.js:580 Skipping poll because previous request is still in progress
16:50:38.246 api-integration.js:533 Full refresh received 100 runs, updating recentRunsCache
16:50:38.246 api-integration.js:628 Test 18016 has end_time, keeping active for grace period (status: Running)
16:50:38.246 api-integration.js:709 Test 18016 completion data: {passed_cases: 0, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
16:50:38.247 api-integration.js:753 Test 18016 - No pass/fail data available (passed_cases=0, failed_cases=0, error="undefined")
16:50:38.247 api-integration.js:757 Test 18016 - Raw end time value: "2025-07-28 13:50:30" (type: string)
16:50:38.247 api-integration.js:772 Test 18016 - Timezone fix applied: "2025-07-28 13:50:30" -> corrected for 3h offset -> 2025-07-28T13:50:30.000Z
16:50:38.247 api-integration.js:779 Test 18016 - Parsed end time: 2025-07-28T13:50:30.000Z, Current time: 2025-07-28T13:50:38.247Z
16:50:38.247 api-integration.js:780 Test 18016 - Parsed end time (local): Mon Jul 28 2025 16:50:30 GMT+0300 (Eastern European Summer Time), Current time (local): Mon Jul 28 2025 16:50:38 GMT+0300 (Eastern European Summer Time)
16:50:38.247 api-integration.js:781 Test 18016 - End time timestamp: 1753710630000, Current time timestamp: 1753710638247
16:50:38.247 api-integration.js:787 Test 18016 - Recent completion (8.2s ago), keeping as running to allow test results to be written
16:50:38.247 api-integration.js:677 Skipping test 18015 as it was previously marked as completed
16:50:38.247 api-integration.js:677 Skipping test 18014 as it was previously marked as completed
16:50:38.247 api-integration.js:677 Skipping test 18013 as it was previously marked as completed
16:50:38.247 api-integration.js:677 Skipping test 18012 as it was previously marked as completed
16:50:38.247 api-integration.js:677 Skipping test 18011 as it was previously marked as completed
16:50:38.247 api-integration.js:677 Skipping test 18010 as it was previously marked as completed
16:50:38.247 api-integration.js:677 Skipping test 18009 as it was previously marked as completed
16:50:38.247 api-integration.js:677 Skipping test 18008 as it was previously marked as completed
16:50:38.247 api-integration.js:677 Skipping test 18007 as it was previously marked as completed
16:50:38.247 api-integration.js:677 Skipping test 18006 as it was previously marked as completed
16:50:38.247 api-integration.js:677 Skipping test 18005 as it was previously marked as completed
16:50:38.247 api-integration.js:677 Skipping test 18004 as it was previously marked as completed
16:50:38.247 api-integration.js:677 Skipping test 18003 as it was previously marked as completed
16:50:38.247 api-integration.js:677 Skipping test 18002 as it was previously marked as completed
16:50:38.247 api-integration.js:677 Skipping test 18001 as it was previously marked as completed
16:50:38.247 api-integration.js:677 Skipping test 18000 as it was previously marked as completed
16:50:38.247 api-integration.js:677 Skipping test 17999 as it was previously marked as completed
16:50:38.247 api-integration.js:677 Skipping test 17998 as it was previously marked as completed
16:50:38.247 api-integration.js:677 Skipping test 17997 as it was previously marked as completed
16:50:38.247 api-integration.js:677 Skipping test 17996 as it was previously marked as completed
16:50:38.247 api-integration.js:677 Skipping test 17995 as it was previously marked as completed
16:50:38.247 api-integration.js:677 Skipping test 17994 as it was previously marked as completed
16:50:38.247 api-integration.js:677 Skipping test 17993 as it was previously marked as completed
16:50:38.247 api-integration.js:677 Skipping test 17992 as it was previously marked as completed
16:50:38.247 api-integration.js:677 Skipping test 17991 as it was previously marked as completed
16:50:38.247 api-integration.js:677 Skipping test 17990 as it was previously marked as completed
16:50:38.247 api-integration.js:677 Skipping test 17989 as it was previously marked as completed
16:50:38.247 api-integration.js:677 Skipping test 17988 as it was previously marked as completed
16:50:38.247 api-integration.js:677 Skipping test 17987 as it was previously marked as completed
16:50:38.247 api-integration.js:677 Skipping test 17986 as it was previously marked as completed
16:50:38.247 api-integration.js:677 Skipping test 17985 as it was previously marked as completed
16:50:38.247 api-integration.js:677 Skipping test 17984 as it was previously marked as completed
16:50:38.247 api-integration.js:677 Skipping test 17983 as it was previously marked as completed
16:50:38.247 api-integration.js:677 Skipping test 17982 as it was previously marked as completed
16:50:38.247 api-integration.js:677 Skipping test 17981 as it was previously marked as completed
16:50:38.247 api-integration.js:677 Skipping test 17980 as it was previously marked as completed
16:50:38.247 api-integration.js:677 Skipping test 17979 as it was previously marked as completed
16:50:38.247 api-integration.js:677 Skipping test 17978 as it was previously marked as completed
16:50:38.248 api-integration.js:677 Skipping test 17977 as it was previously marked as completed
16:50:38.248 api-integration.js:677 Skipping test 17976 as it was previously marked as completed
16:50:38.248 api-integration.js:677 Skipping test 17975 as it was previously marked as completed
16:50:38.248 api-integration.js:677 Skipping test 17974 as it was previously marked as completed
16:50:38.248 api-integration.js:677 Skipping test 17973 as it was previously marked as completed
16:50:38.248 api-integration.js:677 Skipping test 17972 as it was previously marked as completed
16:50:38.248 api-integration.js:677 Skipping test 17971 as it was previously marked as completed
16:50:38.248 api-integration.js:677 Skipping test 17970 as it was previously marked as completed
16:50:38.248 api-integration.js:677 Skipping test 17969 as it was previously marked as completed
16:50:38.248 api-integration.js:677 Skipping test 17968 as it was previously marked as completed
16:50:38.248 api-integration.js:677 Skipping test 17967 as it was previously marked as completed
16:50:38.248 api-integration.js:677 Skipping test 17966 as it was previously marked as completed
16:50:38.248 api-integration.js:677 Skipping test 17965 as it was previously marked as completed
16:50:38.248 api-integration.js:677 Skipping test 17964 as it was previously marked as completed
16:50:38.248 api-integration.js:677 Skipping test 17963 as it was previously marked as completed
16:50:38.248 api-integration.js:677 Skipping test 17962 as it was previously marked as completed
16:50:38.248 api-integration.js:677 Skipping test 17961 as it was previously marked as completed
16:50:38.248 api-integration.js:677 Skipping test 17960 as it was previously marked as completed
16:50:38.248 api-integration.js:677 Skipping test 17959 as it was previously marked as completed
16:50:38.248 api-integration.js:677 Skipping test 17958 as it was previously marked as completed
16:50:38.248 api-integration.js:677 Skipping test 17957 as it was previously marked as completed
16:50:38.248 api-integration.js:677 Skipping test 17956 as it was previously marked as completed
16:50:38.248 api-integration.js:677 Skipping test 17955 as it was previously marked as completed
16:50:38.248 api-integration.js:677 Skipping test 17954 as it was previously marked as completed
16:50:38.248 api-integration.js:677 Skipping test 17953 as it was previously marked as completed
16:50:38.248 api-integration.js:677 Skipping test 17952 as it was previously marked as completed
16:50:38.248 api-integration.js:677 Skipping test 17951 as it was previously marked as completed
16:50:38.248 api-integration.js:677 Skipping test 17950 as it was previously marked as completed
16:50:38.248 api-integration.js:677 Skipping test 17949 as it was previously marked as completed
16:50:38.248 api-integration.js:677 Skipping test 17948 as it was previously marked as completed
16:50:38.248 api-integration.js:677 Skipping test 17947 as it was previously marked as completed
16:50:38.248 api-integration.js:677 Skipping test 17946 as it was previously marked as completed
16:50:38.248 api-integration.js:677 Skipping test 17945 as it was previously marked as completed
16:50:38.248 api-integration.js:677 Skipping test 17944 as it was previously marked as completed
16:50:38.248 api-integration.js:677 Skipping test 17943 as it was previously marked as completed
16:50:38.248 api-integration.js:677 Skipping test 17942 as it was previously marked as completed
16:50:38.248 api-integration.js:677 Skipping test 17941 as it was previously marked as completed
16:50:38.248 api-integration.js:677 Skipping test 17940 as it was previously marked as completed
16:50:38.248 api-integration.js:677 Skipping test 17939 as it was previously marked as completed
16:50:38.248 api-integration.js:677 Skipping test 17938 as it was previously marked as completed
16:50:38.248 api-integration.js:677 Skipping test 17937 as it was previously marked as completed
16:50:38.248 api-integration.js:677 Skipping test 17936 as it was previously marked as completed
16:50:38.248 api-integration.js:677 Skipping test 17935 as it was previously marked as completed
16:50:38.248 api-integration.js:677 Skipping test 17934 as it was previously marked as completed
16:50:38.248 api-integration.js:677 Skipping test 17933 as it was previously marked as completed
16:50:38.248 api-integration.js:677 Skipping test 17932 as it was previously marked as completed
16:50:38.248 api-integration.js:677 Skipping test 17931 as it was previously marked as completed
16:50:38.248 api-integration.js:677 Skipping test 17930 as it was previously marked as completed
16:50:38.248 api-integration.js:677 Skipping test 17929 as it was previously marked as completed
16:50:38.248 api-integration.js:677 Skipping test 17928 as it was previously marked as completed
16:50:38.248 api-integration.js:677 Skipping test 17927 as it was previously marked as completed
16:50:38.249 api-integration.js:677 Skipping test 17926 as it was previously marked as completed
16:50:38.249 api-integration.js:677 Skipping test 17925 as it was previously marked as completed
16:50:38.249 api-integration.js:677 Skipping test 17924 as it was previously marked as completed
16:50:38.249 api-integration.js:677 Skipping test 17923 as it was previously marked as completed
16:50:38.249 api-integration.js:677 Skipping test 17922 as it was previously marked as completed
16:50:38.249 api-integration.js:677 Skipping test 17921 as it was previously marked as completed
16:50:38.249 api-integration.js:677 Skipping test 17920 as it was previously marked as completed
16:50:38.249 api-integration.js:677 Skipping test 17919 as it was previously marked as completed
16:50:38.249 api-integration.js:677 Skipping test 17918 as it was previously marked as completed
16:50:38.249 api-integration.js:677 Skipping test 17917 as it was previously marked as completed
16:50:38.249 api-integration.js:1499 Updating dashboard counters from recent runs data
16:50:38.249 api-integration.js:1510 Found 18 runs for current user: <EMAIL>
16:50:38.249 api-integration.js:1530 Dashboard counters updated: Total=18, Passed=11, Failed=6, Running=1
16:50:38.249 api-integration.js:2928 Test 18016 is older (10846.2s old), using standard smart merge
16:50:38.249 api-integration.js:2938 Smart merge: 2 raw tests merged to 1 unique tests
16:50:38.249 api-integration.js:3075 Normalized test data for actual TSN_ID 18016 (original map key: 18016): {status: 'running', type: 'Test Suite', id: '312', ts_id: 332, startTime: '2025-07-28 13:49:52', …}
16:50:38.249 api-integration.js:1643 Deduplicated 2 raw tests to 1 processed tests
16:50:38.249 api-integration.js:1665 Found 1 active tests that will always be shown with Stop buttons
16:50:38.249 api-integration.js:2316 [getCurrentUser] Got user from apiService credentials: <EMAIL>
16:50:38.249 api-integration.js:1669 Current filter: all Current user: <EMAIL>
16:50:38.249 api-integration.js:1545 Applying 'all' filter, returning 1 tests
16:50:38.249 api-integration.js:1734 Test 18016 - Using test data from: activeTests map, status: running
16:50:38.249 api-integration.js:1776 Test 18016 status calculation - hasEndTime: true, testInfo.status: "running", passed: 0, failed: 0
16:50:38.249 api-integration.js:1809 Test 18016 - Respecting timing-based running status (recent completion)
16:50:38.249 api-integration.js:1877 Test status for 18016: {statusClass: 'running', statusText: 'Running', hasFailedTests: false, passed: 0, failed: 0, …}
16:50:38.249 api-integration.js:1903 Test info for rendering: {testName: 'PE2.1 Sanity Test', testId: 332, tsn_id: '18016', isTestSuite: true, status: 'running', …}
16:50:38.250 api-integration.js:1958 Rendering card for session 18016: {testName: 'PE2.1 Sanity Test', testId: 332, sessionId: '18016', status: 'running', statusText: 'Running', …}
16:50:38.250 api-integration.js:555 Completed poll request #5, releasing lock
16:50:41.240 api-integration.js:451 Starting poll request #6
16:50:41.240 api-integration.js:461 Using since_id=18015 for incremental polling (highestRecentTsnId=18016)
16:50:41.240 unified-api-service.js:316 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
16:50:41.241 unified-api-service.js:321 Making GET request to: http://localhost:3000/local/recent-runs
16:50:46.947 api-integration.js:628 Test 18016 has end_time, keeping active for grace period (status: Failed)
16:50:46.948 api-integration.js:709 Test 18016 completion data: {passed_cases: 3, failed_cases: 2, passed: undefined, failed: undefined, error: undefined, …}
16:50:46.948 api-integration.js:723 Test 18016 - Detected as FAILED: failed_cases=2, failed=undefined
16:50:46.948 api-integration.js:802 Test 18016 has completed with status: failed (will be visible for 15-second grace period)
16:50:46.948 dashboard.js:5 UI NOTIFICATION to be displayed [error]: Suite Finished: PE2.1 Sanity Test - Status: Failed. Cases Passed: 3, Cases Failed: 2.
16:50:46.948 api-integration.js:1598 Grace period (25s) expired for test 18016, removing from active tests, status: failed
16:50:46.948 api-integration.js:2938 Smart merge: 1 raw tests merged to 1 unique tests
16:50:46.949 api-integration.js:3075 Normalized test data for actual TSN_ID 18016 (original map key: N/A): {tsn_id: '18016', tc_id: null, ts_id: 332, startTime: '2025-07-28 13:49:52', endTime: '2025-07-28 13:50:30', …}
16:50:46.949 api-integration.js:1643 Deduplicated 1 raw tests to 1 processed tests
16:50:46.949 api-integration.js:1665 Found 1 active tests that will always be shown with Stop buttons
16:50:46.949 api-integration.js:2316 [getCurrentUser] Got user from apiService credentials: <EMAIL>
16:50:46.949 api-integration.js:1669 Current filter: all Current user: <EMAIL>
16:50:46.949 api-integration.js:1545 Applying 'all' filter, returning 1 tests
16:50:46.949 api-integration.js:1734 Test 18016 - Using test data from: recentRunsCache, status: running
16:50:46.949 api-integration.js:1776 Test 18016 status calculation - hasEndTime: true, testInfo.status: "running", passed: 0, failed: 0
16:50:46.949 api-integration.js:1792 Test 18016 - Using completion detection status: failed
16:50:46.949 api-integration.js:1877 Test status for 18016: {statusClass: 'failed', statusText: 'Failed', hasFailedTests: false, passed: 0, failed: 0, …}
16:50:46.950 api-integration.js:1903 Test info for rendering: {testName: 'PE2.1 Sanity Test', testId: 332, tsn_id: '18016', isTestSuite: true, status: 'running', …}
16:50:46.950 api-integration.js:1958 Rendering card for session 18016: {testName: 'PE2.1 Sanity Test', testId: 332, sessionId: '18016', status: 'failed', statusText: 'Failed', …}
16:50:46.950 api-integration.js:1458 Found 1 new completed tests to count in dashboard
16:50:46.951 api-integration.js:1472 Passed tests: 0, Failed tests: 1
16:50:46.951 api-integration.js:1483 Updated dashboard counters: {total: 19, successful: 11, failed: 7}
16:50:46.951 api-integration.js:1499 Updating dashboard counters from recent runs data
16:50:46.951 api-integration.js:1510 Found 18 runs for current user: <EMAIL>
16:50:46.951 api-integration.js:1530 Dashboard counters updated: Total=18, Passed=11, Failed=6, Running=1
16:50:46.951 api-integration.js:1598 Grace period (25s) expired for test 18016, removing from active tests, status: failed
16:50:46.951 api-integration.js:2938 Smart merge: 1 raw tests merged to 1 unique tests
16:50:46.951 api-integration.js:3075 Normalized test data for actual TSN_ID 18016 (original map key: N/A): {tsn_id: '18016', tc_id: null, ts_id: 332, startTime: '2025-07-28 13:49:52', endTime: '2025-07-28 13:50:30', …}
16:50:46.952 api-integration.js:1643 Deduplicated 1 raw tests to 1 processed tests
16:50:46.952 api-integration.js:1665 Found 1 active tests that will always be shown with Stop buttons
16:50:46.952 api-integration.js:2316 [getCurrentUser] Got user from apiService credentials: <EMAIL>
16:50:46.952 api-integration.js:1669 Current filter: all Current user: <EMAIL>
16:50:46.952 api-integration.js:1545 Applying 'all' filter, returning 1 tests
16:50:46.952 api-integration.js:1734 Test 18016 - Using test data from: recentRunsCache, status: running
16:50:46.952 api-integration.js:1776 Test 18016 status calculation - hasEndTime: true, testInfo.status: "running", passed: 0, failed: 0
16:50:46.952 api-integration.js:1792 Test 18016 - Using completion detection status: failed
16:50:46.952 api-integration.js:1877 Test status for 18016: {statusClass: 'failed', statusText: 'Failed', hasFailedTests: false, passed: 0, failed: 0, …}
16:50:46.953 api-integration.js:1903 Test info for rendering: {testName: 'PE2.1 Sanity Test', testId: 332, tsn_id: '18016', isTestSuite: true, status: 'running', …}
16:50:46.953 api-integration.js:1958 Rendering card for session 18016: {testName: 'PE2.1 Sanity Test', testId: 332, sessionId: '18016', status: 'failed', statusText: 'Failed', …}
16:50:46.953 api-integration.js:555 Completed poll request #6, releasing lock
16:50:51.255 api-integration.js:451 Starting poll request #7
16:50:51.255 api-integration.js:461 Using since_id=18015 for incremental polling (highestRecentTsnId=18016)
16:50:51.256 unified-api-service.js:316 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
16:50:51.256 unified-api-service.js:321 Making GET request to: http://localhost:3000/local/recent-runs
16:50:56.948 api-integration.js:677 Skipping test 18016 as it was previously marked as completed
16:50:56.948 api-integration.js:1499 Updating dashboard counters from recent runs data
16:50:56.948 api-integration.js:1510 Found 18 runs for current user: <EMAIL>
16:50:56.948 api-integration.js:1530 Dashboard counters updated: Total=18, Passed=11, Failed=6, Running=1
16:50:56.948 api-integration.js:2938 Smart merge: 1 raw tests merged to 1 unique tests
16:50:56.948 api-integration.js:3075 Normalized test data for actual TSN_ID 18016 (original map key: N/A): {tsn_id: '18016', tc_id: null, ts_id: 332, startTime: '2025-07-28 13:49:52', endTime: '2025-07-28 13:50:30', …}
16:50:56.948 api-integration.js:1643 Deduplicated 1 raw tests to 1 processed tests
16:50:56.948 api-integration.js:1665 Found 0 active tests that will always be shown with Stop buttons
16:50:56.948 api-integration.js:2316 [getCurrentUser] Got user from apiService credentials: <EMAIL>
16:50:56.948 api-integration.js:1669 Current filter: all Current user: <EMAIL>
16:50:56.949 api-integration.js:1545 Applying 'all' filter, returning 1 tests
16:50:56.949 api-integration.js:1734 Test 18016 - Using test data from: recentRunsCache, status: running
16:50:56.949 api-integration.js:1776 Test 18016 status calculation - hasEndTime: true, testInfo.status: "running", passed: 0, failed: 0
16:50:56.949 api-integration.js:1809 Test 18016 - Respecting timing-based running status (recent completion)
16:50:56.949 api-integration.js:1877 Test status for 18016: {statusClass: 'running', statusText: 'Running', hasFailedTests: false, passed: 0, failed: 0, …}
16:50:56.949 api-integration.js:1903 Test info for rendering: {testName: 'PE2.1 Sanity Test', testId: 332, tsn_id: '18016', isTestSuite: true, status: 'running', …}
16:50:56.949 api-integration.js:1958 Rendering card for session 18016: {testName: 'PE2.1 Sanity Test', testId: 332, sessionId: '18016', status: 'running', statusText: 'Running', …}
16:50:56.950 api-integration.js:555 Completed poll request #7, releasing lock
16:51:00.115 api-integration.js:2496 Active tests filter set to: mine
16:51:00.115 api-integration.js:2938 Smart merge: 1 raw tests merged to 1 unique tests
16:51:00.115 api-integration.js:3075 Normalized test data for actual TSN_ID 18016 (original map key: N/A): {tsn_id: '18016', tc_id: null, ts_id: 332, startTime: '2025-07-28 13:49:52', endTime: '2025-07-28 13:50:30', …}
16:51:00.115 api-integration.js:1643 Deduplicated 1 raw tests to 1 processed tests
16:51:00.115 api-integration.js:1665 Found 0 active tests that will always be shown with Stop buttons
16:51:00.115 api-integration.js:2316 [getCurrentUser] Got user from apiService credentials: <EMAIL>
16:51:00.115 api-integration.js:1669 Current filter: mine Current user: <EMAIL>
16:51:00.116 api-integration.js:1566 Test 18016: user=<EMAIL>, filter=mine, isMyTest=true, include=true
16:51:00.116 api-integration.js:1570 Applied 'mine' filter: 1/1 tests included
16:51:00.116 api-integration.js:1734 Test 18016 - Using test data from: recentRunsCache, status: running
16:51:00.116 api-integration.js:1776 Test 18016 status calculation - hasEndTime: true, testInfo.status: "running", passed: 0, failed: 0
16:51:00.116 api-integration.js:1809 Test 18016 - Respecting timing-based running status (recent completion)
16:51:00.116 api-integration.js:1877 Test status for 18016: {statusClass: 'running', statusText: 'Running', hasFailedTests: false, passed: 0, failed: 0, …}
16:51:00.116 api-integration.js:1903 Test info for rendering: {testName: 'PE2.1 Sanity Test', testId: 332, tsn_id: '18016', isTestSuite: true, status: 'running', …}
16:51:00.117 api-integration.js:1958 Rendering card for session 18016: {testName: 'PE2.1 Sanity Test', testId: 332, sessionId: '18016', status: 'running', statusText: 'Running', …}
16:51:00.117 api-integration.js:2439 [API Integration] Active tests filter set to: mine
16:51:00.117 api-integration.js:2446 [API Integration] Re-rendering active tests due to filter change.
16:51:00.117 api-integration.js:2938 Smart merge: 1 raw tests merged to 1 unique tests
16:51:00.117 api-integration.js:3075 Normalized test data for actual TSN_ID 18016 (original map key: N/A): {tsn_id: '18016', tc_id: null, ts_id: 332, startTime: '2025-07-28 13:49:52', endTime: '2025-07-28 13:50:30', …}
16:51:00.117 api-integration.js:1643 Deduplicated 1 raw tests to 1 processed tests
16:51:00.117 api-integration.js:1665 Found 0 active tests that will always be shown with Stop buttons
16:51:00.117 api-integration.js:2316 [getCurrentUser] Got user from apiService credentials: <EMAIL>
16:51:00.117 api-integration.js:1669 Current filter: mine Current user: <EMAIL>
16:51:00.117 api-integration.js:1566 Test 18016: user=<EMAIL>, filter=mine, isMyTest=true, include=true
16:51:00.118 api-integration.js:1570 Applied 'mine' filter: 1/1 tests included
16:51:00.118 api-integration.js:1734 Test 18016 - Using test data from: recentRunsCache, status: running
16:51:00.118 api-integration.js:1776 Test 18016 status calculation - hasEndTime: true, testInfo.status: "running", passed: 0, failed: 0
16:51:00.118 api-integration.js:1809 Test 18016 - Respecting timing-based running status (recent completion)
16:51:00.118 api-integration.js:1877 Test status for 18016: {statusClass: 'running', statusText: 'Running', hasFailedTests: false, passed: 0, failed: 0, …}
16:51:00.118 api-integration.js:1903 Test info for rendering: {testName: 'PE2.1 Sanity Test', testId: 332, tsn_id: '18016', isTestSuite: true, status: 'running', …}
16:51:00.118 api-integration.js:1958 Rendering card for session 18016: {testName: 'PE2.1 Sanity Test', testId: 332, sessionId: '18016', status: 'running', statusText: 'Running', …}
16:51:00.851 api-integration.js:2496 Active tests filter set to: others
16:51:00.851 api-integration.js:2938 Smart merge: 1 raw tests merged to 1 unique tests
16:51:00.851 api-integration.js:3075 Normalized test data for actual TSN_ID 18016 (original map key: N/A): {tsn_id: '18016', tc_id: null, ts_id: 332, startTime: '2025-07-28 13:49:52', endTime: '2025-07-28 13:50:30', …}
16:51:00.851 api-integration.js:1643 Deduplicated 1 raw tests to 1 processed tests
16:51:00.851 api-integration.js:1665 Found 0 active tests that will always be shown with Stop buttons
16:51:00.851 api-integration.js:2316 [getCurrentUser] Got user from apiService credentials: <EMAIL>
16:51:00.851 api-integration.js:1669 Current filter: others Current user: <EMAIL>
16:51:00.851 api-integration.js:1566 Test 18016: user=<EMAIL>, filter=others, isMyTest=true, include=false
16:51:00.851 api-integration.js:1570 Applied 'others' filter: 0/1 tests included
16:51:00.851 api-integration.js:2439 [API Integration] Active tests filter set to: others
16:51:00.851 api-integration.js:2446 [API Integration] Re-rendering active tests due to filter change.
16:51:00.851 api-integration.js:2938 Smart merge: 1 raw tests merged to 1 unique tests
16:51:00.851 api-integration.js:3075 Normalized test data for actual TSN_ID 18016 (original map key: N/A): {tsn_id: '18016', tc_id: null, ts_id: 332, startTime: '2025-07-28 13:49:52', endTime: '2025-07-28 13:50:30', …}
16:51:00.851 api-integration.js:1643 Deduplicated 1 raw tests to 1 processed tests
16:51:00.851 api-integration.js:1665 Found 0 active tests that will always be shown with Stop buttons
16:51:00.851 api-integration.js:2316 [getCurrentUser] Got user from apiService credentials: <EMAIL>
16:51:00.852 api-integration.js:1669 Current filter: others Current user: <EMAIL>
16:51:00.852 api-integration.js:1566 Test 18016: user=<EMAIL>, filter=others, isMyTest=true, include=false
16:51:00.852 api-integration.js:1570 Applied 'others' filter: 0/1 tests included
16:51:01.243 api-integration.js:451 Starting poll request #8
16:51:01.243 api-integration.js:461 Using since_id=18015 for incremental polling (highestRecentTsnId=18016)
16:51:01.243 unified-api-service.js:316 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
16:51:01.243 unified-api-service.js:321 Making GET request to: http://localhost:3000/local/recent-runs
16:51:02.043 api-integration.js:2496 Active tests filter set to: all
16:51:02.043 api-integration.js:2938 Smart merge: 1 raw tests merged to 1 unique tests
16:51:02.044 api-integration.js:3075 Normalized test data for actual TSN_ID 18016 (original map key: N/A): {tsn_id: '18016', tc_id: null, ts_id: 332, startTime: '2025-07-28 13:49:52', endTime: '2025-07-28 13:50:30', …}
16:51:02.044 api-integration.js:1643 Deduplicated 1 raw tests to 1 processed tests
16:51:02.044 api-integration.js:1665 Found 0 active tests that will always be shown with Stop buttons
16:51:02.044 api-integration.js:2316 [getCurrentUser] Got user from apiService credentials: <EMAIL>
16:51:02.044 api-integration.js:1669 Current filter: all Current user: <EMAIL>
16:51:02.044 api-integration.js:1545 Applying 'all' filter, returning 1 tests
16:51:02.044 api-integration.js:1734 Test 18016 - Using test data from: recentRunsCache, status: running
16:51:02.044 api-integration.js:1776 Test 18016 status calculation - hasEndTime: true, testInfo.status: "running", passed: 0, failed: 0
16:51:02.044 api-integration.js:1809 Test 18016 - Respecting timing-based running status (recent completion)
16:51:02.044 api-integration.js:1877 Test status for 18016: {statusClass: 'running', statusText: 'Running', hasFailedTests: false, passed: 0, failed: 0, …}
16:51:02.044 api-integration.js:1903 Test info for rendering: {testName: 'PE2.1 Sanity Test', testId: 332, tsn_id: '18016', isTestSuite: true, status: 'running', …}
16:51:02.045 api-integration.js:1958 Rendering card for session 18016: {testName: 'PE2.1 Sanity Test', testId: 332, sessionId: '18016', status: 'running', statusText: 'Running', …}
16:51:02.045 api-integration.js:2439 [API Integration] Active tests filter set to: all
16:51:02.045 api-integration.js:2446 [API Integration] Re-rendering active tests due to filter change.
16:51:02.045 api-integration.js:2938 Smart merge: 1 raw tests merged to 1 unique tests
16:51:02.045 api-integration.js:3075 Normalized test data for actual TSN_ID 18016 (original map key: N/A): {tsn_id: '18016', tc_id: null, ts_id: 332, startTime: '2025-07-28 13:49:52', endTime: '2025-07-28 13:50:30', …}
16:51:02.045 api-integration.js:1643 Deduplicated 1 raw tests to 1 processed tests
16:51:02.045 api-integration.js:1665 Found 0 active tests that will always be shown with Stop buttons
16:51:02.045 api-integration.js:2316 [getCurrentUser] Got user from apiService credentials: <EMAIL>
16:51:02.046 api-integration.js:1669 Current filter: all Current user: <EMAIL>
16:51:02.046 api-integration.js:1545 Applying 'all' filter, returning 1 tests
16:51:02.046 api-integration.js:1734 Test 18016 - Using test data from: recentRunsCache, status: running
16:51:02.046 api-integration.js:1776 Test 18016 status calculation - hasEndTime: true, testInfo.status: "running", passed: 0, failed: 0
16:51:02.046 api-integration.js:1809 Test 18016 - Respecting timing-based running status (recent completion)
16:51:02.046 api-integration.js:1877 Test status for 18016: {statusClass: 'running', statusText: 'Running', hasFailedTests: false, passed: 0, failed: 0, …}
16:51:02.046 api-integration.js:1903 Test info for rendering: {testName: 'PE2.1 Sanity Test', testId: 332, tsn_id: '18016', isTestSuite: true, status: 'running', …}
16:51:02.046 api-integration.js:1958 Rendering card for session 18016: {testName: 'PE2.1 Sanity Test', testId: 332, sessionId: '18016', status: 'running', statusText: 'Running', …}
16:51:06.939 api-integration.js:677 Skipping test 18016 as it was previously marked as completed
16:51:06.940 api-integration.js:1499 Updating dashboard counters from recent runs data
16:51:06.940 api-integration.js:1510 Found 18 runs for current user: <EMAIL>
16:51:06.940 api-integration.js:1530 Dashboard counters updated: Total=18, Passed=11, Failed=6, Running=1
16:51:06.940 api-integration.js:2938 Smart merge: 1 raw tests merged to 1 unique tests
16:51:06.940 api-integration.js:3075 Normalized test data for actual TSN_ID 18016 (original map key: N/A): {tsn_id: '18016', tc_id: null, ts_id: 332, startTime: '2025-07-28 13:49:52', endTime: '2025-07-28 13:50:30', …}
16:51:06.941 api-integration.js:1643 Deduplicated 1 raw tests to 1 processed tests
16:51:06.941 api-integration.js:1665 Found 0 active tests that will always be shown with Stop buttons
16:51:06.941 api-integration.js:2316 [getCurrentUser] Got user from apiService credentials: <EMAIL>
16:51:06.941 api-integration.js:1669 Current filter: all Current user: <EMAIL>
16:51:06.941 api-integration.js:1545 Applying 'all' filter, returning 1 tests
16:51:06.942 api-integration.js:1734 Test 18016 - Using test data from: recentRunsCache, status: running
16:51:06.942 api-integration.js:1776 Test 18016 status calculation - hasEndTime: true, testInfo.status: "running", passed: 0, failed: 0
16:51:06.942 api-integration.js:1809 Test 18016 - Respecting timing-based running status (recent completion)
16:51:06.942 api-integration.js:1877 Test status for 18016: {statusClass: 'running', statusText: 'Running', hasFailedTests: false, passed: 0, failed: 0, …}
16:51:06.942 api-integration.js:1903 Test info for rendering: {testName: 'PE2.1 Sanity Test', testId: 332, tsn_id: '18016', isTestSuite: true, status: 'running', …}
16:51:06.942 api-integration.js:1958 Rendering card for session 18016: {testName: 'PE2.1 Sanity Test', testId: 332, sessionId: '18016', status: 'running', statusText: 'Running', …}
16:51:06.943 api-integration.js:555 Completed poll request #8, releasing lock
16:51:11.251 api-integration.js:451 Starting poll request #9
16:51:11.251 api-integration.js:461 Using since_id=18015 for incremental polling (highestRecentTsnId=18016)
16:51:11.251 unified-api-service.js:316 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
16:51:11.251 unified-api-service.js:321 Making GET request to: http://localhost:3000/local/recent-runs
16:51:11.985 unified-auth-client.js:354 🔍 Performing periodic session validation...
16:51:11.991 unified-auth-client.js:472 ✅ JWT authentication state stored for: <EMAIL>
16:51:11.991 unified-auth-client.js:330 ✅ Session validated with server
16:51:16.930 api-integration.js:677 Skipping test 18016 as it was previously marked as completed
16:51:16.930 api-integration.js:1499 Updating dashboard counters from recent runs data
16:51:16.930 api-integration.js:1510 Found 18 runs for current user: <EMAIL>
16:51:16.930 api-integration.js:1530 Dashboard counters updated: Total=18, Passed=11, Failed=6, Running=1
16:51:16.931 api-integration.js:2938 Smart merge: 1 raw tests merged to 1 unique tests
16:51:16.931 api-integration.js:3075 Normalized test data for actual TSN_ID 18016 (original map key: N/A): {tsn_id: '18016', tc_id: null, ts_id: 332, startTime: '2025-07-28 13:49:52', endTime: '2025-07-28 13:50:30', …}
16:51:16.931 api-integration.js:1643 Deduplicated 1 raw tests to 1 processed tests
16:51:16.931 api-integration.js:1665 Found 0 active tests that will always be shown with Stop buttons
16:51:16.932 api-integration.js:2316 [getCurrentUser] Got user from apiService credentials: <EMAIL>
16:51:16.932 api-integration.js:1669 Current filter: all Current user: <EMAIL>
16:51:16.932 api-integration.js:1545 Applying 'all' filter, returning 1 tests
16:51:16.932 api-integration.js:1734 Test 18016 - Using test data from: recentRunsCache, status: running
16:51:16.933 api-integration.js:1776 Test 18016 status calculation - hasEndTime: true, testInfo.status: "running", passed: 0, failed: 0
16:51:16.933 api-integration.js:1809 Test 18016 - Respecting timing-based running status (recent completion)
16:51:16.933 api-integration.js:1877 Test status for 18016: {statusClass: 'running', statusText: 'Running', hasFailedTests: false, passed: 0, failed: 0, …}
16:51:16.933 api-integration.js:1903 Test info for rendering: {testName: 'PE2.1 Sanity Test', testId: 332, tsn_id: '18016', isTestSuite: true, status: 'running', …}
16:51:16.934 api-integration.js:1958 Rendering card for session 18016: {testName: 'PE2.1 Sanity Test', testId: 332, sessionId: '18016', status: 'running', statusText: 'Running', …}
16:51:16.934 api-integration.js:555 Completed poll request #9, releasing lock
16:51:21.242 api-integration.js:451 Starting poll request #10
16:51:21.242 api-integration.js:461 Using since_id=18015 for incremental polling (highestRecentTsnId=18016)
16:51:21.242 unified-api-service.js:316 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
16:51:21.242 unified-api-service.js:321 Making GET request to: http://localhost:3000/local/recent-runs
16:51:26.944 api-integration.js:677 Skipping test 18016 as it was previously marked as completed
16:51:26.944 api-integration.js:1499 Updating dashboard counters from recent runs data
16:51:26.944 api-integration.js:1510 Found 18 runs for current user: <EMAIL>
16:51:26.944 api-integration.js:1530 Dashboard counters updated: Total=18, Passed=11, Failed=6, Running=1
16:51:26.944 api-integration.js:2938 Smart merge: 1 raw tests merged to 1 unique tests
16:51:26.944 api-integration.js:3075 Normalized test data for actual TSN_ID 18016 (original map key: N/A): {tsn_id: '18016', tc_id: null, ts_id: 332, startTime: '2025-07-28 13:49:52', endTime: '2025-07-28 13:50:30', …}
16:51:26.944 api-integration.js:1643 Deduplicated 1 raw tests to 1 processed tests
16:51:26.944 api-integration.js:1665 Found 0 active tests that will always be shown with Stop buttons
16:51:26.946 api-integration.js:2316 [getCurrentUser] Got user from apiService credentials: <EMAIL>
16:51:26.946 api-integration.js:1669 Current filter: all Current user: <EMAIL>
16:51:26.946 api-integration.js:1545 Applying 'all' filter, returning 1 tests
16:51:26.946 api-integration.js:1734 Test 18016 - Using test data from: recentRunsCache, status: running
16:51:26.946 api-integration.js:1776 Test 18016 status calculation - hasEndTime: true, testInfo.status: "running", passed: 0, failed: 0
16:51:26.946 api-integration.js:1809 Test 18016 - Respecting timing-based running status (recent completion)
16:51:26.946 api-integration.js:1877 Test status for 18016: {statusClass: 'running', statusText: 'Running', hasFailedTests: false, passed: 0, failed: 0, …}
16:51:26.946 api-integration.js:1903 Test info for rendering: {testName: 'PE2.1 Sanity Test', testId: 332, tsn_id: '18016', isTestSuite: true, status: 'running', …}
16:51:26.946 api-integration.js:1958 Rendering card for session 18016: {testName: 'PE2.1 Sanity Test', testId: 332, sessionId: '18016', status: 'running', statusText: 'Running', …}
16:51:26.946 api-integration.js:555 Completed poll request #10, releasing lock
16:51:31.253 api-integration.js:451 Starting poll request #11
16:51:31.254 api-integration.js:461 Using since_id=18015 for incremental polling (highestRecentTsnId=18016)
16:51:31.254 unified-api-service.js:316 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
16:51:31.254 unified-api-service.js:321 Making GET request to: http://localhost:3000/local/recent-runs
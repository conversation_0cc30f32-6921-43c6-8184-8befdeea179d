/**
 * Admin API Routes
 * Provides endpoints for user management and system administration
 */

const express = require('express');
const router = express.Router();
const userManager = require('../auth/user-manager');
const authService = require('../auth/auth-service');
const auditLogger = require('../utils/audit-logger');
const { requireRole, requirePermission } = require('../middleware/auth');
const { requireRoles, requirePermissions } = require('../middleware/session-validation');
const ValidationUtils = require('../utils/validation');

/**
 * Get all users (admin only)
 */
router.get('/users', requireRoles('admin'), (req, res) => {
  try {
    const users = userManager.getAllUsers();
    res.json({
      success: true,
      data: users,
      count: users.length
    });
  } catch (error) {
    console.error('Error fetching users:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch users'
    });
  }
});

/**
 * Get user by ID (admin only)
 */
router.get('/users/:uid', requireRoles('admin'), (req, res) => {
  try {
    const { uid } = req.params;
    
    // Validate UID
    const uidValidation = ValidationUtils.validateEmail(uid);
    if (!uidValidation.isValid) {
      return res.status(400).json({
        success: false,
        message: uidValidation.error
      });
    }

    const user = userManager.getUser(uidValidation.sanitized);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    res.json({
      success: true,
      data: user
    });
  } catch (error) {
    console.error('Error fetching user:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch user'
    });
  }
});

/**
 * Add new user (admin only)
 */
router.post('/users', requireRoles('admin'), (req, res) => {
  try {
    const { uid, password, role, name } = req.body;

    // Validate input
    const validation = ValidationUtils.validateCredentials({ uid, password });
    if (!validation.isValid) {
      return res.status(400).json({
        success: false,
        message: 'Invalid input: ' + validation.errors.join(', ')
      });
    }

    // Validate role
    const roles = userManager.getRoles();
    if (role && !roles[role]) {
      return res.status(400).json({
        success: false,
        message: `Invalid role. Available roles: ${Object.keys(roles).join(', ')}`
      });
    }

    // Validate name
    if (name) {
      const nameValidation = ValidationUtils.validateString(name, {
        maxLength: 100,
        name: 'name'
      });
      if (!nameValidation.isValid) {
        return res.status(400).json({
          success: false,
          message: nameValidation.error
        });
      }
    }

    // Add user
    const success = userManager.addUser({
      uid: validation.sanitized.uid,
      password: validation.sanitized.password,
      role: role || 'viewer',
      name: name || validation.sanitized.uid
    });

    // Log admin action
    auditLogger.logAdminAction({
      adminUid: req.user.uid,
      action: 'CREATE_USER',
      targetUid: validation.sanitized.uid,
      ip: req.ip || req.connection.remoteAddress,
      details: {
        role: role || 'viewer',
        name: name || validation.sanitized.uid
      },
      success: success
    });

    if (success) {
      console.log(`✅ Admin ${req.user.uid} added new user: ${validation.sanitized.uid}`);
      res.json({
        success: true,
        message: 'User added successfully',
        data: { uid: validation.sanitized.uid }
      });
    } else {
      res.status(400).json({
        success: false,
        message: 'Failed to add user (user may already exist)'
      });
    }
  } catch (error) {
    console.error('Error adding user:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to add user'
    });
  }
});

/**
 * Update user (admin only)
 */
router.put('/users/:uid', requireRoles('admin'), (req, res) => {
  try {
    const { uid } = req.params;
    const { password, role, name, active } = req.body;

    // Validate UID
    const uidValidation = ValidationUtils.validateEmail(uid);
    if (!uidValidation.isValid) {
      return res.status(400).json({
        success: false,
        message: uidValidation.error
      });
    }

    // Check if user exists
    const existingUser = userManager.getUser(uidValidation.sanitized);
    if (!existingUser) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    const updates = {};

    // Validate password if provided
    if (password) {
      const passwordValidation = ValidationUtils.validatePassword(password);
      if (!passwordValidation.isValid) {
        return res.status(400).json({
          success: false,
          message: passwordValidation.error
        });
      }
      updates.password = passwordValidation.sanitized;
    }

    // Validate role if provided
    if (role) {
      const roles = userManager.getRoles();
      if (!roles[role]) {
        return res.status(400).json({
          success: false,
          message: `Invalid role. Available roles: ${Object.keys(roles).join(', ')}`
        });
      }
      updates.role = role;
    }

    // Validate name if provided
    if (name) {
      const nameValidation = ValidationUtils.validateString(name, {
        maxLength: 100,
        name: 'name'
      });
      if (!nameValidation.isValid) {
        return res.status(400).json({
          success: false,
          message: nameValidation.error
        });
      }
      updates.name = nameValidation.sanitized;
    }

    // Validate active status if provided
    if (typeof active === 'boolean') {
      updates.active = active;
    }

    // Update user
    const success = userManager.updateUser(uidValidation.sanitized, updates);

    if (success) {
      console.log(`✅ Admin ${req.user.uid} updated user: ${uidValidation.sanitized}`);
      res.json({
        success: true,
        message: 'User updated successfully'
      });
    } else {
      res.status(400).json({
        success: false,
        message: 'Failed to update user'
      });
    }
  } catch (error) {
    console.error('Error updating user:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update user'
    });
  }
});

/**
 * Deactivate user (admin only)
 */
router.delete('/users/:uid', requireRoles('admin'), (req, res) => {
  try {
    const { uid } = req.params;

    // Validate UID
    const uidValidation = ValidationUtils.validateEmail(uid);
    if (!uidValidation.isValid) {
      return res.status(400).json({
        success: false,
        message: uidValidation.error
      });
    }

    // Prevent self-deletion
    if (uidValidation.sanitized === req.user.uid) {
      return res.status(400).json({
        success: false,
        message: 'Cannot deactivate your own account'
      });
    }

    // Deactivate user
    const success = userManager.deactivateUser(uidValidation.sanitized);

    if (success) {
      console.log(`✅ Admin ${req.user.uid} deactivated user: ${uidValidation.sanitized}`);
      res.json({
        success: true,
        message: 'User deactivated successfully'
      });
    } else {
      res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }
  } catch (error) {
    console.error('Error deactivating user:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to deactivate user'
    });
  }
});

/**
 * Get available roles (admin only)
 */
router.get('/roles', requireRoles('admin'), (req, res) => {
  try {
    const roles = userManager.getRoles();
    res.json({
      success: true,
      data: roles
    });
  } catch (error) {
    console.error('Error fetching roles:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch roles'
    });
  }
});

/**
 * Get system status (admin only)
 */
router.get('/status', requireRoles('admin'), (req, res) => {
  try {
    const users = userManager.getAllUsers();
    const roles = userManager.getRoles();
    
    res.json({
      success: true,
      data: {
        totalUsers: users.length,
        activeUsers: users.filter(u => u.active).length,
        inactiveUsers: users.filter(u => !u.active).length,
        usersByRole: Object.keys(roles).reduce((acc, role) => {
          acc[role] = users.filter(u => u.role === role && u.active).length;
          return acc;
        }, {}),
        availableRoles: Object.keys(roles),
        sessionTimeout: userManager.getSessionTimeout(),
        security: {
          lockedAccounts: authService.getLockoutStats().lockedAccounts,
          suspiciousIPs: authService.getLockoutStats().suspiciousIPs,
          accountsWithFailedAttempts: authService.getLockoutStats().accountsWithFailedAttempts
        }
      }
    });
  } catch (error) {
    console.error('Error fetching system status:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch system status'
    });
  }
});

/**
 * Get account lockout information (admin only)
 * GET /admin/lockout/:uid
 */
router.get('/lockout/:uid', requireRoles('admin'), (req, res) => {
  try {
    const { uid } = req.params;

    const uidValidation = ValidationUtils.validateEmail(uid);
    if (!uidValidation.isValid) {
      return res.status(400).json({
        success: false,
        message: uidValidation.error
      });
    }

    const lockoutInfo = authService.getAccountLockoutInfo(uidValidation.sanitized);

    res.json({
      success: true,
      data: lockoutInfo
    });
  } catch (error) {
    console.error('Error fetching lockout info:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch lockout information'
    });
  }
});

/**
 * Unlock account (admin only)
 * POST /admin/unlock/:uid
 */
router.post('/unlock/:uid', requireRoles('admin'), (req, res) => {
  try {
    const { uid } = req.params;

    const uidValidation = ValidationUtils.validateEmail(uid);
    if (!uidValidation.isValid) {
      return res.status(400).json({
        success: false,
        message: uidValidation.error
      });
    }

    const success = authService.unlockAccount(uidValidation.sanitized);

    // Log admin action
    auditLogger.logAdminAction({
      adminUid: req.user.uid,
      action: 'UNLOCK_ACCOUNT',
      targetUid: uidValidation.sanitized,
      ip: req.ip || req.connection.remoteAddress,
      details: {
        wasLocked: success
      },
      success: true
    });

    if (success) {
      console.log(`✅ Admin ${req.user.uid} unlocked account: ${uidValidation.sanitized}`);
      res.json({
        success: true,
        message: 'Account unlocked successfully'
      });
    } else {
      res.json({
        success: true,
        message: 'Account was not locked'
      });
    }
  } catch (error) {
    console.error('Error unlocking account:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to unlock account'
    });
  }
});

/**
 * Get security statistics (admin only)
 * GET /admin/security-stats
 */
router.get('/security-stats', requireRoles('admin'), (req, res) => {
  try {
    const stats = authService.getLockoutStats();

    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('Error fetching security stats:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch security statistics'
    });
  }
});

/**
 * Get audit logs (admin only)
 * GET /admin/audit-logs
 */
router.get('/audit-logs', requireRoles('admin'), (req, res) => {
  try {
    const { startDate, endDate, logType = 'audit', limit = 100 } = req.query;

    let start = null;
    let end = null;

    if (startDate) {
      start = new Date(startDate);
      if (isNaN(start.getTime())) {
        return res.status(400).json({
          success: false,
          message: 'Invalid start date format'
        });
      }
    }

    if (endDate) {
      end = new Date(endDate);
      if (isNaN(end.getTime())) {
        return res.status(400).json({
          success: false,
          message: 'Invalid end date format'
        });
      }
    }

    const logs = auditLogger.getLogs(start, end, logType);
    const limitedLogs = logs.slice(0, parseInt(limit));

    res.json({
      success: true,
      data: {
        logs: limitedLogs,
        total: logs.length,
        returned: limitedLogs.length,
        filters: {
          startDate: start ? start.toISOString() : null,
          endDate: end ? end.toISOString() : null,
          logType: logType,
          limit: parseInt(limit)
        }
      }
    });
  } catch (error) {
    console.error('Error fetching audit logs:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch audit logs'
    });
  }
});

/**
 * Get security statistics (admin only)
 * GET /admin/security-statistics
 */
router.get('/security-statistics', requireRoles('admin'), (req, res) => {
  try {
    const { startDate, endDate } = req.query;

    let start = null;
    let end = null;

    if (startDate) {
      start = new Date(startDate);
      if (isNaN(start.getTime())) {
        return res.status(400).json({
          success: false,
          message: 'Invalid start date format'
        });
      }
    }

    if (endDate) {
      end = new Date(endDate);
      if (isNaN(end.getTime())) {
        return res.status(400).json({
          success: false,
          message: 'Invalid end date format'
        });
      }
    }

    const stats = auditLogger.getSecurityStats(start, end);

    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('Error fetching security statistics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch security statistics'
    });
  }
});

module.exports = router;

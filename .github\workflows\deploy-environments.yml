name: Deploy to Environments

# Temporarily disabled for test corrections - manual trigger only
on:
  workflow_dispatch:
    inputs:
      version:
        description: 'Version to deploy'
        required: true
  pull_request:
    branches: [ main ]

jobs:
  deploy-staging:
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    environment:
      name: staging
      url: https://smarttest-staging.github.io
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Build for staging
      run: |
        npm run build
        echo "STAGING ENVIRONMENT" > ./frontend/server/public/environment.txt
      env:
        NODE_ENV: staging

    - name: Deploy to GitHub Pages (Staging)
      uses: peaceiris/actions-gh-pages@v3
      with:
        github_token: ${{ secrets.GITHUB_TOKEN }}
        publish_dir: ./frontend/server/public
        destination_dir: staging

  deploy-production:
    runs-on: ubuntu-latest
    if: startsWith(github.ref, 'refs/tags/v')
    environment:
      name: production
      url: https://smarttest.github.io
    needs: [deploy-staging]
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Build for production
      run: |
        npm run build
        echo "PRODUCTION ENVIRONMENT" > ./frontend/server/public/environment.txt
      env:
        NODE_ENV: production

    - name: Run production tests
      run: npm run test:production --if-present

    - name: Deploy to GitHub Pages (Production)
      uses: peaceiris/actions-gh-pages@v3
      with:
        github_token: ${{ secrets.GITHUB_TOKEN }}
        publish_dir: ./frontend/server/public

    - name: Create deployment status
      uses: chrnorm/deployment-action@v2
      with:
        token: ${{ secrets.GITHUB_TOKEN }}
        environment-url: https://smarttest.github.io
        deployment-status: success

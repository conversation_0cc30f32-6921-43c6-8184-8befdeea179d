# SmartTest API Authentication

This document describes the authentication mechanisms used by the SmartTest API server.

## Authentication Methods

The SmartTest API server uses two authentication mechanisms:

1. **Local Authentication**: A simple username and password authentication mechanism for local API endpoints.
2. **External Authentication**: Cookie-based authentication for external API endpoints on port 9080.

## Local Authentication

Local authentication uses a simple username and password mechanism. Authentication credentials can be provided in two ways:

1. As query parameters in GET requests
2. In the request body for POST requests

## Authentication Parameters

The following parameters are used for authentication:

- `uid`: User ID (username or email)
- `password`: User password

## Example: GET Request

```
GET /local/test-cases?uid=test_user&password=password
```

## Example: POST Request

```json
{
  "uid": "test_user",
  "password": "password",
  "other_parameter": "value"
}
```

## Local Authentication Flow

1. The client sends a request with authentication credentials
2. The `validateCredentials` middleware extracts the credentials from the request
3. The middleware validates the credentials against the configured values
4. If the credentials are valid, the middleware attaches the user information to the request object and allows the request to proceed
5. If the credentials are invalid, the middleware returns a 401 Unauthorized response

## External Authentication

External authentication is used for API endpoints that interact with the external API on port 9080. This authentication mechanism uses cookies to maintain session state with the external API.

### External Authentication Parameters

The same parameters are used for external authentication as for local authentication:

- `uid`: User ID (username or email)
- `password`: User password

### External Authentication Flow

1. The client sends a request with authentication credentials
2. The service extracts the credentials from the request
3. The service uses the credentials to log in to the external API using the `/AutoRun/Login` endpoint
4. The external API returns a `JSESSIONID` cookie
5. The service stores the cookie and uses it for subsequent requests to the external API
6. The service forwards the response from the external API to the client

## Authentication Response

### Successful Authentication

If authentication is successful, the request proceeds to the requested endpoint.

### Failed Authentication

If authentication fails, the server returns a 401 Unauthorized response:

```json
{
  "success": false,
  "message": "Authentication failed. Invalid credentials."
}
```

If authentication credentials are missing, the server returns a 401 Unauthorized response:

```json
{
  "success": false,
  "message": "Authentication failed. Please provide uid and password."
}
```

## Development Mode

In development mode, authentication can be skipped by setting the `NODE_ENV` environment variable to `development` and the `SKIP_AUTH` environment variable to `true`. This is useful for testing and development purposes.

## Configuring Authentication

Authentication credentials are configured in the `.env` file or as environment variables:

```
TEST_USER=test_user
TEST_PASSWORD=password
```

Alternative credentials can be configured in the `app-config.js` file:

```javascript
const ALT_TEST_USERS = [
  { uid: '<EMAIL>', password: 'test' },
  { uid: '<EMAIL>', password: 'password' }
];
```

## Cookie Management

The cookie authentication service manages cookies for external API authentication:

- Cookies are cached in memory to avoid unnecessary login requests
- Cookies expire after 30 minutes
- If a cookie expires, the service automatically logs in again to get a new cookie

## Security Considerations

- The local authentication mechanism is simple and suitable for development and testing purposes
- The external authentication mechanism uses cookies, which are vulnerable to cross-site request forgery (CSRF) attacks
- For production use, consider implementing a more secure authentication mechanism, such as JWT or OAuth
- Always use HTTPS in production to encrypt authentication credentials and cookies in transit
- Consider implementing rate limiting to prevent brute force attacks
- Consider implementing account lockout after multiple failed authentication attempts
- Consider implementing CSRF protection for cookie-based authentication

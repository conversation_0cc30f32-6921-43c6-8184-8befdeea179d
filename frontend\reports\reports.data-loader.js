function showLoadingIndicators(isIncrementalRefresh = false) {
    console.log('Showing loading indicators');

    // Always show the main loading indicator (the existing one from HTML)
    const loadingIndicator = document.getElementById('loading-indicator');
    if (loadingIndicator) {
        loadingIndicator.style.display = 'block';
        console.log('Showing main loading indicator');
    }

    // For the table overlay
    if (elements.tableLoadingOverlay) {
        // Make sure the overlay exists in the DOM
        elements.tableLoadingOverlay = createTableLoadingOverlay();

        // Show it with appropriate opacity based on refresh type
        if (isIncrementalRefresh) {
            elements.tableLoadingOverlay.style.backgroundColor = 'rgba(255, 255, 255, 0.5)';
        } else {
            elements.tableLoadingOverlay.style.backgroundColor = 'rgba(255, 255, 255, 0.8)';
        }

        elements.tableLoadingOverlay.style.display = 'flex';
        console.log('Showing table loading overlay');
    }

    // Start the progress bar animation
    if (elements.progressBar) {
        // Make sure the progress bar exists in the DOM
        elements.progressBar = createProgressBar();

        elements.progressBar.style.display = 'block';
        elements.progressBar.style.width = '0%';

        // Simulate progress (goes to 90% then completes when data is loaded)
        setTimeout(() => { elements.progressBar.style.width = '40%'; }, 200);
        setTimeout(() => { elements.progressBar.style.width = '70%'; }, 600);
        setTimeout(() => { elements.progressBar.style.width = '90%'; }, 1200);
        console.log('Animating progress bar');
    }

    // Apply pulse animation to loading cell if it exists
    const loadingCell = document.querySelector('td.text-center');
    if (loadingCell) {
        // Add a pulsing animation
        loadingCell.innerHTML = '<div class="spinner-border spinner-border-sm text-primary me-2" role="status"></div> Loading data...';

        // Add animation if not already applied
        if (!document.getElementById('loading-animations-style')) {
            const style = document.createElement('style');
            style.id = 'loading-animations-style';
            style.textContent = `
                @keyframes pulse {
                    0% { opacity: 0.6; }
                    50% { opacity: 1; }
                    100% { opacity: 0.6; }
                }
                .loading-pulse {
                    animation: pulse 1.5s infinite ease-in-out;
                }
            `;
            document.head.appendChild(style);
        }

        loadingCell.classList.add('loading-pulse');
        console.log('Enhanced loading cell with animation');
    }
}

// Hide loading indicators
function hideLoadingIndicators() {
    console.log('Hiding loading indicators');

    // Hide the main loading indicator
    const loadingIndicator = document.getElementById('loading-indicator');
    if (loadingIndicator) {
        loadingIndicator.style.display = 'none';
    }

    // Hide the table overlay
    if (elements.tableLoadingOverlay) {
        // Complete with fade out effect
        elements.tableLoadingOverlay.style.opacity = '0';
        setTimeout(() => {
            elements.tableLoadingOverlay.style.display = 'none';
        }, 300);
    }

    // Complete the progress bar and then hide it
    if (elements.progressBar) {
        elements.progressBar.style.width = '100%';
        setTimeout(() => {
            elements.progressBar.style.display = 'none';
        }, 500);
    }

    // Remove animation from loading cell
    const loadingCell = document.querySelector('td.text-center.loading-pulse');
    if (loadingCell) {
        loadingCell.classList.remove('loading-pulse');
    }
}

// Load reports data from the server
function loadReportsData(options = {}) {
    const {
        isIncrementalRefresh = false,
        forceFullReload = false,
        showLoading = true
    } = options;

    let timeRangeToSend = currentState.activeTimeRange;
    if (options.timeRange) { // If a specific timeRange is passed (e.g., from event listener)
        timeRangeToSend = options.timeRange;
        currentState.activeTimeRange = options.timeRange; // Update activeTimeRange
    }

    if (showLoading) {
        // Use our enhanced loading indicators
        showLoadingIndicators(isIncrementalRefresh);
    }

    const apiParams = {
        time_range: typeof timeRangeToSend === 'object' ? JSON.stringify(timeRangeToSend) : timeRangeToSend
    };

    if (isIncrementalRefresh && currentState.lastSeenTsnId) {
        apiParams.since_tsn_id = currentState.lastSeenTsnId;
    } else if (forceFullReload || !isIncrementalRefresh) {
        // For full reload, reset lastSeenTsnId to ensure all data for the range is fetched
        currentState.lastSeenTsnId = null;
    }
    // Note: We are NOT sending a client-side pagination `limit` here for full range fetches.
    // The backend will return all records for the given time_range.

    console.log(`Loading reports with params:`, apiParams);

    if (window.apiService) {
        if (!window.apiService.credentials.uid) {
            window.apiService.loadCredentials();
        }

        window.apiService.getTestReports(apiParams)
            .then(apiResponse => {
                // console.log('API Response:', apiResponse); // Commented out to prevent excessive logging
                let reportsToProcess = null;
                let success = false;
                let totalRecords = 0;
                let highestTsnId = null;
                let rawErrorMessage = null;

                if (apiResponse && apiResponse.success === true) { // Standard object response {success: true, reports: [], ...}
                    success = true;
                    reportsToProcess = apiResponse.reports || apiResponse.newRuns || [];
                    totalRecords = apiResponse.totalRecords || reportsToProcess.length;
                    highestTsnId = apiResponse.highestTsnIdInResponse || apiResponse.latestTsnIdInDelta;
                } else if (Array.isArray(apiResponse)) { // Direct array response [...]
                    success = true;
                    reportsToProcess = apiResponse;
                    totalRecords = apiResponse.length;
                    // highestTsnId might not be available with a direct array response, defaults to null
                } else { // Error or unexpected format
                    if (apiResponse && apiResponse.message) { // e.g. {success: false, message: "..."}
                        rawErrorMessage = apiResponse.message;
                    } else {
                        rawErrorMessage = 'Failed to load reports. Bad or unexpected API response format.';
                        console.error('Error or unsuccessful/malformed response from API:', apiResponse);
                    }
                }

                if (!success) {
                    displayError(rawErrorMessage || 'Failed to load reports due to an unspecified error.');
                    currentState.reports = []; // Clear reports on error
                    currentState.totalRecordsForActiveTimeRange = 0;
                    currentState.lastSeenTsnId = null;
                    displayReports([]); // Update table to show error or no data
                    updateCounters();
                    return;
                }

                // If we reach here, success is true and reportsToProcess is set
                if (isIncrementalRefresh) {
                    // Filter out duplicates based on tsn_id before merging
                    const existingTsnIds = new Set(currentState.reports.map(r => r.tsn_id));
                    const newUniqueReports = reportsToProcess.filter(r => !existingTsnIds.has(r.tsn_id));

                    currentState.reports = [...newUniqueReports, ...currentState.reports];
                    // Sort by start_time descending after merge if necessary, or rely on API order for new runs
                    currentState.reports.sort((a, b) => new Date(b.start_time) - new Date(a.start_time));

                    console.log(`Incremental refresh: Added ${newUniqueReports.length} new reports. Total now: ${currentState.reports.length}`);
                } else {
                    currentState.reports = reportsToProcess;
                }

                currentState.totalRecordsForActiveTimeRange = totalRecords; // This might be an estimate if API doesn't provide it for direct array
                if (highestTsnId) { // Only update if we got one from an object response
                    currentState.lastSeenTsnId = highestTsnId;
                }

                // Display the processed reports and update counters
                displayReports(currentState.reports);
                updateCounters();
                // Hide loading indicators before updating the UI
                hideLoadingIndicators();

                // Apply fade-in effect to the new data
                const tableBody = elements.reportsTable.querySelector('tbody');
                if (tableBody) {
                    tableBody.style.opacity = '0';
                    setTimeout(() => {
                        tableBody.style.transition = 'opacity 0.3s ease-in-out';
                        tableBody.style.opacity = '1';
                    }, 50);
                }

                updateCharts(currentState.reports); // Assuming updateCharts uses currentState.reports
                updateRefreshStatus();
            })
            .catch(error => {
                // Hide loading indicators even on error
                hideLoadingIndicators();

                console.error('Error loading reports:', error);
                displayError('Failed to load reports. Please try again later.');
                currentState.reports = [];
                currentState.totalRecordsForActiveTimeRange = 0;
                currentState.lastSeenTsnId = null;
                displayReports([]);
                updateCounters();
            })
            .finally(() => {
                if (showLoading) {
                    hideLoadingIndicators();
                }
                updateRefreshStatus();
            });
    } else {
        // Check for API service with more resilient approach
        if (!window.apiService) {
            console.warn('API Service not available yet, attempting to initialize connection...');
            // Wait briefly and retry once to see if API service becomes available
            setTimeout(() => {
                if (window.apiService) {
                    console.log('API Service now available, proceeding with data load');
                    loadReportsData(options);
                } else {
                    console.error('API Service not available after retry');
                    displayError('Failed to load reports data. API service not available. Please refresh the page.');
                    hideLoadingIndicators();
                }
            }, 500);
            return;
        }
    }
}

/**
 * Security Testing Routes
 * Provides endpoints for testing security features and headers
 */

const express = require('express');
const router = express.Router();

/**
 * Security headers test endpoint
 * GET /security-test/headers
 */
router.get('/headers', (req, res) => {
  res.json({
    success: true,
    message: 'Security headers test endpoint',
    timestamp: new Date().toISOString(),
    headers: {
      'Content-Security-Policy': res.getHeader('Content-Security-Policy'),
      'X-Frame-Options': res.getHeader('X-Frame-Options'),
      'X-Content-Type-Options': res.getHeader('X-Content-Type-Options'),
      'X-XSS-Protection': res.getHeader('X-XSS-Protection'),
      'Referrer-Policy': res.getHeader('Referrer-Policy'),
      'Permissions-Policy': res.getHeader('Permissions-Policy'),
      'Strict-Transport-Security': res.getHeader('Strict-Transport-Security'),
      'Cross-Origin-Embedder-Policy': res.getHeader('Cross-Origin-Embedder-Policy'),
      'Cross-Origin-Opener-Policy': res.getHeader('Cross-Origin-Opener-Policy'),
      'Cross-Origin-Resource-Policy': res.getHeader('Cross-Origin-Resource-Policy'),
      'X-SmartTest-Security': res.getHeader('X-SmartTest-Security')
    },
    environment: process.env.NODE_ENV || 'development'
  });
});

/**
 * CSRF protection test endpoint
 * POST /security-test/csrf
 */
router.post('/csrf', (req, res) => {
  res.json({
    success: true,
    message: 'CSRF protection test passed',
    timestamp: new Date().toISOString(),
    csrfToken: req.headers['x-csrf-token'] || 'not-provided'
  });
});

/**
 * Rate limiting test endpoint
 * GET /security-test/rate-limit
 */
router.get('/rate-limit', (req, res) => {
  res.json({
    success: true,
    message: 'Rate limiting test endpoint',
    timestamp: new Date().toISOString(),
    ip: req.ip || req.connection.remoteAddress,
    rateLimitHeaders: {
      'X-RateLimit-Limit': res.getHeader('X-RateLimit-Limit'),
      'X-RateLimit-Remaining': res.getHeader('X-RateLimit-Remaining'),
      'X-RateLimit-Reset': res.getHeader('X-RateLimit-Reset')
    }
  });
});

/**
 * Authentication test endpoint
 * GET /security-test/auth
 */
router.get('/auth', (req, res) => {
  const isAuthenticated = !!req.user;
  
  res.json({
    success: true,
    message: 'Authentication test endpoint',
    timestamp: new Date().toISOString(),
    authenticated: isAuthenticated,
    user: isAuthenticated ? {
      uid: req.user.uid,
      role: req.user.role,
      permissions: req.permissions || []
    } : null,
    sessionInfo: req.session ? {
      id: req.session.id,
      lastActivity: req.session.lastActivity
    } : null
  });
});

/**
 * Security scan endpoint - provides comprehensive security status
 * GET /security-test/scan
 */
router.get('/scan', (req, res) => {
  const securityStatus = {
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development',
    
    // Security headers check
    headers: {
      csp: !!res.getHeader('Content-Security-Policy'),
      frameOptions: !!res.getHeader('X-Frame-Options'),
      contentTypeOptions: !!res.getHeader('X-Content-Type-Options'),
      xssProtection: !!res.getHeader('X-XSS-Protection'),
      referrerPolicy: !!res.getHeader('Referrer-Policy'),
      permissionsPolicy: !!res.getHeader('Permissions-Policy'),
      hsts: !!res.getHeader('Strict-Transport-Security'),
      coep: !!res.getHeader('Cross-Origin-Embedder-Policy'),
      coop: !!res.getHeader('Cross-Origin-Opener-Policy'),
      corp: !!res.getHeader('Cross-Origin-Resource-Policy')
    },
    
    // Authentication status
    authentication: {
      enabled: true,
      jwtTokens: true,
      sessionManagement: true,
      csrfProtection: true
    },
    
    // Rate limiting status
    rateLimiting: {
      enabled: true,
      ipBased: true,
      accountLockout: true
    },
    
    // Security features
    features: {
      inputValidation: true,
      sqlInjectionProtection: true,
      xssProtection: true,
      securityLogging: true,
      accountLockout: true,
      progressiveLockout: true
    },
    
    // Recommendations
    recommendations: []
  };

  // Add recommendations based on environment
  if (process.env.NODE_ENV !== 'production') {
    securityStatus.recommendations.push(
      'Enable HTTPS in production',
      'Set strong session secrets',
      'Configure proper CORS policies',
      'Enable security monitoring'
    );
  }

  // Check for potential issues
  if (!res.getHeader('Strict-Transport-Security') && process.env.NODE_ENV === 'production') {
    securityStatus.recommendations.push('Enable HSTS for production');
  }

  res.json({
    success: true,
    message: 'Security scan completed',
    data: securityStatus
  });
});

/**
 * Security configuration endpoint (admin only)
 * GET /security-test/config
 */
router.get('/config', (req, res) => {
  // This would typically require admin authentication
  const config = {
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development',
    
    // Security configuration
    security: {
      sessionTimeout: process.env.SESSION_TIMEOUT || 3600,
      maxFailedAttempts: 5,
      lockoutDuration: 15 * 60, // 15 minutes
      rateLimitWindow: 15 * 60, // 15 minutes
      rateLimitMax: 100,
      csrfEnabled: true,
      securityHeadersEnabled: true
    },
    
    // Feature flags
    features: {
      accountLockout: true,
      progressiveLockout: true,
      ipBlocking: true,
      suspiciousActivityDetection: true,
      securityLogging: true,
      auditTrail: true
    }
  };

  res.json({
    success: true,
    message: 'Security configuration',
    data: config
  });
});

module.exports = router;

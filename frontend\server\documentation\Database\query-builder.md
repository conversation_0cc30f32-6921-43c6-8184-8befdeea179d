# SQL Query Builder

## Overview

The SQL Query Builder provides a fluent interface for constructing SQL queries. It allows you to build complex queries programmatically, making your code more readable and maintainable. The query builder also supports AI-driven query construction, which will be useful for future AI integration.

## Basic Usage

```javascript
const QueryBuilder = require('./utils/query-builder');

// Create a query builder
const queryBuilder = new QueryBuilder();

// Build a simple query
queryBuilder.select('test_case', ['tc_id', 'name', 'status']);
queryBuilder.where('status', '=', 'active');
queryBuilder.limit(10);

// Build the SQL query
const { sql, params } = queryBuilder.build();
// sql: "SELECT tc_id, name, status FROM test_case WHERE status = ? LIMIT ?"
// params: ["active", 10]
```

## Query Builder Methods

### Select

```javascript
// Select all columns from a table
queryBuilder.select('test_case');

// Select specific columns from a table
queryBuilder.select('test_case', ['tc_id', 'name', 'status']);

// Select columns with aliases
queryBuilder.select('test_case tc', ['tc.tc_id', 'tc.name', 'tc.status']);
```

### Where

```javascript
// Add a simple WHERE condition
queryBuilder.where('status', '=', 'active');

// Add a WHERE condition with a NULL value
queryBuilder.where('end_ts', 'IS', null);

// Add multiple WHERE conditions (combined with AND)
queryBuilder.where('status', '=', 'active');
queryBuilder.where('tc_id', '>', 1000);
```

### Join

```javascript
// Add an INNER JOIN
queryBuilder.join('test_case_group tcg', 'tc.tc_id = tcg.tc_id');

// Add a LEFT JOIN
queryBuilder.leftJoin('test_case_group tcg', 'tc.tc_id = tcg.tc_id');

// Add a RIGHT JOIN
queryBuilder.rightJoin('test_case_group tcg', 'tc.tc_id = tcg.tc_id');

// Add a JOIN with a specific type
queryBuilder.join('test_case_group tcg', 'tc.tc_id = tcg.tc_id', 'LEFT');
```

### Order By

```javascript
// Add an ORDER BY clause (ascending)
queryBuilder.orderBy('tc_id');

// Add an ORDER BY clause (descending)
queryBuilder.orderBy('tc_id', 'DESC');
```

### Group By

```javascript
// Add a GROUP BY clause with a single column
queryBuilder.groupBy('tc_id');

// Add a GROUP BY clause with multiple columns
queryBuilder.groupBy(['tc_id', 'status']);
```

### Having

```javascript
// Add a HAVING clause
queryBuilder.having('COUNT(*) > 5');
```

### Limit

```javascript
// Add a LIMIT clause
queryBuilder.limit(10);
```

## Complex Queries

The query builder can be used to construct complex queries:

```javascript
const queryBuilder = new QueryBuilder();

// Select columns from multiple tables
queryBuilder.select('test_result r', [
  'r.tsn_id', 
  'r.tc_id', 
  'r.outcome', 
  'r.creation_time',
  'o.txt'
]);

// Join with another table
queryBuilder.join('output o', 'r.cnt = o.cnt');

// Add WHERE conditions
queryBuilder.where('r.tsn_id', '=', '12345');
queryBuilder.where('r.outcome', '=', 'P');

// Add GROUP BY
queryBuilder.groupBy('r.tc_id');

// Add HAVING
queryBuilder.having('COUNT(*) > 1');

// Add ORDER BY
queryBuilder.orderBy('r.creation_time', 'ASC');

// Add LIMIT
queryBuilder.limit(100);

// Build the SQL query
const { sql, params } = queryBuilder.build();
```

## AI-Driven Query Construction

The query builder supports AI-driven query construction through two methods:

### From Specification

```javascript
// Create a query builder from a structured query object
const queryBuilder = QueryBuilder.fromSpec({
  type: 'SELECT',
  table: 'test_case',
  columns: ['tc_id', 'name', 'status'],
  where: [
    { column: 'status', operator: '=', value: 'active' },
    { column: 'tc_id', operator: '>', value: 1000 }
  ],
  joins: [
    { table: 'test_case_group tcg', condition: 'tc.tc_id = tcg.tc_id', type: 'INNER' }
  ],
  groupBy: ['tc_id'],
  having: ['COUNT(*) > 1'],
  orderBy: { column: 'tc_id', direction: 'DESC' },
  limit: 10
});

// Build the SQL query
const { sql, params } = queryBuilder.build();
```

### From Natural Language Description

```javascript
// Create a query builder from a natural language description
const queryBuilder = QueryBuilder.fromDescription('Get all active test cases with ID greater than 1000');

// Build the SQL query
const { sql, params } = queryBuilder.build();
```

> Note: The `fromDescription` method is a placeholder for future AI integration. Currently, it returns a basic query builder.

## Implementation Details

The query builder is implemented in `utils/query-builder.js`. It uses a fluent interface pattern, where each method returns the query builder instance, allowing method chaining.

### Query Object Structure

The query builder maintains an internal query object with the following structure:

```javascript
{
  type: 'SELECT',
  table: null,
  columns: ['*'],
  where: [],
  orderBy: null,
  limit: null,
  joins: [],
  groupBy: null,
  having: []
}
```

### Build Method

The `build` method constructs the SQL query and parameters from the query object:

```javascript
build() {
  const params = [];
  
  // Build SELECT clause
  let sql = `${this.query.type} ${this.query.columns.join(', ')} FROM ${this.query.table}`;
  
  // Add joins
  if (this.query.joins.length > 0) {
    sql += ' ' + this.query.joins.map(join => 
      `${join.type} JOIN ${join.table} ON ${join.condition}`
    ).join(' ');
  }
  
  // Add where conditions
  if (this.query.where.length > 0) {
    sql += ' WHERE ' + this.query.where.map(condition => {
      if (condition.value === null) {
        if (condition.operator.toUpperCase() === 'IS' || condition.operator.toUpperCase() === 'IS NOT') {
          return `${condition.column} ${condition.operator} NULL`;
        } else {
          return `${condition.column} IS NULL`;
        }
      } else {
        params.push(condition.value);
        return `${condition.column} ${condition.operator} ?`;
      }
    }).join(' AND ');
  }
  
  // Add group by
  if (this.query.groupBy) {
    sql += ` GROUP BY ${this.query.groupBy.join(', ')}`;
  }
  
  // Add having
  if (this.query.having.length > 0) {
    sql += ` HAVING ${this.query.having.join(' AND ')}`;
  }
  
  // Add order by
  if (this.query.orderBy) {
    sql += ` ORDER BY ${this.query.orderBy.column} ${this.query.orderBy.direction}`;
  }
  
  // Add limit
  if (this.query.limit) {
    sql += ' LIMIT ?';
    params.push(this.query.limit);
  }
  
  return { sql, params };
}
```

## Best Practices

1. **Use Parameterized Queries**: Always use the query builder's parameter substitution to prevent SQL injection.
2. **Chain Methods**: Use method chaining for better readability.
3. **Be Specific**: Select only the columns you need to improve performance.
4. **Use Joins Carefully**: Joins can be expensive, so use them judiciously.
5. **Limit Results**: Always limit the number of rows returned to improve performance.

## Examples

### Example 1: Get Test Cases

```javascript
const queryBuilder = new QueryBuilder();
queryBuilder.select('test_case', ['tc_id', 'name', 'status']);
queryBuilder.where('status', '=', 'active');
queryBuilder.orderBy('tc_id', 'DESC');
queryBuilder.limit(10);

const { sql, params } = queryBuilder.build();
// sql: "SELECT tc_id, name, status FROM test_case WHERE status = ? ORDER BY tc_id DESC LIMIT ?"
// params: ["active", 10]
```

### Example 2: Get Test Results with Output

```javascript
const queryBuilder = new QueryBuilder();
queryBuilder.select('test_result r', [
  'r.tsn_id', 
  'r.tc_id', 
  'r.outcome', 
  'r.creation_time',
  'o.txt'
]);
queryBuilder.join('output o', 'r.cnt = o.cnt');
queryBuilder.where('r.tsn_id', '=', '12345');
queryBuilder.orderBy('r.creation_time', 'ASC');

const { sql, params } = queryBuilder.build();
// sql: "SELECT r.tsn_id, r.tc_id, r.outcome, r.creation_time, o.txt FROM test_result r INNER JOIN output o ON r.cnt = o.cnt WHERE r.tsn_id = ? ORDER BY r.creation_time ASC"
// params: ["12345"]
```

### Example 3: Get Test Case Count by Status

```javascript
const queryBuilder = new QueryBuilder();
queryBuilder.select('test_case', [
  'status',
  'COUNT(*) as count'
]);
queryBuilder.groupBy('status');
queryBuilder.orderBy('count', 'DESC');

const { sql, params } = queryBuilder.build();
// sql: "SELECT status, COUNT(*) as count FROM test_case GROUP BY status ORDER BY count DESC"
// params: []
```

### Example 4: Get Active Tests for a User

```javascript
const queryBuilder = new QueryBuilder();
queryBuilder.select('test_session s', [
  's.tsn_id', 
  'COALESCE(s.tc_id, 0) as tc_id', 
  's.uid as initiator_user', 
  's.start_ts as creation_time'
]);
queryBuilder.where('s.end_ts', 'IS', null);
queryBuilder.where('s.uid', '=', 'test_user');
queryBuilder.orderBy('s.start_ts', 'DESC');
queryBuilder.limit(20);

const { sql, params } = queryBuilder.build();
// sql: "SELECT s.tsn_id, COALESCE(s.tc_id, 0) as tc_id, s.uid as initiator_user, s.start_ts as creation_time FROM test_session s WHERE s.end_ts IS NULL AND s.uid = ? ORDER BY s.start_ts DESC LIMIT ?"
// params: ["test_user", 20]
```

## Conclusion

The SQL Query Builder provides a powerful and flexible way to construct SQL queries programmatically. It supports a wide range of SQL features and can be used to build complex queries with ease. The AI-driven query construction capabilities provide a foundation for future AI integration.

name: Automated Release

# Temporarily disabled for test corrections - manual trigger only
on:
  workflow_dispatch:
    inputs:
      release_type:
        description: 'Release type'
        required: true
        default: 'patch'
        type: choice
        options:
        - patch
        - minor
        - major

jobs:
  release:
    runs-on: ubuntu-latest
    # Manual trigger only - no conditional needed
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0
        token: ${{ secrets.GITHUB_TOKEN }}

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Run tests
      run: npm test

    - name: Build application
      run: npm run build

    - name: Generate changelog
      id: changelog
      uses: TriPSs/conventional-changelog-action@v3
      with:
        github-token: ${{ secrets.GITHUB_TOKEN }}
        output-file: 'CHANGELOG.md'
        release-count: '10'
        version-file: 'package.json'
        version-path: 'version'
        create-summary: 'true'

    - name: Create Release
      id: create_release
      uses: actions/create-release@v1
      if: ${{ steps.changelog.outputs.skipped == 'false' }}
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        tag_name: ${{ steps.changelog.outputs.tag }}
        release_name: Release ${{ steps.changelog.outputs.tag }}
        body: ${{ steps.changelog.outputs.clean_changelog }}
        draft: false
        prerelease: false

    - name: Upload Release Assets
      if: ${{ steps.changelog.outputs.skipped == 'false' }}
      uses: actions/upload-release-asset@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        upload_url: ${{ steps.create_release.outputs.upload_url }}
        asset_path: ./smarttest-${{ steps.changelog.outputs.version }}.tar.gz
        asset_name: smarttest-${{ steps.changelog.outputs.version }}.tar.gz
        asset_content_type: application/gzip

    - name: Notify on Slack/Discord (Optional)
      if: ${{ steps.changelog.outputs.skipped == 'false' }}
      run: |
        echo "🚀 SmartTest ${{ steps.changelog.outputs.tag }} has been released!"
        echo "Changelog: ${{ steps.changelog.outputs.clean_changelog }}"

const mysql = require('mysql2/promise');

async function checkTestResults() {
  let connection;
  try {
    console.log('Connecting to database to check test 17977 results...');
    connection = await mysql.createConnection({
      host: 'mprts-qa02.lab.wagerworks.com',
      user: 'rgs_rw',
      password: 'rgs_rw',
      database: 'rgs_test'
    });
    
    console.log('Connected successfully\n');
    
    // Check test session details
    console.log('=== TEST SESSION 17977 DETAILS ===');
    const [sessionData] = await connection.query(`
      SELECT tsn_id, uid, start_ts, end_ts, error, tc_id, ts_id, pj_id 
      FROM test_session 
      WHERE tsn_id = 17977
    `);
    
    if (sessionData.length === 0) {
      console.log('❌ Test session 17977 not found');
      return;
    }
    
    const session = sessionData[0];
    console.log('Session data:');
    console.log(JSON.stringify(session, null, 2));
    
    // Parse the error field
    const errorField = session.error;
    console.log(`\nError field: "${errorField}"`);
    
    if (errorField && typeof errorField === 'string') {
      const errorMatch = errorField.match(/^(\d+):(\d+)\/(\d+)$/);
      if (errorMatch) {
        const failed = parseInt(errorMatch[1], 10);
        const passed = parseInt(errorMatch[2], 10);
        const total = parseInt(errorMatch[3], 10);
        
        console.log(`Parsed results: ${failed} failed, ${passed} passed, ${total} total`);
        
        const expectedStatus = failed > 0 ? 'failed' : (passed > 0 ? 'passed' : 'completed');
        console.log(`Expected status: ${expectedStatus}`);
      } else {
        console.log('Error field does not match expected format');
      }
    }
    
    // Check individual test results
    console.log('\n=== INDIVIDUAL TEST RESULTS ===');

    // First, check what columns are available
    const [columns] = await connection.query(`
      DESCRIBE test_result
    `);
    console.log('Available columns in test_result table:');
    columns.forEach(col => console.log(`  - ${col.Field} (${col.Type})`));

    const [testResults] = await connection.query(`
      SELECT tr.*, tc.name as test_case_name
      FROM test_result tr
      LEFT JOIN test_case tc ON tr.tc_id = tc.tc_id
      WHERE tr.tsn_id = 17977
      LIMIT 5
    `);
    
    if (testResults.length === 0) {
      console.log('No individual test results found');
    } else {
      console.log(`Found ${testResults.length} test results:`);
      
      let passedCount = 0;
      let failedCount = 0;
      
      testResults.forEach((result, i) => {
        const outcome = result.outcome === 'P' ? 'PASSED' : (result.outcome === 'F' ? 'FAILED' : result.outcome);
        console.log(`  ${i+1}. ${result.test_case_name || `TC ${result.tc_id}`}: ${outcome}`);
        
        if (result.outcome === 'P') passedCount++;
        else if (result.outcome === 'F') failedCount++;
      });
      
      console.log(`\nSummary: ${passedCount} passed, ${failedCount} failed`);
      
      // Verify against error field
      if (errorField) {
        const errorMatch = errorField.match(/^(\d+):(\d+)\/(\d+)$/);
        if (errorMatch) {
          const errorFailed = parseInt(errorMatch[1], 10);
          const errorPassed = parseInt(errorMatch[2], 10);
          
          const countsMatch = (failedCount === errorFailed && passedCount === errorPassed);
          console.log(`Error field matches individual results: ${countsMatch ? '✅' : '❌'}`);
          
          if (!countsMatch) {
            console.log(`  Error field: ${errorFailed} failed, ${errorPassed} passed`);
            console.log(`  Individual results: ${failedCount} failed, ${passedCount} passed`);
          }
        }
      }
    }
    
    // Check test suite details
    console.log('\n=== TEST SUITE DETAILS ===');
    const [suiteData] = await connection.query(`
      SELECT ts_id, name, comments
      FROM test_suite 
      WHERE ts_id = ?
    `, [session.ts_id]);
    
    if (suiteData.length > 0) {
      console.log('Test suite:', suiteData[0]);
    } else {
      console.log('Test suite details not found');
    }
    
    console.log('\n=== CONCLUSION ===');
    console.log('✅ Database connection works');
    console.log('✅ Test session data retrieved');
    console.log('✅ Error field format confirmed');
    console.log('✅ Individual test results available');
    console.log('✅ Status parsing logic should work correctly');
    
  } catch (error) {
    console.error('❌ Database operation error:', error);
  } finally {
    if (connection) {
      console.log('\nClosing connection');
      await connection.end();
    }
  }
}

checkTestResults().catch(console.error);

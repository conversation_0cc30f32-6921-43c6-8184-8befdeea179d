# SmartTest External API Integration

This document describes the integration with external APIs used by the SmartTest API server.

## Overview

The SmartTest API server integrates with external APIs to perform various operations, such as running tests, stopping tests, and retrieving test results. The external APIs are hosted on two different ports:

1. **Port 5080 Endpoints**: These endpoints use form data authentication with `uid` and `password` parameters.
2. **Port 9080 Endpoints**: These endpoints use cookie-based authentication with a `JSESSIONID` cookie.

## External API Endpoints

### Port 5080 Endpoints

#### Run Test Case / Test Suite

```
POST /AutoRun/CaseRunner
```

Executes a test case or test suite.

**Request Parameters:**

- `uid` (required): User ID
- `password` (required): Password
- `tc_id` or `ts_id` (required): Test case ID or test suite ID
- `envir` (required): Environment (e.g., qa02)
- `shell_host` (required): Shell host (e.g., jps-qa10-app01)

**Response:**

HTML response containing the test session ID (tsn_id) in the format:
```
Your test session id: 12345
```

### Port 9080 Endpoints

#### Login

```
POST /AutoRun/Login
```

Authenticates a user and returns a JSESSIONID cookie.

**Request Parameters:**

- `uid` (required): User ID
- `password` (required): Password

**Response:**

HTTP 302 redirect with a `Set-Cookie` header containing the JSESSIONID cookie.

#### Stop Test

```
POST /AutoRun/RemoveSession
```

Stops a running test.

**Request Parameters:**

- `tsn_id` (required): Test session ID

**Required Headers:**

- `Cookie: JSESSIONID=<jsessionid>`

**Response:**

Text response containing "Removed" if successful.

#### Get Test Report Summary

```
GET /AutoRun/ReportSummary
```

Retrieves a summary of a test run.

**Request Parameters:**

- `tsn_id` (required): Test session ID

**Required Headers:**

- `Cookie: JSESSIONID=<jsessionid>`

**Response:**

HTML response containing test summary information, including:
- Start Time
- End Time
- Case(s) passed
- Case(s) failed
- Overall status (PASS/FAIL)

#### Get Test Report Details

```
GET /AutoRun/ReportDetails
```

Retrieves detailed information about a test run.

**Request Parameters:**

- `tsn_id` (required): Test session ID
- `index` (optional): Page number for pagination

**Required Headers:**

- `Cookie: JSESSIONID=<jsessionid>`

**Response:**

HTML response containing detailed test steps, including:
- Test case ID
- Sequence index
- Outcome (P/F)
- Description
- Input
- Output

## Cookie Authentication

For endpoints on port 9080, the SmartTest API server uses cookie-based authentication. The cookie authentication flow is as follows:

1. The client sends a request with authentication credentials
2. The server uses the credentials to log in to the external API using the `/AutoRun/Login` endpoint
3. The external API returns a `JSESSIONID` cookie
4. The server stores the cookie and uses it for subsequent requests to the external API
5. The server forwards the response from the external API to the client

The cookie authentication service manages cookies for external API authentication:

- Cookies are cached in memory to avoid unnecessary login requests
- Cookies expire after 30 minutes
- If a cookie expires, the service automatically logs in again to get a new cookie

## Client-Side Direct API Integration

In addition to server-side integration, the SmartTest application implements direct client-side integration with external APIs for specific features:

### Reports Page Direct Integration

The reports page implements a hybrid data access approach that combines direct external API integration with database access:

1. **Test Results Table**: Uses direct external API integration to fetch test results from port 9080 endpoints
2. **Test Details View**: Uses direct external API integration to fetch test details from port 9080 endpoints
3. **Analytics Charts**: Uses database queries for complex analytics (future enhancement)

#### Benefits of Direct Integration

- **Performance**: Direct API calls avoid the overhead of the database layer
- **Reliability**: Reports page works even if the database is unavailable
- **Real-time Data**: 10-second polling interval keeps data current

#### Implementation Details

- **External API Service**: Client-side JavaScript service that handles authentication and data fetching
- **Session ID Service**: Client-side service that manages test session IDs from multiple sources
- **HTML Parsing**: Client-side parsing of HTML responses from external APIs

For more details on the hybrid approach, see [Hybrid Data Access](../Integration/hybrid-data-access.md).

## SmartTest API Endpoints

The SmartTest API server provides the following endpoints that interact with the external APIs:

### Run Test Case / Test Suite

```
POST /api/case-runner
```

Executes a test case or test suite.

**Request Body:**

```json
{
  "tc_id": "3180",  // or "ts_id": "101"
  "uid": "test_user",
  "password": "password",
  "envir": "qa02",
  "shell_host": "jps-qa10-app01"
}
```

**Response:**

```json
{
  "success": true,
  "tsn_id": "12345",
  "message": "Test case 3180 started successfully with session ID 12345"
}
```

### Get Test Status

```
GET /api/test-status
```

Retrieves the status of a test run.

**Query Parameters:**

- `tsn_id` (required): Test session ID
- `uid` (required): User ID
- `password` (required): Password

**Response:**

```json
{
  "success": true,
  "tsn_id": "12345",
  "status": "running",
  "progress": 50,
  "start_time": "2023-01-01 12:00:00",
  "end_time": null,
  "passed": 3,
  "failed": 0
}
```

### Stop Test

```
POST /api/stop-test
```

Stops a running test.

**Request Body:**

```json
{
  "tsn_id": "12345",
  "uid": "test_user",
  "password": "password"
}
```

**Response:**

```json
{
  "success": true,
  "message": "Test session 12345 stopped successfully"
}
```

## Error Handling

The SmartTest API server handles errors that occur during external API integration:

- If the external API returns an error, the SmartTest API server returns a 500 Internal Server Error response with the error message
- If the authentication fails, the SmartTest API server returns a 401 Unauthorized response
- If the request parameters are invalid, the SmartTest API server returns a 400 Bad Request response

## Security Considerations

- The SmartTest API server does not log sensitive information such as passwords
- The SmartTest API server does not forward problematic headers that might cause issues with the external API
- The SmartTest API server validates the response from the external API before returning it to the client
- For cookie-based authentication, the SmartTest API server uses a dedicated cookie authentication service to manage cookies
- Cookies are cached in memory to avoid unnecessary login requests
- Cookies expire after 30 minutes to reduce the risk of unauthorized access

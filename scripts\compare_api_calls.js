/**
 * API Call Comparison Script
 * 
 * This script compares our original API call with the successful one.
 */

const axios = require('axios');
const fs = require('fs');

async function main() {
  try {
    console.log('Comparing API calls...');

    // Original parameters from our script
    const originalParams = new URLSearchParams();
    originalParams.append('uid', '<EMAIL>');
    originalParams.append('password', 'test');
    originalParams.append('tc_id', '3180');
    originalParams.append('envir', 'qa02');
    originalParams.append('shell_host', 'jps-qa10-app01');
    originalParams.append('file_path', '/home/<USER>/');
    originalParams.append('operatorConfigs', 'operatorNameConfigs');
    originalParams.append('kafka_server', 'kafka-qa-a0.lab.wagerworks.com');
    originalParams.append('dataCenter', 'GU');
    originalParams.append('rgs_env', 'qa02');
    originalParams.append('old_version', '0');
    originalParams.append('networkType1', 'multi-site');
    originalParams.append('networkType2', 'multi-site');
    originalParams.append('sign', '-');
    originalParams.append('rate_src', 'local');
    
    // User differentiation fields we added
    originalParams.append('user_id', 'Iakov.Volfkovich');
    originalParams.append('username', 'Iakov.Volfkovich');

    const originalData = originalParams.toString();
    
    // Working parameters from the curl command
    const workingData = "uid=<EMAIL>&password=test&tc_id=3180&envir=qa02&shell_host=jps-qa10-app01&file_path=/home/<USER>/&operatorConfigs=operatorNameConfigs&kafka_server=kafka-qa-a0.lab.wagerworks.com&dataCenter=GU&rgs_env=qa02&old_version=0&&networkType1=multi-site&networkType2=multi-site&sign=-&rate_src=local";
    
    console.log('Original Parameters:');
    console.log(originalData);
    console.log('\nWorking Parameters:');
    console.log(workingData);
    
    // Find differences
    console.log('\nKey Differences:');
    
    // Convert both to objects for comparison
    const originalObj = {};
    for (const [key, value] of new URLSearchParams(originalData).entries()) {
      originalObj[key] = value;
    }
    
    const workingObj = {};
    for (const [key, value] of new URLSearchParams(workingData).entries()) {
      workingObj[key] = value;
    }
    
    // Find keys in original not in working
    console.log('Extra parameters in original:');
    for (const key in originalObj) {
      if (!(key in workingObj)) {
        console.log(`- ${key}: ${originalObj[key]}`);
      }
    }
    
    // Find keys in working not in original
    console.log('\nMissing parameters in original:');
    for (const key in workingObj) {
      if (!(key in originalObj)) {
        console.log(`- ${key}: ${workingObj[key]}`);
      }
    }
    
    // Find keys with different values
    console.log('\nDifferent values:');
    for (const key in originalObj) {
      if (key in workingObj && originalObj[key] !== workingObj[key]) {
        console.log(`- ${key}: "${originalObj[key]}" vs "${workingObj[key]}"`);
      }
    }
    
    // Make API call with the working parameters
    console.log('\nMaking API call with working parameters...');
    const response = await axios.post(
      'http://mprts-qa02.lab.wagerworks.com:5080/AutoRun/CaseRunner',
      workingData,
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      }
    );
    
    console.log('API Response Status:', response.status);
    
    // Extract tsn_id from the response
    let tsnId = null;
    if (typeof response.data === 'string') {
      const match = response.data.match(/ReportSummary\?tsn_id=(\d+)/);
      if (match && match[1]) {
        tsnId = match[1];
      }
    }
    
    if (tsnId) {
      console.log(`Successfully extracted tsn_id: ${tsnId}`);
      
      // Create a fixed API call that works with our user
      console.log('\nFixed Parameters for our tests:');
      const fixedData = workingData.replace('<EMAIL>', '<EMAIL>');
      console.log(fixedData);
      
      // Make the corrected API call
      console.log('\nMaking API call with fixed parameters for our user...');
      const fixedResponse = await axios.post(
        'http://mprts-qa02.lab.wagerworks.com:5080/AutoRun/CaseRunner',
        fixedData,
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
          }
        }
      );
      
      console.log('API Response Status:', fixedResponse.status);
      
      // Extract tsn_id from the fixed response
      let fixedTsnId = null;
      if (typeof fixedResponse.data === 'string') {
        const match = fixedResponse.data.match(/ReportSummary\?tsn_id=(\d+)/);
        if (match && match[1]) {
          fixedTsnId = match[1];
          console.log(`Successfully extracted tsn_id from fixed call: ${fixedTsnId}`);
        } else {
          console.log('Could not extract tsn_id from fixed response');
        }
      }
    }
    
  } catch (error) {
    console.error('Error:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data (partial):', 
        typeof error.response.data === 'string' 
          ? error.response.data.substring(0, 200) 
          : JSON.stringify(error.response.data).substring(0, 200)
      );
    }
  }
}

main().catch(console.error); 
/**
 * dashboard-recent-runs.js
 *
 * This module manages the 'Recent Runs' table on the dashboard.
 * It handles fetching, rendering, and periodically refreshing the list of recent test executions.
 */

import { config } from './dashboard-config.js';
import * as api from './dashboard-api.js';
import { formatters } from './formatters.js';
import { viewTestDetails } from './dashboard-details-modal.js';

let refreshIntervalId = null;

/**
 * Initializes the recent runs functionality, including the initial data load and setting up polling.
 */
export function initializeRecentRuns() {
    refreshRecentRuns(); // Initial load

    if (refreshIntervalId) {
        clearInterval(refreshIntervalId);
    }
    refreshIntervalId = setInterval(refreshRecentRuns, config.constants.RECENT_RUNS_POLLING_INTERVAL);
}

/**
 * Fetches the latest test runs and updates the recent runs table.
 * This function is exported to be callable from other modules.
 */
export async function refreshRecentRuns() {
    try {
        const runs = await api.getRecentRuns(config.constants.RECENT_RUNS_LIMIT);
        config.state.recentRunsCache = runs;
        renderRecentRunsTable(runs);
    } catch (error) {
        console.error('Error refreshing recent runs:', error);
        const tableBody = config.elements.recentRunsTable?.querySelector('tbody');
        if (tableBody) {
            tableBody.innerHTML = '<tr><td colspan="7" class="ms-fontColor-error">Failed to load recent runs.</td></tr>';
        }
    }
}

/**
 * Renders the recent runs data into the table.
 * @param {Array} runs - An array of recent run objects.
 */
function renderRecentRunsTable(runs) {
    const tableBody = config.elements.recentRunsTable?.querySelector('tbody');
    if (!tableBody) return;

    if (runs.length === 0) {
        tableBody.innerHTML = '<tr><td colspan="7">No recent test runs found.</td></tr>';
        return;
    }

    tableBody.innerHTML = runs.map(run => createRecentRunRow(run)).join('');

    // Attach event listeners to the 'View Details' buttons
    tableBody.querySelectorAll('.view-details-btn').forEach(button => {
        button.addEventListener('click', (event) => {
            const tsnId = event.currentTarget.dataset.tsnId;
            viewTestDetails(parseInt(tsnId, 10));
        });
    });
}

/**
 * Creates the HTML for a single row in the recent runs table.
 * @param {object} run - The test run data.
 * @returns {string} The HTML string for the table row.
 */
function createRecentRunRow(run) {
    const { tsn_id, suite_name, status, user, start_time, end_time, passed_cases, total_cases } = run;
    const duration = formatters.formatDuration(start_time, end_time);
    const statusClass = formatters.getStatusClass(status);

    return `
        <tr data-tsn-id="${tsn_id}">
            <td>${tsn_id}</td>
            <td>${suite_name || 'N/A'}</td>
            <td><span class="ms-status-badge ${statusClass}">${status}</span></td>
            <td>${formatters.formatUserEmail(user)}</td>
            <td>${formatters.formatDateTime(start_time)}</td>
            <td>${duration}</td>
            <td>${passed_cases ?? 'N/A'} / ${total_cases ?? 'N/A'}</td>
            <td>
                <button class="ms-Button ms-Button--primary view-details-btn" data-tsn-id="${tsn_id}">
                    <span class="ms-Button-label">View Details</span>
                </button>
            </td>
        </tr>
    `;
}

/**
 * Highlights a specific test run in the recent runs table.
 * @param {string} testRunId - The ID of the test run to highlight.
 */
function highlightTest(testRunId) {
    if (!testRunId) return;

    const table = config.elements.recentRunsTable;
    if (!table) return;

    // Remove existing highlights
    const existingHighlighted = table.querySelector('.highlight');
    if (existingHighlighted) {
        existingHighlighted.classList.remove('highlight');
    }

    // Find and highlight the new row
    const rowToHighlight = table.querySelector(`tr[data-tsn-id='${testRunId}']`);
    if (rowToHighlight) {
        rowToHighlight.classList.add('highlight');
        rowToHighlight.scrollIntoView({ behavior: 'smooth', block: 'center' });
    } else {
        console.warn(`Could not find test run ${testRunId} to highlight.`);
    }
}

/**
 * Checks URL parameters for actions like highlighting a specific test run.
 * This function is exported to be called from the main module.
 */
export function handleUrlParameters() {
    const urlParams = new URLSearchParams(window.location.search);
    const testIdToHighlight = urlParams.get('test_id');

    if (testIdToHighlight) {
        // Use a timeout to ensure the table has been rendered before highlighting
        setTimeout(() => highlightTest(testIdToHighlight), 1000);
    }
}

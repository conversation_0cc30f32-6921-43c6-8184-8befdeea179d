# Script to query test_case table schema and related information
$server = "mprts-qa02.lab.wagerworks.com"
$database = "rgs_test"
$dbUser = "rgs_rw"
$dbPassword = "rgs_rw"
$sshUser = "volfkoi"
$sshKeyPath = "$env:USERPROFILE\.ssh\id_rsa_dbserver"

# Create output directory if it doesn't exist
$outputDir = "documentation\Database\scripts_output"
if (-not (Test-Path $outputDir)) {
    New-Item -ItemType Directory -Path $outputDir -Force
}

# Get test_case table schema
Write-Host "Getting schema for test_case table" -ForegroundColor Cyan
$query1 = "DESCRIBE test_case;"
$cmd1 = "ssh -i `"$sshKeyPath`" -o HostKeyAlgorithms=+ssh-rsa ${sshUser}@${server} `"mysql --user=$dbUser --password='$dbPassword' $database --execute='$query1'`""
Write-Host "Command: $cmd1" -ForegroundColor Gray
Invoke-Expression $cmd1 | Out-File -FilePath "$outputDir\schema_test_case.txt"
Write-Host "Test case schema saved to $outputDir\schema_test_case.txt" -ForegroundColor Green

# Get test_case_step table schema (if it exists)
Write-Host "Getting schema for test_case_step table" -ForegroundColor Cyan
$query2 = "DESCRIBE test_case_step;"
$cmd2 = "ssh -i `"$sshKeyPath`" -o HostKeyAlgorithms=+ssh-rsa ${sshUser}@${server} `"mysql --user=$dbUser --password='$dbPassword' $database --execute='$query2'`""
Invoke-Expression $cmd2 | Out-File -FilePath "$outputDir\schema_test_case_step.txt"
Write-Host "Test case step schema saved to $outputDir\schema_test_case_step.txt" -ForegroundColor Green

# Get parameter tables schema (if they exist)
$paramTables = @("test_parameter", "project_parameter", "suite_parameter", "case_parameter", "step_parameter")
foreach ($table in $paramTables) {
    Write-Host "Getting schema for $table table" -ForegroundColor Cyan
    $query3 = "DESCRIBE $table;"
    $cmd3 = "ssh -i `"$sshKeyPath`" -o HostKeyAlgorithms=+ssh-rsa ${sshUser}@${server} `"mysql --user=$dbUser --password='$dbPassword' $database --execute='$query3'`""
    Invoke-Expression $cmd3 | Out-File -FilePath "$outputDir\schema_${table}.txt"
    Write-Host "Schema for $table saved to $outputDir\schema_${table}.txt" -ForegroundColor Green
}

# Get sample test case data
Write-Host "Getting sample test case data" -ForegroundColor Cyan
$query4 = "SELECT * FROM test_case LIMIT 1;"
$cmd4 = "ssh -i `"$sshKeyPath`" -o HostKeyAlgorithms=+ssh-rsa ${sshUser}@${server} `"mysql --user=$dbUser --password='$dbPassword' $database --execute='$query4'`""
Invoke-Expression $cmd4 | Out-File -FilePath "$outputDir\sample_test_case.txt"
Write-Host "Sample test case data saved to $outputDir\sample_test_case.txt" -ForegroundColor Green

# Check if test_plan table exists
Write-Host "Checking for test_plan table" -ForegroundColor Cyan
$query5 = "DESCRIBE test_plan;"
$cmd5 = "ssh -i `"$sshKeyPath`" -o HostKeyAlgorithms=+ssh-rsa ${sshUser}@${server} `"mysql --user=$dbUser --password='$dbPassword' $database --execute='$query5'`""
Invoke-Expression $cmd5 | Out-File -FilePath "$outputDir\schema_test_plan.txt"
Write-Host "Test plan schema saved to $outputDir\schema_test_plan.txt" -ForegroundColor Green

# Count records in key tables
Write-Host "Getting record counts in key tables" -ForegroundColor Cyan
$query6 = @"
SELECT 'test_project' as table_name, COUNT(*) as record_count FROM test_project
UNION ALL
SELECT 'test_suite_group', COUNT(*) FROM test_suite_group
UNION ALL
SELECT 'test_case_group', COUNT(*) FROM test_case_group
UNION ALL
SELECT 'test_case', COUNT(*) FROM test_case
UNION ALL
SELECT 'test_result', COUNT(*) FROM test_result;
"@
$cmd6 = "ssh -i `"$sshKeyPath`" -o HostKeyAlgorithms=+ssh-rsa ${sshUser}@${server} `"mysql --user=$dbUser --password='$dbPassword' $database --execute='$query6'`""
Invoke-Expression $cmd6 | Out-File -FilePath "$outputDir\table_record_counts.txt"
Write-Host "Table record counts saved to $outputDir\table_record_counts.txt" -ForegroundColor Green

Write-Host "All schema information has been collected!" -ForegroundColor Green 
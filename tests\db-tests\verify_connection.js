/**
 * Connection Verification Tool
 * 
 * This script verifies SSH and database connections to specified environments.
 * It consolidates functionality from multiple test scripts to provide a single
 * tool for verifying connections.
 * 
 * Usage:
 *   node verify_connection.js [environment] [test_type]
 * 
 * Examples:
 *   node verify_connection.js qa02           # Test all connection types on qa02
 *   node verify_connection.js qa01 ssh       # Test only SSH on qa01
 *   node verify_connection.js qa03 database  # Test only database on qa03
 */

const { Client } = require('ssh2');
const fs = require('fs');
const path = require('path');
const mysql = require('mysql2/promise');
const dbConnector = require('../utils/db-connector');

// Process command-line arguments
const args = process.argv.slice(2);
const environment = args[0] || 'qa02';
const testType = args[1] || 'all'; // 'all', 'ssh', 'database', 'direct', 'tunnel'

// Main function
async function main() {
  console.log(`\n=== CONNECTION VERIFICATION FOR ${environment.toUpperCase()} ===\n`);
  
  // Validate environment
  if (!dbConnector.environments[environment]) {
    console.error(`Error: Unknown environment '${environment}'`);
    console.error(`Available environments: ${Object.keys(dbConnector.environments).join(', ')}`);
    process.exit(1);
  }
  
  // Get environment configuration
  const config = dbConnector.environments[environment];
  
  // Display configuration
  console.log('Environment Configuration:');
  console.log('--------------------------');
  console.log(`SSH Host: ${config.SSH_HOST}`);
  console.log(`SSH User: ${config.SSH_USER}`);
  console.log(`SSH Port: ${config.SSH_PORT}`);
  console.log(`SSH Key: ${config.SSH_KEY_PATH}`);
  console.log(`Database Host: ${config.DB_HOST}`);
  console.log(`Database Name: ${config.DB_NAME}`);
  console.log(`API URL: ${config.BASE_URL}`);
  console.log('--------------------------\n');
  
  // Run tests based on test type
  let sshSuccess = false;
  let directSuccess = false;
  let tunnelSuccess = false;
  
  try {
    // SSH connection test
    if (testType === 'all' || testType === 'ssh') {
      console.log('1. Testing SSH connection...');
      sshSuccess = await testSshConnection(config);
    }
    
    // Database connection test - direct method
    if (testType === 'all' || testType === 'database' || testType === 'direct') {
      console.log('\n2. Testing direct database connection via SSH...');
      directSuccess = await testDatabaseDirect(environment);
    }
    
    // Database connection test - tunnel method
    if (testType === 'all' || testType === 'database' || testType === 'tunnel') {
      console.log('\n3. Testing database connection via SSH tunnel...');
      tunnelSuccess = await testDatabaseTunnel(environment);
    }
    
    // Summary
    console.log('\n=== TEST SUMMARY ===');
    
    if (testType === 'all' || testType === 'ssh') {
      console.log(`SSH Connection: ${sshSuccess ? 'SUCCESS ✅' : 'FAILED ❌'}`);
    }
    
    if (testType === 'all' || testType === 'database' || testType === 'direct') {
      console.log(`Direct Database: ${directSuccess ? 'SUCCESS ✅' : 'FAILED ❌'}`);
    }
    
    if (testType === 'all' || testType === 'database' || testType === 'tunnel') {
      console.log(`Tunnel Database: ${tunnelSuccess ? 'SUCCESS ✅' : 'FAILED ❌'}`);
    }
    
    // Recommended approach
    if (testType === 'all' || testType === 'database') {
      console.log('\nRecommended connection method:');
      if (directSuccess && !tunnelSuccess) {
        console.log('Use DIRECT method (SSH command execution)');
      } else if (!directSuccess && tunnelSuccess) {
        console.log('Use TUNNEL method (SSH port forwarding)');
      } else if (directSuccess && tunnelSuccess) {
        // For QA01, direct is typically better; for QA02/QA03, tunnel is typically better
        if (environment === 'qa01') {
          console.log('Both methods work - recommend DIRECT method for this environment');
        } else {
          console.log('Both methods work - recommend TUNNEL method for this environment');
        }
      } else {
        console.log('Neither connection method worked. Check your SSH key and network settings.');
      }
    }
    
    process.exit(0);
  } catch (error) {
    console.error(`\nError: ${error.message}`);
    process.exit(1);
  }
}

/**
 * Test SSH connection
 * @param {Object} config - Environment configuration
 * @returns {Promise<boolean>} Success status
 */
async function testSshConnection(config) {
  return new Promise((resolve) => {
    try {
      console.log(`Connecting to ${config.SSH_HOST}:${config.SSH_PORT} as ${config.SSH_USER}...`);
      
      // Check if SSH key exists
      if (!fs.existsSync(config.SSH_KEY_PATH)) {
        console.error(`SSH key not found at ${config.SSH_KEY_PATH}`);
        resolve(false);
        return;
      }
      
      // Create SSH client
      const sshClient = new Client();
      
      // Set up connection parameters
      const sshConfig = {
        host: config.SSH_HOST,
        port: config.SSH_PORT,
        username: config.SSH_USER,
        privateKey: fs.readFileSync(config.SSH_KEY_PATH)
      };
      
      // Set timeout
      const timeout = setTimeout(() => {
        console.error('SSH connection timed out after 10 seconds');
        sshClient.end();
        resolve(false);
      }, 10000);
      
      // Set up connection handlers
      sshClient.on('ready', () => {
        clearTimeout(timeout);
        console.log('✅ SSH connection established successfully');
        
        // Test executing a remote command
        console.log('\nExecuting remote command: hostname...');
        sshClient.exec('hostname', (err, stream) => {
          if (err) {
            console.error(`❌ Failed to execute command: ${err.message}`);
            sshClient.end();
            resolve(false);
            return;
          }
          
          let output = '';
          stream.on('data', (data) => {
            output += data.toString();
          });
          
          stream.on('close', (code) => {
            console.log(`Command output: ${output.trim()}`);
            console.log(`Exit code: ${code}`);
            
            // Test MySQL port on the remote server
            console.log('\nTesting MySQL port on remote server...');
            sshClient.exec('nc -zv 127.0.0.1 3306 || echo "Port 3306 is not open"', (err, stream) => {
              if (err) {
                console.error(`❌ Failed to test port: ${err.message}`);
                sshClient.end();
                resolve(false);
                return;
              }
              
              let portOutput = '';
              stream.on('data', (data) => {
                portOutput += data.toString();
              });
              
              stream.stderr.on('data', (data) => {
                portOutput += data.toString();
              });
              
              stream.on('close', (code) => {
                console.log(`Port test output: ${portOutput.trim()}`);
                console.log(`Exit code: ${code}`);
                
                console.log('✅ SSH connection tests completed successfully');
                sshClient.end();
                resolve(true);
              });
            });
          });
        });
      });
      
      sshClient.on('error', (err) => {
        clearTimeout(timeout);
        console.error(`❌ SSH connection error: ${err.message}`);
        resolve(false);
      });
      
      // Connect to SSH server
      sshClient.connect(sshConfig);
    } catch (error) {
      console.error(`❌ SSH test error: ${error.message}`);
      resolve(false);
    }
  });
}

/**
 * Test database connection using direct SSH method
 * @param {string} environment - Environment name
 * @returns {Promise<boolean>} Success status
 */
async function testDatabaseDirect(environment) {
  try {
    // Initialize connection with direct method
    await dbConnector.init(environment, { forceDirect: true, debug: true });
    console.log('✅ Direct database connection initialized successfully');
    
    // Test a simple query
    console.log('\nExecuting test query: SELECT 1 AS test_value');
    const result = await dbConnector.query('SELECT 1 AS test_value');
    console.log('Query result:', JSON.stringify(result));
    
    // Test a more complex query
    console.log('\nGetting database version:');
    const versionResult = await dbConnector.query('SELECT VERSION() AS version');
    console.log('Version:', versionResult[0].value || versionResult[0].column1);
    
    // Get database information
    console.log('\nGetting database information:');
    const infoResult = await dbConnector.query(`
      SELECT 
        database() as db_name,
        current_user() as current_user
    `);
    console.log(`Database name: ${infoResult[0].column1}`);
    console.log(`Current user: ${infoResult[0].column2}`);
    
    // Test database tables
    console.log('\nChecking for test tables:');
    const tablesResult = await dbConnector.query(`
      SELECT COUNT(*) FROM test_session
    `);
    console.log(`Found ${tablesResult[0].value || tablesResult[0].column1} rows in test_session table`);
    
    // Close connection
    await dbConnector.close();
    console.log('\n✅ Direct database tests completed successfully');
    return true;
  } catch (error) {
    console.error(`❌ Direct database test error: ${error.message}`);
    
    try {
      await dbConnector.close();
    } catch (closeError) {
      // Ignore close errors
    }
    
    return false;
  }
}

/**
 * Test database connection using SSH tunnel method
 * @param {string} environment - Environment name
 * @returns {Promise<boolean>} Success status
 */
async function testDatabaseTunnel(environment) {
  try {
    // Initialize connection with tunnel method
    await dbConnector.init(environment, { forceTunnel: true, debug: true });
    console.log('✅ Tunnel database connection initialized successfully');
    
    // Test a simple query
    console.log('\nExecuting test query: SELECT 1 AS test_value');
    const result = await dbConnector.query('SELECT 1 AS test_value');
    console.log('Query result:', JSON.stringify(result));
    
    // Test a more complex query
    console.log('\nGetting database version:');
    const versionResult = await dbConnector.query('SELECT VERSION() AS version');
    console.log('Version:', versionResult[0].version);
    
    // Get database information
    console.log('\nGetting database information:');
    const infoResult = await dbConnector.query(`
      SELECT 
        database() as db_name,
        current_user() as current_user
    `);
    console.log(`Database name: ${infoResult[0].db_name}`);
    console.log(`Current user: ${infoResult[0].current_user}`);
    
    // Test database tables
    console.log('\nChecking for test tables:');
    const tablesResult = await dbConnector.query(`
      SELECT COUNT(*) as count FROM test_session
    `);
    console.log(`Found ${tablesResult[0].count} rows in test_session table`);
    
    // Close connection
    await dbConnector.close();
    console.log('\n✅ Tunnel database tests completed successfully');
    return true;
  } catch (error) {
    console.error(`❌ Tunnel database test error: ${error.message}`);
    
    try {
      await dbConnector.close();
    } catch (closeError) {
      // Ignore close errors
    }
    
    return false;
  }
}

// Run the main function
main().catch(console.error); 
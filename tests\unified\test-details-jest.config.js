/**
 * Simplified Jest configuration for running the test-details integration test
 */

module.exports = {
  // Test environment
  testEnvironment: 'node',
  
  // Root directory for tests
  rootDir: '../../',
  
  // Test match pattern
  testMatch: [
    '<rootDir>/tests/unified/integration/test-details-integration.test.js'
  ],
  
  // Module paths
  moduleDirectories: ['node_modules', '<rootDir>'],
  
  // Simple reporter
  reporters: ['default'],
  
  // Clear mocks between tests
  clearMocks: true,
  
  // Timeout for tests
  // Using modern Jest format for timeout
  testTimeout: 30000,
  
  // No special setup files
  setupFilesAfterEnv: [],
  
  // Verbose output
  verbose: true
};

# Automated Release Management

This document explains our automated release system implemented in `.github/workflows/release.yml` and how it handles versioning, changelog generation, and deployments for SmartTest.

## 🎯 Release Automation Overview

Our release system automatically handles:
- **Version Bumping**: Semantic version increments based on commit messages
- **Changelog Generation**: Automatic changelog updates from conventional commits
- **GitHub Releases**: Professional release notes with assets
- **Environment Deployment**: Automatic staging and production deployments

## 🔄 Release Workflow Triggers

### **Automatic Triggers**
```yaml
on:
  push:
    branches: [ main ]
```
- **Every push to main branch** triggers release evaluation
- Only creates releases for commits that warrant them (feat, fix, breaking changes)
- Skips release creation for documentation or chore commits

### **Manual Triggers**
```yaml
workflow_dispatch:
  inputs:
    release_type:
      type: choice
      options: [patch, minor, major]
```
- **Manual releases** via GitHub Actions UI
- Choose specific release type (patch/minor/major)
- Useful for emergency releases or when automatic detection fails

## 📊 Release Process Flow

### Step 1: Pre-Release Validation (2-3 minutes)
```yaml
- name: Run tests
  run: npm test

- name: Build application  
  run: npm run build
```

**Validation Checks**:
- ✅ All unit tests pass
- ✅ Integration tests complete successfully
- ✅ Production build creates all necessary assets
- ✅ No security vulnerabilities in dependencies

**Failure Handling**: If any validation fails, the release is aborted and team is notified.

### Step 2: Version & Changelog Generation (1-2 minutes)
```yaml
- name: Generate changelog
  uses: TriPSs/conventional-changelog-action@v3
  with:
    github-token: ${{ secrets.GITHUB_TOKEN }}
    output-file: 'CHANGELOG.md'
    release-count: '10'
```

**Process**:
1. **Analyzes commits** since last release using conventional commit format
2. **Determines version bump** based on commit types:
   - `feat:` → Minor version bump (1.2.0 → 1.3.0)
   - `fix:` → Patch version bump (1.2.0 → 1.2.1)  
   - `BREAKING CHANGE:` → Major version bump (1.2.0 → 2.0.0)
3. **Updates package.json** with new version
4. **Generates changelog** with categorized changes
5. **Creates git tag** with new version

### Step 3: GitHub Release Creation (1 minute)
```yaml
- name: Create Release
  uses: actions/create-release@v1
  with:
    tag_name: ${{ steps.changelog.outputs.tag }}
    release_name: Release ${{ steps.changelog.outputs.tag }}
    body: ${{ steps.changelog.outputs.clean_changelog }}
```

**Release Assets**:
- **Source code** (automatically attached by GitHub)
- **Build artifacts** (frontend/server/public/ folder)
- **Release notes** (generated from changelog)
- **Version metadata** (JSON file with version info)

### Step 4: Environment Deployment (5-10 minutes)
**Automatic Staging Deployment**:
- Deploys to staging environment immediately
- Runs smoke tests to verify deployment
- Updates deployment status in GitHub

**Production Deployment** (for tagged releases):
- Requires manual approval in GitHub Environments
- Deploys only after approval from designated reviewers
- Includes rollback procedures if deployment fails

## 📝 Changelog Generation

### **Automatic Categorization**
Our changelog automatically organizes commits into categories:

```markdown
## [1.3.0] - 2024-01-15

### 🚀 Features
- feat(dashboard): add multi-select test suite functionality (#45)
- feat(api): implement automated test retries (#47)

### 🐛 Bug Fixes  
- fix(auth): resolve session timeout issues (#44)
- fix(reports): correct timezone display in test results (#46)

### 🔧 Dependencies
- chore(deps): update node-fetch to v2.7.0 for security
- chore(deps): bump axios from 1.3.4 to 1.8.4

### 📚 Documentation
- docs(devops): add comprehensive GitHub automation guide
- docs(api): update endpoint documentation with examples
```

### **Commit Analysis Rules**
| Commit Type | Changelog Section | Version Impact |
|-------------|------------------|----------------|
| `feat:` | 🚀 Features | Minor |
| `fix:` | 🐛 Bug Fixes | Patch |
| `perf:` | ⚡ Performance | Patch |
| `security:` | 🔒 Security | Patch |
| `docs:` | 📚 Documentation | None |
| `chore:` | 🔧 Dependencies | None |
| `BREAKING CHANGE:` | ⚠️ Breaking Changes | Major |

## 🏷️ Version Management

### **Current Versioning Strategy**
- **Current Version**: 1.2.0 (aligned with git tag)
- **Format**: MAJOR.MINOR.PATCH (semantic versioning)
- **Branch Strategy**: Feature branches → main → automatic release

### **NPM Scripts for Manual Version Control**
```json
{
  "scripts": {
    "version:patch": "npm version patch --no-git-tag-version",
    "version:minor": "npm version minor --no-git-tag-version", 
    "version:major": "npm version major --no-git-tag-version",
    "release:patch": "npm run version:patch && npm run build && git add .",
    "release:minor": "npm run version:minor && npm run build && git add .",
    "release:major": "npm run version:major && npm run build && git add ."
  }
}
```

### **Emergency Release Process**
For critical hotfixes that need immediate release:

```bash
# 1. Create hotfix branch
git checkout -b hotfix/critical-security-fix

# 2. Implement fix with proper commit message
git commit -m "fix(security): patch critical authentication vulnerability"

# 3. Create PR to main
gh pr create --title "🚨 Critical Security Hotfix" --body "Emergency fix for authentication vulnerability"

# 4. After PR approval and merge, release triggers automatically
# 5. Manual production deployment approval required
```

## 🚨 Release Failure Handling

### **Common Failure Scenarios**

#### **Test Failures During Release**
**Symptoms**: ❌ Release workflow fails at test stage
**Investigation**:
1. Check test logs in GitHub Actions
2. Identify which tests are failing
3. Determine if failures are related to new code or environment

**Resolution**:
```bash
# Fix failing tests locally first
npm test

# Create hotfix if tests pass locally but fail in CI
git checkout -b hotfix/ci-test-fix
# Make necessary changes
git commit -m "fix(ci): resolve test failures in release environment" 
```

#### **Changelog Generation Issues**
**Symptoms**: ⚠️ Empty changelog or incorrect version bump
**Common Causes**:
- Commits don't follow conventional commit format
- Previous release tag is missing or incorrect
- Duplicate version tags in repository

**Resolution**:
```bash
# Check commit messages since last release
git log --oneline v1.2.0..HEAD

# Verify conventional commit format
# If commits are incorrectly formatted, may need manual release
```

#### **Deployment Failures**
**Symptoms**: 🚀 Release created but deployment fails
**Investigation**:
1. Check deployment logs in Environments tab
2. Verify environment secrets and configuration
3. Test build artifacts locally

**Rollback Process**:
```bash
# Automatic rollback via GitHub Environments
# Or manual rollback:
git revert HEAD
git push origin main
# This triggers new release with reverted changes
```

## 📊 Release Metrics & Monitoring

### **Key Metrics to Track**
- **Release Frequency**: Target 1-2 releases per week
- **Release Success Rate**: Target >95% successful releases
- **Time to Production**: Target <30 minutes from merge to production
- **Rollback Rate**: Target <5% of releases require rollback

### **Monitoring Dashboard**
**GitHub Insights**:
- Repository → Insights → Community → Releases
- Track release frequency and size
- Monitor changelog quality and completeness

**Custom Metrics** (tracked in documentation):
```markdown
## Release Performance (Updated Weekly)
- **Last 30 Days**: 8 releases, 100% success rate
- **Average Time to Production**: 22 minutes
- **Rollbacks**: 0 (0%)
- **Emergency Releases**: 1 (security patch)
```

## 🔄 Continuous Improvement

### **Monthly Release Retrospectives**
**Review Areas**:
1. **Release Frequency**: Are we releasing too often/infrequently?
2. **Changelog Quality**: Are release notes clear and helpful?
3. **Deployment Time**: Can we optimize the deployment pipeline?
4. **Failure Analysis**: What caused any release failures?

### **Automation Enhancements**
**Planned Improvements**:
- **Slack/Discord Notifications**: Notify team of new releases
- **Automated Testing**: More comprehensive pre-release testing
- **Release Templates**: Standardized release note templates
- **Deployment Health Checks**: Automatic verification of successful deployments

## 📚 Related Documentation

- **[Semantic Versioning Guide](../versioning/semantic-versioning-guide.md)**: Detailed versioning strategy
- **[Conventional Commits](../references/conventional-commits.md)**: Commit message format guide
- **[Deployment Environments](deployment-environments.md)**: Environment-specific deployment details
- **[Troubleshooting](../operations/troubleshooting.md)**: Common release issues and solutions

---

**Best Practice**: Always test your changes locally and ensure conventional commit messages are used to maintain the quality of our automated release system.

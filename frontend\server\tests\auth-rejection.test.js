/**
 * Authentication Rejection Tests
 * Comprehensive tests for unauthorized access scenarios and invalid credentials
 */

const request = require('supertest');
const express = require('express');
const cookieParser = require('cookie-parser');

// Import the modules we need to test
const authRoutes = require('../routes/auth');
const adminRoutes = require('../routes/admin');
const { validateJWTToken, requireRoles, requirePermissions } = require('../middleware/session-validation');
const { securityHeaders, rateLimiters } = require('../middleware/security');

// Create test app
const createTestApp = () => {
  const app = express();
  app.use(express.json());
  app.use(cookieParser());
  app.use(securityHeaders);
  
  // Add auth routes
  app.use('/auth', authRoutes);
  
  // Add protected admin routes
  app.use('/admin', validateJWTToken, requireRoles('admin'), adminRoutes);
  
  // Add test protected endpoints
  app.get('/protected/read', validateJWTToken, requirePermissions('read'), (req, res) => {
    res.json({ success: true, message: 'Read access granted' });
  });
  
  app.post('/protected/write', validateJWTToken, requirePermissions('write'), (req, res) => {
    res.json({ success: true, message: 'Write access granted' });
  });
  
  app.delete('/protected/delete', validateJWTToken, requirePermissions('delete'), (req, res) => {
    res.json({ success: true, message: 'Delete access granted' });
  });
  
  return app;
};

describe('Authentication Rejection Tests', () => {
  let app;
  
  beforeEach(() => {
    app = createTestApp();
  });

  describe('Invalid Login Attempts', () => {
    test('should reject login with invalid credentials', async () => {
      const response = await request(app)
        .post('/auth/login')
        .send({
          uid: '<EMAIL>',
          password: 'wrongpassword'
        });

      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
      expect(response.body.code).toBe('INVALID_CREDENTIALS');
      expect(response.body.message).toContain('Authentication failed');
    });

    test('should reject login with missing credentials', async () => {
      const response = await request(app)
        .post('/auth/login')
        .send({});

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });

    test('should reject login with malformed email', async () => {
      const response = await request(app)
        .post('/auth/login')
        .send({
          uid: 'not-an-email',
          password: 'password123'
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });

    test('should reject login with empty password', async () => {
      const response = await request(app)
        .post('/auth/login')
        .send({
          uid: '<EMAIL>',
          password: ''
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });

    test('should reject login with SQL injection attempt', async () => {
      const response = await request(app)
        .post('/auth/login')
        .send({
          uid: "<EMAIL>'; DROP TABLE users; --",
          password: 'password'
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });

    test('should reject login with XSS attempt', async () => {
      const response = await request(app)
        .post('/auth/login')
        .send({
          uid: '<script>alert("xss")</script>@example.com',
          password: 'password'
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });
  });

  describe('Unauthorized Access to Protected Routes', () => {
    test('should reject access to protected route without token', async () => {
      const response = await request(app)
        .get('/protected/read');

      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
      expect(response.body.code).toBe('ACCESS_TOKEN_MISSING');
    });

    test('should reject access with invalid token', async () => {
      const response = await request(app)
        .get('/protected/read')
        .set('Authorization', 'Bearer invalid-token');

      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
      expect(response.body.code).toBe('TOKEN_INVALID');
    });

    test('should reject access with malformed token', async () => {
      const response = await request(app)
        .get('/protected/read')
        .set('Authorization', 'Bearer not.a.jwt.token');

      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
      expect(response.body.code).toBe('TOKEN_INVALID');
    });

    test('should reject access with expired token', async () => {
      // Create an expired JWT token for testing
      const expiredToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyLCJleHAiOjE1MTYyMzkwMjJ9.invalid';
      
      const response = await request(app)
        .get('/protected/read')
        .set('Authorization', `Bearer ${expiredToken}`);

      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
    });
  });

  describe('Permission-Based Access Control', () => {
    test('should reject access when user lacks required permissions', async () => {
      // This would require a valid token with insufficient permissions
      // For now, we test the middleware behavior with no token
      const response = await request(app)
        .post('/protected/write');

      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
      expect(response.body.code).toBe('ACCESS_TOKEN_MISSING');
    });

    test('should reject delete operation without delete permission', async () => {
      const response = await request(app)
        .delete('/protected/delete');

      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
      expect(response.body.code).toBe('ACCESS_TOKEN_MISSING');
    });
  });

  describe('Admin Route Access Control', () => {
    test('should reject admin access without authentication', async () => {
      const response = await request(app)
        .get('/admin/users');

      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
      expect(response.body.code).toBe('ACCESS_TOKEN_MISSING');
    });

    test('should reject admin user creation without authentication', async () => {
      const response = await request(app)
        .post('/admin/users')
        .send({
          uid: '<EMAIL>',
          password: 'password123',
          role: 'tester'
        });

      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
      expect(response.body.code).toBe('ACCESS_TOKEN_MISSING');
    });

    test('should reject admin user deletion without authentication', async () => {
      const response = await request(app)
        .delete('/admin/users/<EMAIL>');

      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
      expect(response.body.code).toBe('ACCESS_TOKEN_MISSING');
    });
  });

  describe('Session Validation', () => {
    test('should reject session validation without token', async () => {
      const response = await request(app)
        .get('/auth/validate');

      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
      expect(response.body.code).toBe('ACCESS_TOKEN_MISSING');
    });

    test('should reject token refresh without refresh token', async () => {
      const response = await request(app)
        .post('/auth/refresh');

      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
      expect(response.body.code).toBe('REFRESH_TOKEN_MISSING');
    });
  });

  describe('Input Validation and Sanitization', () => {
    test('should reject requests with oversized payloads', async () => {
      const largePayload = {
        uid: 'a'.repeat(10000) + '@example.com',
        password: 'b'.repeat(10000)
      };

      const response = await request(app)
        .post('/auth/login')
        .send(largePayload);

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });

    test('should reject requests with null bytes', async () => {
      const response = await request(app)
        .post('/auth/login')
        .send({
          uid: 'test\<EMAIL>',
          password: 'password\x00'
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });

    test('should reject requests with control characters', async () => {
      const response = await request(app)
        .post('/auth/login')
        .send({
          uid: 'test\r\<EMAIL>',
          password: 'password\t'
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });
  });

  describe('Security Headers', () => {
    test('should include security headers in responses', async () => {
      const response = await request(app)
        .get('/auth/validate');

      expect(response.headers['x-frame-options']).toBe('DENY');
      expect(response.headers['x-content-type-options']).toBe('nosniff');
      expect(response.headers['x-xss-protection']).toBe('1; mode=block');
      expect(response.headers['content-security-policy']).toBeDefined();
      expect(response.headers['x-smarttest-security']).toBe('enabled');
    });

    test('should not expose server information', async () => {
      const response = await request(app)
        .get('/auth/validate');

      expect(response.headers['x-powered-by']).toBeUndefined();
      expect(response.headers['server']).toBeUndefined();
    });
  });

  describe('Error Handling', () => {
    test('should not expose sensitive information in error messages', async () => {
      const response = await request(app)
        .post('/auth/login')
        .send({
          uid: '<EMAIL>',
          password: 'wrongpassword'
        });

      expect(response.body.message).not.toContain('database');
      expect(response.body.message).not.toContain('sql');
      expect(response.body.message).not.toContain('internal');
      expect(response.body.message).not.toContain('stack');
    });

    test('should return consistent error format', async () => {
      const response = await request(app)
        .get('/protected/read');

      expect(response.body).toHaveProperty('success');
      expect(response.body).toHaveProperty('message');
      expect(response.body).toHaveProperty('code');
      expect(response.body.success).toBe(false);
    });
  });
});

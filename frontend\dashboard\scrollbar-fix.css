
/* SmartTest Dashboard - Scrollbar Fixes
 * This stylesheet fixes multiple scrollbar issues by applying modern CSS layout patterns
 * while maintaining compatibility with the existing HTML structure.
 */

/* Global scrolling strategy: body is the only scrollable container */
html, body {
    height: 100%;
    margin: 0;
    padding: 0;
}

body {
    overflow-y: auto !important; /* Main vertical scrollbar */
    overflow-x: hidden;
}

/* Fix main container layout */
.ms-container {
    min-height: calc(100vh - 56px); /* Use min-height instead of fixed height */
    height: auto !important;
    display: flex;
    flex-direction: column;
}

/* Fix layout wrapper */
.ms-layout {
    display: flex;
    flex: 1;
}

/* Fix navigation area */
.ms-nav {
    width: 240px;
    background-color: var(--teams-light);
    border-right: 1px solid var(--teams-border);
    overflow-y: visible !important; /* Remove scrollbar */
    position: sticky;
    top: 56px; /* Header height */
    align-self: flex-start;
    height: calc(100vh - 56px);
}

/* Fix content area */
.ms-content {
    flex: 1;
    padding: 1.5rem;
    overflow-y: visible !important; /* Remove scrollbar */
}

/* Fix card containers */
.ms-card {
    margin-bottom: 1.5rem;
    border-radius: 4px;
    border: 1px solid var(--teams-border);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    overflow: visible !important; /* Remove scrollbar */
}

.ms-card-content {
    padding: 1.5rem;
}

/* Fix card container layout */
.ms-card-container,
.test-suite-cards-container {
    overflow: visible !important;
    height: auto !important;
    max-height: none !important;
}

/* Fix test suite selection list */
.ms-selection-list {
    border: 1px solid var(--teams-border);
    border-radius: 4px;
    margin-top: 0.5rem;
    max-height: 300px;
    overflow-y: auto; /* Keep this scrollbar for long lists */
}

/* Fix table containers to avoid double scrollbars */
.ms-table-container {
    margin-bottom: 1.5rem;
    overflow-x: auto; /* Keep horizontal scroll for tables if needed */
    max-height: none !important; /* Remove any height constraint */
}

/* Fix for active tests container */
#active-tests-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

/* Fix for predefined test suites */
#predefined-suites-container {
    overflow: visible !important; /* Remove any scrollbars */
    max-height: none !important; /* Remove height constraints */
}

.ms-predefined-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr); /* Fixed 3 columns */
    gap: 1rem;
    margin-bottom: 1.5rem;
    overflow: visible !important;
}

/* Media query adjustments */
@media (max-width: 768px) {
    .ms-layout {
        flex-direction: column;
    }
    
    .ms-predefined-grid {
        grid-template-columns: 1fr; /* Single column on mobile */
    }
    
    .ms-nav {
        width: 100%;
        height: auto;
        position: relative;
        top: 0;
    }

    #active-tests-container,
    .ms-predefined-grid {
        grid-template-columns: 1fr;
    }
}

/* Fix for modals */
.ms-modal {
    z-index: 9999;
}

/* Fix for available test suites container */
#available-testsuites {
    height: auto;
    max-height: 300px; /* Reasonable height for scrolling */
}

/* Responsive grid improvements */
.ms-grid {
    width: 100%;
}

.ms-grid-row {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.ms-grid-col {
    flex: 1 1 300px;
    margin-bottom: 0;
}

/* Override specific ms-card instances that need scrolling */
.ms-modal-content {
    max-height: 80vh;
    overflow-y: auto;
}

/**
 * Authentication middleware
 */
const { AUTH_CONFIG } = require('../config/app-config');
const userManager = require('../auth/user-manager');
const authService = require('../auth/auth-service');
const devConfig = require('../config/development');
const ValidationUtils = require('../utils/validation');

// Authentication middleware
const validateCredentials = async (req, res, next) => {
  // Extract credentials from request
  const uid = req.body.uid || req.query.uid;
  const password = req.body.password || req.query.password;

  // Skip authentication for development/testing (only if explicitly enabled)
  if (devConfig.shouldSkipAuth()) {
    const devUser = devConfig.getDevUser();
    req.user = {
      uid: uid || devUser.uid,
      role: devUser.role,
      name: devUser.name,
      isDevelopmentUser: true
    };
    devConfig.logDevWarning(`Authentication bypassed for user: ${req.user.uid}`);
    return next();
  }

  // Validate and sanitize input
  const credentialsValidation = ValidationUtils.validateCredentials({ uid, password });
  if (!credentialsValidation.isValid) {
    return res.status(400).json({
      success: false,
      message: 'Invalid input: ' + credentialsValidation.errors.join(', ')
    });
  }

  const sanitizedCredentials = credentialsValidation.sanitized;

  // Use centralized authentication service
  const clientIp = req.ip || req.connection.remoteAddress || 'unknown';

  try {
    const authResult = await authService.authenticate(
      sanitizedCredentials.uid,
      sanitizedCredentials.password,
      clientIp
    );

    if (authResult.success) {
      req.user = authResult.user;
      req.session = authResult.session;
      req.permissions = authResult.permissions;

      console.log(`✅ User authenticated: ${authResult.user.uid} (${authResult.user.role})`);
      next();
    } else {
      console.log(`❌ Authentication failed: ${authResult.error}`);

      const statusCode = authResult.code === 'RATE_LIMITED' || authResult.code === 'ACCOUNT_LOCKED' ? 429 : 401;

      return res.status(statusCode).json({
        success: false,
        message: authResult.error,
        code: authResult.code,
        retryAfter: authResult.retryAfter
      });
    }
  } catch (err) {
    console.error('Authentication error:', err);
    return res.status(500).json({
      success: false,
      message: 'Internal authentication error.'
    });
  }
};

/**
 * Permission checking middleware
 * @param {string} permission - Required permission
 * @returns {Function} Express middleware function
 */
const requirePermission = (permission) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required.'
      });
    }

    // Skip permission check for development users
    if (req.user.isDevelopmentUser) {
      devConfig.logDevWarning(`Permission check bypassed for dev user: ${permission}`);
      return next();
    }

    // Check if user has required permission
    if (!userManager.hasPermission(req.user.uid, permission)) {
      console.log(`❌ Permission denied: ${req.user.uid} lacks '${permission}' permission`);
      return res.status(403).json({
        success: false,
        message: `Access denied. Required permission: ${permission}`
      });
    }

    console.log(`✅ Permission granted: ${req.user.uid} has '${permission}' permission`);
    next();
  };
};

/**
 * Role checking middleware
 * @param {string|Array} roles - Required role(s)
 * @returns {Function} Express middleware function
 */
const requireRole = (roles) => {
  const requiredRoles = Array.isArray(roles) ? roles : [roles];

  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required.'
      });
    }

    // Skip role check for development users
    if (req.user.isDevelopmentUser) {
      devConfig.logDevWarning(`Role check bypassed for dev user: ${requiredRoles.join(', ')}`);
      return next();
    }

    // Check if user has required role
    if (!requiredRoles.includes(req.user.role)) {
      console.log(`❌ Role denied: ${req.user.uid} has '${req.user.role}' but needs one of: ${requiredRoles.join(', ')}`);
      return res.status(403).json({
        success: false,
        message: `Access denied. Required role: ${requiredRoles.join(' or ')}`
      });
    }

    console.log(`✅ Role granted: ${req.user.uid} has required role '${req.user.role}'`);
    next();
  };
};

module.exports = {
  validateCredentials,
  requirePermission,
  requireRole
};

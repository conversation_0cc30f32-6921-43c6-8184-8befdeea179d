/* Test Card UI Improvements */

/* Test card layout improvements */
.test-card {
  display: flex;
  flex-direction: column;
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.12);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.test-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Card header improvements */
.test-card-header {
  position: relative;
  padding: 16px 16px 12px;
  background-color: #fcfcfc;
  border-bottom: none;
}

/* Meta section for test metadata */
.test-card-meta {
  padding: 0 16px 12px;
  background-color: #fcfcfc;
  border-bottom: 1px solid #eaeaea;
}

.test-card .test-name {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 6px 0;
  padding-left: 16px;
  color: #333;
  line-height: 1.4;
  overflow: visible;
  white-space: normal;
  word-break: break-word;
}

.test-card .meta-info {
  color: #666;
  font-size: 12px;
  display: flex;
  align-items: center;
  padding-left: 16px;
}

/* Status indicator */
.status-indicator {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  border-radius: 0;
}

/* Card body improvements */
.test-card-body {
  padding: 16px;
  background-color: white;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* Test stats row */
.test-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.user-indicator {
  font-size: 13px;
  color: #505050;
  padding: 2px 8px;
  background-color: #f5f5f5;
  border-radius: 12px;
  max-width: 60%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Status badge */
.status-badge {
  font-size: 12px;
  font-weight: 600;
  padding: 3px 10px;
  border-radius: 12px;
  text-transform: uppercase;
  letter-spacing: 0.3px;
}

.status-badge.running {
  background-color: rgba(52, 168, 83, 0.15);
  color: #34a853;
}

.status-badge.passed {
  background-color: rgba(16, 124, 16, 0.15);
  color: #107c10;
}

.status-badge.failed {
  background-color: rgba(209, 52, 56, 0.15);
  color: #d13438;
}

.status-badge.pending {
  background-color: rgba(98, 100, 167, 0.15);
  color: #6264a7;
}

.status-badge.queued {
  background-color: rgba(0, 120, 212, 0.15);
  color: #0078d4;
}

/* Improved progress bar */
.progress-container {
  height: 8px;
  background-color: #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
  margin: 4px 0;
}

.progress-bar {
  height: 100%;
  font-size: 10px;
  font-weight: 600;
  line-height: 8px;
  text-align: right;
  padding-right: 6px;
  color: white;
  min-width: 20px;
  transition: width 0.6s ease;
}

/* Test metrics improvements */
.test-metrics-container {
  margin-top: 8px;
}

.test-metrics {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  font-size: 13px;
}

.metric-group {
  display: flex;
  align-items: center;
  gap: 6px;
}

.metric-label {
  color: #505050;
  font-weight: 500;
}

.metric {
  font-weight: 600;
}

.metric.passed {
  color: #107c10;
}

.metric.failed {
  color: #d13438;
}

.metric.total {
  color: #0078d4;
}

/* Card footer improvements */
.test-card-footer {
  padding: 12px 16px;
  background-color: #fafafa;
  border-top: 1px solid #eaeaea;
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.test-card-footer .btn {
  min-width: 80px;
  padding: 6px 12px;
  font-size: 13px;
  font-weight: 600;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.test-card-footer .btn-danger {
  background-color: #d13438;
  color: white;
  border: none;
}

.test-card-footer .btn-danger:hover {
  background-color: #c13035;
}

.test-card-footer .btn-light {
  background-color: #f3f2f1;
  color: #323130;
  border: 1px solid #edebe9;
}

.test-card-footer .btn-light:hover {
  background-color: #e9e8e7;
}

/* Animation for running tests */
@keyframes pulse {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.6;
  }
}

.test-card.running .status-badge {
  animation: pulse 1.5s infinite;
}

/* Test running indicator styles */
.test-running-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 16px 0;
  gap: 12px;
  min-height: 80px;
}

.running-message {
  font-size: 14px;
  color: #505050;
  font-weight: 500;
}

/* Spinner animation */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.spinner {
  width: 24px;
  height: 24px;
  border: 3px solid rgba(0, 120, 212, 0.2);
  border-radius: 50%;
  border-top-color: #0078d4;
  animation: spin 1s ease-in-out infinite;
}

/**
 * Test Reports Service
 * Handles generating and processing test reports
 */
const db = require('../database');

/**
 * Calculate duration between two timestamps
 * @param {string} start - Start timestamp
 * @param {string} end - End timestamp
 * @returns {string} - Formatted duration
 */
const calculateDuration = (start, end) => {
  if (!start || !end) return 'Unknown';
  const startTime = new Date(start);
  const endTime = new Date(end);
  const durationMs = endTime - startTime;

  // Format as minutes:seconds
  const minutes = Math.floor(durationMs / 60000);
  const seconds = Math.floor((durationMs % 60000) / 1000);
  return `${minutes}:${seconds.toString().padStart(2, '0')}`;
};

/**
 * Formats a user string (like "firstname.lastname" or an email) into "Firstname Lastname".
 * @param {string} userString - The user identifier string.
 * @returns {string} - The formatted display name.
 */
const formatUserForDisplay = (userString) => {
  if (!userString) return 'Unknown';

  let namePart = userString;
  if (userString.includes('@')) {
    namePart = userString.split('@')[0];
  }

  const nameWithSpaces = namePart.replace(/[._]/g, ' ');

  return nameWithSpaces
    .split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ');
};

/**
 * Get test reports with enriched data
 * @param {Object} query - Query parameters
 * @returns {Promise<Object>} - Object containing enriched reports and total records
 */
async function getTestReports(query = {}) {
  // db.getRecentRuns is now expected to return an object like:
  // { runs: [], totalRecords: N, highestTsnIdInResponse: M } for full fetch, OR
  // { newRuns: [], totalRecords: N, latestTsnIdInDelta: M } for incremental fetch.
  const dbResponse = await db.getRecentRuns(query);

  const reportsToEnrich = query.since_tsn_id ? (dbResponse.newRuns || []) : (dbResponse.runs || []);
  const enrichedReports = [];

  for (const run of reportsToEnrich) {
    try {
      enrichedReports.push({
        tsn_id: run.tsn_id,
        test_id: run.tc_id || run.test_id,
        type: run.type || 'Test Case',
        environment: run.envir || run.environment || 'QA02',
        status: run.status || 'unknown',
        start_time: run.start_time,
        end_time: run.end_time,
        duration: run.duration || calculateDuration(run.start_time, run.end_time),
        pass_rate: run.pass_rate || 0,
        uid: run.uid,
        user_display: formatUserForDisplay(run.user_display || run.uid),
        test_name: run.test_name,
        passed_cases: run.passed_cases,
        failed_cases: run.failed_cases,
        total_cases: run.total_cases
      });
    } catch (error) {
      console.error(`[Test Reports Service] Error processing report for tsn_id ${run.tsn_id}:`, error);
      // Optionally, add a placeholder or skip problematic run
       enrichedReports.push({
        tsn_id: run.tsn_id,
        uid: run.uid, // Ensure uid is present for filtering
        user_display: formatUserForDisplay(run.user_display || run.uid), // Ensure user_display is present
        status: 'processing_error',
        // Add other essential fields if possible, or mark as error
      });
    }
  }

  if (query.since_tsn_id) {
    return {
      newEnrichedRuns: enrichedReports,
      totalRecords: dbResponse.totalRecords || 0,
      latestTsnIdInDelta: dbResponse.latestTsnIdInDelta
    };
  } else {
    return {
      enrichedReports: enrichedReports,
      totalRecords: dbResponse.totalRecords || 0,
      highestTsnIdInResponse: dbResponse.highestTsnIdInResponse
    };
  }
}

module.exports = {
  getTestReports,
  calculateDuration
};

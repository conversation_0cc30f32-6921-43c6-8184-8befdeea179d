/**
 * Performance Monitor - Tracks and displays performance metrics for the config module
 */

class PerformanceMonitor {
    constructor() {
        this.isVisible = false;
        this.updateInterval = null;
        this.metrics = {
            requestCount: 0,
            cacheHitRatio: 0,
            averageResponseTime: 0,
            activePollingIntervals: 0,
            lastUpdateTime: null
        };
        
        this.createMonitorUI();
        console.log('PerformanceMonitor initialized');
    }

    createMonitorUI() {
        // Create performance metrics display
        const metricsDiv = document.createElement('div');
        metricsDiv.id = 'performance-metrics';
        metricsDiv.className = 'performance-metrics hidden';
        metricsDiv.innerHTML = `
            <h4>Performance Metrics</h4>
            <div class="metric-item">
                <span>Cache Hit Ratio:</span>
                <span class="metric-value" id="cache-hit-ratio">0%</span>
            </div>
            <div class="metric-item">
                <span>Avg Response Time:</span>
                <span class="metric-value" id="avg-response-time">0ms</span>
            </div>
            <div class="metric-item">
                <span>Active Intervals:</span>
                <span class="metric-value" id="active-intervals">0</span>
            </div>
            <div class="metric-item">
                <span>Requests Saved:</span>
                <span class="metric-value" id="requests-saved">0</span>
            </div>
            <div class="metric-item">
                <span>Cache Size:</span>
                <span class="metric-value" id="cache-size">0</span>
            </div>
            <div class="metric-item">
                <span>Last Update:</span>
                <span class="metric-value" id="last-update">Never</span>
            </div>
            <div style="margin-top: 8px; text-align: center;">
                <button onclick="window.performanceMonitor.toggle()" style="font-size: 10px; padding: 2px 6px;">Hide</button>
                <button onclick="window.performanceMonitor.reset()" style="font-size: 10px; padding: 2px 6px; margin-left: 4px;">Reset</button>
            </div>
        `;
        
        document.body.appendChild(metricsDiv);
        this.metricsDiv = metricsDiv;
        
        // Add keyboard shortcut to toggle (Ctrl+Shift+P)
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.shiftKey && e.key === 'P') {
                this.toggle();
            }
        });
    }

    show() {
        this.isVisible = true;
        this.metricsDiv.classList.remove('hidden');
        this.startUpdating();
        console.log('Performance monitor shown (Ctrl+Shift+P to toggle)');
    }

    hide() {
        this.isVisible = false;
        this.metricsDiv.classList.add('hidden');
        this.stopUpdating();
    }

    toggle() {
        if (this.isVisible) {
            this.hide();
        } else {
            this.show();
        }
    }

    startUpdating() {
        if (this.updateInterval) return;
        
        this.updateInterval = setInterval(() => {
            this.updateMetrics();
        }, 1000); // Update every second
    }

    stopUpdating() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
        }
    }

    updateMetrics() {
        try {
            // Get metrics from RequestManager
            const requestMetrics = window.requestManager ? window.requestManager.getMetrics() : {};
            
            // Get metrics from PollingCoordinator
            const pollingStatus = window.pollingCoordinator ? window.pollingCoordinator.getStatus() : {};
            
            // Update UI elements
            this.updateElement('cache-hit-ratio', `${Math.round((requestMetrics.cacheHitRatio || 0) * 100)}%`);
            this.updateElement('avg-response-time', `${Math.round(requestMetrics.averageResponseTime || 0)}ms`);
            this.updateElement('active-intervals', pollingStatus.activeIntervals ? pollingStatus.activeIntervals.length : 0);
            this.updateElement('requests-saved', requestMetrics.requestsSaved || 0);
            this.updateElement('cache-size', requestMetrics.cacheSize || 0);
            this.updateElement('last-update', new Date().toLocaleTimeString());
            
            // Update color coding based on performance
            this.updatePerformanceColors(requestMetrics);
            
        } catch (error) {
            console.warn('Error updating performance metrics:', error);
        }
    }

    updateElement(id, value) {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = value;
        }
    }

    updatePerformanceColors(metrics) {
        const cacheHitElement = document.getElementById('cache-hit-ratio');
        const responseTimeElement = document.getElementById('avg-response-time');
        
        if (cacheHitElement) {
            const hitRatio = metrics.cacheHitRatio || 0;
            if (hitRatio > 0.7) {
                cacheHitElement.style.color = '#107c10'; // Green
            } else if (hitRatio > 0.4) {
                cacheHitElement.style.color = '#ff8c00'; // Orange
            } else {
                cacheHitElement.style.color = '#d13438'; // Red
            }
        }
        
        if (responseTimeElement) {
            const responseTime = metrics.averageResponseTime || 0;
            if (responseTime < 500) {
                responseTimeElement.style.color = '#107c10'; // Green
            } else if (responseTime < 1000) {
                responseTimeElement.style.color = '#ff8c00'; // Orange
            } else {
                responseTimeElement.style.color = '#d13438'; // Red
            }
        }
    }

    reset() {
        if (window.requestManager) {
            window.requestManager.clearCache();
            window.requestManager.metrics = {
                requestsSaved: 0,
                cacheHits: 0,
                cacheMisses: 0,
                averageResponseTime: 0
            };
        }
        
        console.log('Performance metrics reset');
        this.updateMetrics();
    }

    /**
     * Log performance event for analysis
     */
    logEvent(eventType, data = {}) {
        const event = {
            timestamp: Date.now(),
            type: eventType,
            data: data
        };
        
        console.log(`[PERF] ${eventType}:`, data);
        
        // Store in session storage for analysis
        const events = JSON.parse(sessionStorage.getItem('perf_events') || '[]');
        events.push(event);
        
        // Keep only last 100 events
        if (events.length > 100) {
            events.splice(0, events.length - 100);
        }
        
        sessionStorage.setItem('perf_events', JSON.stringify(events));
    }

    /**
     * Get performance summary
     */
    getSummary() {
        const requestMetrics = window.requestManager ? window.requestManager.getMetrics() : {};
        const pollingStatus = window.pollingCoordinator ? window.pollingCoordinator.getStatus() : {};
        
        return {
            cacheEfficiency: {
                hitRatio: requestMetrics.cacheHitRatio || 0,
                totalRequests: (requestMetrics.cacheHits || 0) + (requestMetrics.cacheMisses || 0),
                requestsSaved: requestMetrics.requestsSaved || 0
            },
            responseTime: {
                average: requestMetrics.averageResponseTime || 0,
                rating: this.getRatingForResponseTime(requestMetrics.averageResponseTime || 0)
            },
            polling: {
                activeIntervals: pollingStatus.activeIntervals ? pollingStatus.activeIntervals.length : 0,
                subscriberCounts: pollingStatus.subscriberCounts || {},
                lastUpdates: pollingStatus.lastUpdates || {}
            },
            recommendations: this.getRecommendations(requestMetrics, pollingStatus)
        };
    }

    getRatingForResponseTime(responseTime) {
        if (responseTime < 300) return 'Excellent';
        if (responseTime < 500) return 'Good';
        if (responseTime < 1000) return 'Fair';
        return 'Poor';
    }

    getRecommendations(requestMetrics, pollingStatus) {
        const recommendations = [];
        
        if ((requestMetrics.cacheHitRatio || 0) < 0.5) {
            recommendations.push('Consider increasing cache TTL for better hit ratio');
        }
        
        if ((requestMetrics.averageResponseTime || 0) > 1000) {
            recommendations.push('Response times are high - check network or server performance');
        }
        
        const activeIntervals = pollingStatus.activeIntervals ? pollingStatus.activeIntervals.length : 0;
        if (activeIntervals > 3) {
            recommendations.push('Many active polling intervals - consider consolidating');
        }
        
        return recommendations;
    }

    /**
     * Export performance data for analysis
     */
    exportData() {
        const summary = this.getSummary();
        const events = JSON.parse(sessionStorage.getItem('perf_events') || '[]');
        
        const exportData = {
            timestamp: new Date().toISOString(),
            summary: summary,
            events: events
        };
        
        const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `smarttest-performance-${Date.now()}.json`;
        a.click();
        URL.revokeObjectURL(url);
        
        console.log('Performance data exported');
    }
}

// Create global instance
window.performanceMonitor = new PerformanceMonitor();

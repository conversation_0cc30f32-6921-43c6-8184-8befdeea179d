/**
 * Exact API Call Script
 * 
 * This script replicates the exact curl command provided in the example.
 */

const axios = require('axios');
const fs = require('fs');

async function main() {
  try {
    console.log('Making API call identical to the provided curl command...');

    // Exactly replicating the curl command data
    const data = "uid=<EMAIL>&password=test&tc_id=3180&envir=qa02&shell_host=jps-qa10-app01&file_path=/home/<USER>/&operatorConfigs=operatorNameConfigs&kafka_server=kafka-qa-a0.lab.wagerworks.com&dataCenter=GU&rgs_env=qa02&old_version=0&&networkType1=multi-site&networkType2=multi-site&sign=-&rate_src=local";
    
    // Make the API request with the exact same data
    const response = await axios.post(
      'http://mprts-qa02.lab.wagerworks.com:5080/AutoRun/CaseRunner',
      data,
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      }
    );
    
    console.log('API Response Status:', response.status);
    
    // Save the response data to a file for analysis
    fs.writeFileSync('api_response.html', typeof response.data === 'string' ? response.data : JSON.stringify(response.data, null, 2));
    console.log('Response saved to api_response.html');
    
    // Try to extract tsn_id from the response
    let tsnId = null;
    if (typeof response.data === 'string') {
      // Try various patterns
      const patterns = [
        /ReportSummary\?tsn_id=(\d+)/,
        /CaseEditor\?tsn_id=(\d+)/,
        /tsn_id=(\d+)/,
        /tsn_id.*?(\d+)/
      ];
      
      for (const pattern of patterns) {
        const match = response.data.match(pattern);
        if (match && match[1]) {
          tsnId = match[1];
          console.log(`Found tsn_id ${tsnId} using pattern: ${pattern}`);
          break;
        }
      }
    }
    
    if (tsnId) {
      console.log(`Successfully extracted tsn_id: ${tsnId}`);
    } else {
      console.log('Could not extract tsn_id from response');
      
      // Print part of the response to help debug
      if (typeof response.data === 'string') {
        console.log('Response snippet:');
        console.log(response.data.substring(0, 500) + '...');
      }
    }
    
  } catch (error) {
    console.error('API call error:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data (partial):', 
        typeof error.response.data === 'string' 
          ? error.response.data.substring(0, 500) 
          : JSON.stringify(error.response.data).substring(0, 500)
      );
    }
  }
}

main().catch(console.error); 
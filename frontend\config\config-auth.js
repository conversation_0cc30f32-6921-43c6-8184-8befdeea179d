/**
 * config-auth.js
 *
 * Authentication handler for the Test Runner page that matches Dashboard functionality
 */

class ConfigAuth {
    constructor() {
        // Initialize unified auth client
        this.unifiedAuthClient = window.unifiedAuthClient;

        this.elements = {
            loginModal: document.getElementById('login-modal'),
            loginForm: document.getElementById('login-form'),
            loginButton: document.getElementById('login-button'),
            logoutButton: document.getElementById('logout-button'),
            usernameInput: document.getElementById('username'),
            passwordInput: document.getElementById('password'),
            loginStatus: document.getElementById('login-status'),
            userDisplay: document.getElementById('user-display'),
            environmentDisplay: document.getElementById('environment-display')
        };

        this.isAuthenticated = false;
        this.currentUser = null;
    }

    /**
     * Initialize authentication system
     */
    init() {
        this.bindEvents();
        this.checkInitialAuth();
        this.updateUI();
    }

    /**
     * Bind event listeners
     */
    bindEvents() {
        if (this.elements.loginForm) {
            this.elements.loginForm.addEventListener('submit', (e) => this.handleLogin(e));
        }

        if (this.elements.loginButton) {
            this.elements.loginButton.addEventListener('click', () => this.showLoginModal());
        }

        if (this.elements.logoutButton) {
            this.elements.logoutButton.addEventListener('click', () => this.handleLogout());
        }

        // Close modal when clicking outside
        if (this.elements.loginModal) {
            this.elements.loginModal.addEventListener('click', (e) => {
                if (e.target === this.elements.loginModal) {
                    this.hideLoginModal();
                }
            });
        }
    }

    /**
     * Check for existing authentication
     */
    checkInitialAuth() {
        try {
            // Check if user is already authenticated via unified auth client
            if (this.unifiedAuthClient && this.unifiedAuthClient.isAuthenticated && this.unifiedAuthClient.currentUser) {
                this.isAuthenticated = true;
                this.currentUser = this.unifiedAuthClient.currentUser;
                this.hideLoginModal();

                console.log('✅ Config: Found existing JWT authentication for:', this.currentUser.uid);
                return true;
            } else {
                console.log('❌ Config: No valid JWT authentication found');
                this.showLoginModal();
                return false;
            }
        } catch (error) {
            console.error('Error checking JWT authentication:', error);
            this.showLoginModal();
            return false;
        }
    }

    /**
     * Show login modal
     */
    showLoginModal() {
        if (this.elements.loginModal) {
            this.elements.loginModal.style.display = 'flex';
            this.elements.loginModal.classList.add('active');
        }

        // Clear any previous error messages
        this.hideLoginError();

        if (this.elements.usernameInput) {
            this.elements.usernameInput.focus();
        }
    }

    /**
     * Hide login modal
     */
    hideLoginModal() {
        if (this.elements.loginModal) {
            this.elements.loginModal.style.display = 'none';
            this.elements.loginModal.classList.remove('active');
        }
    }

    /**
     * Handle login form submission
     */
    async handleLogin(event) {
        event.preventDefault();

        const username = this.elements.usernameInput?.value.trim();
        const password = this.elements.passwordInput?.value.trim();

        if (!username || !password) {
            this.showLoginError('Please enter both username and password');
            return;
        }

        try {
            // Use unified auth client for login
            const result = await this.unifiedAuthClient.login(username, password);

            if (result.success) {
                // Update authentication state
                this.isAuthenticated = true;
                this.currentUser = result.user;

                // Hide modal and update UI
                this.hideLoginModal();
                this.updateUI();
                this.hideLoginError();

                console.log('Login successful for:', username);

                // Update API service with credentials (for backward compatibility)
                if (window.apiService) {
                    window.apiService.setCredentials(username, password);
                }

                // Trigger a custom event for other components
                window.dispatchEvent(new CustomEvent('auth:login', {
                    detail: { username: result.user.uid, user: result.user }
                }));

            } else {
                this.showLoginError(result.error || 'Login failed. Please check your credentials.');
            }

        } catch (error) {
            console.error('Login error:', error);
            this.showLoginError('Login failed. Please try again.');
        }
    }

    /**
     * Handle logout
     */
    async handleLogout() {
        try {
            // Use unified auth client for logout
            await this.unifiedAuthClient.logout();

            // Update authentication state
            this.isAuthenticated = false;
            this.currentUser = null;

            // Clear API service credentials
            if (window.apiService && window.apiService.clearCredentials) {
                window.apiService.clearCredentials();
            }

            // Update UI and show login modal
            this.updateUI();
            this.showLoginModal();

            console.log('Logout successful');

            // Trigger a custom event for other components
            window.dispatchEvent(new CustomEvent('auth:logout'));

        } catch (error) {
            console.error('Logout error:', error);
        }
    }

    /**
     * Update UI based on authentication state
     */
    updateUI() {
        // Format user display name (same as dashboard)
        const formatUserDisplay = (email) => {
            if (!email || !email.includes('@')) return email;
            const username = email.split('@')[0];
            return username.split('.').map(part => 
                part.charAt(0).toUpperCase() + part.slice(1)
            ).join(' ');
        };

        // Update user display element (right side of header)
        if (this.elements.userDisplay) {
            if (this.isAuthenticated) {
                const friendlyName = formatUserDisplay(this.currentUser?.uid);
                this.elements.userDisplay.textContent = `Logged in as: ${friendlyName}`;
            } else {
                this.elements.userDisplay.textContent = 'Not logged in';
            }
        }

        // Hide environment display element (not needed)
        if (this.elements.environmentDisplay) {
            this.elements.environmentDisplay.style.display = 'none';
        }

        // Update button visibility
        if (this.elements.loginButton) {
            this.elements.loginButton.style.display = this.isAuthenticated ? 'none' : 'inline-flex';
        }

        if (this.elements.logoutButton) {
            this.elements.logoutButton.style.display = this.isAuthenticated ? 'inline-flex' : 'none';
        }
    }

    /**
     * Show login error message
     */
    showLoginError(message) {
        if (this.elements.loginStatus) {
            this.elements.loginStatus.textContent = message;
            this.elements.loginStatus.style.display = 'block';
        }
    }

    /**
     * Hide login error message
     */
    hideLoginError() {
        if (this.elements.loginStatus) {
            this.elements.loginStatus.style.display = 'none';
        }
    }

    /**
     * Get current JWT authentication status
     */
    getAuthStatus() {
        return {
            isAuthenticated: this.isAuthenticated,
            user: this.currentUser,
            credentials: this.isAuthenticated ? {
                uid: this.currentUser?.uid || sessionStorage.getItem('smarttest_uid'),
                isJWTAuth: true
            } : null
        };
    }
}

// Initialize authentication when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.configAuth = new ConfigAuth();
    window.configAuth.init();
});

// Export for use by other modules
window.ConfigAuth = ConfigAuth;

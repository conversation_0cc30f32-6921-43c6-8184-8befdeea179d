# SmartTest Authentication System Tests

This directory contains comprehensive tests for the SmartTest authentication system, covering security features, access controls, and integration scenarios.

## Test Structure

### Authentication Rejection Tests (`auth-rejection.test.js`)
Tests for unauthorized access scenarios and invalid credentials:
- Invalid login attempts (wrong credentials, malformed input)
- Unauthorized access to protected routes
- Permission-based access control
- Admin route access control
- Session validation
- Input validation and sanitization
- Security headers verification
- Error handling

### Security Integration Tests (`security-integration.test.js`)
Tests for security features integration:
- Security headers integration
- Rate limiting enforcement
- CSRF protection
- Input sanitization
- Session management
- Security logging
- Error handling
- Performance and DoS protection

## Running Tests

### Prerequisites
```bash
cd frontend/server/tests
npm install
```

### Test Commands

```bash
# Run all tests
npm test

# Run authentication rejection tests only
npm run test:auth

# Run security integration tests only
npm run test:security

# Run all tests sequentially (recommended for integration tests)
npm run test:all

# Run tests with coverage report
npm run test:coverage

# Run tests in watch mode (for development)
npm run test:watch

# Run tests with verbose output
npm run test:verbose

# Run tests for CI/CD (no watch, with coverage)
npm run test:ci
```

## Test Configuration

### Environment Variables
- `NODE_ENV=test` - Set automatically by test setup
- `TEST_VERBOSE=1` - Enable console output during tests

### Jest Configuration
- Test environment: Node.js
- Coverage collection from auth, middleware, and routes
- Custom test setup with utilities and mocks
- 10-second timeout for integration tests

## Test Utilities

The test setup provides global utilities:

### `testUtils`
- `generateTestUser()` - Create test user data
- `generateInvalidData()` - Create malicious test data
- `mockRequest()` - Mock Express request object
- `mockResponse()` - Mock Express response object
- `wait(ms)` - Async delay utility

### `testConstants`
- Common test values (emails, passwords, roles, etc.)

## Security Test Coverage

### Authentication & Authorization
- ✅ Invalid credential rejection
- ✅ Missing token rejection
- ✅ Malformed token rejection
- ✅ Expired token rejection
- ✅ Permission-based access control
- ✅ Role-based access control

### Input Validation
- ✅ SQL injection prevention
- ✅ XSS prevention
- ✅ Oversized payload rejection
- ✅ Null byte injection prevention
- ✅ Control character filtering

### Security Features
- ✅ Rate limiting enforcement
- ✅ CSRF protection
- ✅ Security headers application
- ✅ Session management
- ✅ Audit logging
- ✅ Error handling

### Infrastructure Security
- ✅ DoS protection
- ✅ Concurrent request handling
- ✅ Malformed header handling
- ✅ Information disclosure prevention

## Test Data

### Valid Test Data
```javascript
{
  uid: '<EMAIL>',
  password: 'TestPassword123!',
  role: 'tester',
  name: 'Test User'
}
```

### Invalid Test Data
- SQL injection attempts
- XSS payloads
- Oversized strings
- Null bytes and control characters
- Malformed emails and weak passwords

## Expected Test Results

### Authentication Rejection Tests
- All unauthorized access attempts should be rejected with appropriate HTTP status codes
- Error messages should not expose sensitive information
- Security headers should be present in all responses

### Security Integration Tests
- Rate limiting should prevent abuse
- CSRF protection should block unauthorized state changes
- Input sanitization should prevent injection attacks
- Session management should work correctly

## Troubleshooting

### Common Issues

1. **Tests timing out**
   - Increase Jest timeout in test-setup.js
   - Check for unresolved promises

2. **Rate limiting interfering with tests**
   - Tests run with mocked rate limiters
   - Use `--runInBand` to run tests sequentially

3. **Console output noise**
   - Set `TEST_VERBOSE=1` to see console output
   - Use `testUtils.enableConsole()` for debugging

### Debugging Tests

```bash
# Run specific test with verbose output
TEST_VERBOSE=1 npm test -- --testNamePattern="should reject login"

# Run tests with Node.js debugger
node --inspect-brk node_modules/.bin/jest --runInBand

# Generate coverage report
npm run test:coverage
open coverage/lcov-report/index.html
```

## Integration with CI/CD

For continuous integration, use:
```bash
npm run test:ci
```

This command:
- Runs all tests without watch mode
- Generates coverage reports
- Exits with appropriate status codes
- Suitable for automated testing pipelines

## Security Considerations

These tests verify that:
1. No unauthorized access is possible
2. All inputs are properly validated and sanitized
3. Security headers are correctly applied
4. Rate limiting prevents abuse
5. CSRF protection is effective
6. Error messages don't leak sensitive information
7. Session management is secure

Regular execution of these tests helps ensure the authentication system maintains its security posture as the codebase evolves.

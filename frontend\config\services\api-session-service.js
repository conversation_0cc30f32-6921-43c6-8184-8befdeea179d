/**
 * Test Session and Input Query Service for SmartTest Frontend
 *
 * This service extends the core ApiService functionality to provide methods for
 * working with test sessions and input queries.
 */

// Note: envConfig is available globally via window.envConfig

class TestSessionService {
  constructor() {
    // Use the existing ApiService instance
    this.apiService = window.apiService;
    
    // Default test session parameters - now using environment config
    this.defaultSessionParams = {
      environment: window.envConfig.current.name.toLowerCase(),
      description: 'Test session created via SmartTest frontend'
    };
    
    // Configure retry and polling settings
    this.pollingInterval = 5000; // 5 seconds between status checks
    this.maxPollAttempts = 60; // 5 minutes total polling time by default
  }
  
  /**
   * Create a new test session
   * @param {Object} params - Test session parameters
   * @param {string} params.test_type - Type of test (e.g. 'smoke', 'regression')
   * @param {string} params.environment - Test environment
   * @param {string} params.description - Session description
   * @returns {Promise<string>} - Test session ID
   */
  async createTestSession(params = {}) {
    const sessionParams = {
      ...this.defaultSessionParams,
      ...params
    };
    
    try {
      // First try the API client method
      try {
        const response = await window.apiClient.createTestSession(sessionParams);
        if (response && response.session_id) {
          return response.session_id;
        }
      } catch (clientError) {
        console.warn('API client createTestSession failed, falling back to apiService:', clientError);
      }
      
      // Fall back to API service method if available
      if (this.apiService.createTestSession) {
        const response = await this.apiService.createTestSession(sessionParams);
        return this.extractSessionId(response);
      }
      
      // Last resort - create test session through a custom implementation
      console.warn('Falling back to custom test session creation');
      const response = await fetch(`${window.envConfig.current.apiBaseUrl}/sessions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(sessionParams)
      });
      
      if (!response.ok) {
        throw new Error(`Failed to create test session: ${response.status} ${response.statusText}`);
      }
      
      const data = await response.json();
      return data.session_id || data.id;
    } catch (error) {
      console.error('Error creating test session:', error);
      throw error;
    }
  }
  
  /**
   * Get test session status
   * @param {string} sessionId - Test session ID
   * @returns {Promise<Object>} - Test session status
   */
  async getTestSessionStatus(sessionId) {
    if (!sessionId) {
      throw new Error('Session ID is required');
    }
    
    try {
      // First try the API client method
      try {
        const response = await window.apiClient.getTestSession(sessionId);
        if (response) {
          return response;
        }
      } catch (clientError) {
        console.warn('API client getTestSession failed, falling back to apiService:', clientError);
      }
      
      // Fall back to API service method
      if (this.apiService.getTestStatus) {
        return this.apiService.getTestStatus(sessionId);
      }
      
      // Last resort - custom implementation
      console.warn('Falling back to custom test session status retrieval');
      const response = await fetch(`${window.envConfig.current.apiBaseUrl}/sessions/${sessionId}`);
      
      if (!response.ok) {
        throw new Error(`Failed to get test session status: ${response.status} ${response.statusText}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error(`Error getting test session status for ${sessionId}:`, error);
      throw error;
    }
  }
  
  /**
   * Get all test sessions
   * @param {Object} options - Query options
   * @param {number} options.limit - Maximum number of sessions to retrieve
   * @param {number} options.offset - Offset for pagination
   * @param {string} options.status - Filter by status
   * @returns {Promise<Array>} - Array of test sessions
   */
  async getAllTestSessions(options = {}) {
    try {
      // First try the API client method
      try {
        const response = await window.apiClient.getTestSessions(options);
        if (response) {
          return response;
        }
      } catch (clientError) {
        console.warn('API client getTestSessions failed, falling back to apiService:', clientError);
      }
      
      // Fall back to custom implementation
      console.warn('Falling back to custom test sessions retrieval');
      
      // Build query string for parameters
      const queryParams = new URLSearchParams();
      if (options.limit) queryParams.append('limit', options.limit);
      if (options.offset) queryParams.append('offset', options.offset);
      if (options.status) queryParams.append('status', options.status);
      
      const queryString = queryParams.toString() ? `?${queryParams.toString()}` : '';
      const response = await fetch(`${window.envConfig.current.apiBaseUrl}/sessions${queryString}`);
      
      if (!response.ok) {
        throw new Error(`Failed to get test sessions: ${response.status} ${response.statusText}`);
      }
      
      const data = await response.json();
      return data.sessions || data;
    } catch (error) {
      console.error('Error getting test sessions:', error);
      throw error;
    }
  }
  
  /**
   * Update test session status
   * @param {string} sessionId - Test session ID
   * @param {string} status - New status ('created', 'running', 'completed', 'failed', 'cancelled')
   * @param {number} progress - Optional progress percentage (0-100)
   * @returns {Promise<boolean>} - Success status
   */
  async updateTestSessionStatus(sessionId, status, progress = null) {
    if (!sessionId || !status) {
      throw new Error('Session ID and status are required');
    }
    
    try {
      // First try the API client method
      try {
        const response = await window.apiClient.updateTestSessionStatus(sessionId, {
          status,
          progress
        });
        return true;
      } catch (clientError) {
        console.warn('API client updateTestSessionStatus failed, falling back to custom implementation:', clientError);
      }
      
      // Custom implementation
      const response = await fetch(`${window.envConfig.current.apiBaseUrl}/sessions/${sessionId}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          status,
          progress
        })
      });
      
      return response.ok;
    } catch (error) {
      console.error(`Error updating test session status for ${sessionId}:`, error);
      throw error;
    }
  }
  
  /**
   * Get test session report
   * @param {string} sessionId - Test session ID
   * @returns {Promise<Object>} - Test session report
   */
  async getTestSessionReport(sessionId) {
    if (!sessionId) {
      throw new Error('Session ID is required');
    }
    
    try {
      // Try the API service first (handles fallbacks for us)
      if (this.apiService.getReportSummary) {
        return this.apiService.getReportSummary(sessionId);
      }
      
      // Fall back to custom implementation
      console.warn('Falling back to custom report retrieval');
      const response = await fetch(`${window.envConfig.current.apiBaseUrl}/sessions/${sessionId}/report`);
      
      if (!response.ok) {
        throw new Error(`Failed to get test session report: ${response.status} ${response.statusText}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error(`Error getting test session report for ${sessionId}:`, error);
      throw error;
    }
  }
  
  /**
   * Log a test input query
   * @param {Object} params - Query parameters
   * @param {string} params.session_id - Test session ID
   * @param {string} params.query - Input query
   * @param {number} params.execution_time - Query execution time in ms
   * @param {string} params.status - Query status ('success', 'error', 'warning')
   * @param {string} params.result - Optional query result
   * @returns {Promise<string>} - Query ID
   */
  async logInputQuery(params = {}) {
    const requiredParams = ['session_id', 'query', 'execution_time', 'status'];
    requiredParams.forEach(param => {
      if (params[param] === undefined) {
        throw new Error(`Missing required parameter: ${param}`);
      }
    });
    
    try {
      // First try the API client method
      try {
        const response = await window.apiClient.logInputQuery(params);
        return response.query_id || response.id;
      } catch (clientError) {
        console.warn('API client logInputQuery failed, falling back to apiService:', clientError);
      }
      
      // Fall back to API service method
      if (this.apiService.logInputQuery) {
        const response = await this.apiService.logInputQuery(params);
        return response.query_id || response.id || true;
      }
      
      // Last resort - custom implementation
      console.warn('Falling back to custom query logging');
      const response = await fetch(`${window.envConfig.current.apiBaseUrl}/queries`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(params)
      });
      
      if (!response.ok) {
        throw new Error(`Failed to log input query: ${response.status} ${response.statusText}`);
      }
      
      const data = await response.json();
      return data.query_id || data.id;
    } catch (error) {
      console.error('Error logging input query:', error);
      throw error;
    }
  }
  
  /**
   * Get input queries for a session
   * @param {string} sessionId - Test session ID
   * @param {Object} options - Query options
   * @param {number} options.limit - Maximum number of queries to retrieve
   * @param {number} options.offset - Offset for pagination
   * @param {string} options.status - Filter by status
   * @returns {Promise<Array>} - Array of input queries
   */
  async getInputQueries(sessionId, options = {}) {
    if (!sessionId) {
      throw new Error('Session ID is required');
    }
    
    try {
      // First try the API client method
      try {
        const response = await window.apiClient.getInputQueries(sessionId, options);
        return response;
      } catch (clientError) {
        console.warn('API client getInputQueries failed, falling back to custom implementation:', clientError);
      }
      
      // Build query string for parameters
      const queryParams = new URLSearchParams();
      if (options.limit) queryParams.append('limit', options.limit);
      if (options.offset) queryParams.append('offset', options.offset);
      if (options.status) queryParams.append('status', options.status);
      
      const queryString = queryParams.toString() ? `?${queryParams.toString()}` : '';
      const response = await fetch(`${window.envConfig.current.apiBaseUrl}/sessions/${sessionId}/queries${queryString}`);
      
      if (!response.ok) {
        throw new Error(`Failed to get input queries: ${response.status} ${response.statusText}`);
      }
      
      const data = await response.json();
      return data.queries || data;
    } catch (error) {
      console.error(`Error getting input queries for session ${sessionId}:`, error);
      throw error;
    }
  }
  
  /**
   * Get query execution stats for a session
   * @param {string} sessionId - Test session ID
   * @returns {Promise<Object>} - Query execution statistics
   */
  async getQueryStats(sessionId) {
    if (!sessionId) {
      throw new Error('Session ID is required');
    }
    
    try {
      // First try the API client method
      try {
        const response = await window.apiClient.getQueryStats(sessionId);
        return response;
      } catch (clientError) {
        console.warn('API client getQueryStats failed, falling back to custom implementation:', clientError);
      }
      
      // Custom implementation
      const response = await fetch(`${window.envConfig.current.apiBaseUrl}/sessions/${sessionId}/query-stats`);
      
      if (!response.ok) {
        throw new Error(`Failed to get query stats: ${response.status} ${response.statusText}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error(`Error getting query stats for session ${sessionId}:`, error);
      throw error;
    }
  }
  
  /**
   * Extract session ID from response
   * @param {any} response - API response
   * @returns {string} - Session ID or null
   */
  extractSessionId(response) {
    if (!response) return null;
    
    if (typeof response === 'object') {
      // Check various property names
      if (response.session_id) return response.session_id;
      if (response.sessionId) return response.sessionId;
      if (response.id) return response.id;
      if (response.data && response.data.session_id) return response.data.session_id;
    }
    
    if (typeof response === 'string') {
      // Try to extract using regex
      const patterns = [
        /session_id=(\w+)/,
        /sessionId=(\w+)/,
        /session ID:?\s*(\w+)/i
      ];
      
      for (const pattern of patterns) {
        const match = response.match(pattern);
        if (match && match[1]) {
          return match[1];
        }
      }
    }
    
    console.warn('Could not extract session ID from response:', response);
    return null;
  }
  
  /**
   * Run predefined test suite
   * @param {string} suiteType - Test suite type ('smoke', 'regression', 'performance', 'security')
   * @param {Object} options - Additional options
   * @returns {Promise<string>} - Test session ID
   */
  async runPredefinedTestSuite(suiteType, options = {}) {
    // Map suite types to test suite IDs
    const suiteMap = {
      smoke: options.smokeTestSuiteId || 101,
      regression: options.regressionTestSuiteId || 102,
      performance: options.performanceTestSuiteId || 103, 
      security: options.securityTestSuiteId || 104
    };
    
    if (!suiteType || !suiteMap[suiteType.toLowerCase()]) {
      throw new Error(`Unknown suite type: ${suiteType}. Valid types: ${Object.keys(suiteMap).join(', ')}`);
    }
    
    // Create a test session
    const sessionId = await this.createTestSession({
      test_type: suiteType,
      environment: options.environment || this.defaultSessionParams.environment,
      description: options.description || `${suiteType.charAt(0).toUpperCase() + suiteType.slice(1)} test suite execution`
    });
    
    // Update status to running
    await this.updateTestSessionStatus(sessionId, 'running');
    
    // Run the test suite
    try {
      const tsId = suiteMap[suiteType.toLowerCase()];
      console.log(`Running ${suiteType} test suite (ID: ${tsId})`);
      
      const tsnId = await this.apiService.runTestSuite(tsId, {
        environment: options.environment || this.defaultSessionParams.environment,
        ...options.testParams
      });
      
      // Log the execution
      await this.logInputQuery({
        session_id: sessionId,
        query: `Executed ${suiteType} test suite (ID: ${tsId})`,
        execution_time: 0, // Will be updated later
        status: 'success',
        result: JSON.stringify({ tsn_id: tsnId })
      });
      
      // Start polling for test completion
      this.pollTestStatus(tsnId, sessionId);
      
      return sessionId;
    } catch (error) {
      // Update session status to failed
      await this.updateTestSessionStatus(sessionId, 'failed');
      
      // Log the error
      await this.logInputQuery({
        session_id: sessionId,
        query: `Executed ${suiteType} test suite`,
        execution_time: 0,
        status: 'error',
        result: error.message
      });
      
      console.error(`Error running ${suiteType} test suite:`, error);
      throw error;
    }
  }
  
  /**
   * Run a custom test suite
   * @param {Object} options - Custom suite options
   * @param {string} options.name - Suite name
   * @param {Array<number>} options.testCases - Array of test case IDs
   * @param {string} options.environment - Test environment
   * @returns {Promise<string>} - Test session ID
   */
  async runCustomTestSuite(options = {}) {
    if (!options.name || !options.testCases || options.testCases.length === 0) {
      throw new Error('Suite name and test cases are required');
    }
    
    // Create a test session
    const sessionId = await this.createTestSession({
      test_type: 'custom',
      environment: options.environment || this.defaultSessionParams.environment,
      description: `Custom test suite: ${options.name}`
    });
    
    // Update status to running
    await this.updateTestSessionStatus(sessionId, 'running');
    
    try {
      console.log(`Running custom test suite with ${options.testCases.length} test cases`);
      
      // Use the runDynamicTestSuite method if available
      let tsnId;
      if (this.apiService.runDynamicTestSuite) {
        tsnId = await this.apiService.runDynamicTestSuite({
          name: options.name,
          description: options.description || `Custom test suite created at ${new Date().toISOString()}`,
          test_cases: options.testCases,
          environment: options.environment || this.defaultSessionParams.environment
        });
      } else {
        // Fallback: Run the first test case
        console.warn('runDynamicTestSuite not available, running first test case only');
        tsnId = await this.apiService.runTestCase(options.testCases[0], {
          environment: options.environment || this.defaultSessionParams.environment
        });
      }
      
      // Log the execution
      await this.logInputQuery({
        session_id: sessionId,
        query: `Executed custom test suite with ${options.testCases.length} test cases`,
        execution_time: 0,
        status: 'success',
        result: JSON.stringify({ tsn_id: tsnId, test_cases: options.testCases })
      });
      
      // Start polling for test completion
      this.pollTestStatus(tsnId, sessionId);
      
      return sessionId;
    } catch (error) {
      // Update session status to failed
      await this.updateTestSessionStatus(sessionId, 'failed');
      
      // Log the error
      await this.logInputQuery({
        session_id: sessionId,
        query: `Executed custom test suite with ${options.testCases.length} test cases`,
        execution_time: 0,
        status: 'error',
        result: error.message
      });
      
      console.error('Error running custom test suite:', error);
      throw error;
    }
  }
  
  /**
   * Poll for test status until completion
   * @param {number} tsnId - Test suite run ID
   * @param {string} sessionId - Session ID to update
   * @private
   */
  async pollTestStatus(tsnId, sessionId) {
    let attempts = 0;
    let isComplete = false;
    
    const poll = async () => {
      try {
        if (attempts >= this.maxPollAttempts) {
          console.warn(`Test polling timed out after ${attempts} attempts`);
          await this.updateTestSessionStatus(sessionId, 'unknown');
          return;
        }
        
        attempts++;
        
        const status = await this.apiService.getTestStatus(tsnId);
        isComplete = status.isComplete || status.status === 'completed' || status.status === 'failed';
        
        if (isComplete) {
          // Update session status
          const newStatus = (status.status === 'failed' || status.failures > 0) ? 'failed' : 'completed';
          await this.updateTestSessionStatus(sessionId, newStatus);
          
          // Log the completion
          await this.logInputQuery({
            session_id: sessionId,
            query: `Test completion check`,
            execution_time: attempts * this.pollingInterval,
            status: newStatus === 'completed' ? 'success' : 'error',
            result: JSON.stringify(status)
          });
          
          console.log(`Test ${tsnId} completed with status: ${newStatus}`);
        } else {
          // Continue polling
          setTimeout(poll, this.pollingInterval);
        }
      } catch (error) {
        console.error(`Error polling test status:`, error);
        // Continue polling despite errors
        setTimeout(poll, this.pollingInterval);
      }
    };
    
    // Start polling
    setTimeout(poll, this.pollingInterval);
  }
}

// Create and export a singleton instance
const testSessionService = new TestSessionService();

// Export for use in other modules
window.testSessionService = testSessionService;

// Auto-initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  console.log('Test Session Service initialized');
});
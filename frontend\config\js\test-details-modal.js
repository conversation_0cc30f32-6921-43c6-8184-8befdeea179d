/**
 * Test Details Modal
 * Handles the display of detailed test information in a modal dialog
 */

class TestDetailsModal {
    constructor() {
        this.modal = null;
        this.isVisible = false;
        this.currentTsnId = null;
        
        this.init();
    }
    
    init() {
        // Create modal with our original beautiful design
        const modalHtml = `
            <div id="test-details-modal" class="modal">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3 id="modal-title">Test Details</h3>
                        <button class="close-btn" type="button" aria-label="Close modal">&times;</button>
                    </div>
                    <div class="modal-body" id="modal-body">
                        <!-- Content will be populated dynamically -->
                    </div>
                </div>
            </div>
        `;

        // Add to DOM
        document.body.insertAdjacentHTML('beforeend', modalHtml);
        this.modal = document.getElementById('test-details-modal');

        // Event listeners
        const closeBtn = this.modal.querySelector('.close-btn');
        closeBtn.addEventListener('click', () => this.hide());
        
        this.modal.addEventListener('click', (e) => {
            if (e.target === this.modal) this.hide();
        });

        // ESC key to close
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.modal.classList.contains('show')) {
                this.hide();
            }
        });
    }

    /**
     * Get user credentials from various sources (JWT-based)
     * @returns {Object} credentials object with uid (password not needed for JWT)
     */
    getCredentials() {
        let credentials = { uid: null, password: null };

        // Try to get from unified auth client first (JWT-based)
        if (window.unifiedAuthClient && window.unifiedAuthClient.isAuthenticated && window.unifiedAuthClient.currentUser) {
            const user = window.unifiedAuthClient.currentUser;
            credentials = { uid: user.uid, password: '', isJWTAuth: true };
            console.log('Using JWT credentials from unified auth client:', user.uid);
            return credentials;
        }

        // Try to get from window.apiService
        if (window.apiService && window.apiService.credentials && window.apiService.credentials.uid) {
            credentials = window.apiService.credentials;
            // For JWT auth, password might be empty - that's OK
            if (!credentials.password) {
                credentials.password = '';
                credentials.isJWTAuth = true;
            }
            console.log('Using credentials from API service:', credentials.uid);
            return credentials;
        }

        // Try to get from appState
        if (window.appState && window.appState.credentials && window.appState.credentials.uid) {
            credentials = window.appState.credentials;
            console.log('Using credentials from appState:', credentials.uid);
            return credentials;
        }

        // Try to get from session storage (JWT-based)
        const sessionUid = sessionStorage.getItem('smarttest_uid');
        if (sessionUid) {
            credentials = { uid: sessionUid, password: '', isJWTAuth: true };
            console.log('Using JWT credentials from session storage:', sessionUid);
            return credentials;
        }

        // Try to get from localStorage (legacy fallback)
        try {
            const storedCredentials = localStorage.getItem('userCredentials');
            if (storedCredentials) {
                credentials = JSON.parse(storedCredentials);
                console.log('Using credentials from localStorage:', credentials.uid);
                return credentials;
            }
        } catch (error) {
            console.warn('Error parsing stored credentials:', error);
        }

        console.warn('No credentials found in any source');
        return credentials;
    }

    /**
     * Format date/time for display in user's local timezone
     * @param {string} dateTime - ISO date string
     * @returns {string} formatted date/time
     */
    formatDateTime(dateTime) {
        if (!dateTime) return 'N/A';

        try {
            const date = new Date(dateTime);
            if (isNaN(date.getTime())) return 'Invalid Date';

            // Use user's local timezone with readable format
            return date.toLocaleString('en-US', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: false,
                timeZoneName: 'short' // Show timezone abbreviation (e.g., PST, EST)
            });
        } catch (error) {
            console.warn('Error formatting date:', error);
            return 'Invalid Date';
        }
    }

    /**
     * Calculate duration between start and end times
     * @param {string} startTime - ISO start time
     * @param {string} endTime - ISO end time
     * @returns {string} formatted duration
     */
    calculateDuration(startTime, endTime) {
        if (!startTime) return 'N/A';
        if (!endTime) return 'Running...';
        
        try {
            const start = new Date(startTime);
            const end = new Date(endTime);
            
            if (isNaN(start.getTime()) || isNaN(end.getTime())) {
                return 'Invalid Duration';
            }
            
            const diffMs = end.getTime() - start.getTime();
            const diffSecs = Math.floor(diffMs / 1000);
            const diffMins = Math.floor(diffSecs / 60);
            const diffHours = Math.floor(diffMins / 60);
            
            const hours = diffHours;
            const minutes = diffMins % 60;
            const seconds = diffSecs % 60;
            
            if (hours > 0) {
                return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            } else {
                return `${minutes}:${seconds.toString().padStart(2, '0')}`;
            }
        } catch (error) {
            console.warn('Error calculating duration:', error);
            return 'Invalid Duration';
        }
    }

    async show(tsnId) {
        this.currentTsnId = tsnId;

        // Update title (will be updated again with better name after loading data)
        const title = this.modal.querySelector('#modal-title');
        title.textContent = `Test Details`;

        // Show loading state
        this.showLoading();
        
        // Show modal with animation
        this.modal.style.display = 'block';
        this.modal.classList.add('show');

        try {
            // Get credentials
            const credentials = this.getCredentials();
            if (!credentials.uid) {
                throw new Error('No credentials available. Please log in first.');
            }

            // For JWT auth, password is not required
            if (!credentials.password && !credentials.isJWTAuth) {
                console.warn('No password found, but proceeding with JWT authentication');
                credentials.password = ''; // Set empty password for JWT auth
            }

            let testDetails = null;

            // Try external API first since it's faster and more reliable
            console.log('Attempting to load test details from external API first...');
            try {
                if (typeof loadTestDetailsFromExternalApi === 'function') {
                    testDetails = await loadTestDetailsFromExternalApi(tsnId, credentials);
                    console.log('Successfully loaded from external API:', testDetails);
                }
            } catch (externalError) {
                console.log('External API failed, trying other APIs:', externalError.message);

                // Try optimized config API service as fallback
                if (window.configApiService) {
                    try {
                        console.log('Using optimized ConfigApiService as fallback...');
                        testDetails = await window.configApiService.getTestDetails(tsnId, credentials);
                        console.log('Successfully loaded from ConfigApiService:', testDetails);
                    } catch (configError) {
                        console.log('ConfigApiService failed, trying database API:', configError.message);

                        // Database API as last resort
                        try {
                            console.log('Attempting to load test details from database API...');
                            if (typeof loadTestDetailsFromDatabaseApi === 'function') {
                                testDetails = await loadTestDetailsFromDatabaseApi(tsnId, credentials);
                                console.log('Successfully loaded from database API:', testDetails);
                            }
                        } catch (dbError) {
                            console.error('All APIs failed:', dbError.message);
                            throw new Error(`Failed to load test details: ${externalError.message}`);
                        }
                    }
                } else {
                    // Fallback if ConfigApiService not available
                    console.log('ConfigApiService not available, trying database API...');
                    try {
                        if (typeof loadTestDetailsFromDatabaseApi === 'function') {
                            testDetails = await loadTestDetailsFromDatabaseApi(tsnId, credentials);
                            console.log('Successfully loaded from database API:', testDetails);
                        }
                    } catch (dbError) {
                        console.error('All APIs failed:', dbError.message);
                        throw new Error(`Failed to load test details: ${externalError.message}`);
                    }
                }
            }

            if (testDetails) {
                this.showTestDetails(testDetails);
            } else {
                throw new Error('No test details returned from API');
            }

        } catch (error) {
            console.error('Error loading test details:', error);
            this.showError('Error loading test details: ' + error.message);
        }
    }
    
    hide() {
        this.modal.style.display = 'none';
        this.modal.classList.remove('show');
        this.currentTsnId = null;
    }
    
    showLoading() {
        const modalBody = this.modal.querySelector('#modal-body');
        modalBody.innerHTML = `
            <div style="text-align: center; padding: 40px;">
                <div class="loading-spinner"></div>
                <p class="loading-text">Loading test details...</p>
            </div>
        `;
    }
    
    showError(message) {
        const modalBody = this.modal.querySelector('#modal-body');
        modalBody.innerHTML = `
            <div style="text-align: center; padding: 40px;">
                <p style="color: #d13438; font-size: 16px; margin: 0;">${message}</p>
            </div>
        `;
    }
    
    transformTestData(testDetails) {
        // Determine the best display name for the test
        let displayName = 'Unknown Test';
        let sessionId = 'N/A';
        let environment = 'Unknown';
        let shellHost = 'Unknown';
        let userName = 'Unknown User';
        
        // Extract session ID - prioritize tsn_id, then id
        if (testDetails.tsn_id && testDetails.tsn_id !== 'N/A') {
            sessionId = testDetails.tsn_id;
        } else if (testDetails.id && testDetails.id !== 'N/A') {
            sessionId = testDetails.id;
        }
        
        // Parse environment and other parameters from originalParameters or variables
        const params = testDetails.originalParameters || testDetails.variables || {};
        
        // Extract environment (envir parameter) - handle multiple formats
        if (params.envir) {
            // Extract just the environment part (qa01, qa02, etc.)
            const envirMatch = params.envir.match(/^(qa\d+)/i);
            if (envirMatch) {
                environment = envirMatch[1].toUpperCase(); // QA01, QA02, etc.
            } else {
                environment = params.envir;
            }
        } else if (testDetails.environment && testDetails.environment !== 'Unknown') {
            environment = testDetails.environment;
        } else if (testDetails.envir && testDetails.envir !== 'Unknown') {
            // Handle direct envir field
            environment = testDetails.envir;
        } else if (testDetails.env && testDetails.env !== 'Unknown') {
            // Handle env field variation
            environment = testDetails.env;
        } else {
            // Fallback: try to get environment from system configuration
            // Check if there's a global environment configuration available
            if (window.apiService && window.apiService.envConfig && window.apiService.envConfig.environment) {
                environment = window.apiService.envConfig.environment.toUpperCase();
            } else {
                // Last resort: use QA02 as default since that's what the system is configured for
                environment = 'QA02';
            }
        }
        
        // Extract shell host
        if (params.shell_host) {
            shellHost = params.shell_host;
        } else if (params.envir) {
            // Try to extract from envir string
            const hostMatch = params.envir.match(/shell_host=([^\s&]+)/);
            if (hostMatch) {
                shellHost = hostMatch[1];
            }
        }
        
        // Extract and format user name
        let userEmail = '';
        if (params.uid) {
            userEmail = params.uid;
        } else if (testDetails.user) {
            userEmail = testDetails.user;
        }
        
        if (userEmail && userEmail.includes('@')) {
            // Extract name from email (e.g., <EMAIL> -> Yuri Shvartz)
            const namePart = userEmail.split('@')[0];
            const nameParts = namePart.split('.');
            if (nameParts.length >= 2) {
                userName = nameParts.map(part => 
                    part.charAt(0).toUpperCase() + part.slice(1).toLowerCase()
                ).join(' ');
            } else {
                userName = namePart.charAt(0).toUpperCase() + namePart.slice(1).toLowerCase();
            }
        } else if (userEmail) {
            userName = userEmail;
        }
        
        // Determine best test name based on available data
        if (testDetails.name && testDetails.name !== 'Unknown Test') {
            // Use the provided name if it's meaningful
            displayName = testDetails.name;
        } else if (testDetails.tc_name && testDetails.tc_name.trim()) {
            // Use test case name if available
            displayName = testDetails.tc_name;
        } else if (testDetails.comments && testDetails.comments.trim()) {
            // Use comments as display name if available
            displayName = testDetails.comments;
        } else if (testDetails.comment && testDetails.comment.trim()) {
            // Use comment field if available
            displayName = testDetails.comment;
        } else if (testDetails.test_cases && testDetails.test_cases.length === 1) {
            // Single test case - use its description as the test name
            const testCase = testDetails.test_cases[0];
            if (testCase.description && testCase.description.trim()) {
                displayName = `Test Case ${testCase.tc_id}: ${testCase.description.trim()}`;
            } else if (testCase.tc_id) {
                displayName = `Test Case ${testCase.tc_id}`;
            }
        } else if (testDetails.test_cases && testDetails.test_cases.length > 1) {
            // Multiple test cases - create a descriptive name
            displayName = `Test Suite (${testDetails.test_cases.length} cases)`;
        } else if (testDetails.tc_id) {
            // Use test case ID if available
            displayName = `Test Case ${testDetails.tc_id}`;
        } else if (testDetails.test_id && testDetails.test_id !== '') {
            // Fallback to test ID if available
            displayName = `Test ${testDetails.test_id}`;
        } else if (sessionId !== 'N/A') {
            // Last resort - use session ID
            displayName = `Test Session ${sessionId}`;
        }
        
        return {
            displayName: displayName,
            sessionId: sessionId,
            environment: environment,
            shellHost: shellHost,
            userName: userName,
            userEmail: userEmail
        };
    }
    
    showTestDetails(testDetails) {
        const modalBody = document.getElementById('modal-body');
        const statusClass = this.getStatusClass(testDetails.status);
        const statusIcon = this.getStatusIcon(testDetails.status);

        // Transform test data to get better display names
        const displayData = this.transformTestData(testDetails);

        // Update modal title with better display name
        const title = this.modal.querySelector('#modal-title');
        title.textContent = displayData.displayName;

        // Format the status text based on the status value
        const statusText = testDetails.status ? testDetails.status.toLowerCase() : 'unknown';
        const formattedStatus = statusText === 'success' ? 'passed' : statusText;
        
        modalBody.innerHTML = `
            <div class="test-details-container">
                <!-- Header Section with Status -->
                <div class="test-header">
                    <div class="test-title">
                        <h4>${displayData.displayName}</h4>
                        <div class="test-id">Session ID: ${displayData.sessionId}</div>
                    </div>
                    <div class="status-container">
                        <div class="status-badge ${statusClass}">
                            ${statusIcon} ${formattedStatus}
                        </div>
                    </div>
                </div>

                <!-- Test Information - Consolidated -->
                <div class="details-section">
                    <h5><i class="ms-Icon ms-Icon--Info"></i> Test Information</h5>
                    <div class="details-grid">
                        <div class="detail-row">
                            <span class="detail-label">Duration</span>
                            <span class="detail-value">${this.calculateDuration(testDetails.start_time, testDetails.end_time)}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Environment</span>
                            <span class="detail-value environment-tag">${displayData.environment}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Shell Host</span>
                            <span class="detail-value">${displayData.shellHost}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">User</span>
                            <span class="detail-value">${displayData.userName}</span>
                        </div>
                    </div>
                </div>

                <!-- Test Parameters Section -->
                ${this.renderParametersSection(testDetails)}

                <!-- Execution Timeline -->
                <div class="timeline-section">
                    <h5><i class="ms-Icon ms-Icon--Clock"></i> Execution Timeline</h5>
                    <div class="timestamps-grid">
                        <div class="timestamp-item">
                            <span class="timestamp-label">Started</span>
                            <span class="timestamp-value">${this.formatDateTime(testDetails.start_time) || 'Unknown'}</span>
                        </div>
                        <div class="timestamp-item">
                            <span class="timestamp-label">Completed</span>
                            <span class="timestamp-value">${this.formatDateTime(testDetails.end_time) || 'In Progress'}</span>
                        </div>
                    </div>
                </div>

                <!-- Comments Section -->
                ${testDetails.comments ? `
                <div class="comments-section">
                    <h5><i class="ms-Icon ms-Icon--Comment"></i> Comments</h5>
                    <div class="comment-box">${testDetails.comments}</div>
                </div>
                ` : ''}

                <!-- Actions -->
                ${testDetails.details_url ? `
                <div class="actions-section">
                    <a href="${testDetails.details_url}" target="_blank" class="action-button">
                        <i class="ms-Icon ms-Icon--View"></i> View Full Report
                    </a>
                </div>
                ` : ''}
            </div>
        `;
    }

    hide() {
        this.modal.style.display = 'none';
        this.modal.classList.remove('show');
        this.currentTsnId = null;
    }

    toggleParameters() {
        const content = this.modal.querySelector('.params-content');
        const icon = this.modal.querySelector('.toggle-icon');

        if (!content || !icon) return;

        if (content.style.display === 'none') {
            content.style.display = 'block';
            icon.textContent = '▼';
        } else {
            content.style.display = 'none';
            icon.textContent = '▶';
        }
    }

    renderParameters(parameters) {
        if (!parameters || Object.keys(parameters).length === 0) {
            return '';
        }

        const paramHtml = Object.entries(parameters)
            .map(([key, value]) => `
                <div class="detail-item">
                    <strong>${key}:</strong>
                    <span>${value}</span>
                </div>
            `).join('');

        return paramHtml;
    }

    renderParametersSection(testDetails) {
        const parameters = testDetails.originalParameters || testDetails.variables || {};

        if (!parameters || Object.keys(parameters).length === 0) {
            return ''; // Don't show section if no parameters
        }

        // Filter out parameters that are already shown in the main Test Information section
        const filteredParameters = Object.entries(parameters).filter(([key, value]) => {
            const lowerKey = key.toLowerCase();
            return lowerKey !== 'uid' &&
                   lowerKey !== 'envir' &&
                   lowerKey !== 'shell_host';
        });

        if (filteredParameters.length === 0) {
            return ''; // Don't show section if no additional parameters after filtering
        }

        const paramCount = filteredParameters.length;
        const parametersHtml = filteredParameters
            .map(([key, value]) => `
                <div class="param-row">
                    <span class="param-label">${key}</span>
                    <span class="param-value">${value || 'N/A'}</span>
                </div>
            `).join('');

        return `
            <div class="parameters-section">
                <h5><i class="ms-Icon ms-Icon--Settings"></i> Test Parameters</h5>
                <div class="params-toggle">
                    <button type="button" class="toggle-btn" onclick="window.testDetailsModal.toggleParameters()">
                        <span class="toggle-icon">▶</span> Show All Parameters (${paramCount})
                    </button>
                </div>
                <div class="params-content" style="display: none;">
                    <div class="params-grid">
                        ${parametersHtml}
                    </div>
                </div>
            </div>
        `;
    }

    getStatusClass(status) {
        if (!status) return 'unknown';
        const s = status.toLowerCase();
        if (s === 'success' || s === 'passed' || s === 'completed' || s === 'pass' || s === 'p') return 'passed';
        if (s === 'failed' || s === 'error' || s === 'fail' || s === 'f') return 'failed';
        if (s === 'running' || s === 'in progress' || s === 'active') return 'running';
        if (s === 'queued' || s === 'pending' || s === 'waiting') return 'queued';
        if (s === 'cancelled' || s === 'canceled' || s === 'stopped') return 'cancelled';
        return 'unknown';
    }

    getStatusIcon(status) {
        if (!status) return '?';
        const s = status.toLowerCase();
        if (s === 'success' || s === 'passed' || s === 'completed' || s === 'pass' || s === 'p') return '✓';
        if (s === 'failed' || s === 'error' || s === 'fail' || s === 'f') return '✗';
        if (s === 'running' || s === 'in progress' || s === 'active') return '⟳';
        if (s === 'queued' || s === 'pending' || s === 'waiting') return '⏳';
        if (s === 'cancelled' || s === 'canceled' || s === 'stopped') return '⏹';
        return '?';
    }
    
    // Public method to check if modal is visible
    isOpen() {
        return this.isVisible;
    }
    
    // Public method to get current TSN ID
    getCurrentTsnId() {
        return this.currentTsnId;
    }
}

// Initialize the modal when the script loads
document.addEventListener('DOMContentLoaded', () => {
    window.testDetailsModal = new TestDetailsModal();
});

// Also initialize immediately if DOM is already loaded
if (document.readyState === 'loading') {
    // DOM is still loading, wait for DOMContentLoaded
} else {
    // DOM is already loaded
    window.testDetailsModal = new TestDetailsModal();
}




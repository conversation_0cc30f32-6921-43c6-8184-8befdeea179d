<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Config Performance Fix Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        pre { background-color: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
        button { padding: 8px 16px; margin: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>Config Performance Fix Test</h1>
    
    <div id="status-container">
        <div class="status info">
            <strong>Testing Fix:</strong> Checking if the "Cannot read properties of undefined (reading 'subscribe')" error is resolved.
        </div>
    </div>
    
    <h2>Test Steps</h2>
    <ol>
        <li>Load simple performance optimizations</li>
        <li>Initialize config module</li>
        <li>Test polling functionality</li>
        <li>Verify no errors occur</li>
    </ol>
    
    <div>
        <button onclick="runTest()">Run Test</button>
        <button onclick="clearResults()">Clear Results</button>
    </div>
    
    <h2>Test Results</h2>
    <div id="results"></div>
    
    <h2>Console Output</h2>
    <pre id="console-output"></pre>
    
    <!-- Mock API Service for testing -->
    <script>
        // Mock API Service
        window.apiService = {
            credentials: { uid: '', password: '' },
            setCredentials: function(uid, password) {
                this.credentials = { uid, password };
                console.log('Mock API: Credentials set', { uid, password });
            },
            getRecentRuns: async function(options = {}) {
                console.log('Mock API: getRecentRuns called with options:', options);
                // Simulate API delay
                await new Promise(resolve => setTimeout(resolve, 100));
                
                // Return mock data
                return [
                    { tsn_id: '12345', tc_id: '3180', status: 'passed', name: 'Test 1', start_time: new Date().toISOString() },
                    { tsn_id: '12346', tc_id: '3181', status: 'running', name: 'Test 2', start_time: new Date().toISOString() },
                    { tsn_id: '12347', tc_id: '3182', status: 'failed', name: 'Test 3', start_time: new Date().toISOString() }
                ];
            },
            getActiveTests: async function() {
                console.log('Mock API: getActiveTests called');
                await new Promise(resolve => setTimeout(resolve, 50));
                return [
                    { tsn_id: '12346', tc_id: '3181', status: 'running', name: 'Test 2' }
                ];
            },
            getTestDetails: async function(tsnId) {
                console.log('Mock API: getTestDetails called for', tsnId);
                await new Promise(resolve => setTimeout(resolve, 75));
                return { tsn_id: tsnId, tc_id: '3180', status: 'passed', name: 'Test Details' };
            }
        };
        
        // Mock app state
        window.appState = {
            activeTests: new Map(),
            recentRunsCache: [],
            pollInterval: null,
            credentials: { uid: 'test', password: 'test' }
        };
        
        // Mock functions that config.js expects
        window.processRecentRunsData = function(data) {
            console.log('Mock: processRecentRunsData called with', data.length, 'items');
        };
        
        window.renderRecentRuns = function(data) {
            console.log('Mock: renderRecentRuns called with', data.length, 'items');
        };
        
        window.updateTestCounters = function() {
            console.log('Mock: updateTestCounters called');
        };
        
        window.updateActiveTestsDisplay = function(data) {
            console.log('Mock: updateActiveTestsDisplay called with', data.length, 'items');
        };
        
        // Capture console output
        const originalConsoleLog = console.log;
        const originalConsoleError = console.error;
        const originalConsoleWarn = console.warn;
        let consoleOutput = [];
        
        function captureConsole() {
            console.log = function(...args) {
                consoleOutput.push('LOG: ' + args.join(' '));
                originalConsoleLog.apply(console, args);
                updateConsoleOutput();
            };
            
            console.error = function(...args) {
                consoleOutput.push('ERROR: ' + args.join(' '));
                originalConsoleError.apply(console, args);
                updateConsoleOutput();
            };
            
            console.warn = function(...args) {
                consoleOutput.push('WARN: ' + args.join(' '));
                originalConsoleWarn.apply(console, args);
                updateConsoleOutput();
            };
        }
        
        function updateConsoleOutput() {
            document.getElementById('console-output').textContent = consoleOutput.slice(-20).join('\n');
        }
        
        function addResult(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `status ${type}`;
            resultDiv.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            resultsDiv.appendChild(resultDiv);
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
            consoleOutput = [];
            updateConsoleOutput();
        }
        
        async function runTest() {
            addResult('Starting performance fix test...', 'info');
            captureConsole();
            
            try {
                // Test 1: Check if simple optimizations are available
                if (typeof window.initializeSimpleOptimizations === 'function') {
                    addResult('✅ Simple optimizations are available', 'success');
                } else {
                    addResult('❌ Simple optimizations not found', 'error');
                    return;
                }
                
                // Test 2: Check if simple polling coordinator exists
                if (window.simplePollingCoordinator) {
                    addResult('✅ Simple polling coordinator exists', 'success');
                } else {
                    addResult('❌ Simple polling coordinator not found', 'error');
                    return;
                }
                
                // Test 3: Test the startPolling function logic
                addResult('Testing startPolling function...', 'info');
                
                // Simulate the config.js startPolling function
                function testStartPolling() {
                    console.log('Testing polling...');
                    
                    if (window.pollingCoordinator && typeof window.pollingCoordinator.subscribe === 'function') {
                        console.log('Using full optimized polling');
                        return 'full';
                    } else if (window.simplePollingCoordinator && typeof window.simplePollingCoordinator.subscribe === 'function') {
                        console.log('Using simple optimized polling');
                        return 'simple';
                    } else {
                        console.log('Using legacy polling');
                        return 'legacy';
                    }
                }
                
                const pollingType = testStartPolling();
                addResult(`✅ Polling type determined: ${pollingType}`, 'success');
                
                // Test 4: Test subscription
                if (pollingType === 'simple') {
                    addResult('Testing simple polling subscription...', 'info');
                    
                    let receivedData = false;
                    window.simplePollingCoordinator.subscribe('recentRuns', (data) => {
                        receivedData = true;
                        addResult(`✅ Received data callback with ${data.length} items`, 'success');
                    }, 'test-component');
                    
                    // Wait a moment and check if subscription worked
                    setTimeout(() => {
                        if (receivedData) {
                            addResult('✅ Subscription test passed', 'success');
                        } else {
                            addResult('⚠️ No data received yet (this is normal)', 'warning');
                        }
                    }, 2000);
                }
                
                addResult('✅ All tests completed without errors!', 'success');
                addResult('The "Cannot read properties of undefined" error should be fixed.', 'success');
                
            } catch (error) {
                addResult(`❌ Test failed with error: ${error.message}`, 'error');
                console.error('Test error:', error);
            }
        }
        
        // Initialize when page loads
        window.addEventListener('load', () => {
            addResult('Page loaded, ready for testing', 'info');
        });
    </script>
    
    <!-- Load the simple optimizations script -->
    <script src="./performance/simple-optimizations.js"></script>
    
    <!-- Simulate parts of config.js that are needed for testing -->
    <script>
        // Wait for simple optimizations to load
        setTimeout(() => {
            if (window.simplePollingCoordinator) {
                addResult('✅ Simple optimizations loaded successfully', 'success');
            } else {
                addResult('❌ Simple optimizations failed to load', 'error');
            }
        }, 1000);
    </script>
</body>
</html>

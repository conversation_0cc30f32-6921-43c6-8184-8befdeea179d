/**
 * Dashboard Authentication Initialization
 * Handles JWT authentication setup and session restoration
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 Dashboard: Starting authentication initialization...');
    
    // Initialize JWT authentication system
    try {
        // Only clear legacy password-based credentials, preserve JWT data
        const legacyPwd = sessionStorage.getItem('smarttest_pwd');
        if (legacyPwd) {
            console.log('Clearing legacy password credential to prevent conflicts');
            sessionStorage.removeItem('smarttest_pwd');
        }
        
        // Check if unified auth client is available and properly initialized
        if (window.unifiedAuthClient) {
            console.log('✅ Dashboard: Unified auth client available');
            if (window.unifiedAuthClient.isAuthenticated) {
                console.log('✅ Dashboard: User already authenticated via JWT');
                // User is already authenticated, update UI
                const currentUser = window.unifiedAuthClient.getCurrentUser();
                if (currentUser && currentUser.uid) {
                    handleSuccessfulLogin(currentUser.uid);
                }
            } else {
                console.log('ℹ️ Dashboard: No active JWT session, user needs to login');
            }
        } else {
            console.log('⚠️ Dashboard: Unified auth client not available yet, waiting...');
            // Wait for it to load and initialize
            let attempts = 0;
            const checkAuthClient = setInterval(() => {
                attempts++;
                if (window.unifiedAuthClient) {
                    clearInterval(checkAuthClient);
                    console.log('✅ Dashboard: Unified auth client loaded after', attempts, 'attempts');
                    if (window.unifiedAuthClient.isAuthenticated) {
                        console.log('✅ Dashboard: User authenticated via JWT after client load');
                        // User is already authenticated, update UI
                        const currentUser = window.unifiedAuthClient.getCurrentUser();
                        if (currentUser && currentUser.uid) {
                            handleSuccessfulLogin(currentUser.uid);
                        }
                    }
                } else if (attempts > 50) { // Stop after 5 seconds
                    clearInterval(checkAuthClient);
                    console.log('❌ Dashboard: Unified auth client failed to load');
                }
            }, 100);
        }
    } catch (error) {
        console.log('Error initializing JWT authentication:', error);
    }

    // Check for API service and initialize dashboard event listeners
    if (window.apiService) {
        console.log('Unified API Service is available, initializing dashboard listeners.');
        initDashboard(); // This only sets up listeners now, which is safe.
    } else {
        console.error('Unified API Service not found! Make sure api-service.js is loaded correctly.');
        showNotification('Error', 'Unified API Service not found. Please refresh the page.', 'error');
    }

    // Login form handling
    const loginForm = document.getElementById('login-form');
    const loginButton = document.getElementById('login-button');
    const logoutButton = document.getElementById('logout-button');
    const loginStatus = document.getElementById('login-status');

    if (loginForm && loginButton) {
        loginForm.addEventListener('submit', async function(e) {
            e.preventDefault();

            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value;

            if (!username || !password) {
                if (loginStatus) {
                    loginStatus.textContent = 'Please enter both username and password.';
                    loginStatus.style.display = 'block';
                }
                return;
            }

            // Disable login button and show loading state
            loginButton.disabled = true;
            loginButton.textContent = 'Logging in...';

            showNotification('Logging in', 'Please wait...', 'info');
            if(loginStatus) loginStatus.style.display = 'none';

            try {
                // Use unified auth client for login
                if (window.unifiedAuthClient) {
                    const result = await window.unifiedAuthClient.login(username, password);
                    if (result.success) {
                        // On successful login, dashboard.js will handle initialization via the 'apiservice-ready' event.
                        handleSuccessfulLogin(username);

                        // Also set credentials in legacy API service for backward compatibility
                        if (window.apiService) {
                            window.apiService.setCredentials(username, password);
                            // Dispatch the apiservice-ready event
                            setTimeout(() => {
                                document.dispatchEvent(new CustomEvent('apiservice-ready', {
                                    detail: { apiService: window.apiService, username: username }
                                }));
                            }, 50);
                        }
                    } else {
                        if(loginStatus) {
                            loginStatus.textContent = result.error || 'Invalid credentials. Please try again.';
                            loginStatus.style.display = 'block';
                        }
                        showNotification('Login Failed', result.error || 'Invalid credentials.', 'error');
                    }
                } else {
                    // Fallback to legacy API service login
                    const success = await window.apiService.login(username, password);
                    if (success) {
                        handleSuccessfulLogin(username);
                    } else {
                        if(loginStatus) {
                            loginStatus.textContent = 'Invalid credentials. Please try again.';
                            loginStatus.style.display = 'block';
                        }
                        showNotification('Login Failed', 'Invalid credentials.', 'error');
                    }
                }
            } catch (error) {
                console.error('Login error:', error);
                if(loginStatus) {
                    loginStatus.textContent = 'An error occurred during login.';
                    loginStatus.style.display = 'block';
                }
                showNotification('Login Error', 'An unexpected error occurred.', 'error');
            }
        });
    }

    // Button handlers
    if(loginButton) {
        loginButton.addEventListener('click', showLoginModal);
    }
    if(logoutButton) {
        logoutButton.addEventListener('click', handleLogout);
    }

    console.log('✅ Dashboard: Authentication initialization completed');
});

// Global flag to prevent duplicate login notifications
let loginNotificationShown = false;

/**
 * Format email address to user-friendly display name
 * @param {string} email - Email address
 * @returns {string} Formatted user-friendly name
 */
function formatUserDisplay(email) {
    if (!email) return 'Unknown User';
    
    // Extract username from email (before @)
    const username = email.split('@')[0];
    
    // Convert to friendly format (capitalize first letter, replace dots/underscores with spaces)
    return username
        .replace(/[._]/g, ' ')
        .split(' ')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
        .join(' ');
}

/**
 * Handle successful login
 */
function handleSuccessfulLogin(username) {
    console.log('✅ Dashboard: Login successful for user:', username);

    // Update UI to reflect authenticated state
    const userDisplay = document.getElementById('user-display');
    if (userDisplay) {
        const friendlyName = formatUserDisplay(username);
        userDisplay.textContent = `Logged in as: ${friendlyName}`;
        console.log(`Updated user display: ${username} -> ${friendlyName}`);
    }

    // Hide login modal
    hideLoginModal();

    // Update button visibility
    const loginButton = document.getElementById('login-button');
    const logoutButton = document.getElementById('logout-button');
    if (loginButton) loginButton.style.display = 'none';
    if (logoutButton) logoutButton.style.display = 'inline-block';

    // Ensure loading spinner is hidden after successful login
    setTimeout(() => {
        const loadingIndicator = document.getElementById('loading-indicator');
        if (loadingIndicator) {
            loadingIndicator.style.display = 'none';
        }
    }, 1000); // Give dashboard components time to initialize

    // Initialize dashboard components
    if (typeof initDashboard === 'function') {
        initDashboard();
    }

    // Show success notification only once per session
    if (!loginNotificationShown) {
        showNotification('Login successful', 'Welcome to SmartTest Dashboard!', 'success');
        loginNotificationShown = true;
    }
}

/**
 * Hide login modal
 */
function hideLoginModal() {
    const loginModal = document.getElementById('login-modal');
    if (loginModal) {
        loginModal.classList.remove('active');
        loginModal.style.display = 'none';
    }
}

/**
 * Show login modal
 */
function showLoginModal() {
    const loginModal = document.getElementById('login-modal');
    const loginStatus = document.getElementById('login-status');

    if (loginModal) {
        loginModal.classList.add('active');
        loginModal.style.display = 'flex';

        // Clear any previous error messages
        if (loginStatus) {
            loginStatus.textContent = '';
            loginStatus.style.display = 'none';
        }

        // Focus on username field
        const usernameField = document.getElementById('username');
        if (usernameField) {
            usernameField.focus();
        }
    }
}

/**
 * Handle logout
 */
function handleLogout() {
    console.log('🔓 Dashboard: Logout initiated');

    // Reset login notification flag
    loginNotificationShown = false;

    // Use unified auth client for logout
    if (window.unifiedAuthClient) {
        window.unifiedAuthClient.logout();
    }

    // Clear legacy API service credentials
    if (window.apiService && typeof window.apiService.clearCredentials === 'function') {
        window.apiService.clearCredentials();
    }

    // Update UI
    const userDisplay = document.querySelector('.user-display');
    if (userDisplay) {
        userDisplay.textContent = 'Not logged in';
    }

    // Show login modal
    showLoginModal();

    // Show logout button, hide login button
    const loginButton = document.getElementById('login-button');
    const logoutButton = document.getElementById('logout-button');
    if (loginButton) loginButton.style.display = 'inline-block';
    if (logoutButton) logoutButton.style.display = 'none';

    showNotification('Logged out', 'You have been logged out successfully.', 'info');
}

# Test Run Flow Documentation

## Overview

This document explains the complete workflow for running tests, monitoring their execution, and retrieving results using the database and API interfaces. It provides a step-by-step guide for implementing these processes in our SmartTest application.

## Test Execution Workflow

### 1. Test Initiation

Tests can be initiated in two ways:

#### Single Test Case Execution
```
POST http://mprts-qa02.lab.wagerworks.com:5080/AutoRun/CaseRunner
```
With parameters:
- `tc_id`: Test case ID
- Authentication parameters
- Environment parameters

#### Test Suite Execution
```
POST http://mprts-qa02.lab.wagerworks.com:5080/AutoRun/CaseRunner
```
With parameters:
- `ts_id`: Test suite ID
- Authentication parameters
- Environment parameters

### 2. Test Execution Process

After a test is initiated:

1. The system assigns a unique `tsn_id` (test suite run ID)
2. The test case or suite executes on the specified environment
3. Results are recorded in the database in real-time automatically
4. Step-by-step execution details are logged in the `output` table

### 3. Monitoring Test Progress

To monitor test execution progress:

```sql
SELECT tsn_id, tc_id, outcome, COUNT(*) 
FROM test_result r, output i  
WHERE i.cnt = r.cnt 
  AND r.tsn_id = [YOUR_TSN_ID]
GROUP BY tc_id, outcome, tsn_id 
ORDER BY creation_time ASC;
```

This query returns:
- The test cases being executed
- Their current outcome status
- The count of output entries

### 4. Retrieving Test Results

After test execution completes:

#### Summary Results
```sql
SELECT tsn_id, 
       SUM(CASE WHEN outcome='P' THEN 1 ELSE 0 END) AS passed_cases,
       SUM(CASE WHEN outcome='F' THEN 1 ELSE 0 END) AS failed_cases,
       TIMEDIFF(MAX(creation_time), MIN(creation_time)) AS duration
FROM test_result
WHERE tsn_id = [YOUR_TSN_ID]
GROUP BY tsn_id;
```

#### Detailed Failure Information
```sql
SELECT r.cnt, seq_index, tsn_id, tc_id, outcome, txt 
FROM test_result r, output i  
WHERE i.cnt = r.cnt 
  AND r.tsn_id = [YOUR_TSN_ID] 
  AND outcome = 'F';
```

## Implementation in SmartTest

### Frontend Integration

For our SmartTest application, we should implement:

1. **Test Execution Component**
   - Form to collect test parameters
   - API service to initiate test execution
   - Return and store the `tsn_id` for tracking

2. **Test Monitoring Component**
   - Polling mechanism to check test status. Test status is shown 
   - Real-time updates of test progress
   - Visual indicators for running, passed, and failed tests

3. **Results Dashboard**
   - Summary view of test results
   - Detailed view of test outputs
   - Filtering and search capabilities
   - Export functionality for reports

### Database Access Layer

Create a service layer that:

1. Establishes secure connections to the database
2. Executes predefined queries with proper parameters
3. Formats results for frontend consumption
4. Implements caching for improved performance
5. Handles connection errors gracefully

### Example Implementation Flow

```mermaid
sequenceDiagram
    participant User
    participant Frontend
    participant API Service
    participant Database
    
    User->>Frontend: Select test to run
    Frontend->>API Service: Send test execution request
    API Service->>+Database: Initiate test (POST CaseRunner)
    Database-->>-API Service: Return tsn_id
    API Service-->>Frontend: Return test initiation confirmation
    Frontend->>Frontend: Start polling for updates
    
    loop Every 5 seconds while test running
        Frontend->>API Service: Request status update
        API Service->>Database: Query test status
        Database-->>API Service: Return current status
        API Service-->>Frontend: Update status display
    end
    
    User->>Frontend: View test results
    Frontend->>API Service: Request detailed results
    API Service->>Database: Query detailed results
    Database-->>API Service: Return result data
    API Service-->>Frontend: Display formatted results
```

## Parameter Management

Test parameters can be defined at different levels:

1. **Test Case Step Level**: Specific to a step in a test case
2. **Test Case Level**: Applied to all steps in a test case
3. **Test Suite Level**: Applied to all test cases in a suite
4. **Test Project Level**: Applied to all test suites in a project

Parameters at higher levels can override those at lower levels, providing flexibility in test configuration.

## Test Capabilities

The testing framework supports:

1. **Web Service Testing**:
   - REST and SOAP calls
   - Multiple data formats (JSON, AVRO, XML)
   - Template-based request generation

2. **Database Testing**:
   - Query execution across different databases (Oracle, MySQL, PostgreSQL)
   - Automated result validation
   - Data extraction with column and record indexing

3. **Workflow Control**:
   - Loops and conditional execution
   - Step jumps and flow control
   - Test case invocation from other test cases

4. **System Integration**:
   - SSH command execution
   - Script running on remote servers
   - XSLT transformations for response processing

## Test Initiator Tracking

To support user differentiation in the Active Tests panel, we need to track which user initiated each test:

### Database Schema Updates

The `test_result` table should be extended to include:
- `initiator_user_id`: The ID of the user who initiated the test
- `initiator_username`: The username of the user who initiated the test

### Initiating Tests with User Information

When initiating a test, the user's information should be included:

```
POST http://mprts-qa02.lab.wagerworks.com:5080/AutoRun/CaseRunner
```
With additional parameters:
- `user_id`: Current user's ID
- `username`: Current user's username

### Querying Active Tests with User Information

To retrieve active tests with user information:

```sql
SELECT tsn_id, tc_id, outcome, initiator_user_id, initiator_username, COUNT(*) 
FROM test_result r, output i  
WHERE i.cnt = r.cnt 
  AND r.tsn_id IN (SELECT DISTINCT tsn_id FROM test_result WHERE status = 'RUNNING')
GROUP BY tc_id, outcome, tsn_id, initiator_user_id, initiator_username
ORDER BY creation_time ASC;
```

### Implementation in UI

The Active Tests panel should:
1. Display visual indicators (different colors/icons) for tests initiated by the current user vs. other users
2. Show tooltips with initiator username when hovering over a test card
3. Optionally provide filtering to show only current user's tests, only others' tests, or all tests

## Error Handling

Implement robust error handling for:

1. **Connection Issues**:
   - Database connection failures
   - API request timeouts
   - Authentication failures

2. **Test Execution Errors**:
   - Invalid test parameters
   - Missing dependencies
   - Environment configuration problems

3. **Result Processing Issues**:
   - Invalid response formats
   - Incomplete test data
   - Query execution errors

## Reporting and Analytics

Implement advanced reporting features:

1. **Historical Trend Analysis**:
   - Pass/fail rates over time
   - Test execution duration trends
   - Failure pattern identification

2. **Custom Reports**:
   - Filter by test type, environment, outcome
   - Group results by various criteria
   - Export in multiple formats (CSV, PDF, Excel)

3. **Notification System**:
   - Alerts for test failures
   - Scheduled report generation
   - Integration with communication platforms 
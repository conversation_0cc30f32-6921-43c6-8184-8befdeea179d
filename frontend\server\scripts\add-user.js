#!/usr/bin/env node

/**
 * Add User Script
 * Command-line tool for adding users to the SmartTest authentication system
 */

const fs = require('fs');
const path = require('path');

// Check for required dependencies
try {
  var bcrypt = require('bcrypt');
  var { program } = require('commander');
} catch (error) {
  console.error('❌ Missing required dependencies. Please install them first:');
  console.error('   npm install bcrypt commander');
  console.error('');
  console.error('Error details:', error.message);
  process.exit(1);
}

// Configuration
const ALLOWED_USERS_FILE = path.join(__dirname, '..', 'config', 'allowed-users.json');
const VALID_ROLES = ['admin', 'tester', 'viewer'];
const SALT_ROUNDS = 12;

/**
 * Load existing users
 */
function loadUsers() {
  try {
    if (fs.existsSync(ALLOWED_USERS_FILE)) {
      const data = fs.readFileSync(ALLOWED_USERS_FILE, 'utf8');
      return JSON.parse(data);
    } else {
      return {
        users: [],
        lastModified: new Date().toISOString(),
        version: "1.0"
      };
    }
  } catch (error) {
    console.error('Error loading users file:', error.message);
    process.exit(1);
  }
}

/**
 * Save users to file
 */
function saveUsers(userData) {
  try {
    // Create config directory if it doesn't exist
    const configDir = path.dirname(ALLOWED_USERS_FILE);
    if (!fs.existsSync(configDir)) {
      fs.mkdirSync(configDir, { recursive: true });
    }

    // Update metadata
    userData.lastModified = new Date().toISOString();
    
    // Write file with proper formatting
    fs.writeFileSync(ALLOWED_USERS_FILE, JSON.stringify(userData, null, 2), 'utf8');
    console.log(`✅ Users file updated: ${ALLOWED_USERS_FILE}`);
  } catch (error) {
    console.error('Error saving users file:', error.message);
    process.exit(1);
  }
}

/**
 * Validate email format
 */
function validateEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Validate password strength
 */
function validatePassword(password) {
  // Simplified validation for development/testing
  if (password.length < 4) {
    return 'Password must be at least 4 characters long';
  }

  // Allow simple passwords for development
  return null;
}

/**
 * Add a new user
 */
async function addUser(options) {
  console.log('🔐 SmartTest User Management - Add User');
  console.log('=====================================\n');

  // Validate inputs
  if (!validateEmail(options.uid)) {
    console.error('❌ Error: Invalid email format');
    process.exit(1);
  }

  if (!VALID_ROLES.includes(options.role)) {
    console.error(`❌ Error: Invalid role. Must be one of: ${VALID_ROLES.join(', ')}`);
    process.exit(1);
  }

  const passwordError = validatePassword(options.password);
  if (passwordError) {
    console.error(`❌ Error: ${passwordError}`);
    process.exit(1);
  }

  // Load existing users
  const userData = loadUsers();

  // Check if user already exists
  const existingUser = userData.users.find(user => user.uid === options.uid);
  if (existingUser) {
    if (options.force) {
      console.log(`⚠️ User ${options.uid} already exists. Updating due to --force flag.`);
      // Remove existing user
      userData.users = userData.users.filter(user => user.uid !== options.uid);
    } else {
      console.error(`❌ Error: User ${options.uid} already exists. Use --force to update.`);
      process.exit(1);
    }
  }

  try {
    // Hash password
    console.log('🔒 Hashing password...');
    const hashedPassword = await bcrypt.hash(options.password, SALT_ROUNDS);

    // Create user object
    const newUser = {
      uid: options.uid,
      password: hashedPassword,
      role: options.role,
      name: options.name || options.uid,
      active: true,
      createdAt: new Date().toISOString(),
      lastLogin: null
    };

    // Add user
    userData.users.push(newUser);

    // Save to file
    saveUsers(userData);

    // Success message
    console.log('\n✅ User added successfully!');
    console.log('========================');
    console.log(`Email: ${newUser.uid}`);
    console.log(`Name: ${newUser.name}`);
    console.log(`Role: ${newUser.role}`);
    console.log(`Active: ${newUser.active}`);
    console.log(`Created: ${newUser.createdAt}`);
    
    console.log('\n📝 Next steps:');
    console.log('- User can now login with their credentials');
    console.log('- Check the admin interface to verify the user was added');
    console.log('- Review user permissions based on their role');

  } catch (error) {
    console.error('❌ Error creating user:', error.message);
    process.exit(1);
  }
}

/**
 * List existing users
 */
function listUsers() {
  console.log('👥 SmartTest Users');
  console.log('==================\n');

  const userData = loadUsers();

  if (userData.users.length === 0) {
    console.log('No users found.');
    return;
  }

  console.log(`Total users: ${userData.users.length}`);
  console.log(`Last modified: ${userData.lastModified}\n`);

  userData.users.forEach((user, index) => {
    console.log(`${index + 1}. ${user.uid}`);
    console.log(`   Name: ${user.name}`);
    console.log(`   Role: ${user.role}`);
    console.log(`   Active: ${user.active}`);
    console.log(`   Created: ${user.createdAt}`);
    console.log(`   Last Login: ${user.lastLogin || 'Never'}`);
    console.log('');
  });
}

/**
 * Remove a user
 */
function removeUser(uid, force = false) {
  console.log('🗑️ SmartTest User Management - Remove User');
  console.log('==========================================\n');

  const userData = loadUsers();
  const userIndex = userData.users.findIndex(user => user.uid === uid);

  if (userIndex === -1) {
    console.error(`❌ Error: User ${uid} not found.`);
    process.exit(1);
  }

  const user = userData.users[userIndex];

  if (!force) {
    console.log(`⚠️ About to remove user: ${user.uid} (${user.name})`);
    console.log('Use --force to confirm removal.');
    process.exit(1);
  }

  // Remove user
  userData.users.splice(userIndex, 1);
  saveUsers(userData);

  console.log(`✅ User ${uid} removed successfully.`);
}

// CLI Configuration
program
  .name('add-user')
  .description('SmartTest User Management Tool')
  .version('1.0.0');

program
  .command('add')
  .description('Add a new user')
  .requiredOption('-u, --uid <email>', 'User email address')
  .requiredOption('-p, --password <password>', 'User password')
  .requiredOption('-r, --role <role>', `User role (${VALID_ROLES.join(', ')})`)
  .option('-n, --name <name>', 'User display name')
  .option('-f, --force', 'Force update if user exists')
  .action(addUser);

program
  .command('list')
  .description('List all users')
  .action(listUsers);

program
  .command('remove')
  .description('Remove a user')
  .requiredOption('-u, --uid <email>', 'User email address')
  .option('-f, --force', 'Force removal without confirmation')
  .action((options) => removeUser(options.uid, options.force));

// Help examples
program.on('--help', () => {
  console.log('');
  console.log('Examples:');
  console.log('  $ node add-user.js add -u <EMAIL> -p SecurePass123! -r admin -n "Admin User"');
  console.log('  $ node add-user.js add -u <EMAIL> -p TestPass456! -r tester');
  console.log('  $ node add-user.js list');
  console.log('  $ node add-user.js remove -u <EMAIL> --force');
  console.log('');
  console.log('Password Requirements:');
  console.log('  - At least 4 characters long');
  console.log('  - Simple passwords like "test" are allowed for development');
  console.log('');
  console.log('Available Roles:');
  console.log('  - admin: Full system access including user management');
  console.log('  - tester: Can run tests and view results');
  console.log('  - viewer: Read-only access to test results');
});

// Parse command line arguments
program.parse();

// If no command provided, show help
if (!process.argv.slice(2).length) {
  program.outputHelp();
}

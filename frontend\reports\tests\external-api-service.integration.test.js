/**
 * Integration tests for ExternalApiService
 * 
 * These tests make actual API calls to the external service.
 * They require valid credentials and network access to run.
 * 
 * To run these tests:
 * npx jest --config=jest.config.js frontend/reports/tests/external-api-service.integration.test.js
 */

// Import required modules
const fs = require('fs');
const path = require('path');

// Test credentials (replace with actual test credentials)
const TEST_CREDENTIALS = {
  uid: 'test_user',
  password: 'test_password'
};

// Test session ID (replace with an actual session ID)
const TEST_SESSION_ID = '13782';

// Create a new instance of ExternalApiService for testing
let service;

// Setup before tests
beforeAll(() => {
  // Mock window and document for JSDOM
  global.window = global.window || {};
  global.document = global.document || {
    createElement: jest.fn(() => ({
      innerHTML: '',
      querySelectorAll: jest.fn(() => []),
      querySelector: jest.fn(() => null)
    }))
  };
  
  // Load the ExternalApiService class
  require('../services/external-api-service');
  
  // Get the global instance
  service = window.externalApiService;
  
  // Log the service initialization
  console.log('ExternalApiService initialized for integration tests');
});

// Cleanup after tests
afterAll(() => {
  // Clean up any resources
  console.log('Integration tests completed');
});

describe('ExternalApiService Integration Tests', () => {
  // Run all tests by default
  describe('Authentication', () => {
    test('should login successfully with valid credentials', async () => {
      const jsessionId = await service.login(
        TEST_CREDENTIALS.uid,
        TEST_CREDENTIALS.password
      );
      
      // Verify the login was successful
      expect(jsessionId).toBeDefined();
      expect(jsessionId.length).toBeGreaterThan(0);
      expect(service.jsessionId).toBe(jsessionId);
      expect(service.jsessionExpiry).toBeGreaterThan(Date.now());
    });
    
    test('should get a valid session', async () => {
      const jsessionId = await service.getValidSession(
        TEST_CREDENTIALS.uid,
        TEST_CREDENTIALS.password
      );
      
      // Verify the session is valid
      expect(jsessionId).toBeDefined();
      expect(jsessionId.length).toBeGreaterThan(0);
      expect(service.isSessionValid()).toBe(true);
    });
  });
  
  describe('API Requests', () => {
    // Make sure we have a valid session before each test
    beforeEach(async () => {
      await service.getValidSession(
        TEST_CREDENTIALS.uid,
        TEST_CREDENTIALS.password
      );
    });
    
    test('should fetch report summary', async () => {
      const summary = await service.getReportSummary(
        TEST_SESSION_ID,
        TEST_CREDENTIALS.uid,
        TEST_CREDENTIALS.password
      );
      
      // Verify the summary data
      expect(summary).toBeDefined();
      expect(summary.tsn_id).toBe(TEST_SESSION_ID);
      expect(summary.status).toBeDefined();
      
      // Save the response for updating mock data
      fs.writeFileSync(
        path.join(__dirname, 'mocks/actual-summary-response.json'),
        JSON.stringify(summary, null, 2)
      );
      
      console.log('Report summary:', summary);
    });
    
    test('should fetch report details', async () => {
      const details = await service.getReportDetails(
        TEST_SESSION_ID,
        1,
        TEST_CREDENTIALS.uid,
        TEST_CREDENTIALS.password
      );
      
      // Verify the details data
      expect(details).toBeDefined();
      expect(details.tsn_id).toBe(TEST_SESSION_ID);
      expect(details.test_cases).toBeDefined();
      expect(Array.isArray(details.test_cases)).toBe(true);
      
      // Save the response for updating mock data
      fs.writeFileSync(
        path.join(__dirname, 'mocks/actual-details-response.json'),
        JSON.stringify(details, null, 2)
      );
      
      console.log('Report details:', details);
    });
    
    test('should fetch multiple report summaries', async () => {
      // Use multiple session IDs
      const sessionIds = [TEST_SESSION_ID, '13783', '13784'];
      
      const reports = await service.getRecentTestRuns(
        sessionIds,
        TEST_CREDENTIALS.uid,
        TEST_CREDENTIALS.password,
        3
      );
      
      // Verify the reports data
      expect(reports).toBeDefined();
      expect(Array.isArray(reports)).toBe(true);
      expect(reports.length).toBeGreaterThan(0);
      
      // Save the response for updating mock data
      fs.writeFileSync(
        path.join(__dirname, 'mocks/actual-recent-runs-response.json'),
        JSON.stringify(reports, null, 2)
      );
      
      console.log('Recent test runs:', reports);
    });
    
    test('should run test case 3180', async () => {
      // Make a POST request to run test case 3180
      const response = await service.makeAuthenticatedRequest(
        '/AutoRun/CaseRunner',
        {
          tc_id: '3180',
          envir: 'QA',
          shell_host: 'localhost',
          browser: 'chrome'
        },
        TEST_CREDENTIALS.uid,
        TEST_CREDENTIALS.password,
        'POST'
      );
      
      // Verify the response
      expect(response).toBeDefined();
      expect(response.ok).toBe(true);
      
      // Extract the session ID from the response
      const responseText = await response.text();
      console.log('Test run response:', responseText);
      
      // Save the response for reference
      fs.writeFileSync(
        path.join(__dirname, 'mocks/actual-case-runner-response.html'),
        responseText
      );
    });
  });
});

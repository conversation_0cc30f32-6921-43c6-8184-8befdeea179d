/* Dashboard Styles - Microsoft Teams UI */

:root {
    /* Teams color palette */
    --teams-base: #f3f2f1;
    --teams-primary: #6264a7;
    --teams-primary-hover: #7174b4;
    --teams-success: #92c353;
    --teams-danger: #d13438;
    --teams-warning: #ffaa44;
    --teams-info: #2d8cff;
    --teams-dark: #252423;
    --teams-light: #ffffff;
    --teams-border: #e1dfdd;
    --teams-text: #252423;
    --teams-text-light: #f3f2f1;
}

body {
    font-family: 'Segoe UI', -apple-system, BlinkMacSystemFont, 'Roboto', 'Helvetica Neue', sans-serif;
    background-color: var(--teams-base);
    color: var(--teams-text);
    margin: 0;
    padding: 0;
    height: 100vh;
    overflow-x: hidden;
}

/* Header */
.ms-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: var(--teams-primary);
    color: var(--teams-light);
    padding: 0.75rem 1.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

.ms-header-brand {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--teams-light);
    text-decoration: none;
}

.ms-header-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.ms-environment-display {
    font-size: 0.875rem;
    color: var(--teams-light);
}

/* Layout */
.ms-container {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 56px);
}

.ms-layout {
    display: flex;
    flex: 1;
}

/* Navigation */
.ms-nav {
    width: 240px;
    background-color: var(--teams-light);
    border-right: 1px solid var(--teams-border);
    overflow-y: auto;
}

.ms-nav-content {
    padding: 1rem 0;
}

.ms-nav-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.ms-nav-item {
    margin: 0;
}

.ms-nav-link {
    display: block;
    padding: 0.75rem 1.5rem;
    color: var(--teams-text);
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 400;
    transition: background-color 0.2s;
}

.ms-nav-link:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

.ms-nav-link-active {
    background-color: rgba(98, 100, 167, 0.1);
    border-left: 3px solid var(--teams-primary);
    font-weight: 600;
    color: var(--teams-primary);
}

/* Content */
.ms-content {
    flex: 1;
    padding: 1.5rem;
    overflow-y: auto;
}

.ms-content-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid var(--teams-border);
}

.ms-section-title {
    margin: 1.5rem 0 1rem;
    font-weight: 600;
}

/* Grid */
.ms-grid {
    display: flex;
    flex-direction: column;
    margin: 0 -0.75rem;
    width: 100%;
}

.ms-grid-row {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    margin-bottom: 1.5rem;
    align-items: stretch;
    justify-content: space-between;
}

.ms-grid-col {
    padding: 0 0.75rem;
    margin-bottom: 1.5rem;
    box-sizing: border-box;
    display: flex;
}

.ms-sm3 {
    width: 25%;
    flex: 0 0 calc(25% - 1rem);
    max-width: calc(25% - 1rem);
}

.ms-sm6 {
    width: 50%;
    flex: 0 0 calc(50% - 1rem);
    max-width: calc(50% - 1rem);
}

/* Status Cards */
.ms-stat-card {
    border-radius: 4px;
    padding: 1.25rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    transition: transform 0.2s, box-shadow 0.2s;
    margin-bottom: 0;
    width: 100%;
    height: 100%;
    justify-content: center;
}

.ms-stat-title {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    /* Teams-specific styling for status titles */
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.ms-stat-value {
    font-size: 2rem;
    font-weight: 700;
}

/* Status colors - Teams specific */
.ms-bgColor-themePrimary {
    background-color: var(--teams-primary);
}

.ms-bgColor-green {
    background-color: var(--teams-success);
}

.ms-bgColor-red {
    background-color: var(--teams-danger);
}

.ms-bgColor-orange {
    background-color: var(--teams-warning);
}

/* Table */
.ms-table-container {
    margin-bottom: 1.5rem;
    overflow-x: auto;
}

.ms-Table {
    width: 100%;
    border-collapse: collapse;
}

.ms-Table th {
    background-color: #f3f2f1;
    font-weight: 600;
    text-align: left;
    padding: 0.75rem;
    border-bottom: 1px solid var(--teams-border);
}

.ms-Table td {
    padding: 0.75rem;
    border-bottom: 1px solid var(--teams-border);
}

.ms-Table--fixed {
    table-layout: fixed;
}

.ms-Table tr:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

/* Status text in table */
.ms-status {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.5rem;
    border-radius: 3px;
    font-weight: 600;
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.ms-status-passed {
    background-color: rgba(146, 195, 83, 0.2);
    color: var(--teams-success);
}

.ms-status-failed {
    background-color: rgba(209, 52, 56, 0.2);
    color: var(--teams-danger);
}

.ms-status-skipped {
    background-color: rgba(255, 170, 68, 0.2);
    color: var(--teams-warning);
}

/* Buttons */
.ms-Button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 80px;
    height: 32px;
    padding: 0 16px;
    border-radius: 2px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s;
    border: none;
    outline: none;
}

.ms-Button--default {
    background-color: var(--teams-light);
    color: var(--teams-text);
    border: 1px solid var(--teams-border);
}

.ms-Button--default:hover {
    background-color: var(--teams-base);
}

.ms-Button--primary {
    background-color: var(--teams-primary);
    color: var(--teams-light);
}

.ms-Button--primary:hover {
    background-color: var(--teams-primary-hover);
}

/* Utilities */
.ms-hidden {
    display: none !important;
}

.ms-fontColor-white {
    color: var(--teams-light);
}

/* Media queries */
@media (max-width: 768px) {
    .ms-layout {
        flex-direction: column;
    }
    
    .ms-nav {
        width: 100%;
        border-right: none;
        border-bottom: 1px solid var(--teams-border);
    }
    
    .ms-sm3, .ms-sm6 {
        width: 100%;
        flex: 0 0 100%;
        max-width: 100%;
    }
    
    .ms-content-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .ms-content-actions {
        margin-top: 1rem;
    }
}

/* Fix for medium screens */
@media (min-width: 769px) and (max-width: 1200px) {
    .ms-sm3 {
        width: 50%;
        flex: 0 0 50%;
        max-width: 50%;
    }
}

/* API Integration UI Components */

/* Modal */
.ms-modal {
    display: none;
    position: fixed;
    z-index: 9999; /* Increased z-index */
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.6); /* Darkened background */
}

.ms-modal.active {
    display: block !important;
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.ms-modal-content {
    background-color: var(--teams-light);
    margin: 10% auto;
    padding: 20px;
    border: 1px solid var(--teams-border);
    border-radius: 4px;
    width: 80%;
    max-width: 600px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    position: relative;
}

.ms-modal-close {
    position: absolute;
    top: 10px;
    right: 15px;
    color: #aaa;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.ms-modal-close:hover,
.ms-modal-close:focus {
    color: var(--teams-text);
    text-decoration: none;
}

/* Form Group */
.ms-form-group {
    margin-bottom: 1rem;
}

.ms-form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.ms-form-group input {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid var(--teams-border);
    border-radius: 2px;
    font-size: 14px;
}

.ms-form-actions {
    display: flex;
    justify-content: flex-end;
    margin-top: 1.5rem;
}

/* Card Container */
.ms-card-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.ms-card {
    background-color: var(--teams-light);
    border: 1px solid var(--teams-border);
    border-radius: 4px;
    padding: 1rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    transition: transform 0.2s, box-shadow 0.2s;
    margin-bottom: 0;
    width: 100%;
    height: 100%;
    justify-content: center;
}

.ms-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.ms-card h3 {
    margin-top: 0;
    margin-bottom: 0.5rem;
    font-size: 1rem;
    font-weight: 600;
}

.ms-card p {
    margin: 0.5rem 0;
    font-size: 0.875rem;
}

/* Active Test Card */
.active-test-card {
    background-color: var(--teams-light);
    border-left: 4px solid var(--teams-info);
    border-radius: 4px;
    padding: 1rem;
    margin-bottom: 1rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.active-test-card.running {
    border-left-color: var(--teams-info);
}

.active-test-card.completed {
    border-left-color: var(--teams-success);
}

.active-test-card.failed {
    border-left-color: var(--teams-danger);
}

.active-test-card.stopped {
    border-left-color: var(--teams-warning);
}

.active-test-card h3 {
    margin-top: 0;
    margin-bottom: 0.5rem;
    font-size: 1rem;
    font-weight: 600;
}

.active-test-card p {
    margin: 0.25rem 0;
    font-size: 0.875rem;
}

.button-container {
    display: flex;
    justify-content: flex-end;
    gap: 0.5rem;
    margin-top: 1rem;
}

/* Test Suite Card */
.test-suite-card {
    background-color: var(--teams-light);
    border: 1px solid var(--teams-border);
    border-radius: 4px;
    padding: 1rem;
    margin-bottom: 1rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    transition: transform 0.2s, box-shadow 0.2s;
}

.test-suite-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.test-suite-card h3 {
    margin-top: 0;
    margin-bottom: 0.5rem;
    font-size: 1rem;
    font-weight: 600;
}

.test-suite-card p {
    margin: 0.5rem 0;
    font-size: 0.875rem;
    color: #666;
}

/* Recent Run Card */
.recent-run-card {
    background-color: var(--teams-light);
    border-left: 4px solid var(--teams-info);
    border-radius: 4px;
    padding: 1rem;
    margin-bottom: 1rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.recent-run-card.completed {
    border-left-color: var(--teams-success);
}

.recent-run-card.failed {
    border-left-color: var(--teams-danger);
}

.recent-run-card.running {
    border-left-color: var(--teams-info);
}

/* Loading Indicator */
.ms-loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 2000;
}

.ms-loading-message {
    margin-top: 1rem;
    font-weight: 600;
    color: var(--teams-primary);
}

/* Spinner */
.ms-Spinner {
    position: relative;
    width: 28px;
    height: 28px;
}

.ms-Spinner--large {
    width: 42px;
    height: 42px;
}

.ms-Spinner::before {
    content: '';
    box-sizing: border-box;
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    border: 3px solid #f3f2f1;
    border-top-color: var(--teams-primary);
    animation: spinner 0.8s linear infinite;
}

@keyframes spinner {
    to {
        transform: rotate(360deg);
    }
}

/* Notification Container */
.ms-notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1500;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 10px;
    max-width: 350px;
}

.ms-notification, .ms-MessageBar, .notification {
    background-color: white;
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 12px 16px;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    animation: slideIn 0.3s ease-out;
    min-width: 250px;
    max-width: 100%;
}

.ms-notification.success, .ms-MessageBar--success, .notification.success {
    background-color: #e6f4ea;
    border-left: 4px solid var(--teams-success);
    color: #0d652d;
}

.ms-notification.error, .ms-MessageBar--error, .notification.error {
    background-color: #fdecea;
    border-left: 4px solid var(--teams-danger);
    color: #8e2924;
}

.ms-notification.info, .ms-MessageBar--info, .notification.info {
    background-color: #e8f1fa;
    border-left: 4px solid var(--teams-info);
    color: #0c559d;
}

.ms-notification.warning, .ms-MessageBar--warning, .notification.warning {
    background-color: #fff8e6;
    border-left: 4px solid var(--teams-warning);
    color: #8a5700;
}

.ms-notification.fade-out {
    animation: fadeOut 0.5s ease-out forwards;
}

.ms-notification-message {
    flex: 1;
    font-size: 14px;
    margin-right: 8px;
}

.ms-notification-close {
    background: none;
    border: none;
    color: #999;
    cursor: pointer;
    font-size: 18px;
    padding: 0;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
    }
}

/* Empty Message */
.ms-empty-message {
    text-align: center;
    padding: 2rem;
    color: #666;
    font-style: italic;
    background-color: #f9f9f9;
    border-radius: 4px;
    border: 1px dashed var(--teams-border);
}

/* User Info in Header */
.ms-user-info {
    font-size: 0.875rem;
    color: var(--teams-light);
    margin-right: 1rem;
}

/* Report Table */
.report-table {
    width: 100%;
    border-collapse: collapse;
    margin: 1rem 0;
}

.report-table th,
.report-table td {
    padding: 0.5rem;
    text-align: left;
    border-bottom: 1px solid var(--teams-border);
}

.report-table th {
    background-color: #f3f2f1;
    font-weight: 600;
}

.report-summary {
    margin-top: 1rem;
}

/* API Explorer Link */
.api-explorer-link {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background-color: var(--teams-primary);
    color: white;
    padding: 8px 16px;
    border-radius: 4px;
    text-decoration: none;
    font-weight: 500;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    transition: background-color 0.2s;
}

.api-explorer-link:hover {
    background-color: var(--teams-primary-hover);
}

/* Predefined Test Suites */
.ms-predefined-grid {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    gap: 20px;
    margin-bottom: 24px;
}

.ms-predefined-card {
    background-color: #f9f9f9;
    border: 1px solid #edebe9;
    border-radius: 4px;
    padding: 16px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    flex: 0 0 auto;
    width: 250px;
}

.ms-predefined-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.ms-predefined-card h3 {
    margin-top: 0;
    margin-bottom: 8px;
    color: #323130;
}

.ms-predefined-card p {
    margin-bottom: 16px;
    color: #605e5c;
    min-height: 40px;
}

.ms-predefined-card .ms-Button {
    width: 100%;
}

/* Custom Test Suite Builder */
.ms-checkbox-container {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #edebe9;
    border-radius: 2px;
    padding: 8px;
    margin-bottom: 16px;
    background-color: #fff;
    position: relative;
}

.ms-checkbox-item {
    display: flex;
    flex-direction: column;
    padding: 16px 50px 16px 16px; /* Equal padding on all sides with extra space for checkbox */
    border-bottom: 1px solid #f3f2f1;
    position: relative;
    min-height: 24px; /* Ensure minimum height for proper spacing */
}

.ms-checkbox-item:last-child {
    border-bottom: none;
}

.ms-checkbox-item label {
    font-size: 14px;
    color: #323130;
    cursor: pointer;
    flex: 1;
    display: flex;
    align-items: center;
}

.checkbox-container {
    position: absolute;
    right: 16px; /* Equal distance from right edge */
    top: 50%; /* Center vertically */
    transform: translateY(-50%);
    width: 24px;
    height: 24px;
    display: flex;
    justify-content: center;
    align-items: center;
}

/* Standard checkbox styling */
.ms-checkbox-item input[type="checkbox"] {
    margin: 0;
    cursor: pointer;
    width: 20px;
    height: 20px;
}

/* Ensure consistent spacing for all checkbox items */
.ms-checkbox-item:hover {
    background-color: #f3f2f1;
}

.ms-checkbox-item .ms-test-description {
    font-size: 12px;
    color: #605e5c;
    margin-top: 8px;
}

.ms-form-actions {
    display: flex;
    justify-content: flex-end;
    margin-top: 16px;
}

/* Query History Styles */
.query-history {
    margin-top: 1rem;
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #dee2e6;
}

.query-history h4 {
    margin: 0 0 0.5rem 0;
    color: #495057;
    font-size: 0.9rem;
}

.query-history ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.query-history li {
    padding: 0.5rem;
    border-bottom: 1px solid #dee2e6;
}

.query-history li:last-child {
    border-bottom: none;
}

.query-status {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    border-radius: 3px;
    font-size: 0.8rem;
    font-weight: 500;
    margin-right: 0.5rem;
}

.query-status.success {
    background-color: #d4edda;
    color: #155724;
}

.query-status.error {
    background-color: #f8d7da;
    color: #721c24;
}

.query-status.warning {
    background-color: #fff3cd;
    color: #856404;
}

.query-time {
    color: #6c757d;
    font-size: 0.8rem;
    margin-right: 0.5rem;
}

.query-text {
    margin-top: 0.25rem;
    font-family: monospace;
    font-size: 0.85rem;
    color: #212529;
    word-break: break-word;
}

/* Active Test Card Styles */
.test-card {
    position: relative;
    padding: 1rem;
    margin-bottom: 1rem;
    border-radius: 4px;
    background-color: #fff;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.test-card.running {
    border-left: 4px solid #007bff;
}

.test-card.completed {
    border-left: 4px solid #28a745;
}

.test-card.failed {
    border-left: 4px solid #dc3545;
}

.test-card.error {
    border-left: 4px solid #ff8c00;
}

.test-card.pending {
    border-left: 4px solid #6264a7;
}

.test-card.queued {
    border-left: 4px solid #0078d4;
}

.test-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px;
    background-color: #f9f9f9;
    border-bottom: 1px solid #eaeaea;
}

.test-card .test-info {
    flex: 1;
}

.test-card .test-name {
    font-size: 16px;
    font-weight: 600;
    margin: 0 0 5px 0;
    color: #323130;
}

.test-card .test-id-session {
    font-size: 12px;
    color: #605e5c;
    margin-bottom: 5px;
}

.test-card .test-id-label, 
.test-card .session-label {
    font-weight: 500;
    margin-right: 3px;
}

.test-card .test-user-time {
    font-size: 12px;
    color: #605e5c;
    display: flex;
    justify-content: space-between;
}

.test-card .status-indicator {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 6px;
}

.test-card .status-indicator.running {
    background-color: #34a853;
    animation: pulse 1.5s infinite;
}

.test-card .status-indicator.passed {
    background-color: #107c10;
}

.test-card .status-indicator.failed {
    background-color: #d13438;
}

.test-card .status-indicator.pending {
    background-color: #6264a7;
}

.test-card .status-indicator.queued {
    background-color: #0078d4;
    animation: pulse 2s infinite;
}

.test-card .status-text {
    font-weight: 600;
    font-size: 14px;
}

.test-card.running .status-text {
    color: #34a853;
}

.test-card.passed .status-text {
    color: #107c10;
}

.test-card.failed .status-text {
    color: #d13438;
}

.test-card.queued .status-text {
    color: #0078d4;
}

.test-card-body {
    padding: 15px;
}

.test-card .progress-container {
    background-color: #f0f0f0;
    border-radius: 4px;
    height: 10px;
    overflow: hidden;
    margin-bottom: 10px;
}

.test-card .progress-bar {
    height: 100%;
    text-align: center;
    font-size: 9px;
    line-height: 10px;
    color: white;
    font-weight: 600;
    transition: width 0.5s ease;
}

.test-card .progress-bar.running {
    background-color: #34a853;
}

.test-card .progress-bar.passed {
    background-color: #107c10;
}

.test-card .progress-bar.failed {
    background-color: #d13438;
}

.test-card .progress-bar.pending {
    background-color: #6264a7;
}

.test-card .progress-bar.queued {
    background-color: #0078d4;
}

.test-card .test-progress {
    font-size: 13px;
    color: #605e5c;
    margin-top: 5px;
}

.test-card-footer {
    padding: 10px 15px;
    border-top: 1px solid #eaeaea;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.view-report-btn {
    background-color: #6264a7;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    transition: background-color 0.2s;
}

.view-report-btn:hover {
    background-color: #7174b4;
}

.stop-button {
    background-color: #d13438;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    transition: background-color 0.2s;
}

.stop-button:hover {
    background-color: #e74856;
}

@keyframes pulse {
    0% { opacity: 0.6; }
    50% { opacity: 1; }
    100% { opacity: 0.6; }
}

.ms-empty-message {
    text-align: center;
    padding: 20px;
    color: #605e5c;
    font-size: 14px;
}

.ms-empty-message.error {
    color: #d13438;
}

/* Test Suite Selection */
.ms-test-suite-selection {
    margin-bottom: 24px;
}

.ms-suite-filters {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    margin-bottom: 16px;
    align-items: flex-end;
}

.ms-suite-filter-group {
    display: flex;
    flex-direction: column;
    min-width: 180px;
}

.ms-suite-filter-group label {
    font-size: 12px;
    margin-bottom: 4px;
    color: #605e5c;
    font-weight: 500;
}

.ms-suite-filter-group select {
    padding: 6px 30px 6px 12px;
    border: 1px solid var(--teams-border);
    border-radius: 4px;
    background-color: white;
    font-size: 14px;
    color: var(--teams-text);
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 16 16'%3E%3Cpath fill='%23605e5c' d='M4.5 6l3.5 3.5 3.5-3.5h-7z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 8px center;
}

.ms-suite-filter-group select:focus {
    outline: 2px solid var(--teams-primary);
    outline-offset: 1px;
}

.ms-suite-actions {
    display: flex;
    gap: 8px;
}

.ms-suite-list {
    border: 1px solid var(--teams-border);
    border-radius: 4px;
    background-color: white;
    max-height: 320px;
    overflow-y: auto;
}

.ms-suite-item {
    display: flex;
    padding: 12px 16px;
    border-bottom: 1px solid var(--teams-border);
    transition: background-color 0.2s;
}

.ms-suite-item:last-child {
    border-bottom: none;
}

.ms-suite-item:hover {
    background-color: rgba(98, 100, 167, 0.05);
}

.ms-suite-select {
    display: flex;
    align-items: center;
    margin-right: 12px;
}

.ms-suite-select input[type="radio"] {
    margin: 0;
    width: 16px;
    height: 16px;
}

.ms-suite-info {
    flex: 1;
}

.ms-suite-name {
    font-weight: 600;
    margin-bottom: 4px;
    color: var(--teams-text);
}

.ms-suite-details {
    font-size: 12px;
    color: #605e5c;
}

/* Test Results Table */
#reports-table {
    width: 100%;
    border-collapse: collapse;
}

#reports-table th, 
#reports-table td {
    padding: 8px 12px;
    text-align: left;
    border-bottom: 1px solid var(--teams-border);
}

#reports-table th {
    background-color: #f5f5f5;
    font-weight: 600;
}

#reports-table .text-success {
    color: var(--teams-success);
    font-weight: 600;
}

#reports-table .text-danger {
    color: var(--teams-danger);
    font-weight: 600;
}

#reports-table .table-success {
    background-color: rgba(146, 195, 83, 0.1);
}

#reports-table .table-danger {
    background-color: rgba(209, 52, 56, 0.1);
}

.test-details-btn {
    min-width: auto;
    padding: 4px 12px;
}

/* Test Report Modal */
.ms-report-modal {
    max-width: 80%;
    max-height: 80vh;
    width: 800px;
    overflow: auto;
}

.ms-report-header {
    margin-bottom: 16px;
    padding-bottom: 16px;
    border-bottom: 1px solid var(--teams-border);
}

.ms-report-header h3 {
    margin-top: 0;
    margin-bottom: 12px;
    color: var(--teams-primary);
    font-size: 1.5rem;
}

.ms-report-meta {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 8px 16px;
    margin-bottom: 16px;
}

.ms-report-meta div {
    padding: 4px 0;
}

.ms-report-content {
    max-height: 50vh;
    overflow: auto;
    padding: 16px;
    background-color: #f9f9f9;
    border-radius: 4px;
    border: 1px solid #e5e5e5;
}

.ms-report-content ul {
    padding-left: 20px;
}

.ms-modal-footer {
    margin-top: 20px;
    border-top: 1px solid var(--teams-border);
    padding-top: 12px;
    text-align: right;
}

.status-passed, .status-pass, .status-PASSED, .status-PASS {
    color: var(--teams-success);
    font-weight: 600;
}

.status-failed, .status-fail, .status-FAILED, .status-FAIL {
    color: var(--teams-danger);
    font-weight: 600;
}

.status-running, .status-RUNNING {
    color: var(--teams-info);
    font-weight: 600;
}

/* Add a tooltip for email display */
td[title] {
    position: relative;
    cursor: help;
}

td[title]:hover::after {
    content: attr(title);
    position: absolute;
    bottom: 100%;
    left: 0;
    background-color: #333;
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 10;
}

/* Test Suite Cards Styles */
.test-suite-cards-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 1rem;
    padding: 1rem 0;
    max-height: 500px;
    overflow-y: auto;
}

.test-suite-card {
    background: var(--teams-light);
    border: 2px solid var(--teams-border);
    border-radius: 8px;
    padding: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.test-suite-card:hover {
    border-color: var(--teams-primary);
    box-shadow: 0 4px 8px rgba(98, 100, 167, 0.15);
    transform: translateY(-1px);
}

.test-suite-card.selected {
    border-color: var(--teams-primary);
    background: rgba(98, 100, 167, 0.05);
    box-shadow: 0 4px 8px rgba(98, 100, 167, 0.2);
}

.test-suite-card.selected .card-select-indicator {
    opacity: 1;
    color: var(--teams-primary);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 0.75rem;
}

.card-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--teams-text);
    margin: 0;
    flex: 1;
    padding-right: 0.5rem;
    line-height: 1.3;
}

.card-select-indicator {
    opacity: 0;
    transition: opacity 0.3s ease;
    font-size: 1.2rem;
    color: var(--teams-border);
    flex-shrink: 0;
}

.card-body {
    margin-bottom: 0.75rem;
}

.card-details {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.9rem;
}

.detail-label {
    font-weight: 500;
    color: var(--teams-text);
    min-width: 50px;
}

.detail-value {
    color: var(--teams-primary);
    font-weight: 600;
    text-align: right;
}

.card-description {
    font-size: 0.85rem;
    color: #666;
    margin-top: 0.5rem;
    line-height: 1.4;
    max-height: 3.4em;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.card-url {
    margin-top: 0.5rem;
}

.card-url a {
    color: var(--teams-info);
    text-decoration: none;
    font-size: 0.85rem;
    word-break: break-all;
}

.card-url a:hover {
    text-decoration: underline;
}

.card-actions {
    display: flex;
    justify-content: flex-end;
    padding-top: 0.75rem;
    border-top: 1px solid var(--teams-border);
}

.run-single-suite {
    background: var(--teams-primary);
    color: var(--teams-light);
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.run-single-suite:hover {
    background: var(--teams-primary-hover);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(98, 100, 167, 0.3);
}

.run-single-suite:active {
    transform: translateY(0);
}

.run-single-suite i {
    font-size: 0.8rem;
}

/* Responsive design for cards */
@media (max-width: 768px) {
    .test-suite-cards-container {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }
    
    .test-suite-card {
        padding: 0.75rem;
    }
    
    .card-title {
        font-size: 1rem;
    }
    
    .detail-item {
        font-size: 0.85rem;
    }
}

/* Animation for card selection */
@keyframes cardSelect {
    0% { transform: scale(1); }
    50% { transform: scale(1.02); }
    100% { transform: scale(1); }
}

.test-suite-card.selected {
    animation: cardSelect 0.3s ease;
}

/* Enhance existing run selected button */
#run-selected-suite-btn {
    background: var(--teams-success);
    color: var(--teams-light);
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 4px;
    font-weight: 600;
    transition: all 0.3s ease;
    min-width: 200px;
}

#run-selected-suite-btn:hover:not(:disabled) {
    background: #7db046;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(146, 195, 83, 0.3);
}

#run-selected-suite-btn:disabled {
    background: var(--teams-border);
    color: #999;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}
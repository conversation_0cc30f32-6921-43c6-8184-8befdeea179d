/* Optimized styles for performance-enhanced config module */

/* Test Details Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    animation: fadeIn 0.2s ease-in-out;
}

.modal.show {
    display: block;
}

.modal-content {
    background-color: #fff;
    margin: 5% auto;
    padding: 0;
    border-radius: 4px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
    animation: slideIn 0.3s ease-out;
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid #e1dfdd;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #f8f9fa;
    border-radius: 4px 4px 0 0;
}

.modal-header h3 {
    margin: 0;
    color: #323130;
    font-size: 18px;
    font-weight: 600;
}

.close-btn {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #605e5c;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 2px;
    transition: background-color 0.2s;
}

.close-btn:hover {
    background-color: #e1dfdd;
}

.modal-body {
    padding: 20px;
}

.test-details-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
}

.detail-item {
    padding: 12px;
    background-color: #f8f9fa;
    border-radius: 4px;
    border-left: 3px solid #0078d4;
}

.detail-item.full-width {
    grid-column: 1 / -1;
}

.detail-item strong {
    color: #323130;
    display: block;
    margin-bottom: 4px;
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.status-passed {
    background-color: #dff6dd;
    color: #107c10;
}

.status-failed {
    background-color: #fde7e9;
    color: #d13438;
}

.status-running {
    background-color: #fff4ce;
    color: #8a8886;
}

.status-queued {
    background-color: #e1f5fe;
    color: #0078d4;
}

/* Active Tests Enhanced Styling */
.active-test-item {
    background-color: #fff;
    border-radius: 4px;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.active-test-item:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.test-header {
    border-bottom: 1px solid #e1dfdd;
    padding-bottom: 8px;
}

.test-info {
    flex: 1;
}

.test-name {
    font-size: 14px;
    line-height: 1.4;
}

.test-details {
    line-height: 1.5;
}

.test-details div {
    margin-bottom: 4px;
}

.test-details div:last-child {
    margin-bottom: 0;
}

/* Performance Metrics Display */
.performance-metrics {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background-color: #fff;
    border: 1px solid #e1dfdd;
    border-radius: 4px;
    padding: 12px;
    font-size: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    z-index: 999;
    max-width: 250px;
}

.performance-metrics.hidden {
    display: none;
}

.performance-metrics h4 {
    margin: 0 0 8px 0;
    font-size: 14px;
    color: #323130;
}

.metric-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 4px;
}

.metric-value {
    font-weight: 600;
    color: #0078d4;
}

/* Loading States */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

.loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #e1dfdd;
    border-top: 3px solid #0078d4;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from {
        transform: translateY(-50px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Recent Runs Table Optimizations */
.recent-runs-table {
    table-layout: fixed;
}

.recent-runs-table td {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.recent-runs-table .view-details-btn {
    min-width: 100px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .modal-content {
        margin: 10px;
        max-width: calc(100% - 20px);
    }
    
    .test-details-grid {
        grid-template-columns: 1fr;
    }
    
    .performance-metrics {
        bottom: 10px;
        right: 10px;
        left: 10px;
        max-width: none;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .active-test-item {
        border: 2px solid #000;
    }
    
    .status-badge {
        border: 1px solid #000;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .modal,
    .modal-content,
    .active-test-item {
        animation: none;
        transition: none;
    }
}

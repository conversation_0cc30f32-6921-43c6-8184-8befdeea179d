/**
 * Test Execution Monitor
 * 
 * This script monitors the status of a test execution and displays results.
 * 
 * Usage:
 *   node monitor_test.js [tsnId] [environment] [timeoutMinutes]
 * 
 * Examples:
 *   node monitor_test.js 12345 qa02 10
 */

const dbConnector = require('./db-connector');

// Process command line arguments
const args = process.argv.slice(2);
const tsnId = args[0];
const environment = args[1] || 'qa02';
const timeoutMinutes = parseInt(args[2] || '10', 10);

// Validate arguments
if (!tsnId) {
  console.error('Error: Test Session ID (tsnId) is required');
  console.error('Usage: node monitor_test.js [tsnId] [environment] [timeoutMinutes]');
  console.error('Example: node monitor_test.js 12345 qa02 10');
  process.exit(1);
}

// Main function
async function main() {
  console.log(`\n=== TEST EXECUTION MONITOR ===\n`);
  console.log(`Environment: ${environment}`);
  console.log(`Test Session ID: ${tsnId}`);
  console.log(`Timeout: ${timeoutMinutes} minutes\n`);
  
  try {
    // Connect to database
    console.log(`Connecting to ${environment} database...`);
    await dbConnector.init(environment, { debug: false });
    
    // Check if test session exists
    console.log(`Checking if test session ${tsnId} exists...`);
    const [sessionRows] = await dbConnector.query(`
      SELECT * FROM test_session WHERE tsn_id = ?
    `, [tsnId]);
    
    if (sessionRows.length === 0) {
      throw new Error(`Test session ${tsnId} not found in the database`);
    }
    
    const session = sessionRows[0];
    const username = session.uid || session.column2 || 'Unknown';
    const startTime = session.start_ts || session.column3 || 'Unknown';
    
    console.log(`\nTest Session Details:`);
    console.log(`--------------------------`);
    console.log(`User: ${username}`);
    console.log(`Start Time: ${startTime}`);
    console.log(`--------------------------\n`);
    
    // Check if the test is already complete
    const isAlreadyComplete = session.end_ts || session.column4;
    
    if (isAlreadyComplete) {
      console.log(`This test has already completed at ${session.end_ts || session.column4}`);
      await showTestResults(tsnId);
      await dbConnector.close();
      process.exit(0);
    }
    
    // Monitor the test execution until completion or timeout
    console.log(`Monitoring test execution (timeout: ${timeoutMinutes} minutes)...`);
    await monitorTestRun(tsnId, timeoutMinutes);
    
  } catch (error) {
    console.error(`\nError: ${error.message}`);
    
    // Close the database connection if it was opened
    try {
      await dbConnector.close();
    } catch (closeError) {
      // Ignore close errors
    }
    
    process.exit(1);
  }
}

/**
 * Monitor test execution until completion or timeout
 * @param {string} tsnId - Test session ID
 * @param {number} timeoutMinutes - Monitoring timeout in minutes
 */
async function monitorTestRun(tsnId, timeoutMinutes) {
  let isComplete = false;
  const startTime = Date.now();
  const timeoutMs = timeoutMinutes * 60 * 1000;
  const intervalMs = 5000; // 5 seconds
  
  // Display initial status
  await displayCurrentStatus(tsnId);
  
  while (!isComplete && (Date.now() - startTime < timeoutMs)) {
    // Check if the test session has ended
    const [sessionRows] = await dbConnector.query(`
      SELECT end_ts FROM test_session WHERE tsn_id = ?
    `, [tsnId]);
    
    // If end_ts is not null, the test is complete
    isComplete = sessionRows.length > 0 && (sessionRows[0].end_ts || sessionRows[0].column1) !== null;
    
    if (isComplete) {
      console.log('\n✅ Test execution completed!');
      break;
    }
    
    // Wait before checking again
    await new Promise(resolve => setTimeout(resolve, intervalMs));
    
    // Display updated status
    await displayCurrentStatus(tsnId);
  }
  
  if (!isComplete) {
    console.log(`\n⚠️ Test run monitoring timed out after ${timeoutMinutes} minutes`);
    console.log('The test may still be running, but the script will exit now');
  } else {
    // Show the final test results
    await showTestResults(tsnId);
  }
  
  // Close the database connection
  await dbConnector.close();
}

/**
 * Display current test status
 * @param {string} tsnId - Test session ID
 */
async function displayCurrentStatus(tsnId) {
  try {
    // Get latest test results
    const [resultRows] = await dbConnector.query(`
      SELECT tc_id, outcome, COUNT(*) as count
      FROM test_result
      WHERE tsn_id = ?
      GROUP BY tc_id, outcome
      ORDER BY tc_id
    `, [tsnId]);
    
    console.log(`\nCurrent test status (${new Date().toISOString()}):`);
    console.log('----------------------------------------');
    
    if (resultRows.length === 0) {
      console.log('No test results found yet - execution may not have started');
      return;
    }
    
    // Group results by test case
    const testCases = {};
    
    resultRows.forEach(row => {
      const tcId = row.tc_id || row.column1;
      const outcome = row.outcome || row.column2;
      const count = row.count || row.column3 || 0;
      
      if (!testCases[tcId]) {
        testCases[tcId] = {};
      }
      
      testCases[tcId][outcome] = count;
    });
    
    // Display results
    for (const [tcId, outcomes] of Object.entries(testCases)) {
      const outcomeStr = Object.entries(outcomes)
        .map(([outcome, count]) => `${outcome}: ${count}`)
        .join(', ');
      
      console.log(`Test Case ${tcId}: ${outcomeStr}`);
    }
    
    // Get activity timestamp
    const [activityRows] = await dbConnector.query(`
      SELECT MAX(creation_time) as latest_activity
      FROM test_result
      WHERE tsn_id = ?
    `, [tsnId]);
    
    if (activityRows.length > 0 && activityRows[0].latest_activity) {
      const latestActivity = activityRows[0].latest_activity || activityRows[0].column1;
      console.log(`Latest activity: ${latestActivity}`);
    }
  } catch (error) {
    console.error(`Error getting test status: ${error.message}`);
  }
}

/**
 * Show detailed test results
 * @param {string} tsnId - Test session ID
 */
async function showTestResults(tsnId) {
  try {
    // Get test summary
    const [summaryRows] = await dbConnector.query(`
      SELECT 
        tsn_id, 
        SUM(CASE WHEN outcome='P' THEN 1 ELSE 0 END) AS passed_cases,
        SUM(CASE WHEN outcome='F' THEN 1 ELSE 0 END) AS failed_cases,
        TIMEDIFF(MAX(creation_time), MIN(creation_time)) AS duration
      FROM test_result
      WHERE tsn_id = ?
      GROUP BY tsn_id
    `, [tsnId]);
    
    console.log('\n=== TEST SUMMARY ===');
    
    if (summaryRows.length === 0) {
      console.log('No test results available');
      return;
    }
    
    const summary = summaryRows[0];
    console.log(`Test Run ID: ${summary.tsn_id || summary.column1}`);
    console.log(`Passed Cases: ${summary.passed_cases || summary.column2 || 0}`);
    console.log(`Failed Cases: ${summary.failed_cases || summary.column3 || 0}`);
    console.log(`Duration: ${summary.duration || summary.column4 || 'Unknown'}`);
    
    // Check if there are failures
    const failedCases = summary.failed_cases || summary.column3 || 0;
    
    if (failedCases > 0) {
      // Get failure details
      const [failureRows] = await dbConnector.query(`
        SELECT r.tc_id, r.outcome, r.seq_index, i.txt
        FROM test_result r
        JOIN output i ON r.cnt = i.cnt
        WHERE r.tsn_id = ? AND r.outcome = 'F'
        ORDER BY r.tc_id, r.seq_index
      `, [tsnId]);
      
      console.log('\n=== FAILURE DETAILS ===');
      
      // Group failures by test case
      const failuresByTestCase = {};
      
      failureRows.forEach(row => {
        const tcId = row.tc_id || row.column1;
        const seqIndex = row.seq_index || row.column3;
        const txt = row.txt || row.column4;
        
        if (!failuresByTestCase[tcId]) {
          failuresByTestCase[tcId] = [];
        }
        
        failuresByTestCase[tcId].push({
          seq_index: seqIndex,
          output: txt
        });
      });
      
      // Print failures by test case
      for (const [tcId, failures] of Object.entries(failuresByTestCase)) {
        console.log(`\nTest Case ${tcId} Failures:`);
        failures.forEach((failure, index) => {
          console.log(`  Failure #${index + 1} (Step ${failure.seq_index}):`);
          // Truncate the output if it's too long
          const outputText = failure.output.substring(0, 150);
          console.log(`  ${outputText}${failure.output.length > 150 ? '...' : ''}`);
        });
      }
    } else {
      console.log('\n✅ All test cases passed!');
    }
  } catch (error) {
    console.error(`Error getting test results: ${error.message}`);
  }
}

// Run the main function
main().catch(console.error); 
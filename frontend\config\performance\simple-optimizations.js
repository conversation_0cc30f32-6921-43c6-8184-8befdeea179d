/**
 * Simple Performance Optimizations
 * Lightweight version that can be included directly in config.js
 * Provides basic request deduplication and caching without external dependencies
 */

(function() {
    'use strict';
    
    // Simple Request Manager
    class SimpleRequestManager {
        constructor() {
            this.pendingRequests = new Map();
            this.cache = new Map();
            this.cacheConfig = {
                recentRuns: 3000,    // 3 seconds TTL
                activeTests: 2000,   // 2 seconds TTL
                testDetails: 30000   // 30 seconds TTL
            };
        }
        
        async executeRequest(key, requestFn, cacheType = 'recentRuns') {
            // Check cache first
            const cached = this.getFromCache(key, cacheType);
            if (cached) {
                console.log(`Cache HIT: ${key}`);
                return cached;
            }
            
            // Check if request is pending
            if (this.pendingRequests.has(key)) {
                console.log(`Request deduplication: ${key}`);
                return await this.pendingRequests.get(key);
            }
            
            // Execute new request
            const promise = this.executeNewRequest(key, requestFn, cacheType);
            this.pendingRequests.set(key, promise);
            
            return promise;
        }
        
        async executeNewRequest(key, requestFn, cacheType) {
            try {
                const result = await requestFn();
                this.setCache(key, result, cacheType);
                console.log(`Request completed: ${key}`);
                return result;
            } catch (error) {
                console.error(`Request failed: ${key}`, error);
                throw error;
            } finally {
                this.pendingRequests.delete(key);
            }
        }
        
        getFromCache(key, cacheType) {
            const entry = this.cache.get(key);
            if (!entry) return null;
            
            const ttl = this.cacheConfig[cacheType] || 5000;
            if (Date.now() - entry.timestamp > ttl) {
                this.cache.delete(key);
                return null;
            }
            
            return entry.data;
        }
        
        setCache(key, data, cacheType) {
            this.cache.set(key, {
                data: data,
                timestamp: Date.now(),
                type: cacheType
            });
            
            // Simple LRU - remove oldest if cache is too large
            if (this.cache.size > 50) {
                const firstKey = this.cache.keys().next().value;
                this.cache.delete(firstKey);
            }
        }
        
        clearCache() {
            this.cache.clear();
            console.log('Cache cleared');
        }
        
        getStats() {
            return {
                cacheSize: this.cache.size,
                pendingRequests: this.pendingRequests.size
            };
        }
    }
    
    // Simple Polling Coordinator
    class SimplePollingCoordinator {
        constructor() {
            this.intervals = new Map();
            this.subscribers = new Map();
            this.data = new Map();
        }
        
        subscribe(dataType, callback, componentId) {
            if (!this.subscribers.has(dataType)) {
                this.subscribers.set(dataType, new Map());
            }
            this.subscribers.get(dataType).set(componentId, callback);
            
            // Send current data if available
            if (this.data.has(dataType)) {
                callback(this.data.get(dataType));
            }
            
            console.log(`Subscribed ${componentId} to ${dataType}`);
        }
        
        unsubscribe(componentId) {
            for (const [dataType, subs] of this.subscribers.entries()) {
                subs.delete(componentId);
            }
            console.log(`Unsubscribed ${componentId}`);
        }
        
        startPolling(dataType, pollFn, interval) {
            if (this.intervals.has(dataType)) {
                clearInterval(this.intervals.get(dataType));
            }
            
            // Poll immediately
            this.poll(dataType, pollFn);
            
            // Set up interval
            const intervalId = setInterval(() => {
                this.poll(dataType, pollFn);
            }, interval);
            
            this.intervals.set(dataType, intervalId);
            console.log(`Started polling ${dataType} every ${interval}ms`);
        }
        
        async poll(dataType, pollFn) {
            try {
                const data = await pollFn();
                this.data.set(dataType, data);
                this.notifySubscribers(dataType, data);
            } catch (error) {
                console.error(`Polling error for ${dataType}:`, error);
            }
        }
        
        notifySubscribers(dataType, data) {
            const subs = this.subscribers.get(dataType);
            if (subs) {
                subs.forEach((callback, componentId) => {
                    try {
                        callback(data);
                    } catch (error) {
                        console.error(`Callback error for ${componentId}:`, error);
                    }
                });
            }
        }
        
        stopPolling(dataType) {
            const intervalId = this.intervals.get(dataType);
            if (intervalId) {
                clearInterval(intervalId);
                this.intervals.delete(intervalId);
                console.log(`Stopped polling ${dataType}`);
            }
        }
        
        stopAllPolling() {
            this.intervals.forEach((intervalId, dataType) => {
                clearInterval(intervalId);
                console.log(`Stopped polling ${dataType}`);
            });
            this.intervals.clear();
        }
    }
    
    // Initialize simple optimizations
    function initializeSimpleOptimizations() {
        console.log('🚀 Initializing simple performance optimizations...');
        
        // Create global instances
        window.simpleRequestManager = new SimpleRequestManager();
        window.simplePollingCoordinator = new SimplePollingCoordinator();
        
        // Wrap API service methods if available
        if (window.apiService) {
            wrapApiService();
        } else {
            // Wait for API service
            document.addEventListener('apiservice-ready', wrapApiService);
        }
        
        console.log('✅ Simple performance optimizations initialized');
    }
    
    function wrapApiService() {
        if (!window.apiService || window.apiService._optimized) return;
        
        const originalGetRecentRuns = window.apiService.getRecentRuns;
        window.apiService.getRecentRuns = async function(options = {}) {
            const key = `recentRuns_${JSON.stringify(options)}`;
            return await window.simpleRequestManager.executeRequest(
                key,
                () => originalGetRecentRuns.call(this, options),
                'recentRuns'
            );
        };
        
        const originalGetActiveTests = window.apiService.getActiveTests;
        window.apiService.getActiveTests = async function() {
            const key = 'activeTests';
            return await window.simpleRequestManager.executeRequest(
                key,
                () => originalGetActiveTests.call(this),
                'activeTests'
            );
        };
        
        const originalGetTestDetails = window.apiService.getTestDetails;
        window.apiService.getTestDetails = async function(tsnId) {
            const key = `testDetails_${tsnId}`;
            return await window.simpleRequestManager.executeRequest(
                key,
                () => originalGetTestDetails.call(this, tsnId),
                'testDetails'
            );
        };
        
        window.apiService._optimized = true;
        console.log('✅ API service optimized with simple request manager');
    }
    
    // Setup coordinated polling for config module
    function setupConfigPolling() {
        if (!window.apiService || !window.simplePollingCoordinator) {
            setTimeout(setupConfigPolling, 100);
            return;
        }

        // Start polling for recent runs (8 seconds)
        window.simplePollingCoordinator.startPolling(
            'recentRuns',
            async () => {
                try {
                    return await window.apiService.getRecentRuns({ limit: 50, type: 'single_case' });
                } catch (error) {
                    console.warn('Failed to get recent runs, falling back to basic call:', error);
                    return await window.apiService.getRecentRuns({ limit: 50, type: 'single_case' });
                }
            },
            8000
        );

        // Start polling for active tests (2 seconds)
        window.simplePollingCoordinator.startPolling(
            'activeTests',
            async () => {
                try {
                    // Get recent runs and filter for active tests (those without end_ts/end_time)
                    const recentRuns = await window.apiService.getRecentRuns({
                        limit: 100,
                        type: 'single_case' // Only get single test cases, not test suites
                    });

                    // Filter for active tests: no end_time and has tc_id (single test cases)
                    const activeTests = recentRuns.filter(run =>
                        !run.end_time &&
                        !run.end_ts &&
                        run.tc_id &&
                        run.tc_id !== '' &&
                        run.tc_id !== null
                    );

                    console.log(`Found ${activeTests.length} active tests from ${recentRuns.length} recent runs`);
                    return activeTests;

                } catch (error) {
                    console.warn('Failed to get active tests:', error);
                    return [];
                }
            },
            2000
        );

        console.log('✅ Coordinated polling setup complete');
    }
    
    // Expose functions globally
    window.initializeSimpleOptimizations = initializeSimpleOptimizations;
    window.setupConfigPolling = setupConfigPolling;
    
    // Auto-initialize
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeSimpleOptimizations);
    } else {
        initializeSimpleOptimizations();
    }
    
    // Setup polling after a short delay
    setTimeout(setupConfigPolling, 1000);
    
})();

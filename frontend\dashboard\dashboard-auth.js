/**
 * dashboard-auth.js
 *
 * This module handles all user authentication logic, including login/logout UI,
 * form submissions, and session state management using the unified auth client.
 */

import { config } from './dashboard-config.js';
import * as api from './dashboard-api.js';
import { notifications } from './dashboard-ui.js';

// Import unified auth client
let unifiedAuthClient = null;

// Initialize unified auth client when available
function initUnifiedAuth() {
    if (window.unifiedAuthClient && !unifiedAuthClient) {
        unifiedAuthClient = window.unifiedAuthClient;

        // Set up event listeners
        unifiedAuthClient.addEventListener('login', handleAuthLogin);
        unifiedAuthClient.addEventListener('logout', handleAuthLogout);
        unifiedAuthClient.addEventListener('sessionExpired', handleSessionExpired);

        console.log('✅ Dashboard connected to unified auth client');
        return true;
    }
    return false;
}

// Try to initialize immediately, or wait for it to be available
if (!initUnifiedAuth()) {
    // Wait for unified auth client to be available
    const checkInterval = setInterval(() => {
        if (initUnifiedAuth()) {
            clearInterval(checkInterval);
        }
    }, 100);

    // Stop trying after 5 seconds
    setTimeout(() => clearInterval(checkInterval), 5000);
}

/**
 * Initializes all authentication-related event listeners.
 */
export function initializeAuth() {
    config.elements.loginForm?.addEventListener('submit', handleLoginSubmit);
    config.elements.loginButton?.addEventListener('click', showLoginModal);
    config.elements.logoutButton?.addEventListener('click', handleLogout);

    // Check initial login state from unified auth client
    if (unifiedAuthClient && unifiedAuthClient.isAuthenticated) {
        const currentUser = unifiedAuthClient.getCurrentUser();
        updateLoginUI(true, currentUser.uid);
        // Don't call handleAuthLogin here to avoid duplicate events
    } else {
        updateLoginUI(false);
        // Show login modal if not authenticated
        showLoginModal();
    }
}

/**
 * Shows the login modal dialog.
 */
function showLoginModal() {
    if (config.elements.loginModal) {
        config.elements.loginModal.style.display = 'block';
        config.elements.loginModal.classList.add('active');
    }
    config.elements.usernameInput?.focus();
}

/**
 * Hides the login modal dialog.
 */
function hideLoginModal() {
    if (config.elements.loginModal) {
        config.elements.loginModal.style.display = 'none';
        config.elements.loginModal.classList.remove('active');
    }
}

/**
 * Handles the submission of the login form.
 * @param {Event} event - The form submission event.
 */
async function handleLoginSubmit(event) {
    event.preventDefault();
    const username = config.elements.usernameInput?.value;
    const password = config.elements.passwordInput?.value;

    if (!username || !password) {
        notifications.error('Username and password are required.', 'Login Error');
        return;
    }

    notifications.info('Logging in...', 'Please wait');

    try {
        // Use unified auth client if available, otherwise fallback to API
        if (unifiedAuthClient) {
            const result = await unifiedAuthClient.login(username, password);
            if (result.success) {
                // handleAuthLogin will be called automatically via event listener
                hideLoginModal();
                notifications.success(`Welcome, ${username}!`, 'Login Successful');
            } else {
                notifications.error(result.error || 'Invalid credentials. Please try again.', 'Login Failed');
                if (config.elements.loginStatus) {
                    config.elements.loginStatus.textContent = result.error || 'Invalid credentials.';
                }
            }
        } else {
            // Fallback to original API method
            const success = await api.login(username, password);
            if (success) {
                handleSuccessfulLogin(username);
            } else {
                notifications.error('Invalid credentials. Please try again.', 'Login Failed');
                if (config.elements.loginStatus) {
                    config.elements.loginStatus.textContent = 'Invalid credentials.';
                }
            }
        }
    } catch (error) {
        console.error('Login error:', error);
        notifications.error('An unexpected error occurred during login.', 'Login Error');
    }
}

/**
 * Handles the UI updates and state changes for a successful login.
 * @param {string} username - The username of the logged-in user.
 */
function handleSuccessfulLogin(username) {
    hideLoginModal();
    updateLoginUI(true, username);
    notifications.success(`Welcome, ${username}!`, 'Login Successful');
    // The main app will re-initialize or refresh data as needed.
    // We can dispatch an event to notify other modules.
    document.dispatchEvent(new CustomEvent('auth-state-changed', { detail: { loggedIn: true } }));
}

/**
 * Handles the user logout process.
 */
async function handleLogout() {
    try {
        if (unifiedAuthClient) {
            await unifiedAuthClient.logout();
            // handleAuthLogout will be called automatically via event listener
        } else {
            // Fallback to original API method
            await api.logout();
            updateLoginUI(false);
            notifications.info('You have been logged out.', 'Logout');
            document.dispatchEvent(new CustomEvent('auth-state-changed', { detail: { loggedIn: false } }));
        }
    } catch (error) {
        console.error('Logout error:', error);
        notifications.error('An error occurred during logout.', 'Logout Error');
    }
}

/**
 * Handle unified auth login event
 * @param {Object} data - Login event data
 */
function handleAuthLogin(data) {
    const user = data.user;
    updateLoginUI(true, user.uid);
    document.dispatchEvent(new CustomEvent('auth-state-changed', { detail: { loggedIn: true } }));

    // Update API service with credentials if available
    if (window.apiService && window.apiService.setCredentials) {
        // Note: In production, you wouldn't store passwords in memory
        // This is for backward compatibility with existing API service
        const storedPassword = sessionStorage.getItem('smarttest_pwd');
        if (storedPassword) {
            window.apiService.setCredentials(user.uid, storedPassword);
        }
    }

    console.log('✅ Dashboard: User authenticated via unified auth client');
}

/**
 * Handle unified auth logout event
 */
function handleAuthLogout() {
    updateLoginUI(false);
    notifications.info('You have been logged out.', 'Logout');
    document.dispatchEvent(new CustomEvent('auth-state-changed', { detail: { loggedIn: false } }));

    // Clear any cached data
    clearDashboardData();

    console.log('✅ Dashboard: User logged out via unified auth client');
}

/**
 * Clear dashboard data on logout
 */
function clearDashboardData() {
    try {
        // Clear any sensitive dashboard data
        if (window.apiService && typeof window.apiService.clearCache === 'function') {
            window.apiService.clearCache();
        }

        // Clear any displayed user data
        const userElements = document.querySelectorAll('[data-user-info]');
        userElements.forEach(element => {
            element.textContent = '';
        });

        // Reset any forms
        const forms = document.querySelectorAll('form');
        forms.forEach(form => {
            if (form.id !== 'loginForm') { // Don't reset login form
                form.reset();
            }
        });

        console.log('✅ Dashboard data cleared');
    } catch (error) {
        console.warn('Error clearing dashboard data:', error);
    }
}

/**
 * Handle session expiration
 */
function handleSessionExpired() {
    updateLoginUI(false);
    notifications.warning('Your session has expired. Please log in again.', 'Session Expired');
    showLoginModal();
    document.dispatchEvent(new CustomEvent('auth-state-changed', { detail: { loggedIn: false } }));
    console.log('⏰ Dashboard: Session expired');
}

/**
 * Updates the header and other UI elements based on login state.
 * @param {boolean} isLoggedIn - Whether the user is logged in.
 * @param {string} [username] - The username if logged in.
 */
function updateLoginUI(isLoggedIn, username = '') {
    const { loginButton, logoutButton, userDisplay } = config.elements;

    if (isLoggedIn) {
        if (loginButton) loginButton.style.display = 'none';
        if (logoutButton) logoutButton.style.display = 'inline-block';
        if (userDisplay) userDisplay.textContent = `Logged in as: ${username}`;
    } else {
        if (loginButton) loginButton.style.display = 'inline-block';
        if (logoutButton) logoutButton.style.display = 'none';
        if (userDisplay) userDisplay.textContent = 'Not logged in';
    }
}

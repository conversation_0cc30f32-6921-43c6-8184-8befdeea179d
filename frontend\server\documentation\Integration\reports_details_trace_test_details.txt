\*\*End-to-end trace: what happens inside the app when you hit **“**Details**” in the Test-Runs table**

| #     | Layer                       | What actually happens                                                                                                                                                                                                                                                                           |
| ----- | --------------------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **1** | **UI – click**              | The *updateReportsTable()* routine inserts a **Details** button for every test-run row. A click listener (`detailsBtn.addEventListener('click', …)`) fires and passes that row’s **tsnId** to `loadTestDetails(tsnId)`.                                                                         |
| **2** | **Frontend – controller**   | `loadTestDetails()`<br> a. Pulls user creds (cookie / sessionStorage).<br> b. Looks for the test-run in `currentState.reports` (local cache).<br> c. If not cached → calls `window.apiService.getTestDetails(tsnId)` (because `config.useDirectExternalApi === false`).                         |
| **3** | **ApiService – HTTP call**  | Issues **GET /local/test-details/\:tsn\_id** with `JSESSIONID`.<br>(If the flag were true it would hit the external **/AutoRun/ReportDetails** instead).                                                                                                                                        |
| **4** | **Backend – REST handler**  | Reads `tsn_id`, queries DB tables (test\_session, test\_result, test\_case …).<br>Builds JSON payload: run summary + `test_cases` array (one object per case).                                                                                                                                  |
| **5** | **Frontend – data arrival** | Promise resolves → payload stored in `currentState.currentTestDetails`.<br>Fallback: if the run is a suite and came back without `test_cases`, the code synthesises placeholder rows from the passed/failed counts.                                                                             |
| **6** | **View – summary section**  | `displayTestDetails()` renders the header panel:<br>run name, overall status, env, duration, user, trigger, totals, etc.<br>If `report` HTML is present, embeds it in an `<iframe>`.                                                                                                            |
| **7** | **View – per-case table**   | `updateTestCasesTable(testDetails.cases)`<br> a. Wipes `<tbody>` of **#test-cases-table**.<br> b. Loops through each case object and creates `<tr>` cells for: `tc_id`, `description`, `status` (adds `passed`/`failed` class), `duration`, `error_message`.<br> c. Appends the row to the DOM. |
| **8** | **Result**                  | The modal/section is now populated: summary on top, granular step-by-step results below. User can scroll, copy, or drill further as needed.                                                                                                                                                     |

**Edge cases / alternate paths**

* If `useDirectExternalApi` is toggled **true**, step 3 instead opens **GET /AutoRun/ReportDetails?tsn\_id=…** and parses the returned HTML.
* If the DB returns zero cases, the empty array path still renders a table header with no rows, signalling “no test cases recorded”.

That’s the complete trace from button-press to final detailed view.

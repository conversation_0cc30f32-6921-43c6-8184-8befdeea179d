# SessionStorage Standardization Testing Guide

## Test Objectives

Verify that all modules can access user credentials consistently using standardized sessionStorage keys after the consolidation changes.

## Test Scenarios

### Test 1: Standard Key Usage
**Objective**: Verify modules use `smarttest_uid` as primary key

**Setup**:
```javascript
// Set standard key in browser console
sessionStorage.setItem('smarttest_uid', 'testuser');
sessionStorage.setItem('smarttest_pwd', 'testpass');
```

**Verification Points**:
- Dashboard api-integration.js should find user via `smarttest_uid`
- Shared services should load credentials correctly
- Config modules should authenticate successfully

### Test 2: Backward Compatibility
**Objective**: Verify legacy `currentUser` key still works as fallback

**Setup**:
```javascript
// Clear standard key, set only legacy key
sessionStorage.removeItem('smarttest_uid');
sessionStorage.setItem('currentUser', 'legacyuser');
```

**Verification Points**:
- Dashboard should still find user via legacy fallback
- Logs should show "legacy currentUser" message
- Authentication should work without errors

### Test 3: Cross-Module Consistency
**Objective**: Verify all modules see the same user

**Setup**:
```javascript
// Set standard keys
sessionStorage.setItem('smarttest_uid', 'crossmoduleuser');
sessionStorage.setItem('smarttest_pwd', 'crosspass');
```

**Test Steps**:
1. Login through config module
2. Navigate to dashboard
3. Navigate to reports
4. Check all modules show same user

**Expected Result**: All modules display "crossmoduleuser"

### Test 4: Fallback Chain Priority
**Objective**: Verify correct priority order in credential lookup

**Setup**:
```javascript
// Set multiple keys with different values
sessionStorage.setItem('smarttest_uid', 'standard_user');
sessionStorage.setItem('currentUser', 'legacy_user');
localStorage.setItem('smarttest_uid', 'local_standard');
localStorage.setItem('currentUser', 'local_legacy');
```

**Expected Priority**:
1. `sessionStorage.smarttest_uid` → "standard_user" (should win)
2. `sessionStorage.currentUser` → "legacy_user"
3. `localStorage.smarttest_uid` → "local_standard"  
4. `localStorage.currentUser` → "local_legacy"

## Automated Testing

### Browser Console Tests
```javascript
// Test 1: Clear all and set standard
sessionStorage.clear();
localStorage.clear();
sessionStorage.setItem('smarttest_uid', 'test_standard');

// Check dashboard api-integration
if (window.dashboardApiIntegration) {
  console.log('Dashboard user:', window.dashboardApiIntegration.getCurrentUser());
}

// Check unified API service
if (window.apiService) {
  console.log('API service user:', window.apiService.credentials.uid);
}
```

### Log Verification
Look for these log messages in browser console:

**Success Indicators**:
- `[getCurrentUser] Got user from sessionStorage (smarttest_uid): [user]`
- `API credentials set for user: [user]`
- `Credentials loaded for user: [user]`

**Fallback Indicators**:
- `[getCurrentUser] Got user from sessionStorage (legacy currentUser): [user]`
- `[getCurrentUser] Got user from localStorage (legacy currentUser): [user]`

## Manual Testing Steps

### Step 1: Fresh Login Test
1. Clear all browser storage
2. Login through any module
3. Navigate between modules
4. Verify consistent user display

### Step 2: Refresh Test
1. Login and navigate to dashboard
2. Refresh browser
3. Verify user persists without re-login
4. Check console for standard key usage

### Step 3: Legacy Migration Test
1. Manually set legacy keys in console
2. Refresh page
3. Verify fallback works
4. Login normally
5. Verify migration to standard keys

## Success Criteria

✅ **Primary**: All modules use `smarttest_uid` as first choice
✅ **Compatibility**: Legacy `currentUser` works as fallback
✅ **Consistency**: Same user shown across all modules
✅ **Performance**: No authentication lookup failures
✅ **Logging**: Clear indication of which key source is used

## Rollback Plan

If issues are detected:
1. Revert changes in `frontend/dashboard/api-integration.js`
2. Restore original `currentUser` key usage
3. Remove fallback logic
4. Test authentication works as before

## Implementation Status

- ✅ **Dashboard Module**: Updated to use standard keys with fallback
- ✅ **Base API Service**: Already using standard keys
- ✅ **Unified API Service**: Already using standard keys
- ✅ **Auth Client**: Already using standard keys
- ✅ **Config Module**: Already using standard keys
- ✅ **Reports Module**: Already using standard keys

**Result**: SessionStorage standardization is complete with backward compatibility maintained.

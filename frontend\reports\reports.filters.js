function setupCustomFilters() {
    try {
        console.log('Setting up custom filters');

        // User filter
        $('#filter-user').on('change', function() {
            const selectedUserId = $(this).val();
            if (!reportsDataTable) {
                console.warn('User filter: reportsDataTable not ready.');
                return;
            }

            if (!selectedUserId) {
                // Clear filter if nothing selected
                reportsDataTable.column(7).search('').draw(); // Column 7 is 'Started By' (assuming this is the user display)
                console.log('User filter cleared');
            } else {
                // Custom filtering function to match by user ID attribute
                // Note: This custom search function approach might be complex for chart updates
                // as .rows({ search: 'applied' }) might not reflect this specific custom search immediately
                // or in the way other column().search() filters do.
                // For simplicity and consistency, if possible, it's better if the 'User' column
                // in DataTable contains a value that can be directly searched via reportsDataTable.column(USER_COL_INDEX).search(selectedUserId).draw();
                // However, working with the current structure:
                $.fn.dataTable.ext.search.push(
                    function(settings, data, dataIndex) {
                        // This custom filter function is applied globally.
                        // Ensure it only acts if selectedUserId is set for this specific filter.
                        if (settings.nTable.id !== 'reports-table' || !selectedUserId) {
                            return true; // Don't interfere with other tables or if no user is selected
                        }
                        const rowNode = reportsDataTable.row(dataIndex).node();
                        const rowUserId = $(rowNode).attr('data-user-id'); // Assuming 'data-user-id' holds the value to match selectedUserId
                        return rowUserId === selectedUserId;
                    }
                );
                reportsDataTable.draw();
                $.fn.dataTable.ext.search.pop(); // Remove the custom filter function
                console.log('User filter applied:', selectedUserId);
            }
            
            // Update charts after drawing
            const filteredRowsDataUser = reportsDataTable.rows({ search: 'applied' }).data().toArray();
            const filteredReportObjectsUser = mapDataTableRowsToReportObjects(filteredRowsDataUser);
            updateCharts(filteredReportObjectsUser);
        });

        // Status filter
        $('#filter-status').on('change', function() {
            const value = $(this).val(); // Keep original case if server/data expects it, or .toLowerCase() if needed
            if (!reportsDataTable) {
                console.warn('Status filter: reportsDataTable not ready.');
                return;
            }
            reportsDataTable.column(3).search(value).draw(); // Column 3 is Status
            console.log('Status filter changed to:', value);

            // Update charts
            const filteredRowsDataStatus = reportsDataTable.rows({ search: 'applied' }).data().toArray();
            const filteredReportObjectsStatus = mapDataTableRowsToReportObjects(filteredRowsDataStatus);
            updateCharts(filteredReportObjectsStatus);
        });

        // Test ID filter
        $('#filter-test-id').on('input', function() {
            const value = $(this).val().trim();
            if (!reportsDataTable) {
                console.warn('Test ID filter: reportsDataTable not ready.');
                return;
            }
            reportsDataTable.column(2).search(value).draw(); // Column 2 is Test ID (test case/suite ID)
            console.log('Test ID filter changed to:', value);

            // Update charts
            const filteredRowsDataTestId = reportsDataTable.rows({ search: 'applied' }).data().toArray();
            const filteredReportObjectsTestId = mapDataTableRowsToReportObjects(filteredRowsDataTestId);
            updateCharts(filteredReportObjectsTestId);
        });

        // Reset filters button
        $('#reset-filters').on('click', function() {
            if (!reportsDataTable) {
                console.warn('Reset filters: reportsDataTable not ready.');
                return;
            }
            // Clear all filter inputs
            $('#filter-user').val('');
            $('#filter-status').val('');
            $('#filter-test-id').val('');

            // Reset DataTable filters
            reportsDataTable.search('').columns().every(function() {
                this.search('');
            }).draw();
            console.log('All filters reset');

            // Update charts with all data (reflecting the reset state)
            const allRowsData = reportsDataTable.rows({ search: 'applied' }).data().toArray(); // Should be all rows now
            const allReportObjects = mapDataTableRowsToReportObjects(allRowsData);
            updateCharts(allReportObjects);
        });

        console.log('Custom filters set up successfully');
    } catch (error) {
        console.error('Error setting up custom filters:', error);
    }
}

/**
 * Populate the user filter dropdown with unique users from the table
 */
function populateUserFilter() {
    try {
        console.log('Populating user filter dropdown');
        const userFilter = document.getElementById('filter-user');
        if (!userFilter) {
            console.error('User filter dropdown not found');
            return;
        }

        // Clear existing options except the first one (All Users)
        while (userFilter.options.length > 1) {
            userFilter.remove(1);
        }

        // Get all unique users from the reports data
        if (!currentState.reports || !currentState.reports.length) {
            console.log('No reports data available to populate user filter');
            return;
        }

        console.log('Found reports data for user filter:', currentState.reports.length, 'reports');
        if (currentState.reports.length > 0) {
            console.log('First report for user filter:', currentState.reports[0]);
        }

        // Extract unique users: Key by actual user ID (report.uid), value is display name
        const userMap = new Map();
        currentState.reports.forEach(report => {
            const userId = report.uid; // Actual ID, e.g., email
            let userDisplay = report.user_display; // Preferred display name from server

            // If user_display is not provided by the server, format it from uid
            if (!userDisplay && userId) {
                // Assuming formatUserEmail at line 309 (which makes "Vita Lipstein") is preferred for display.
                // If the one at line 1000 (making "vita.lipstein") is preferred, this call might need adjustment
                // or we rely on user_display being consistently present from server.
                // For now, let's use the more sophisticated formatting if user_display is missing.
                const emailFormattingFunction = formatUserEmail; // Relies on correct one being in scope
                userDisplay = emailFormattingFunction(userId);
            } else if (!userId) {
                // Skip if no usable ID
                return;
            }

            // Add to map if we have a valid ID and display name, and it's not already added
            if (userId && userDisplay && userDisplay !== 'Unknown' && !userMap.has(userId)) {
                userMap.set(userId, userDisplay);
            }
        });

        console.log(`Found ${userMap.size} unique users in reports data`);

        // If no users were found, add a default user for debugging (as per existing logic)
        if (userMap.size === 0) {
            console.log('No valid users found in reports data, adding a default user for testing');
            // This default user won't actually filter anything unless data attributes match.
            const defaultOption = document.createElement('option');
            defaultOption.value = "DefaultUser"; // Or some placeholder ID
            defaultOption.textContent = "DefaultUser";
            // userFilter.appendChild(defaultOption); // Keep or remove based on desired behavior
        }

        // Convert to array and sort by display name
        const sortedUsers = Array.from(userMap.entries())
            .map(([id, display]) => ({ id, display })) // id is userId (email), display is userDisplay
            .sort((a, b) => a.display.localeCompare(b.display));

        // Add options to the dropdown
        sortedUsers.forEach(user => {
            const option = document.createElement('option');
            option.value = user.id; // Use the actual ID (email) for the value attribute
            option.textContent = user.display; // Use the formatted name for the display text
            userFilter.appendChild(option);
        });

        console.log(`User filter populated with ${sortedUsers.length} users`);
    } catch (error) {
        console.error('Error populating user filter:', error);
    }
}

// Document ready function to initialize the page
document.addEventListener('DOMContentLoaded', function() {
    // Set up refresh button event listener
    const refreshBtn = document.getElementById('refresh-btn');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', refreshReports);
    }

    // Initialize timezone selector
    const timezoneSelector = document.getElementById('timezone-select');
    if (timezoneSelector) {
        const savedTimezone = localStorage.getItem('selected_timezone');
        if (savedTimezone) {
            timezoneSelector.value = savedTimezone;
        }
        timezoneSelector.addEventListener('change', function() {
            saveSelectedTimezone(this.value);
            refreshAllTimestamps();
        });
        console.log('Timezone selector initialized with timezone:', getSelectedTimezone());
    } else {
        console.warn('Timezone selector not found');
    }

    // Note: initializeFilters() is called from reports.init.js after all scripts are loaded

    // Initialize charts
    initializeCharts();

    // Initial refresh of timestamps to ensure they're formatted with the correct timezone
    // This should be called AFTER data is potentially loaded if it formats data in the table
    // refreshAllTimestamps();

    // Load initial data for the default time range (e.g., currentState.activeTimeRange which is '24h')
    // DataTable will be initialized after data is loaded and displayReports is called.
    loadReportsData({ forceFullReload: true, showLoading: true });

    // Call refreshAllTimestamps after the initial data load and table population might be better.
    // Or ensure formatDateTime is robust enough to be called on empty/loading table.
});

/**
 * Refresh all timestamps on the page to reflect the selected timezone
 */
function refreshAllTimestamps() {
    console.log('Refreshing all timestamps with timezone:', getSelectedTimezone());

    try {
        // Update timestamps in the reports table
        const reportRows = document.querySelectorAll('#reports-table tbody tr');

        reportRows.forEach(row => {
            // Find the start time and end time cells (columns 4 and 5)
            const startTimeCell = row.cells[4]; // 5th column (0-indexed)
            const endTimeCell = row.cells[5];   // 6th column (0-indexed)

            if (startTimeCell) {
                // Get the original timestamp if we stored it as a data attribute
                const originalStartTime = startTimeCell.dataset.originalTime || startTimeCell.textContent;
                if (originalStartTime && originalStartTime !== '-' && originalStartTime !== 'Not completed') {
                    // Store the original value if we haven't already
                    if (!startTimeCell.dataset.originalTime) {
                        startTimeCell.dataset.originalTime = originalStartTime;
                    }
                    // Format according to the selected timezone
                    startTimeCell.textContent = formatDateTime(originalStartTime);
                }
            }

            if (endTimeCell) {
                // Only update if not "Not completed"
                if (endTimeCell.textContent !== 'Not completed') {
                    const originalEndTime = endTimeCell.dataset.originalTime || endTimeCell.textContent;
                    if (originalEndTime && originalEndTime !== '-') {
                        // Store the original value if we haven't already
                        if (!endTimeCell.dataset.originalTime) {
                            endTimeCell.dataset.originalTime = originalEndTime;
                        }
                        // Format according to the selected timezone
                        endTimeCell.textContent = formatDateTime(originalEndTime);
                    }
                }
            }
        });

        // Update timestamps in test details if open
        const detailStartTime = document.getElementById('detail-start-time');
        if (detailStartTime && detailStartTime.dataset.originalTime) {
            detailStartTime.textContent = formatDateTime(detailStartTime.dataset.originalTime);
        }

        // Update the 'Last updated' timestamp in the refresh status div
        const refreshStatus = document.getElementById('refresh-status');
        if (refreshStatus && refreshStatus.dataset.originalTime) {
            const originalTime = new Date(refreshStatus.dataset.originalTime);
            const selectedTimezone = getSelectedTimezone();
            const timeOptions = { hour: '2-digit', minute: '2-digit', second: '2-digit', hour12: true };

            // Add timezone option if not local
            if (selectedTimezone !== 'local') {
                timeOptions.timeZone = selectedTimezone;
            }

            refreshStatus.textContent = `Last updated: ${originalTime.toLocaleTimeString('en-US', timeOptions)}`;
        }
    } catch (error) {
        console.error('Error refreshing timestamps:', error);
    }
}

// Rerun failed tests from a specific test run

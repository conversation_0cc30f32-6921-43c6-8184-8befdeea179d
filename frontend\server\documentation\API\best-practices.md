# API Best Practices

This document outlines best practices for adding new functionality to the SmartTest API, ensuring proper separation of concerns and maintainability.

## Architectural Principles

### 1. Separation of Concerns

The SmartTest API follows a clear separation of concerns:

- **Frontend Layer**: Handles UI rendering and user interactions
- **API Layer**: Provides a unified interface for data access
- **Server-Side Routes**: Handle HTTP requests and responses
- **Database Layer**: Provides data access and manipulation

### 2. Consistent Response Format

All API responses should follow a consistent format:

```json
{
  "success": true|false,
  "message": "Human-readable message",
  "data": [...] // Response data (if applicable)
}
```

### 3. High-Level Database Abstraction

Always use the high-level database abstraction provided by the database module:

```javascript
// Good: Using high-level abstraction
const recentRuns = await db.getRecentRuns(filters);

// Bad: Direct connection access
const connection = await db.getConnection();
const runs = await db.queries.testSessions.getRecentRuns(connection, filters);
```

## Adding New API Endpoints

### 1. Create a Route File

Create a new route file in the `routes/` directory:

```javascript
/**
 * Example Route
 */
const express = require('express');
const router = express.Router();
const db = require('../database');
const { validateCredentials } = require('../middleware/auth');

// Define your routes
router.get('/example', validateCredentials, async (req, res) => {
  try {
    // Process query parameters
    const { param1, param2 } = req.query;
    
    // Build filters object
    const filters = {};
    if (param1) filters.param1 = param1;
    if (param2) filters.param2 = param2;
    
    // Use the database module with high-level abstraction
    const data = await db.getExampleData(filters);
    
    // Return standardized response
    return res.json({
      success: true,
      data: data || [],
      message: 'Example data retrieved successfully'
    });
  } catch (error) {
    console.error('Error retrieving example data:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to retrieve example data',
      error: error.message
    });
  }
});

module.exports = router;
```

### 2. Register the Route

Register the route in `routes/index.js`:

```javascript
// Import route module
const exampleRoutes = require('./example');

// Register route
router.use('/local', exampleRoutes);
```

### 3. Add Database Method

Add a new method to the database module in `database/index.js`:

```javascript
/**
 * Get example data
 * @param {Object} filters - Optional filters
 * @returns {Promise<Array>} - Example data
 */
async function getExampleData(filters = {}) {
  if (!isInitialized) {
    await init();
  }

  return await queries.example.getExampleData(connection, filters);
}

// Export the method
module.exports = {
  // Existing exports...
  getExampleData
};
```

### 4. Implement Database Query

Create a new query file in `database/queries/` directory:

```javascript
/**
 * Example Queries
 */
const QueryBuilder = require('../utils/query-builder');

/**
 * Get example data
 * @param {Object} connection - Database connection
 * @param {Object} filters - Optional filters
 * @returns {Promise<Array>} - Example data
 */
async function getExampleData(connection, filters = {}) {
  try {
    // Build query
    const queryBuilder = new QueryBuilder();
    queryBuilder.select('example_table', ['id', 'name', 'description']);
    
    // Add filters
    if (filters.param1) {
      queryBuilder.where('param1', '=', filters.param1);
    }
    
    // Add ordering and limit
    queryBuilder.orderBy('id', 'DESC');
    if (filters.limit) {
      queryBuilder.limit(filters.limit);
    }
    
    // Build and execute query
    const { sql, params } = queryBuilder.build();
    const rows = await connection.query(sql, params);
    
    // Process and return results
    return rows.map(row => ({
      id: row.id,
      name: row.name,
      description: row.description
    }));
  } catch (error) {
    console.error('Error in getExampleData:', error);
    return [];
  }
}

module.exports = {
  getExampleData
};
```

## Adding Frontend Integration

### 1. Add ApiService Method

Add a new method to the ApiService:

```javascript
/**
 * Get example data
 * @param {Object} options - Query options
 * @returns {Promise<Array>} - Example data
 */
async getExampleData(options = {}) {
  try {
    const response = await this.getRequest(this.endpoints.exampleEndpoint, options);
    
    // Handle response format
    if (response.success && Array.isArray(response.data)) {
      return response.data;
    } else if (Array.isArray(response)) {
      return response;
    }
    return [];
  } catch (error) {
    console.error('Error getting example data:', error);
    throw error;
  }
}
```

### 2. Add Endpoint to ApiService

Add the new endpoint to the ApiService constructor:

```javascript
this.endpoints = {
  // Existing endpoints...
  exampleEndpoint: '/local/example'
};
```

### 3. Use ApiService in Frontend Components

Use the ApiService in frontend components:

```javascript
// Load example data
async function loadExampleData(options = {}) {
  try {
    // Use ApiService
    const data = await window.apiService.getExampleData(options);
    
    // Process and display data
    displayExampleData(data);
  } catch (error) {
    console.error('Error loading example data:', error);
    displayError('Failed to load example data. Please try again later.');
  }
}
```

## Testing New API Endpoints

### 1. Manual Testing

Test the new endpoint using the browser or API testing tools:

```
GET /local/example?param1=value1&param2=value2
```

### 2. Automated Testing

Add automated tests for the new endpoint:

```javascript
describe('Example API', () => {
  it('should return example data', async () => {
    const response = await request(app)
      .get('/local/example')
      .query({ param1: 'value1', param2: 'value2' })
      .set('Authorization', 'Basic ' + Buffer.from('test:test').toString('base64'));
    
    expect(response.status).toBe(200);
    expect(response.body.success).toBe(true);
    expect(Array.isArray(response.body.data)).toBe(true);
  });
});
```

## Documentation

### 1. Update API Documentation

Update the API documentation in `frontend/server/documentation/API/endpoints.md`:

```markdown
### Example

#### Get Example Data

```
GET /local/example
```

Retrieves example data.

**Query Parameters:**

- `param1` (optional): Filter by param1
- `param2` (optional): Filter by param2
- `limit` (optional): Maximum number of results to return (default: 20)
- `uid` (required): User ID for authentication
- `password` (required): Password for authentication

**Response:**

```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "Example 1",
      "description": "Description 1"
    },
    ...
  ],
  "message": "Example data retrieved successfully"
}
```
```

### 2. Update Architecture Documentation

Update the architecture documentation if necessary.

## Conclusion

Following these best practices ensures that new API functionality is consistent, maintainable, and follows the proper separation of concerns. This makes the codebase easier to understand, test, and extend in the future.

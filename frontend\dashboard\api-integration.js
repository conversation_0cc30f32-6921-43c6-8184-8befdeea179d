/**
 * SmartTest Dashboard - API Integration
 *
 * This file integrates with the unified API service architecture
 * to provide dashboard functionality for the SmartTest application.
 */

// Wait for API service to be initialized
let apiServiceCheckAttempts = 0;
const MAX_API_CHECK_ATTEMPTS = 10;

function checkApiServiceAvailability() {
  if (window.apiService) {
    console.log('API Service found, proceeding with dashboard integration');
    return true;
  }
  
  apiServiceCheckAttempts++;
  if (apiServiceCheckAttempts >= MAX_API_CHECK_ATTEMPTS) {
    console.error('API Service not found after multiple attempts! Make sure api-service.js is loaded before this file.');
    return false;
  }
  
  console.warn(`API Service not found yet (attempt ${apiServiceCheckAttempts}/${MAX_API_CHECK_ATTEMPTS}), retrying...`);
  return false;
}

// Dashboard API Integration
class DashboardApiIntegration {
  /**
   * Constructor initializes the API integration
   */
  constructor() {
    this.initialized = false;
    console.log('DashboardApiIntegration created');

    // Use a more resilient approach to access the API service
    this.initializeApiService();

    // Will be set in initializeApiService();
    this.notifications = {
      info: (message, title, duration) => {
        console.log(`INFO: ${title} - ${message}`);
        window.showNotification(title, message, 'info', duration);
      },
      success: (message, title, duration) => {
        console.log(`SUCCESS: ${title} - ${message}`);
        window.showNotification(title, message, 'success', duration);
      },
      warning: (message, title, duration) => {
        console.log(`WARNING: ${title} - ${message}`);
        window.showNotification(title, message, 'warning', duration);
      },
      error: (message, title, duration) => {
        console.error(`ERROR: ${title} - ${message}`);
        window.showNotification(title, message, 'error', duration);
      }
    };

    this.activeTests = new Map(); // Map of active test runs (tsnId -> status)
    this.completedTestIds = new Set(); // Track completed test IDs to prevent reappearance
    this.statusPollingInterval = null;
    this._isUpdatingStatuses = false; // Flag to prevent concurrent status updates
    this._runningTestSuites = new Set(); // Track which test suites are already running

    // Test suites will be loaded from the API
    this.predefinedSuites = {};

    // Available test cases for custom suite builder
    this.availableTestCases = [];

    // Cache for recent runs reused across dashboard widgets
    this.recentRunsCache = [];
    this.highestRecentTsnId = null;
    this.recentRunsPollingInterval = null;

    // Track completed tests to prevent status reversion
    this.completedTests = new Set();
  }

  /**
   * Ensure TSN ID is always a string to prevent [object Object] keys in Maps
   * @param {any} tsnId - The TSN ID value that might be an object or string
   * @returns {string} - A valid string TSN ID
   */
  ensureStringTsnId(tsnId) {
    if (tsnId === null || tsnId === undefined) {
      console.error('TSN ID is null or undefined, using fallback');
      return 'unknown';
    }
    
    // If it's already a string, return it
    if (typeof tsnId === 'string') {
      return tsnId.trim();
    }
    
    // If it's a number, convert to string
    if (typeof tsnId === 'number') {
      return String(tsnId);
    }
    
    // If it's an object, try to extract a meaningful ID
    if (typeof tsnId === 'object') {
      console.log('TSN ID is an object, attempting to extract ID:', tsnId);
      
      // Handle API response objects with message containing session ID
      if (tsnId.message && typeof tsnId.message === 'string') {
        // Look for patterns like "session ID 16142" or "ID: 16142" in the message
        const sessionIdMatch = tsnId.message.match(/session ID\s+(\d+)|ID[:\s]+(\d+)|run ID[:\s]+(\d+)/i);
        if (sessionIdMatch) {
          const extractedId = sessionIdMatch[1] || sessionIdMatch[2] || sessionIdMatch[3];
          console.log(`Extracted session ID from message: ${extractedId}`);
          return String(extractedId);
        }
      }
      
      // Try common ID fields
      if (tsnId.tsn_id) return String(tsnId.tsn_id);
      if (tsnId.id) return String(tsnId.id);
      if (tsnId.session_id) return String(tsnId.session_id);
      if (tsnId.run_id) return String(tsnId.run_id);
      if (tsnId.testId) return String(tsnId.testId);
      
      // Fallback - use JSON string but log the error
      console.error('Could not extract TSN ID from object, using JSON representation');
      return JSON.stringify(tsnId);
    }
    
    // Fallback for any other type
    return String(tsnId);
  }

  /**
   * Initialize API service with retry logic
   */
  initializeApiService() {
    // Retry function to get API service
    const getApiService = () => {
      if (window.apiService) {
        this.apiService = window.apiService;
        console.log('Successfully connected to API service');
        return true;
      }
      return false;
    };
    
    // Try immediately
    if (getApiService()) {
      return true;
    }
    
    // If not available, set up a retry mechanism
    let attempts = 0;
    const maxAttempts = 5;
    const retryInterval = 500; // ms
    
    const retryTimer = setInterval(() => {
      attempts++;
      console.log(`Attempt ${attempts}/${maxAttempts} to connect to API service...`);
      
      if (getApiService() || attempts >= maxAttempts) {
        clearInterval(retryTimer);
        if (!this.apiService) {
          console.error('Failed to connect to API service after multiple attempts');
          this.notifications?.error('API Service not available', 'Error');
        }
      }
    }, retryInterval);
  }
  
  /**
   * Initialize the API integration
   */
  async initialize() {
    if (this.initialized) {
      console.log('DashboardApiIntegration already initialized. Skipping re-initialization.');
      return true;
    }
    try {
      // Check if API service is initialized
      if (!this.apiService) {
        console.error('Cannot initialize dashboard integration - API service not available');
        this.notifications?.error('API Service not available', 'Error');
        return false;
      }
      
      // Load credentials if not already loaded
      if (!this.apiService.credentials || !this.apiService.credentials.uid || !this.apiService.credentials.password) {
        const credentialsLoaded = this.apiService.loadCredentials && this.apiService.loadCredentials();

        if (!credentialsLoaded) {
          // Show login form if credentials aren't available
          this.showLoginForm && this.showLoginForm();
          return false;
        } else {
          // If credentials were loaded, update UI to show logged in state
          this.handleSuccessfulLogin && this.handleSuccessfulLogin(this.apiService.credentials.uid);
        }
      } else {
        // If credentials are already available, update UI to show logged in state
        this.handleSuccessfulLogin && this.handleSuccessfulLogin(this.apiService.credentials.uid);
      }
    } catch (error) {
      console.error('Error initializing API integration:', error);
      this.notifications?.error('Error initializing API integration', 'Error');
      return false;
    }

    // Start polling for active test statuses
    this.startStatusPolling();

    // Load initial data
    await this.loadDashboardData();

    // Setup event listeners for predefined suites
    this.setupEventListeners();

    // Setup event listeners for active tests filter
    this.setupActiveTestsFilterEventListeners();

    this.initialized = true; // Mark as initialized upon successful completion
    console.log('DashboardApiIntegration successfully initialized.');
    return true;
  }

  /**
   * Load initial dashboard data
   */
  async loadDashboardData() {
    console.log('[DashboardApiIntegration] loadDashboardData: Starting to load dashboard data.');
    try {
      console.log('Loading dashboard data...');

      // First load test suites
      await this.loadTestSuites();

      // Load recent test runs (using cached data when available)
      let recentRuns = [];
      try {
        if (window.initialRecentRuns && Array.isArray(window.initialRecentRuns)) {
          console.log('[DashboardApiIntegration] loadDashboardData: Using window.initialRecentRuns.', window.initialRecentRuns);
          console.log('Using cached recent runs from login');
          this.recentRunsCache = window.initialRecentRuns;
          console.log('[DashboardApiIntegration] loadDashboardData: recentRunsCache populated from window.initialRecentRuns:', JSON.parse(JSON.stringify(this.recentRunsCache)));
          window.initialRecentRuns = null;
        } else {
          console.log('[DashboardApiIntegration] loadDashboardData: window.initialRecentRuns not found, fetching from API.');
          console.log('Fetching recent runs from API...');
          console.log('[DashboardApiIntegration] loadDashboardData: Calling apiService.getRecentRuns().');
          const response = await this.apiService.getRecentRuns({ limit: 20 });
          console.log('[DashboardApiIntegration] loadDashboardData: Received recentRunsResponse from API:', JSON.parse(JSON.stringify(response)));
          if (Array.isArray(response)) {
            recentRuns = response;
          } else if (response && response.success && Array.isArray(response.data)) {
            recentRuns = response.data;
          }
        }
      } catch (error) {
        console.error('Error loading recent runs:', error);
      }

      this.recentRunsCache = recentRuns;
      console.log('[DashboardApiIntegration] loadDashboardData: recentRunsCache populated from API response:', JSON.parse(JSON.stringify(this.recentRunsCache)));
      if (recentRuns.length > 0) {
        this.highestRecentTsnId = window.highestTsnId || recentRuns[0].tsn_id;
      }

      // Update all dashboard widgets from the cached runs
      this.updateDashboardCountersFromRecentRuns(this.recentRunsCache);
      this.renderRecentTests(this.recentRunsCache);
      await this.renderActiveTests();

      // Start polling for new runs
      this.startRecentRunsPolling();
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      this.showError('Failed to load dashboard data. Please check your connection and credentials.');
    }
  }

  /**
   * Load test suites
   */
  async loadTestSuites() {
    try {
      console.log('Loading test suites...');
      const response = await this.apiService.getTestSuites();
      console.log('Test suites response:', response);

      // Extract suites from the response - only handle the new refactored API response format
      const suites = response.success && Array.isArray(response.data) ? response.data : [];

      this.testSuites = suites;
      this.renderTestSuites();
      return suites;
    } catch (error) {
      console.error('Error getting test suites:', error);
      this.showError('Failed to load test suites.');
      return [];
    }
  }

  /**
   * Render test suites in the UI
   */
  renderTestSuites() {
    console.log('Rendering test suites:', this.testSuites);

    if (!this.testSuites || this.testSuites.length === 0) {
      console.log('No test suites to render');
      return;
    }

    // Get the container element
    const container = document.getElementById('test-suites-container');
    if (!container) {
      console.warn('Test suites container not found');
      return;
    }

    // Clear existing content
    container.innerHTML = '';

    // Create cards for each test suite
    this.testSuites.forEach(suite => {
      // Create card
      const card = document.createElement('div');
      card.className = 'ms-Card test-suite-card';

      // Set card content
      card.innerHTML = `
        <div class="ms-Card-header">
          <h3 class="ms-Card-title">${suite.name || `Suite ${suite.ts_id}`}</h3>
        </div>
        <div class="ms-Card-content">
          <p>${suite.comments || 'No description available'}</p>
          <div class="ms-Card-details">
            <span class="ms-Card-detail">ID: ${suite.ts_id}</span>
            <span class="ms-Card-detail">Status: ${suite.status || 'Unknown'}</span>
            <span class="ms-Card-detail">Owner: ${suite.uid || 'Unknown'}</span>
          </div>
        </div>
        <div class="ms-Card-actions">
          <button class="ms-Button ms-Button--primary run-suite-btn" data-suite-id="${suite.ts_id}" data-suite-name="${suite.name || `Suite ${suite.ts_id}`}">
            <span class="ms-Button-label">Run Suite</span>
          </button>
        </div>
      `;

      // Add to container
      container.appendChild(card);

      // Add event listener to run button
      const runButton = card.querySelector('.run-suite-btn');
      if (runButton) {
        runButton.addEventListener('click', () => {
          const suiteId = runButton.getAttribute('data-suite-id');
          const suiteName = runButton.getAttribute('data-suite-name');
          this.runTestSuite(suiteId, { name: suiteName });
        });
      }
    });
  }

  // renderAvailableTestCasesOptimized method removed (Custom Test Suite Builder feature)

  /**
   * Render recent test runs - DEPRECATED
   * @param {Array} recentTests - List of recent test runs
   */
  renderRecentTests(recentTests) {
    // This function is now a stub since the Recent Test Executions section has been removed from the dashboard UI
    // Keeping the function to maintain API compatibility, but it does nothing
    console.debug('renderRecentTests called but is deprecated - Recent Test Executions UI has been removed');
  }

  /**
   * Updates the active tests panel with new content
   * @param {string} content - HTML content to display
   */
  updateActiveTests(content) {
    const activeTestsContainer = document.getElementById('active-tests-container');

    if (activeTestsContainer) {
      // Find or create the message container
      let messageContainer = activeTestsContainer.querySelector('.ms-empty-message');

      if (!messageContainer) {
        messageContainer = document.createElement('div');
        messageContainer.className = 'ms-empty-message';
        activeTestsContainer.appendChild(messageContainer);
      }

      // Update the content
      messageContainer.innerHTML = content;
      console.log('Updated active tests panel with content:', content);
    } else {
      console.warn('Active tests container not found in DOM');
    }
  }

  /**
   * Increments a dashboard counter by the specified amount
   * @param {string} counterType - The counter to increment ('total-tests', 'running-tests', etc.)
   * @param {number} amount - Amount to increment by (default: 1)
   */
  incrementCounter(counterType, amount = 1) {
    const counterElement = document.getElementById(counterType);
    if (counterElement) {
      const currentValue = parseInt(counterElement.textContent, 10) || 0;
      counterElement.textContent = (currentValue + amount).toString();
    }
  }

  /**
   * Refreshes the list of recent test runs
   */
  async refreshRecentRuns() {
    await this.pollRecentRuns();
    return this.recentRunsCache;
  }

  /**
   * Update the recent runs table with data - DEPRECATED
   * @param {Array} recentRuns - The recent runs data
   */
  updateRecentRunsTable(recentRuns = []) {
    // This function is now a stub since the Recent Test Executions section has been removed from the dashboard UI
    // Keeping the function to maintain API compatibility, but it does nothing
    console.debug('updateRecentRunsTable called but is deprecated - Recent Test Executions UI has been removed');
  }

  /**
   * Update counters and active tests using the cached recent runs
   */
  updateFromRecentRuns() {
    this.updateDashboardCountersFromRecentRuns(this.recentRunsCache);
    // renderRecentTests call removed as Recent Test Executions section was removed from UI
    this.renderActiveTests();
  }

  /**
   * Poll server for new runs since the last known tsn_id
   * After refactor, this is the single source of truth for active tests
   */
  async pollRecentRuns() {
    if (!this.highestRecentTsnId) return;

    // Set flag to prevent overlapping requests
    this.isPollingInProgress = true;

    // Generate a unique ID for this request to track it
    const currentRequestId = ++this.pollingRequestId;
    console.log(`Starting poll request #${currentRequestId}`);

    try {
      // First get only new runs since last poll
      // Store request ID at start to detect stale responses
      const requestIdAtStart = currentRequestId;

      // Use 'highestRecentTsnId - 1' to ensure we receive status updates for the most recent test
    // This ensures we get updates when a test completes without waiting for the full refresh
    const sinceIdForPolling = Math.max(1, this.highestRecentTsnId - 1); // Ensure we don't go below 1
    console.log(`Using since_id=${sinceIdForPolling} for incremental polling (highestRecentTsnId=${this.highestRecentTsnId})`); 
    const response = await this.apiService.getRecentRuns({ since_id: sinceIdForPolling });

      // Check if this response is for the most recent request
      if (requestIdAtStart !== this.pollingRequestId) {
        console.log(`Discarding stale response from request #${requestIdAtStart} as newer request #${this.pollingRequestId} has been made`);
        return;
      }

      let newRuns = [];
      if (Array.isArray(response)) {
        newRuns = response;
      } else if (response && response.success && Array.isArray(response.data)) {
        newRuns = response.data;
      }

      if (newRuns.length > 0) {
        // Update cache with new runs
        const existingIds = new Set(this.recentRunsCache.map(r => r.tsn_id));
        newRuns.forEach(run => {
          if (!existingIds.has(run.tsn_id)) {
            this.recentRunsCache.unshift(run);
          }
        });

        // Keep cache from growing too large
        if (this.recentRunsCache.length > 50) {
          this.recentRunsCache = this.recentRunsCache.slice(0, 50);
        }

        // Update highest known ID for future polls
        this.highestRecentTsnId = newRuns[0].tsn_id;

        // Process new runs to update active tests
        this.updateActiveTestsFromRecentRuns(newRuns);

        // Update dashboard with new data
        this.updateFromRecentRuns();
      }

      // Periodically get all recent runs to catch any missed updates
      // This is important to keep active tests in sync with server state
      if (this.fullRefreshCounter === undefined) {
        this.fullRefreshCounter = 0;
      }

      this.fullRefreshCounter++;
      if (this.fullRefreshCounter >= 6) { // Every ~60 seconds (assuming 10s polling interval)
        this.fullRefreshCounter = 0;
        console.log('Performing full recent runs refresh');

        // Store the current request ID before full refresh to detect stale responses
        const fullRefreshRequestId = this.pollingRequestId;
        console.log(`Starting full refresh as part of request #${fullRefreshRequestId}`);

        const fullResponse = await this.apiService.getRecentRuns();

        // Check if this response is for the most recent request
        if (fullRefreshRequestId !== this.pollingRequestId) {
          console.log(`Discarding stale full refresh response from request #${fullRefreshRequestId} as newer request #${this.pollingRequestId} has been made`);
          return;
        }

        let allRuns = [];

        if (Array.isArray(fullResponse)) {
          allRuns = fullResponse;
        } else if (fullResponse && fullResponse.success && Array.isArray(fullResponse.data)) {
          allRuns = fullResponse.data;
        }

        if (allRuns.length > 0) {
          console.log(`Full refresh received ${allRuns.length} runs, updating recentRunsCache`);
          // Replace the entire cache with the full data set
          this.recentRunsCache = allRuns;

          // Update highest known ID for future polls
          if (allRuns[0] && allRuns[0].tsn_id) {
            this.highestRecentTsnId = allRuns[0].tsn_id;
          }

          // Update active tests with complete data
          this.updateActiveTestsFromRecentRuns(allRuns);

          // Update dashboard with new data
          this.updateFromRecentRuns();
        }
      }
    } catch (error) {
      console.error(`Error polling recent runs (request #${currentRequestId}):`, error);
    } finally {
      // Only reset the polling flag if this is still the latest request
      // This prevents race conditions where an older request completes after a newer one
      if (currentRequestId === this.pollingRequestId) {
        console.log(`Completed poll request #${currentRequestId}, releasing lock`);
        this.isPollingInProgress = false;
      } else {
        console.log(`Completed stale poll request #${currentRequestId}, not releasing lock as newer request #${this.pollingRequestId} is in progress`);
      }
    }
  }

  /**
   * Start periodic polling for recent runs
   */
  startRecentRunsPolling() {
    if (this.recentRunsPollingInterval) {
      clearInterval(this.recentRunsPollingInterval);
    }
    
    // Initialize polling state trackers
    this.isPollingInProgress = false;
    this.pollingRequestId = 0;
    
    this.recentRunsPollingInterval = setInterval(() => {
      // Only start a new poll if no previous polling is in progress
      if (!this.isPollingInProgress) {
        this.pollRecentRuns();
      } else {
        console.log('Skipping poll because previous request is still in progress');
      }
    }, 10000);
    
    // Perform an initial poll immediately
    this.pollRecentRuns();
  }
  
  /**
   * DEPRECATED: No longer used after refactor. Kept for backward compatibility.
   * All test status polling now happens via pollRecentRuns and updateActiveTestsFromRecentRuns
   * 
   * Do not add new calls to this function.
   */
  updateTestStatuses() {
    console.warn('updateTestStatuses is deprecated and no longer performs any action');
    // No-op - This function is kept for backward compatibility but does nothing
    // All test status tracking is now handled by pollRecentRuns
  }

  /**
   * Attach event listeners to "View Details" buttons
   */
  attachViewDetailsHandlers() {
    document.querySelectorAll('.view-details').forEach(button => {
      button.addEventListener('click', (event) => {
        const tsnId = event.currentTarget.getAttribute('data-tsn-id');
        if (tsnId) {
          this.viewTestDetails(tsnId);
        }
      });
    });
  }
  
  /**
   * Determines if a test session should be considered active based on its status
   * @param {Object} run - Test run data
   * @returns {boolean} - Whether the test is active
   */
  isTestSessionActive(run) {
    if (!run) return false;

    // Fix: Tests with an end time should remain active during grace period
    // Don't rely on potentially stale status field for this determination
    if (run.end_time || run.end_ts) {
      // Keep ALL completed tests active during grace period
      // The completion detection logic will determine the correct status
      // Grace period cleanup happens in renderActiveTests
      console.log(`Test ${run.tsn_id} has end_time, keeping active for grace period (status: ${run.status})`);
      return true;
    }
    
    // Check status if available
    if (run.status) {
      const status = run.status.toLowerCase();
      
      // Tests that are completed, canceled or have errors are not active
      if (['completed', 'cancelled', 'canceled', 'error'].includes(status)) {
        return false;
      }
      
      // Any other status (running, pending, etc.) is considered active
      return true;
    }
    
    // If we have a start time but no end time, consider it active
    if (run.start_time || run.start_ts) {
      return true;
    }
    
    // Default to active if we can't determine status
    // This ensures we don't lose track of tests
    return true;
  }

  /**
   * Update the active tests map using data from recent runs
   * This is the single source of truth for test status after refactoring
   * @param {Array} recentRuns - Recent runs data from API
   */
  updateActiveTestsFromRecentRuns(recentRuns) {
    if (!recentRuns || !Array.isArray(recentRuns) || recentRuns.length === 0) {
      return;
    }
    
    let needsUIRefresh = false;
    let needsDashboardUpdate = false;
    
    // Process each recent run
    recentRuns.forEach(run => {
      if (!run || !run.tsn_id) return;
      
      const tsnId = String(run.tsn_id);
      const existingTest = this.activeTests.get(tsnId);
      
      // Skip any test that has already been marked as completed AND has passed its grace period
      if (this.completedTestIds.has(tsnId)) {
        console.log(`Skipping test ${tsnId} as it was previously marked as completed`); 
        return;
      }
      
      // Determine if this run should be in active tests
      const isActive = this.isTestSessionActive(run);
      
      // Special handling for completed tests: allow them to be processed during grace period
      const isCompleted = run.end_time && run.status && ['passed', 'failed', 'completed', 'stopped'].includes(run.status.toLowerCase());
      if (isCompleted && !isActive) {
        // For completed tests, we still want to process them to ensure they appear in the UI during grace period
        console.log(`Processing completed test ${tsnId} for grace period display`);
      }
      
      if (isActive || isCompleted) {
        // Create or update the active test entry
        const updatedTest = existingTest || {};
        const oldStatus = existingTest ? existingTest.status : null;
        
        // Update test data with information from recent run
        updatedTest.tsn_id = tsnId;
        updatedTest.tc_id = run.tc_id;
        updatedTest.ts_id = run.ts_id;
        updatedTest.name = run.test_name || run.name || (run.tc_id ? `Test Case ${run.tc_id}` : `Test Suite ${run.ts_id}`);
        updatedTest.type = run.type || (run.tc_id ? 'Test Case' : (run.ts_id ? 'Test Suite' : 'Unknown'));
        updatedTest.startTime = run.start_ts || run.start_time || updatedTest.startTime || new Date();
        
        // Determine status based on recent run data
        if (run.end_ts || run.end_time) {
          updatedTest.endTime = run.end_ts || run.end_time;

          // Debug: Log available data for completion detection
          console.log(`Test ${tsnId} completion data:`, {
            passed_cases: run.passed_cases,
            failed_cases: run.failed_cases,
            passed: run.passed,
            failed: run.failed,
            error: run.error,
            status: run.status
          });

          // Determine final status based on passed/failed counts
          // Fix: Enhanced completion detection logic
          if (run.failed_cases > 0 || run.failed > 0) {
            updatedTest.status = 'failed';
            updatedTest.outcome = 'failed';
            console.log(`Test ${tsnId} - Detected as FAILED: failed_cases=${run.failed_cases}, failed=${run.failed}`);
          } else if (run.passed_cases > 0 || run.passed > 0) {
            updatedTest.status = 'passed';
            updatedTest.outcome = 'passed';
            console.log(`Test ${tsnId} - Detected as PASSED: passed_cases=${run.passed_cases}, passed=${run.passed}`);
          } else {
            // Fix: Check error field for result summary before defaulting to 'completed'
            if (run.error && typeof run.error === 'string' && run.error.trim() !== '') {
              const errorMatch = run.error.match(/^(\d+):(\d+)\/(\d+)$/);
              if (errorMatch) {
                const errorFailed = parseInt(errorMatch[1], 10);
                const errorPassed = parseInt(errorMatch[2], 10);
                console.log(`Test ${tsnId} - Found error field summary: ${run.error} (${errorFailed} failed, ${errorPassed} passed)`);

                if (errorFailed > 0) {
                  updatedTest.status = 'failed';
                  updatedTest.outcome = 'failed';
                } else if (errorPassed > 0) {
                  updatedTest.status = 'passed';
                  updatedTest.outcome = 'passed';
                } else {
                  updatedTest.status = 'completed';
                }
              } else {
                console.log(`Test ${tsnId} - Error field present but not in expected format: "${run.error}"`);
                updatedTest.status = 'completed';
              }
            } else {
              // No passed/failed counts and no error field summary
              // This might be a timing issue - test results not written to DB yet
              console.log(`Test ${tsnId} - No pass/fail data available (passed_cases=${run.passed_cases}, failed_cases=${run.failed_cases}, error="${run.error}")`);

              // Check if this is a very recent completion (within last 30 seconds)
              const rawEndTime = run.end_ts || run.end_time;
              console.log(`Test ${tsnId} - Raw end time value: "${rawEndTime}" (type: ${typeof rawEndTime})`);

              // Fix: Handle timezone parsing issue
              // Database stores local time, but there's a system timezone offset issue
              // Use a simpler approach: add timezone offset manually
              let endTime;
              if (typeof rawEndTime === 'string' && rawEndTime.match(/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/)) {
                // Format: "2025-07-28 10:03:42" - database stores local time
                // Create Date object and add timezone offset to correct the 3-hour difference
                endTime = new Date(rawEndTime.replace(' ', 'T'));

                // Add 3 hours (10800000 ms) to correct the timezone offset issue
                const correctedEndTime = new Date(endTime.getTime() + (3 * 60 * 60 * 1000));
                endTime = correctedEndTime;

                console.log(`Test ${tsnId} - Timezone fix applied: "${rawEndTime}" -> corrected for 3h offset -> ${endTime.toISOString()}`);
              } else {
                endTime = new Date(rawEndTime);
                console.log(`Test ${tsnId} - No timezone fix needed for format: "${rawEndTime}"`);
              }

              const now = new Date();
              console.log(`Test ${tsnId} - Parsed end time: ${endTime.toISOString()}, Current time: ${now.toISOString()}`);
              console.log(`Test ${tsnId} - Parsed end time (local): ${endTime.toString()}, Current time (local): ${now.toString()}`);
              console.log(`Test ${tsnId} - End time timestamp: ${endTime.getTime()}, Current time timestamp: ${now.getTime()}`);

              const timeSinceCompletion = (now.getTime() - endTime.getTime()) / 1000; // seconds

              if (timeSinceCompletion < 30) {
                // Recent completion - keep as running to allow time for test results to be written
                console.log(`Test ${tsnId} - Recent completion (${timeSinceCompletion.toFixed(1)}s ago), keeping as running to allow test results to be written`);
                updatedTest.status = 'running';
                updatedTest.outcome = 'running';
              } else {
                // Older completion with no results - default to completed
                console.log(`Test ${tsnId} - Older completion (${timeSinceCompletion.toFixed(1)}s ago), defaulting to completed`);
                updatedTest.status = 'completed';
              }
            }
          }
          
          // Store the completion status but don't immediately mark as completed
          // Just update the test record with the completed status and end time
          // The grace period logic in renderActiveTests will handle removal after 15 seconds
          if (['passed', 'failed', 'completed'].includes(updatedTest.status)) {
            console.log(`Test ${tsnId} has completed with status: ${updatedTest.status} (will be visible for 15-second grace period)`);

            // Track this test as completed to prevent status reversion
            this.completedTests.add(tsnId);

            // Update the test in the activeTests map but don't add to completedTestIds yet
            this.activeTests.set(tsnId, updatedTest);
          }
        } else if (run.status) {
          // Use status from API if provided
          const status = run.status.toLowerCase();
          if (status === 'fail' || status === 'failed') {
            updatedTest.status = 'failed';
            // Don't mark as completed yet - let the grace period apply
            updatedTest.endTime = updatedTest.endTime || new Date().toISOString();
          } else if (status === 'pass' || status === 'passed') {
            updatedTest.status = 'passed';
            // Don't mark as completed yet - let the grace period apply
            updatedTest.endTime = updatedTest.endTime || new Date().toISOString();
          } else if (['completed', 'cancelled', 'canceled', 'error'].includes(status)) {
            updatedTest.status = status;
            // Don't mark as completed yet - let the grace period apply
            updatedTest.endTime = updatedTest.endTime || new Date().toISOString()
          } else {
            updatedTest.status = status;
          }
        } else {
          // Default to running if no status info
          updatedTest.status = 'running';
        }
        
        // Update test results data
        updatedTest.passed = run.passed_cases || run.passed || updatedTest.passed || 0;
        updatedTest.failed = run.failed_cases || run.failed || updatedTest.failed || 0;
        updatedTest.totalTests = run.total_cases || (updatedTest.passed + updatedTest.failed) || 1;
        updatedTest.progress = run.progress || updatedTest.progress || 0;
        updatedTest.user = run.uid || updatedTest.user;
        
        // Check if the status changed from running to completed
        const statusChanged = oldStatus !== updatedTest.status;
        const justCompleted = statusChanged && 
                             oldStatus === 'running' && 
                             ['passed', 'failed', 'completed'].includes(updatedTest.status);
        
        // Update UI and counters if needed
        if (statusChanged) {
          needsUIRefresh = true;
        }
        
        if (justCompleted) {
          needsDashboardUpdate = true;
          
          // Show notification for completed test suites with accurate results
          if (updatedTest.type === 'Test Suite' || updatedTest.ts_id) {
            const suiteName = updatedTest.name || `Suite ${updatedTest.ts_id || tsnId}`;
            const passedCount = updatedTest.passed || 0;
            const failedCount = updatedTest.failed || 0;
            
            let notifType = 'info';
            let finalStatusMessage = 'Completed';
            
            // Determine notification type and message based on counts
            if (failedCount > 0) {
              notifType = 'error';
              finalStatusMessage = 'Failed';
            } else if (passedCount > 0) {
              notifType = 'success';
              finalStatusMessage = 'Passed';
            }
            
            const title = `Suite Finished: ${suiteName}`;
            const message = `Status: ${finalStatusMessage}. Cases Passed: ${passedCount}, Cases Failed: ${failedCount}.`;
            
            if (typeof window.showNotification === 'function') {
              window.showNotification(title, message, notifType);
            } else {
              console.warn('window.showNotification is not defined. Cannot show suite completion notification.');
            }
            
            // Grace period handling is now managed by renderActiveTests filter logic
            // No need for setTimeout here as renderActiveTests will handle display duration
          }
        }
        
        // Update the active tests map
        this.activeTests.set(tsnId, updatedTest);
      } else if (existingTest && ['passed', 'failed', 'completed'].includes(existingTest.status)) {
        // Keep completed tests in the map for a grace period
        // They'll be cleaned up during renderActiveTests
      } else if (existingTest) {
        // Remove tests that are neither active nor recently completed
        console.log(`Removing inactive test ${tsnId} from active tests map`);
        this.activeTests.delete(tsnId);
        needsUIRefresh = true;
      }
    });
    
    // Update UI if needed
    if (needsUIRefresh) {
      this.renderActiveTests();
    }
    
    // Update dashboard counters if needed
    if (needsDashboardUpdate) {
      this.updateDashboardCounters();
    }
  }

  /**
   * View details for a specific test run
   * @param {string} tsnId - The test session ID to view
   */
  async viewTestDetails(tsnId) {
    try {
      // Check if tsnId is valid
      if (!tsnId || tsnId === 'undefined' || tsnId === 'null') {
        this.showError(`Cannot load report: Invalid test session ID (${tsnId}).`);
        return null;
      }

      const response = await this.apiService.getRequest(`/local/test-details/${tsnId}`);

      // Only handle the new refactored API response format
      const details = response.success && response.data ? response.data : {};

      // Create a dialog to display test details
      const dialog = document.createElement('div');
      dialog.className = 'ms-Dialog ms-Dialog--close';
      dialog.innerHTML = `
        <div class="ms-Dialog-title">Test Run Details - ${tsnId}</div>
        <div class="ms-Dialog-content">
          <div class="ms-Grid">
            <div class="ms-Grid-row">
              <div class="ms-Grid-col ms-sm6">
                <h3>Test Case: ${details.tc_id}</h3>
                <p>Status: <span class="ms-fontColor-${this.getStatusColor(details.status)}">${details.status}</span></p>
                <p>Started: ${new Date(details.start_ts).toLocaleString()}</p>
                <p>User: ${details.uid}</p>
              </div>
              <div class="ms-Grid-col ms-sm6">
                <h3>Results</h3>
                <pre class="ms-font-xs">${JSON.stringify(details.results || {}, null, 2)}</pre>
              </div>
            </div>
          </div>
        </div>
        <div class="ms-Dialog-actions">
          <button class="ms-Button ms-Dialog-action ms-Button--primary">
            <span class="ms-Button-label">Close</span>
          </button>
        </div>
      `;

      document.body.appendChild(dialog);

      // Initialize the dialog component
      const dialogComponent = new fabric.Dialog(dialog);
      dialogComponent.open();

      // Add event listener to close button
      dialog.querySelector('.ms-Dialog-action').addEventListener('click', () => {
        dialogComponent.close();
        setTimeout(() => {
          document.body.removeChild(dialog);
        }, 500);
      });

    } catch (error) {
      console.error(`Error viewing test details for ${tsnId}:`, error);
      this.notifications.error(
        `Failed to load test details: ${error.message}`,
        'Error',
        3000
      );
    }
  }

  /**
   * Get color class based on test status
   * @param {string} status - Test status
   * @returns {string} - Color class
   */
  getStatusColor(status) {
    const statusColors = {
      running: 'blue',
      passed: 'green',
      failed: 'red',
      error: 'redDark',
      pending: 'orange'
    };

    return statusColors[status.toLowerCase()] || 'neutralSecondary';
  }

  /**
   * Run a test case
   * @param {string|number} tcId - Test case ID
   * @param {Object} params - Additional parameters
   */
  async runTestCase(tcId, params = {}) {
    try {
      // Show loading indicator
      this.showLoading(`Running test case ${tcId}...`);
      
      // Update dashboard counters
      this.incrementCounter('running-tests');
      this.incrementCounter('total-tests');

      // Run the test case
      const response = await this.apiService.runTestCase(tcId, params);

      if (response && response.tsn_id) {
        // Ensure tsnId is always a string to prevent [object Object] issues
        const tsnId = this.ensureStringTsnId(response.tsn_id);
        
        // Add to active tests with consistent fields
        this.activeTests.set(tsnId, {
          status: 'running',
          type: 'case',
          id: tcId,
          tc_id: tcId, // Ensure tc_id is set for test case
          startTime: new Date(),
          name: params.name || response.name || `Test Case ${tcId}`,
          totalTests: 1, // Single test case
          user: this.apiService.credentials.uid || 'unknown'
        });

        // Show success message
        this.showSuccess(`Test case ${tcId} started successfully. Run ID: ${tsnId}`);

        // Update the UI
        this.renderActiveTests();
        
        // Start monitoring the test status if not already polling
        if (!this.statusPollingInterval) {
          this.startStatusPolling();
        }

        return tsnId;
      } else {
        throw new Error('Could not get test session ID');
      }
    } catch (error) {
      console.error('Error running test case:', error);
      this.showError(`Failed to run test case ${tcId}: ${error.message}`);
      
      // Decrement the counters since the test didn't start
      this.incrementCounter('running-tests', -1);
      this.incrementCounter('total-tests', -1);
      
      return null;
    } finally {
      this.hideLoading();
      
      // Trigger a quick poll for new runs shortly after starting a test
      setTimeout(() => this.pollRecentRuns(), 3000); // Poll after 3 seconds
    }
  }

  /**
   * Run a test suite
   * @param {number} tsId - Test suite ID
   * @param {Object} params - Additional parameters
   */
  async runTestSuite(tsId, params = {}) {
    try {
      // Show loading indicator
      this.showLoading(`Running test suite ${tsId}...`);

      // Run the test suite
      const tsnId = await this.apiService.runTestSuite(tsId, params);

      if (tsnId) {
        // Ensure tsnId is always a string to prevent [object Object] issues
        const normalizedTsnId = this.ensureStringTsnId(tsnId);
        
        // Add to active tests
        // Fix: Add user field to prevent filter issues
        this.activeTests.set(normalizedTsnId, {
          status: 'running',
          type: 'Test Suite',
          id: tsId,
          ts_id: tsId, // Ensure ts_id is set for test suite
          startTime: new Date(),
          name: params.name || `Test Suite ${tsId}`,
          user: this.apiService.credentials.uid || 'unknown'
        });

        // Show success message
        this.showSuccess(`Test suite ${tsId} started successfully. Run ID: ${normalizedTsnId}`);

        // Update the UI
        this.renderActiveTests();

        return normalizedTsnId;
      } else {
        throw new Error('Could not get test run ID');
      }
    } catch (error) {
      console.error('Error running test suite:', error);
      this.showError(`Failed to run test suite ${tsId}: ${error.message}`);
      return null;
    } finally {
      this.hideLoading();
    }
  }

  /**
   * Stop a running test
   * @param {number} tsnId - Test suite run ID
   */
  async stopTest(tsnId) {
    try {
      // Show loading indicator
      this.showLoading(`Stopping test run ${tsnId}...`);

      // Stop the test
      const success = await this.apiService.stopTest(tsnId);

      if (success) {
        // Update active test status
        const test = this.activeTests.get(tsnId);
        if (test) {
          test.status = 'stopped';
          this.activeTests.set(tsnId, test);
        }

        // Show success message
        this.showSuccess(`Test run ${tsnId} stopped successfully.`);

        // Update the UI
        this.renderActiveTests();

        return true;
      } else {
        throw new Error('Failed to stop test');
      }
    } catch (error) {
      console.error('Error stopping test:', error);
      this.showError(`Failed to stop test run ${tsnId}: ${error.message}`);
      return false;
    } finally {
      this.hideLoading();
    }
  }

  /**
   * View test report
   * @param {number|string} tsnId - Test suite run ID
   */
  async viewTestReport(tsnId) {
    try {
      // Check if tsnId is valid
      if (!tsnId || tsnId === 'undefined' || tsnId === 'null') {
        this.showError(`Cannot load report: Invalid test session ID (${tsnId}).`);
        return null;
      }

      // Make sure tsnId is treated as a string (to avoid type issues)
      const sessionId = String(tsnId).trim();

      // Show loading indicator
      this.showLoading(`Loading report for test run ${sessionId}...`);

      // Log the attempt to get the report
      console.log(`Attempting to fetch report for session ${sessionId}`);

      try {
        // Get the report summary with timeout handling
        const reportPromise = this.apiService.getReportSummary(sessionId);

        // Set a timeout to handle hanging requests
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('Report request timed out')), 15000);
        });

        // Use Promise.race to handle timeouts
        const response = await Promise.race([reportPromise, timeoutPromise]);

        // Only handle the new refactored API response format
        const report = response.success && response.data ? response.data : {};

        // If report was successfully retrieved, show it
        if (report) {
          console.log(`Report data retrieved for session ${sessionId}:`, report);
          this.showReport(sessionId, report);
          return report;
        } else {
          throw new Error('No report data received');
        }
      } catch (reportError) {
        console.error(`Error fetching report for ${sessionId}:`, reportError);

        // Redirect to the reports page with the test ID parameter instead of API URL
        const reportsPageUrl = `/reports/index.html?tsn_id=${sessionId}`;

        console.log(`Redirecting to reports page: ${reportsPageUrl}`);
        window.open(reportsPageUrl, '_blank');

        this.showWarning(`Report viewer had an error, opening reports page in new tab...`);
        return null;
      }
    } catch (error) {
      console.error(`Error viewing report for ${tsnId}:`, error);
      this.showError(`Failed to load report for test run ${tsnId}: ${error.message}`);
      return null;
    } finally {
      this.hideLoading();
    }
  }

  /**
   * Rerun failed tests from a specific test run
   * @param {number} tsnId - Original test suite run ID
   * @param {Object} params - Additional parameters
   * @returns {Promise<number>} - New test suite run ID
   */
  async rerunFailedTests(tsnId, params = {}) {
    try {
      // Show loading indicator
      this.showLoading(`Preparing to rerun failed tests from run ${tsnId}...`);

      // Rerun the failed tests
      const newTsnId = await this.apiService.rerunFailedTests(tsnId, params);

      if (newTsnId) {
        // Ensure newTsnId is always a string to prevent [object Object] issues
        const normalizedTsnId = this.ensureStringTsnId(newTsnId);
        
        // Add to active tests
        this.activeTests.set(normalizedTsnId, {
          status: 'running',
          type: 'rerun',
          originalId: tsnId,
          startTime: new Date(),
          name: params.name || `Rerun of Failed Tests (${tsnId})`
        });

        // Show success message
        this.showSuccess(`Failed tests from run ${tsnId} started successfully. New Run ID: ${normalizedTsnId}`);

        // Update the UI
        this.renderActiveTests();

        return normalizedTsnId;
      } else {
        throw new Error('Could not get test run ID for rerun');
      }
    } catch (error) {
      console.error('Error rerunning failed tests:', error);
      this.showError(`Failed to rerun failed tests from ${tsnId}: ${error.message}`);
      return null;
    } finally {
      this.hideLoading();
    }
  }

  /**
   * Start polling for test status updates
   */
  startStatusPolling() {
    // Clear any existing interval
    if (this.statusPollingInterval) {
      clearInterval(this.statusPollingInterval);
    }

    // Start recent runs polling instead (no longer using updateTestStatuses)
    this.startRecentRunsPolling();
    
    console.log('Status polling moved to unified recent runs polling system');
  }

  /**
   * Update the status of all active tests
   */
  async updateTestStatuses() {
    // Prevent concurrent status updates
    if (this._isUpdatingStatuses) {
      console.log('Already updating test statuses, skipping this cycle');
      return;
    }
    
    this._isUpdatingStatuses = true;
    try {
      let needsUIRefresh = false;
      let needsDashboardUpdate = false;

      // Check each active test
      for (const [tsnId, test] of this.activeTests.entries()) {
        // Ensure tsnId is a string for API calls
        const normalizedTsnId = this.ensureStringTsnId(tsnId);
      // Only check tests that are still running or haven't been marked as completed
      if (test.status === 'running' || !['passed', 'failed', 'completed'].includes(test.status)) {
        try {
          const status = await this.apiService.getTestStatus(normalizedTsnId);
          console.log(`Status update for test ${normalizedTsnId}:`, status);

          // Update status
          if (status && status.success) {
            // Check if status has changed
            const oldStatus = test.status;

            // Determine test status based on API response
            if (status.status === 'fail' || status.status === 'failed') {
              test.status = 'failed';
              test.outcome = 'failed';
            } else if (status.status === 'pass' || status.status === 'passed') {
              test.status = 'passed';
              test.outcome = 'passed';
            } else if (status.end_time) {
              // If end_time exists but status is ambiguous, determine based on passed/failed counts
              if (status.failed > 0) {
                test.status = 'failed';
                test.outcome = 'failed';
              } else if (status.passed > 0) {
                test.status = 'passed';
                test.outcome = 'passed';
              } else {
                test.status = 'completed';
              }
            } else {
              test.status = status.status || 'running';
            }

            // If test just completed, update dashboard counters
            if (oldStatus === 'running' && ['completed', 'passed', 'failed'].includes(test.status)) {
              needsDashboardUpdate = true;
            }

            // Always update UI when we get a status update
            needsUIRefresh = true;

            // Update other test details if available
            if (status.progress !== undefined) test.progress = status.progress;
            if (status.message) test.message = status.message;
            if (status.start_time) test.startTime = new Date(status.start_time);
            if (status.end_time) test.endTime = new Date(status.end_time);
            if (status.passed !== undefined) test.passed = status.passed;
            if (status.failed !== undefined) test.failed = status.failed;

            // If test is completed, stop polling after a short delay
            if (test.endTime || ['passed', 'failed', 'completed'].includes(test.status)) {
              console.log(`Test ${normalizedTsnId} completed with status: ${test.status}`);

              // Show notification if it's a test suite that just completed
              if (test && test.type === 'Test Suite') {
                const suiteName = test.name || (test.id ? `Suite ${test.id}` : `Suite ${normalizedTsnId}`);
                
                // Use the 'status' object directly from the API response for the most accurate, up-to-date information
                const passedCount = status.passed || 0;
                const failedCount = status.failed || 0;

                let notifType = 'info';
                let finalStatusMessage = 'Completed'; // Default status message

                // Determine notification type and message based on counts
                if (failedCount > 0) {
                  notifType = 'error';
                  finalStatusMessage = 'Failed';
                } else if (passedCount > 0) {
                  notifType = 'success';
                  finalStatusMessage = 'Passed';
                }
                // If both are 0, it remains 'info' and 'Completed'
                
                const title = `Suite Finished: ${suiteName}`;
                const message = `Status: ${finalStatusMessage}. Cases Passed: ${passedCount}, Cases Failed: ${failedCount}.`;
                
                if (typeof window.showNotification === 'function') {
                  window.showNotification(title, message, notifType);
                } else {
                  console.warn('window.showNotification is not defined. Cannot show suite completion notification.');
                }
              }

              // Ensure the test status is properly set to a final state
              if (test.status === 'pending') {
                // Fix for tests showing pending after completion
                if (test.failed > 0) {
                  test.status = 'failed';
                } else if (test.passed > 0) {
                  test.status = 'passed';
                } else {
                  test.status = 'completed';
                }
              }

              // Make sure the end time is set
              if (!test.endTime) {
                test.endTime = new Date();
              }

              // Schedule test removal from active tests after showing result
              setTimeout(() => {
                if (this.activeTests.has(tsnId)) {
                  console.log(`Removing completed test ${normalizedTsnId} from active tests`);
                  this.activeTests.delete(tsnId);
                  this.renderActiveTests();
                }
              }, 10000); // Keep showing result for 10 seconds
            }
          }
        } catch (error) {
          console.error(`Error updating status for test ${normalizedTsnId}:`, error);
        }
      }
    }

      // If any test status has changed, re-render the entire active tests section
      if (needsUIRefresh) {
        this.renderActiveTests();
      }

      // If any test has completed, update the dashboard counters
      if (needsDashboardUpdate) {
        this.updateDashboardCounters();
      }
    } finally {
      this._isUpdatingStatuses = false;
    }
  }

  /**
   * Update dashboard counters based on active tests and recent runs
   */
  updateDashboardCounters() {
    try {
      // Get counters from DOM
      const totalCounter = document.getElementById('total-tests');
      const successfulCounter = document.getElementById('successful-tests');
      const failedCounter = document.getElementById('failed-tests');

      if (!totalCounter || !successfulCounter || !failedCounter) {
        console.warn('Dashboard counters not found in DOM');
        return;
      }

      // Get current values
      let totalCount = parseInt(totalCounter.textContent, 10) || 0;
      let successfulCount = parseInt(successfulCounter.textContent, 10) || 0;
      let failedCount = parseInt(failedCounter.textContent, 10) || 0;

      // Count completed tests from our active tests map
      const completedTests = Array.from(this.activeTests.entries())
        .filter(([_, test]) =>
          ['completed', 'passed', 'failed'].includes(test.status) &&
          !test.countedInDashboard
        )
        .map(([id, test]) => {
          // Mark as counted so we don't count it again
          test.countedInDashboard = true;
          return { id, ...test };
        });

      if (completedTests.length === 0) {
        console.log('No new completed tests to count');
        return;
      }

      console.log(`Found ${completedTests.length} new completed tests to count in dashboard`);

      // Increment total for each completed test
      totalCount += completedTests.length;

      // Count passed and failed tests
      const passedTests = completedTests.filter(
        test => test.status === 'passed' || test.outcome === 'passed'
      );

      const failedTests = completedTests.filter(
        test => test.status === 'failed' || test.outcome === 'failed'
      );

      console.log(`Passed tests: ${passedTests.length}, Failed tests: ${failedTests.length}`);

      // Increment counters
      successfulCount += passedTests.length;
      failedCount += failedTests.length;

      // Update DOM
      totalCounter.textContent = totalCount;
      successfulCounter.textContent = successfulCount;
      failedCounter.textContent = failedCount;

      console.log('Updated dashboard counters:', {
        total: totalCount,
        successful: successfulCount,
        failed: failedCount
      });
    } catch (error) {
      console.error('Error updating dashboard counters:', error);
    }
  }

  /**
   * Update dashboard counters based on recent runs data
   * @param {Array} recentRuns - Recent test runs data
   */
  updateDashboardCountersFromRecentRuns(recentRuns) {
    try {
      console.log('Updating dashboard counters from recent runs data');

      if (!recentRuns || !Array.isArray(recentRuns)) {
        console.warn('No valid recent runs data to update counters');
        return;
      }

      // Filter runs to only those from the current user session
      const currentUser = this.apiService.credentials.uid;
      const userRuns = currentUser ? recentRuns.filter(run => run.uid === currentUser) : recentRuns;

      console.log(`Found ${userRuns.length} runs for current user: ${currentUser}`);

      // Calculate totals from recent runs
      const totalRuns = userRuns.length;
      const passedRuns = userRuns.filter(run => run.status && run.status.toLowerCase() === 'passed').length;
      const failedRuns = userRuns.filter(run => run.status && run.status.toLowerCase() === 'failed').length;
      const runningRuns = userRuns.filter(run => run.status && run.status.toLowerCase() === 'running').length;

      // Get counter elements
      const totalCounter = document.getElementById('total-tests');
      const passedCounter = document.getElementById('successful-tests');
      const failedCounter = document.getElementById('failed-tests');
      const runningCounter = document.getElementById('running-tests');

      // Update counter displays if elements exist
      if (totalCounter) totalCounter.textContent = totalRuns;
      if (passedCounter) passedCounter.textContent = passedRuns;
      if (failedCounter) failedCounter.textContent = failedRuns;
      if (runningCounter) runningCounter.textContent = runningRuns;

      console.log(`Dashboard counters updated: Total=${totalRuns}, Passed=${passedRuns}, Failed=${failedRuns}, Running=${runningRuns}`);
    } catch (error) {
      console.error('Error updating dashboard counters:', error);
    }
  }

  /**
   * Apply filtering logic to active tests based on current filter and user
   * @param {Array} tests - Array of test objects to filter
   * @param {string} filter - Filter type ('all', 'mine', 'others')
   * @param {string} currentUser - Current user identifier
   * @returns {Array} Filtered array of tests
   */
  applyActiveTestsFilter(tests, filter, currentUser) {
    if (filter === 'all' || !currentUser) {
      console.log(`Applying 'all' filter, returning ${tests.length} tests`);
      return tests;
    }
    
    const currentUserEmail = currentUser.toLowerCase();
    const currentUsername = currentUserEmail.split('@')[0];
    
    const filteredTests = tests.filter(test => {
      // Get test user identifier
      const testUser = (test.uid || test.user || '').toLowerCase();
      const testUsername = testUser.split('@')[0];
      
      // Check if test belongs to current user
      const isMyTest = testUser === currentUserEmail || testUsername === currentUsername;
      
      // Apply filter logic
      // Fix: Properly handle all three filter types
      const shouldInclude = filter === 'mine' ? isMyTest :
                           filter === 'others' ? !isMyTest :
                           true; // 'all' shows everything
      
      console.log(`Test ${test.tsn_id || test.id}: user=${testUser}, filter=${filter}, isMyTest=${isMyTest}, include=${shouldInclude}`);
      return shouldInclude;
    });
    
    console.log(`Applied '${filter}' filter: ${filteredTests.length}/${tests.length} tests included`);
    return filteredTests;
  }

  /**
   * Render active test runs (grouped by tsn_id)
   */
  async renderActiveTests() {
    const container = document.getElementById('active-tests-container');
    if (!container) return;
    try {
      // Gather active tests from local map and cached recent runs
      let activeTestsRaw = [];
      if (this.activeTests.size > 0) {
        const now = new Date();
        activeTestsRaw = Array.from(this.activeTests.entries()).map(([key_tsn_id, test_value]) => {
          // Embed the map key (actual tsn_id) into the test object
          // also keep original_tsn_id_key for reliable deletion from map
          return { ...test_value, tsn_id_from_map_key: key_tsn_id, original_tsn_id_key: key_tsn_id };
        }).filter(test_with_key => {
          // If test is completed and it's been more than 15 seconds since it ended, filter it out
          // Ensure status check is case-insensitive and includes 'stopped'
          const completedStatuses = ['passed', 'failed', 'completed', 'stopped'];
          if (test_with_key.endTime && test_with_key.status && completedStatuses.includes(test_with_key.status.toLowerCase())) {
            const endTime = new Date(test_with_key.endTime);
            const timeSinceEnd = now - endTime;
            if (timeSinceEnd > 25000) {  // 25 seconds grace period
              // Grace period has expired, now we can fully remove the test
              console.log(`Grace period (25s) expired for test ${test_with_key.original_tsn_id_key}, removing from active tests, status: ${test_with_key.status}`);
              setTimeout(() => {
                // Remove from active tests map
                this.activeTests.delete(test_with_key.original_tsn_id_key);
                // Now add to completedTestIds to prevent re-adding in the future
                this.completedTestIds.add(test_with_key.original_tsn_id_key);
              }, 0);
              return false;
            }
          }
          return true;
        });
      }

      if (Array.isArray(this.recentRunsCache)) {
        const runningFromCache = this.recentRunsCache.filter(r => {
          // Include tests that are still active (no end_time) or explicitly running/queued
          const status = r.status ? r.status.toLowerCase() : '';
          return (!r.end_time && status !== 'passed' && status !== 'failed') || 
                 status === 'running' || status === 'queued';
        }).map(r => ({
          tsn_id: r.tsn_id,
          tc_id: r.tc_id,
          ts_id: r.ts_id,
          startTime: r.start_time,
          endTime: r.end_time,
          // Preserve original status exactly as returned by API
          status: r.status ? r.status.toLowerCase() : 'running',
          api_status: r.status, // Store original API status for reference
          name: r.test_name,
          type: r.type || (r.tc_id ? 'Test Case' : (r.ts_id ? 'Test Suite' : 'Unknown')),
          passed: r.passed_cases,
          failed: r.failed_cases,
          totalTests: r.total_cases,
          user: r.uid
        }));
        activeTestsRaw = activeTestsRaw.concat(runningFromCache);
      }

      // Smart data merging: Deduplicate and merge data BEFORE processing
      // This prevents incomplete database data from overwriting complete activeTests data
      const mergedTests = this.smartMergeTestData(activeTestsRaw);

      // Process each merged test to ensure it has all required fields
      let processedTests = mergedTests.map(test => this.processTestData(test));
      console.log(`Deduplicated ${activeTestsRaw.length} raw tests to ${processedTests.length} processed tests`);
      
      // First, identify all active tests (those that should have Stop buttons)
      const activeTests = processedTests.filter(test => {
        const status = test.status ? test.status.toLowerCase() : '';

        // CRITICAL FIX: Check if test is in activeTests map (most reliable indicator)
        const isInActiveTestsMap = this.activeTests.has(test.tsn_id);

        // For tests in activeTests map, they should have Stop buttons unless clearly completed
        if (isInActiveTestsMap) {
          return status !== 'passed' && status !== 'failed' && status !== 'stopped' && status !== 'completed';
        }

        // For tests not in activeTests map, use the original logic
        return (!test.endTime &&
          status !== 'passed' &&
          status !== 'failed' &&
          status !== 'stopped' &&
          status !== 'completed');
      });
      
      console.log(`Found ${activeTests.length} active tests that will always be shown with Stop buttons`);
      
      // Apply simplified filtering logic
      const currentUser = this.getCurrentUser();
      console.log('Current filter:', this.activeTestsFilter, 'Current user:', currentUser);
      
      // Apply filter to all processed tests
      let displayedTests = this.applyActiveTestsFilter(processedTests, this.activeTestsFilter, currentUser);

      // Group by tsn_id
      const grouped = {};
      displayedTests.forEach(test => {
        const key = test.tsn_id || test.id;
        if (!grouped[key]) grouped[key] = [];
        grouped[key].push(test);
      });

      // Render one card per tsn_id
      container.innerHTML = '';
      Object.entries(grouped).forEach(([tsn_id, tests]) => {
        // Sort tests to prioritize running/active ones for testInfo selection
        tests.sort((a, b) => {
          const aStatus = a.status ? a.status.toLowerCase() : '';
          const bStatus = b.status ? b.status.toLowerCase() : '';

          const aIsRunning = (aStatus === 'running' && !a.endTime);
          const bIsRunning = (bStatus === 'running' && !b.endTime);

          if (aIsRunning && !bIsRunning) return -1; // a (running) comes first
          if (!aIsRunning && bIsRunning) return 1;  // b (running) comes first
        if (aIsRunning && !bIsRunning) return -1; // a (running) comes first
        if (!aIsRunning && bIsRunning) return 1;  // b (running) comes first

        // If both are similarly running or not, prioritize one without an endTime (more active)
        if (!a.endTime && b.endTime) return -1;
        if (a.endTime && !b.endTime) return 1;

        // If statuses are very similar (e.g. both completed with endTime, or both running with no endTime)
        // you might add further tie-breakers, e.g., by latest startTime, or just keep order.
        // For now, if they are equally 'active' or 'inactive' by the above criteria, keep relative order.
        return 0;
      });

        // Aggregate status/progress
        const passed = tests.filter(t => t.status === 'PASSED' || t.status === 'passed' || t.outcome === 'P' || t.outcome === 'passed').length;
        const failed = tests.filter(t => t.status === 'FAILED' || t.status === 'failed' || t.outcome === 'F' || t.outcome === 'failed' || t.failed > 0).length;

        // Fix: Properly determine if test is actually running by checking both status and end time
        // A test is only running if it has no end time AND status indicates it's active
        const running = tests.filter(t => {
          const hasEndTime = t.endTime || t.end_time || t.end_ts;
          const status = (t.status || '').toLowerCase();

          // If test has an end time, it's not running regardless of status
          if (hasEndTime) return false;

          // If no end time, check if status indicates it's actually running
          return status === 'running' || status === 'queued' || status === 'pending' ||
                 (!status && !hasEndTime); // Default to running if no status and no end time
        }).length;
        const total = tests[0].totalTests || tests.length;
        const progress = total === 0 ? 0 : Math.round(((passed + failed) / total) * 100);

        // For test suites, consider failures specifically
        // Fix: Prioritize test data from activeTests map over recentRunsCache
        // Look for test with tsn_id_from_map_key (from activeTests) first
        const testFromActiveMap = tests.find(t => t.tsn_id_from_map_key);
        const testInfo = testFromActiveMap || tests[0]; // Use activeTests data if available, otherwise first test

        console.log(`Test ${tsn_id} - Using test data from: ${testFromActiveMap ? 'activeTests map' : 'recentRunsCache'}, status: ${testInfo.status}`);
        const isTestSuite = testInfo.type === 'Test Suite' || Boolean(testInfo.ts_id);

        // Check individual test metrics
        const hasFailedTests = isTestSuite && (
          testInfo.failed > 0 ||
          tests.some(t => t.failed > 0) ||
          testInfo.status === 'failed' ||
          testInfo.status === 'fail'
        );

        // Determine status for color coding and text based on actual API status
        let statusClass = 'pending';
        let statusText = 'Pending';

        // Use the original API status value if available, respecting its original case
        const apiStatus = testInfo.api_status || testInfo.status;
        
        // Determine if the test is still running or can be stopped
        // Fix: Properly check if test can be stopped based on actual completion status
        const hasEndTime = testInfo.endTime || testInfo.end_time || testInfo.end_ts;
        const status = (testInfo.status || '').toLowerCase();
        const apiStatusLower = (apiStatus || '').toLowerCase();

        // A test is active (can be stopped) ONLY if:
        // 1. It has no end time (not completed in database)
        // 2. AND its status indicates it's actually running/queued
        // 3. AND it's not explicitly marked as completed/failed/passed
        const isActive = !hasEndTime &&
                         (apiStatusLower === 'running' ||
                          apiStatusLower === 'queued' ||
                          apiStatusLower === 'pending' ||
                          (status === 'running' || status === 'queued' || status === 'pending') ||
                          // Default to active if no clear status but no end time
                          (!apiStatusLower && !status && !hasEndTime));

        // Fix: Determine status based on actual completion state first, then API status
        // Priority: 1. Database end_time/end_ts, 2. API status, 3. Calculated values

        if (hasEndTime) {
          // Test is completed - determine final status based on results
          // Fix: First check if we already have a determined status from the test object
          console.log(`Test ${tsn_id} status calculation - hasEndTime: true, testInfo.status: "${testInfo.status}", passed: ${passed}, failed: ${failed}`);

          // CRITICAL FIX: Check if test already has a final status from completion detection
          // The completion detection logic runs first and sets the correct status in activeTests
          const activeTestData = this.activeTests.get(tsn_id);
          const hasCompletionStatus = activeTestData && ['passed', 'failed', 'completed'].includes(activeTestData.status);

          // ENHANCED FIX: Also check for completion based on database data when activeTests is not available
          const hasPassFailData = (passed > 0 || failed > 0) && hasEndTime;
          const isCompletedFromDatabase = hasPassFailData && !activeTestData; // Test removed from activeTests but has completion data

          // CRITICAL FIX: Check if test was previously marked as completed in our tracking
          const wasMarkedCompleted = this.completedTests && this.completedTests.has(tsn_id);

          if (hasCompletionStatus) {
            // Use the status from completion detection (this is the correct final status)
            const completionStatus = activeTestData.status.toLowerCase();
            statusClass = completionStatus;
            statusText = completionStatus.charAt(0).toUpperCase() + completionStatus.slice(1);
            console.log(`Test ${tsn_id} - Using completion detection status: ${statusClass}`);
          } else if (isCompletedFromDatabase) {
            // Test was removed from activeTests but has completion data - determine final status
            if (failed > 0) {
              statusClass = 'failed';
              statusText = 'Failed';
            } else if (passed > 0) {
              statusClass = 'passed';
              statusText = 'Passed';
            } else {
              statusClass = 'completed';
              statusText = 'Completed';
            }
            console.log(`Test ${tsn_id} - Using database completion status: ${statusClass} (passed=${passed}, failed=${failed})`);
          } else if (wasMarkedCompleted && hasEndTime) {
            // Test was previously marked as completed, don't revert to running
            if (failed > 0) {
              statusClass = 'failed';
              statusText = 'Failed';
            } else if (passed > 0) {
              statusClass = 'passed';
              statusText = 'Passed';
            } else {
              statusClass = 'completed';
              statusText = 'Completed';
            }
            console.log(`Test ${tsn_id} - Test was previously completed, maintaining final status: ${statusClass} (passed=${passed}, failed=${failed})`);
          } else if (testInfo.status && testInfo.status.toLowerCase() === 'running') {
            statusClass = 'running';
            statusText = 'Running';
            console.log(`Test ${tsn_id} - Respecting timing-based running status (recent completion)`);
          } else if (testInfo.status && ['passed', 'failed'].includes(testInfo.status.toLowerCase())) {
            const storedStatus = testInfo.status.toLowerCase();
            statusClass = storedStatus;
            statusText = storedStatus.charAt(0).toUpperCase() + storedStatus.slice(1);
            console.log(`Test ${tsn_id} - Using stored test status: ${statusClass}`);
          } else {
            // Fallback to parsing error field and counting results
            let actualPassed = passed;
            let actualFailed = failed;

            // Parse error field if it contains result summary (format: "failed:passed/total")
            if (testInfo.error && typeof testInfo.error === 'string' && testInfo.error.trim() !== '') {
              const errorMatch = testInfo.error.match(/^(\d+):(\d+)\/(\d+)$/);
              if (errorMatch) {
                actualFailed = parseInt(errorMatch[1], 10);
                actualPassed = parseInt(errorMatch[2], 10);
                console.log(`Parsed test results from error field: ${actualFailed} failed, ${actualPassed} passed`);
              }
            }

            if (hasFailedTests || actualFailed > 0) {
              statusClass = 'failed';
              statusText = 'Failed';
            } else if (actualPassed > 0 && actualFailed === 0) {
              statusClass = 'passed';
              statusText = 'Passed';
            } else {
              statusClass = 'completed';
              statusText = 'Completed';
            }
            console.log(`Test ${tsn_id} - Fallback status calculation result: ${statusClass} (actualPassed: ${actualPassed}, actualFailed: ${actualFailed})`);
          }
        } else if (apiStatus) {
          // Test is still running - use API status
          const normalizedStatus = apiStatus.toLowerCase();

          if (normalizedStatus === 'running') {
            statusClass = 'running';
            statusText = testInfo.api_status || 'Running';
          } else if (normalizedStatus === 'queued') {
            statusClass = 'queued';
            statusText = testInfo.api_status || 'Queued';
          } else if (normalizedStatus === 'pending') {
            statusClass = 'pending';
            statusText = testInfo.api_status || 'Pending';
          } else {
            // For any other status value, use the API provided value
            statusClass = normalizedStatus;
            statusText = testInfo.api_status || apiStatus;
          }
        } else {
          // Fallback to calculated values if no API status is available
          if (running > 0) {
            statusClass = 'running';
            statusText = 'Running';
          } else if (hasFailedTests || failed > 0) {
            statusClass = 'failed';
            statusText = 'Failed';
          } else if (passed > 0 && failed === 0) {
            statusClass = 'passed';
            statusText = 'Passed';
          } else {
            statusClass = 'pending';
            statusText = 'Pending';
          }
        }

        console.log(`Test status for ${tsn_id}:`, {
          statusClass,
          statusText,
          hasFailedTests,
          passed,
          failed,
          running,
          isActive,
          testInfo
        });

        // Determine the correct name to display
        // Priority: suiteName > testName > name > "Test Suite/Case" + ID > fallback with tsn_id
        const testName = testInfo.suiteName ||
                      testInfo.testName ||
                      testInfo.name ||
                      (testInfo.ts_id ? `Test Suite ${testInfo.ts_id}` :
                      (testInfo.tc_id ? `Test Case ${testInfo.tc_id}` :
                      `Test Run ${testInfo.tsn_id || tsn_id || 'Unknown'}`));

        // Get the correct ID for test suite/case
        // For test suites, use ts_id (e.g. 332)
        // For test cases, use tc_id
        const testId = testInfo.ts_id || testInfo.tc_id || 'Unknown';

        // For debugging - especially for QUEUED tests
        console.log('Test info for rendering:', {
          testName,
          testId, // This is the test suite ID or test case ID (e.g. 332 for test suite, 3180 for test case)
          tsn_id, // This is the test session ID (run ID)
          isTestSuite,
          status: testInfo.status,
          api_status: testInfo.api_status,
          suiteName: testInfo.suiteName,
          name: testInfo.name,
          testInfo
        });
        
        // Get the actual TSN ID (run ID) from the test info
        // This could be coming from different fields depending on the test type and API response
        const actualTsnId = testInfo.run_id || testInfo.tsn_id || tsn_id;
        
        // Get user info - prioritize display name or extract name from email
        let userName = testInfo.user_display || 'You';
        
        // If we only have email (no display name), extract username part or use first part of email
        if (userName === 'You' && testInfo.user) {
          const email = testInfo.user;
          if (email.includes('@')) {
            // Extract username from email (before @)
            userName = email.split('@')[0];
            
            // Format username to be more readable (e.g., "john.doe" -> "John Doe")
            if (userName.includes('.')) {
              userName = userName.split('.')
                .map(part => part.charAt(0).toUpperCase() + part.slice(1))
                .join(' ');
            } else {
              userName = userName.charAt(0).toUpperCase() + userName.slice(1);
            }
          } else {
            userName = testInfo.user; // Use as is if not an email
          }
        }

        // Format timestamp
        const startTime = testInfo.startTime || testInfo.start_time || new Date().toISOString();
        const formattedTime = new Date(startTime).toLocaleString();

        // Ensure we have a valid tsn_id (string format) for card identification
        const sessionId = String(tsn_id);

        // Ensure testName is never null/undefined for display
        const safeTestName = testName || `Test Run ${sessionId}`;

        // Compose card
        const card = document.createElement('div');
        card.className = `test-card ${statusClass}`;
        card.setAttribute('data-tsn-id', sessionId);

        // Log the card data for debugging
        console.log(`Rendering card for session ${sessionId}:`, {
          testName,
          testId,
          sessionId,
          status: statusClass,
          statusText,
          progress,
          isTestSuite
        });

        card.innerHTML = `
          <div class="test-card-header">
            <div class="status-indicator ${statusClass}"></div>
            <h3 class="test-name">${safeTestName}</h3>
          </div>
          <div class="test-card-meta">
            <div class="meta-info">
              ID: ${testId}-${actualTsnId} • Started ${formattedTime}
            </div>
          </div>
          <div class="test-card-body">
            <div class="test-stats">
              <span class="user-indicator">${userName}</span>
              <div class="status-badge ${statusClass}">${statusText}</div>
            </div>
            
            ${statusClass !== 'running' && statusClass !== 'queued' && statusClass !== 'pending' ? `
              <!-- Only show progress bar and metrics for completed tests -->
              <div class="progress-container">
                <div class="progress-bar ${statusClass}" style="width: ${progress}%">
                  ${progress > 15 ? `${progress}%` : ''}
                </div>
              </div>
              
              <!-- Test metrics display for completed tests -->
              <div class="test-metrics-container">
                <div class="test-metrics">
                  <div class="metric-group">
                    <span class="metric-label">Passed:</span>
                    <span class="metric passed">${passed} <i class="fas fa-check"></i></span>
                  </div>
                  <div class="metric-group">
                    <span class="metric-label">Failed:</span>
                    <span class="metric failed">${failed} <i class="fas fa-times"></i></span>
                  </div>
                  <div class="metric-group">
                    <span class="metric-label">Total:</span>
                    <span class="metric total">${total}</span>
                  </div>
                </div>
              </div>
            ` : ''}
          </div>
          <div class="test-card-footer">
            ${isActive ? `
              <button class="btn btn-sm btn-danger stop-button" onclick="window.dashboardApiIntegration.stopTest('${sessionId}')">
                Stop Test
              </button>
            ` : ''}
          </div>
        `;
        container.appendChild(card);
      });
      if (Object.keys(grouped).length === 0) {
        container.innerHTML = '<div class="ms-empty-message">No active tests running</div>';
      }
    } catch (error) {
      console.error('Error rendering active tests:', error);
      container.innerHTML = '<div class="ms-empty-message error">Error loading active tests</div>';
    }
  }

  /**
   * Show a test report
   * @param {number} tsnId - Test suite run ID
   * @param {Object} report - Report data
   */
  showReport(tsnId, report) {
    // This is a placeholder - implement actual UI in your dashboard
    console.log(`Showing report for test run ${tsnId}:`, report);

    // Example implementation:
    const modal = document.getElementById('report-modal');
    if (modal) {
      const content = modal.querySelector('#report-content');
      if (content) {
        // Format report as HTML
        let html;

        if (typeof report === 'string') {
          // If report is HTML, display it directly
          html = report;
        } else {
          // If report is an object, format it
          html = `
            <h2>Test Run Report: ${tsnId}</h2>
            <div class="report-summary">
              ${this.formatReportSummary(report)}
            </div>
          `;
        }

        content.innerHTML = html;
      }

      modal.style.display = 'block';
    }
  }

  /**
   * Format report summary as HTML
   * @param {Object} report - Report data
   * @returns {string} HTML representation of the report
   */
  formatReportSummary(report) {
    if (!report) return '<p>No report data available</p>';

    // Only handle the new refactored API response format
    if (report.testCases) {
      const testCases = report.testCases || [];
      const status = report.status || 'unknown';
      const duration = report.duration || 'N/A';

      const passedCount = testCases.filter(tc => tc.status === 'passed').length;
      const failedCount = testCases.filter(tc => tc.status === 'failed' || tc.status === 'error').length;
      const skippedCount = testCases.filter(tc => tc.status === 'skipped').length;

      return `
        <div class="report-stats">
          <div class="report-stat">
            <span class="stat-label">Status:</span>
            <span class="stat-value ${status === 'completed' ? 'text-success' : 'text-danger'}">${status}</span>
          </div>
          <div class="report-stat">
            <span class="stat-label">Total Tests:</span>
            <span class="stat-value">${testCases.length}</span>
          </div>
          <div class="report-stat">
            <span class="stat-label">Passed:</span>
            <span class="stat-value text-success">${passedCount}</span>
          </div>
          <div class="report-stat">
            <span class="stat-label">Failed:</span>
            <span class="stat-value text-danger">${failedCount}</span>
          </div>
          <div class="report-stat">
            <span class="stat-label">Skipped:</span>
            <span class="stat-value text-warning">${skippedCount}</span>
          </div>
          <div class="report-stat">
            <span class="stat-label">Duration:</span>
            <span class="stat-value">${duration ? `${duration}s` : 'N/A'}</span>
          </div>
        </div>

        <h3>Test Cases</h3>
        <table class="ms-Table">
          <thead>
            <tr>
              <th>ID</th>
              <th>Name</th>
              <th>Status</th>
              <th>Duration</th>
            </tr>
          </thead>
          <tbody>
            ${testCases.map(tc => `
              <tr class="${tc.status === 'failed' || tc.status === 'error' ? 'ms-bgColor-sharedRedLight10' : tc.status === 'skipped' ? 'ms-bgColor-sharedYellowLight10' : ''}">
                <td>${tc.id}</td>
                <td>${tc.name}</td>
                <td>${tc.status}</td>
                <td>${tc.duration}s</td>
              </tr>
              ${tc.errorMessage ? `
              <tr class="ms-bgColor-sharedRedLight10">
                <td colspan="4" class="error-message">
                  <strong>Error:</strong> ${tc.errorMessage}
                </td>
              </tr>
              ` : ''}
            `).join('')}
          </tbody>
        </table>
      `;
    } else if (report.summary) {
      // Summary format
      const summary = report.summary || {};
      const duration = report.duration || 'N/A';

      return `
        <div class="report-stats">
          <div class="report-stat">
            <span class="stat-label">Total Tests:</span>
            <span class="stat-value">${summary.totalTests || 0}</span>
          </div>
          <div class="report-stat">
            <span class="stat-label">Passed:</span>
            <span class="stat-value text-success">${summary.passed || 0}</span>
          </div>
          <div class="report-stat">
            <span class="stat-label">Failed:</span>
            <span class="stat-value text-danger">${summary.failed || 0}</span>
          </div>
          <div class="report-stat">
            <span class="stat-label">Skipped:</span>
            <span class="stat-value text-warning">${summary.skipped || 0}</span>
          </div>
          <div class="report-stat">
            <span class="stat-label">Duration:</span>
            <span class="stat-value">${duration || 'N/A'}</span>
          </div>
        </div>
      `;
    } else {
      // Generic object
      return `
        <pre>${JSON.stringify(report, null, 2)}</pre>
      `;
    }
  }

  /**
   * Format a timestamp for display
   * @param {string|Date} timestamp - The timestamp to format
   * @returns {string} - The formatted timestamp
   */
  formatTime(timestamp) {
    if (!timestamp) return 'N/A';

    try {
      const date = timestamp instanceof Date ? timestamp : new Date(timestamp);

      // Check if date is valid
      if (isNaN(date.getTime())) {
        return 'Invalid date';
      }

      // Format options
      const options = {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      };

      return date.toLocaleDateString(undefined, options);
    } catch (error) {
      console.error('Error formatting timestamp:', error);
      return 'Error';
    }
  }

  /**
   * Show loading indicator
   * @param {string} message - Loading message
   */
  showLoading(message) {
    // This is a placeholder - implement actual UI in your dashboard
    console.log('Loading:', message);

    // Example implementation:
    const loading = document.getElementById('loading-indicator');
    if (loading) {
      const messageElement = loading.querySelector('.loading-message');
      if (messageElement) {
        messageElement.textContent = message;
      }
      loading.style.display = 'block';
    }
  }

  /**
   * Hide loading indicator
   */
  hideLoading() {
    // This is a placeholder - implement actual UI in your dashboard
    console.log('Loading complete');

    // Example implementation:
    const loading = document.getElementById('loading-indicator');
    if (loading) {
      loading.style.display = 'none';
    }
  }

  /**
   * Show success message
   * @param {string} message - Success message
   */
  showSuccess(message) {
    // This is a placeholder - implement actual UI in your dashboard
    console.log('Success:', message);

    // Example implementation:
    this.showNotification('success', message);
  }

  /**
   * Show error message
   * @param {string} message - Error message
   */
  showError(message) {
    // This is a placeholder - implement actual UI in your dashboard
    console.error('Error:', message);

    // Example implementation:
    this.showNotification('error', message);
  }

  /**
   * Show notification
   * @param {string} type - Notification type (success, error, info)
   * @param {string} message - Notification message
   */
  showNotification(type, message) {
    // Example implementation:
    const container = document.getElementById('notification-container');
    if (container) {
      const notification = document.createElement('div');
      notification.className = `notification ${type}`;
      notification.innerHTML = `
        <span class="notification-message">${message}</span>
        <button class="notification-close">&times;</button>
      `;

      // Add close button handler
      const closeButton = notification.querySelector('.notification-close');
      if (closeButton) {
        closeButton.addEventListener('click', () => {
          notification.remove();
        });
      }

      // Auto-remove after 5 seconds
      setTimeout(() => {
        notification.classList.add('fade-out');
        setTimeout(() => {
          notification.remove();
        }, 500);
      }, 5000);

      container.appendChild(notification);
    }
  }

  /**
   * Get the current user's email or username
   * @returns {string} - Current user's email or username
   */
  getCurrentUser() {
    // Try to get the current user from various sources with proper error handling
    let currentUser = '';
    
    // Method 1: Try to get from API Service credentials (most reliable source)
    if (this.apiService && this.apiService.credentials && this.apiService.credentials.uid) {
      currentUser = this.apiService.credentials.uid;
      console.log('[getCurrentUser] Got user from apiService credentials:', currentUser);
      return currentUser; // Return immediately if we have the user from credentials
    }

    // Method 2: Try window.apiService as backup
    if (!currentUser && window.apiService && window.apiService.credentials && window.apiService.credentials.uid) {
      currentUser = window.apiService.credentials.uid;
      console.log('[getCurrentUser] Got user from window.apiService:', currentUser);
      return currentUser;
    }
    
    // Method 3: Try sessionStorage and localStorage with security error handling
    try {
      // Try accessing sessionStorage first (less restrictive than localStorage in some environments)
      // Use standard 'smarttest_uid' key for consistency with other modules
      currentUser = sessionStorage.getItem('smarttest_uid') || '';
      if (currentUser) {
        console.log('[getCurrentUser] Got user from sessionStorage (smarttest_uid):', currentUser);
        return currentUser;
      }

      // Fallback to legacy 'currentUser' key for backward compatibility
      currentUser = sessionStorage.getItem('currentUser') || '';
      if (currentUser) {
        console.log('[getCurrentUser] Got user from sessionStorage (legacy currentUser):', currentUser);
        return currentUser;
      }
    } catch (e) {
      console.log('Cannot access sessionStorage due to security restrictions');
    }

    // If sessionStorage didn't work, try localStorage with both keys
    if (!currentUser) {
      try {
        // Try standard key first
        currentUser = localStorage.getItem('smarttest_uid') || '';
        if (currentUser) {
          console.log('[getCurrentUser] Got user from localStorage (smarttest_uid):', currentUser);
          return currentUser;
        }

        // Fallback to legacy key
        currentUser = localStorage.getItem('currentUser') || '';
        if (currentUser) {
          console.log('[getCurrentUser] Got user from localStorage (legacy currentUser):', currentUser);
          return currentUser;
        }
      } catch (e) {
        console.log('Cannot access localStorage due to security restrictions');
      }
    }
    
    // Method 4: Try to get from cookies
    if (!currentUser) {
      try {
        const cookies = document.cookie.split(';');
        for (const cookie of cookies) {
          if (cookie.trim().startsWith('currentUser=')) {
            currentUser = cookie.trim().substring('currentUser='.length);
            console.log('[getCurrentUser] Got user from cookies:', currentUser);
            return currentUser;
          }
        }
      } catch (e) {
        console.log('Error accessing cookies:', e.message);
      }
    }
    
    // Method 5: Check if there's a global user variable set by the parent document
    if (!currentUser && window.parent) {
      try {
        if (window.parent.currentUser) {
          currentUser = window.parent.currentUser;
          console.log('[getCurrentUser] Got user from parent frame:', currentUser);
          return currentUser;
        }
      } catch (e) {
        console.log('Cannot access parent frame due to security restrictions');
      }
    }
    
    console.log('[getCurrentUser] Could not determine current user from any source');
    return currentUser;
  }

  /**
   * Set up event listeners for active tests filter buttons
   */
  setupActiveTestsFilterEventListeners() {
  console.log('[API Integration] Attempting to set up active tests filter event listeners...');
    // Find all filter buttons
    const filterButtons = document.querySelectorAll('.active-tests-filter-btn');
    if (!filterButtons || filterButtons.length === 0) {
      console.warn('[API Integration] No active tests filter buttons found in the DOM at the time of setup.');
      return;
    }
    console.log(`[API Integration] Found ${filterButtons.length} active tests filter buttons.`);
    
    // Set the 'all' button as active by default
    filterButtons.forEach(button => {
      const filterType = button.getAttribute('data-filter');
      if (filterType === 'all') {
        button.classList.add('active', 'ms-Button--primary');
        button.classList.remove('ms-Button--default');
        console.log('[API Integration] Set "All Tests" button as active by default');
      } else {
        button.classList.remove('active', 'ms-Button--primary');
        button.classList.add('ms-Button--default');
      }
    });
    
    // Add click event listeners to each button
    filterButtons.forEach(button => {
      button.addEventListener('click', event => {
        // Get the filter type from the data-filter attribute
        const filterType = event.currentTarget.getAttribute('data-filter');
        if (!filterType) {
          console.warn('[API Integration] Filter button clicked, but no data-filter attribute found.');
          return;
        }
        
        // Update the active filter
        this.activeTestsFilter = filterType;
        console.log(`[API Integration] Active tests filter set to: ${this.activeTestsFilter}`);
        
        // Update button active state
        filterButtons.forEach(btn => btn.classList.remove('active'));
        event.currentTarget.classList.add('active');
        
        // Re-render active tests with the new filter
        console.log('[API Integration] Re-rendering active tests due to filter change.');
        this.renderActiveTests();
      });
    });
    console.log('[API Integration] Successfully set up event listeners for active tests filter buttons.');
  }
  
  /**
   * Set up event listeners for predefined test suites and custom suite builder
   */
  setupEventListeners() {
    console.log('Setting up event listeners for dashboard components');

    // Event listeners for predefined suites (Example)
    const predefinedSuitesContainer = document.getElementById('predefined-suites-container');
    if (predefinedSuitesContainer) {
      // Clone the node to remove old listeners and get a fresh one
      const newContainer = predefinedSuitesContainer.cloneNode(true); // Deep clone
      if (predefinedSuitesContainer.parentNode) {
        predefinedSuitesContainer.parentNode.replaceChild(newContainer, predefinedSuitesContainer);
      }
      const freshPredefinedSuitesContainer = newContainer; // Use the new, clean container

      freshPredefinedSuitesContainer.addEventListener('click', (event) => {
        const button = event.target.closest('[data-suite-id]');
        if (button) {
          const suiteId = button.dataset.suiteId;
          const suiteName = button.dataset.suiteName || `Suite ${suiteId}`;
          if (suiteId) {
            console.log(`Run button clicked for suite: ${suiteName} (${suiteId})`);
            this.runTestSuite(suiteId, { name: suiteName });
          }
        }
      });
    }

    // Add listener for the custom test case run button
    // Custom test case input has been removed from the dashboard

    // Add listeners for Active Tests filter buttons
    const filterButtonContainer = document.querySelector('.active-tests-filter');
    if (filterButtonContainer) {
      const filterButtons = filterButtonContainer.querySelectorAll('.active-tests-filter-btn');
      filterButtons.forEach(button => {
        button.addEventListener('click', (event) => {
          const clickedButton = event.currentTarget;
          const filterValue = clickedButton.dataset.filter;

          if (filterValue && this.activeTestsFilter !== filterValue) { // Only update if filter changed
            this.activeTestsFilter = filterValue;
            console.log(`Active tests filter set to: ${this.activeTestsFilter}`);

            // Update active class and styling on buttons
            filterButtons.forEach(btn => {
              btn.classList.remove('active', 'ms-Button--primary');
              btn.classList.add('ms-Button--default');
            });
            
            clickedButton.classList.add('active', 'ms-Button--primary');
            clickedButton.classList.remove('ms-Button--default');

            this.renderActiveTests(); // Re-render the active tests list
          }
        });
      });
      console.log('Event listeners attached to active tests filter buttons.');
    } else {
      console.warn('Active tests filter button container (.active-tests-filter) not found.');
    }
  }

  /**
   * Create and run a custom test suite from selected test cases
   */
  async createAndRunCustomSuite() {
    // Get custom suite name
    const suiteName = document.getElementById('custom-suite-name').value.trim() || 'Custom Test Suite';

    // Get selected test cases
    const selectedCheckboxes = document.querySelectorAll('#available-testcases input[type="checkbox"]:checked');

    if (!selectedCheckboxes || selectedCheckboxes.length === 0) {
      this.showError('Please select at least one test case for your custom suite.');
      return;
    }

    const selectedTestCases = Array.from(selectedCheckboxes).map(checkbox => parseInt(checkbox.value, 10));

    try {
      // Show loading indicator
      this.showLoading(`Creating and running ${suiteName}...`);

      // Create a dynamic test suite request
      const params = {
        name: suiteName,
        description: `Custom test suite created on ${new Date().toLocaleString()}`,
        test_cases: selectedTestCases.map(tcId => ({ id: tcId }))
      };

      // Run the dynamic test suite
      const tsnId = await this.apiService.runDynamicTestSuite(params);

      if (tsnId) {
        // Add to active tests
        this.activeTests.set(tsnId, {
          status: 'running',
          type: 'custom',
          startTime: new Date(),
          name: suiteName,
          progress: 0,
          messages: []
        });

        // Show success message
        this.showSuccess(`${suiteName} started successfully. Run ID: ${tsnId}`);

        // Update the UI
        this.renderActiveTests();

        // Close the custom suite modal
        const modal = document.getElementById('custom-suite-modal');
        if (modal) {
          modal.style.display = 'none';
        }

        // Clear selected checkboxes for next time
        selectedCheckboxes.forEach(checkbox => {
          checkbox.checked = false;
        });

        // Clear suite name for next time
        const nameInput = document.getElementById('custom-suite-name');
        if (nameInput) {
          nameInput.value = '';
        }
      } else {
        this.showError('Failed to start custom test suite. Please try again.');
      }
    } catch (error) {
      console.error('Error running custom suite:', error);
      this.showError(`Failed to run custom test suite: ${error.message}`);
    } finally {
      // Hide loading indicator
      this.hideLoading();
    }
  }

  /**
   * Run a predefined test suite from the API
   * @param {string} suiteId - The identifier for the predefined suite
   * @returns {Promise<string|null>} The test session ID if successful
   */
  async runPredefinedSuite(suiteId) {
    try {
      // Check if this test suite is already running to prevent duplicate runs
      if (this._runningTestSuites.has(suiteId)) {
        console.warn(`Test suite ${suiteId} is already running. Preventing duplicate run.`);
        this.showWarning(`This test suite is already running. Please wait for it to complete.`);
        return null;
      }
      
      // Mark this suite as running to prevent duplicate clicks
      this._runningTestSuites.add(suiteId);
      
      // Show loading indicator
      this.showLoading(`Starting test suite ${suiteId}...`);

      // Get the suite details from the API
      const response = await this.apiService.getRequest(`/local/test-suites/${suiteId}`);

      if (!response.success || !response.data) {
        throw new Error(`Failed to get details for test suite ${suiteId}`);
      }

      const suite = response.data;

      // Create test session
      const sessionId = await this.apiService.createTestSession({
        test_type: suite.name || `Suite ${suiteId}`,
        environment: 'qa02',
        description: suite.comments || `Test suite ${suiteId}`
      });

      // Run the test suite - explicitly set run_once flag to prevent multiple runs
      const tsnId = await this.apiService.runTestSuite(suiteId, {
        name: suite.name || `Suite ${suiteId}`,
        session_id: sessionId,
        run_once: true, // Flag to ensure the suite is run only once
        is_full_suite: true // Indicate this is the full suite, not individual test cases
      });

      if (tsnId) {
        console.log(`Successfully started suite ${suite.name} with session ID ${tsnId}`, {
          suite_id: suiteId,
          session_id: tsnId
        });

        // Add to active tests with proper property names
        this.activeTests.set(tsnId, {
          tsn_id: tsnId,
          ts_id: suiteId, // Store the suite ID properly
          tc_id: suite.testCases[0],
          testName: suite.name, // Use consistent naming for the test name
          suiteName: suite.name, // Add explicit suite name for predefined suites
          status: 'running',
          sessionId: sessionId,
          startTime: new Date(),
          user: this.apiService.credentials.uid || 'You',
          progress: 0,
          messages: [],
          // Store test cases array for progress tracking
          testCases: suite.testCases,
          totalTests: suite.testCases.length
        });

        // Show success message
        this.showSuccess(`${suite.name} started successfully. Run ID: ${tsnId}`);

        // Update the UI
        this.renderActiveTests();

        // Start monitoring
        this.monitorTestSession(sessionId, tsnId);

        return tsnId;
      } else {
        throw new Error('Could not get test run ID');
      }
    } catch (error) {
      console.error('Error running predefined suite:', error);
      this.showError(`Failed to run ${suiteId} suite: ${error.message}`);
      return null;
    } finally {
      this.hideLoading();
    }
  }

  /**
   * Monitor test session status
   * @param {string} sessionId - Session ID
   * @param {number} tsnId - Test suite run ID
   */
  async monitorTestSession(sessionId, tsnId) {
    const pollInterval = 5000; // 5 seconds
    const maxAttempts = 120; // 10 minutes maximum

    let attempts = 0;
    const poll = async () => {
      try {
        // Get session status
        const sessionStatus = await this.apiService.getTestSessionStatus(sessionId);

        // Get query history
        const queryHistory = await this.apiService.getInputQueryHistory(sessionId);

        // Update active test
        const test = this.activeTests.get(tsnId);
        if (test) {
          test.status = sessionStatus.status;
          test.progress = sessionStatus.progress;
          test.messages = sessionStatus.messages;
          test.queryHistory = queryHistory;

          // Update UI
          this.updateTestStatusInUI(tsnId, test);

          // Check if test is complete
          if (sessionStatus.status === 'completed' || sessionStatus.status === 'failed') {
            // Get final report
            const report = await this.apiService.getTestSessionReport(sessionId);
            this.showReport(tsnId, report);
            
            // Get the test suite ID to remove from running list
            const suiteId = test.ts_id;
            if (suiteId && this._runningTestSuites && this._runningTestSuites.has(suiteId)) {
              console.log(`Removing suite ${suiteId} from running suites tracking`);
              this._runningTestSuites.delete(suiteId);
            }

            // Remove from active tests after delay
            setTimeout(() => {
              if (this.activeTests.has(tsnId)) {
                console.log(`Removing completed test ${tsnId} from active tests`);
                this.activeTests.delete(tsnId);
                this.renderActiveTests();
              }
            }, 5000);

            return;
          }
        }

        // Continue polling if not complete
        attempts++;
        if (attempts < maxAttempts) {
          setTimeout(poll, pollInterval);
        } else {
          this.showError('Test session monitoring timed out');
        }
      } catch (error) {
        console.error('Error monitoring test session:', error);
        this.showError('Failed to monitor test session');
      }
    };

    poll();
  }

  /**
   * Update test status in UI
   * @param {number} tsnId - Test suite run ID
   * @param {Object} test - Test status object
   */
  updateTestStatusInUI(tsnId, test) {
    const testCard = document.querySelector(`[data-tsn-id="${tsnId}"]`);
    if (!testCard) return;

    // Update status class
    testCard.className = `test-card ${test.status}`;

    // Update status text
    const statusText = testCard.querySelector('.status-text');
    if (statusText) {
      statusText.textContent = test.status.charAt(0).toUpperCase() + test.status.slice(1);
    }

    // Update progress
    const progressBar = testCard.querySelector('.progress-bar');
    if (progressBar) {
      progressBar.style.width = `${test.progress}%`;
      progressBar.textContent = `${test.progress}%`;
    }

    // Update messages
    const messagesContainer = testCard.querySelector('.messages');
    if (messagesContainer) {
      messagesContainer.innerHTML = test.messages.map(msg =>
        `<div class="message ${msg.type}">${msg.text}</div>`
      ).join('');
    }

    // Update query history
    const queryHistory = testCard.querySelector('.query-history');
    if (queryHistory && test.queryHistory) {
      queryHistory.innerHTML = `
        <h4>Recent Queries</h4>
        <ul>
          ${test.queryHistory.map(query => `
            <li>
              <span class="query-status ${query.status}">${query.status}</span>
              <span class="query-time">${new Date(query.timestamp).toLocaleTimeString()}</span>
              <div class="query-text">${query.query}</div>
            </li>
          `).join('')}
        </ul>
      `;
    }

    // Update buttons
    const stopButton = testCard.querySelector('.stop-button');
    if (stopButton) {
      stopButton.style.display = test.status === 'running' ? 'block' : 'none';
    }
  }

  /**
   * Show the login form modal
   */
  showLoginForm() {
    console.log('Showing login form modal');
    window.showLoginModal();
  }

  /**
   * Handle successful login
   * @param {string} username - The logged in username
   */
  handleSuccessfulLogin(username) {
    console.log(`Handling successful login for user: ${username}`);
    window.handleSuccessfulLogin(username);
  }

  /**
   * Handle logout
   */
  handleLogout() {
    console.log('Handling logout');
    window.handleLogout();
  }

  /**
   * Smart merge test data from multiple sources, prioritizing data completeness
   * @param {Array} rawTests - Array of test data from activeTests and recentRunsCache
   * @returns {Array} - Array of merged test data with duplicates removed
   */
  smartMergeTestData(rawTests) {
    const testMap = new Map();

    // Group tests by tsn_id
    rawTests.forEach(test => {
      const tsnId = test.tsn_id || test.tsn_id_from_map_key;
      if (!tsnId) return;

      if (!testMap.has(tsnId)) {
        testMap.set(tsnId, []);
      }
      testMap.get(tsnId).push(test);
    });

    const mergedTests = [];

    // Merge data for each unique tsn_id
    testMap.forEach((tests, tsnId) => {
      if (tests.length === 1) {
        // No duplicates, use as-is
        mergedTests.push(tests[0]);
        return;
      }

      // Multiple sources for same test - smart merge
      const activeTestData = tests.find(t => t.tsn_id_from_map_key || t.original_tsn_id_key);
      const cacheData = tests.find(t => !t.tsn_id_from_map_key && !t.original_tsn_id_key);

      let mergedTest;

      if (activeTestData && cacheData) {
        // CRITICAL FIX: Make activeTests data completely authoritative for new tests
        // Apply timezone fix to age calculation
        let activeTestAge = Infinity;
        if (activeTestData.startTime) {
          let startTime;
          if (typeof activeTestData.startTime === 'string' && activeTestData.startTime.match(/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/)) {
            // Apply timezone fix for database format
            startTime = new Date(activeTestData.startTime.replace(' ', 'T'));
            startTime = new Date(startTime.getTime() + (3 * 60 * 60 * 1000)); // Add 3 hours offset
          } else {
            startTime = new Date(activeTestData.startTime);
          }
          activeTestAge = (Date.now() - startTime.getTime()) / 1000;
        }
        const isVeryFreshTest = activeTestAge < 60; // Increased from 30 to 60 seconds

        if (isVeryFreshTest) {
          // For very fresh tests, activeTests data is completely authoritative
          // Only allow database to update completion timing data
          mergedTest = { ...activeTestData };

          // Only update these specific fields from database if they indicate completion
          if (cacheData.end_ts || cacheData.endTime) {
            mergedTest.end_ts = cacheData.end_ts;
            mergedTest.endTime = cacheData.endTime;
          }
          if (cacheData.passed_cases !== undefined && cacheData.passed_cases !== null) {
            mergedTest.passed_cases = cacheData.passed_cases;
          }
          if (cacheData.failed_cases !== undefined && cacheData.failed_cases !== null) {
            mergedTest.failed_cases = cacheData.failed_cases;
          }
          if (cacheData.total_cases !== undefined && cacheData.total_cases !== null) {
            mergedTest.total_cases = cacheData.total_cases;
          }

          console.log(`Test ${tsnId} is very fresh (${activeTestAge.toFixed(1)}s old), activeTests data is completely authoritative`);
        } else {
          // For older tests, use the original smart merge logic
          mergedTest = { ...activeTestData };

          // Only update fields from cache if they provide more complete information
          // Preserve test identification data from activeTests
          const preserveFromActive = ['name', 'type', 'ts_id', 'tc_id', 'id', 'testId'];
          const updateFromCache = ['status', 'api_status', 'startTime', 'endTime', 'start_time', 'end_time', 'end_ts', 'passed_cases', 'failed_cases', 'total_cases', 'passed', 'failed', 'totalTests'];

          // Only update from cache if the cache value is more complete
          updateFromCache.forEach(field => {
            const cacheValue = cacheData[field];
            const activeValue = activeTestData[field];

            // Special handling for critical fields - never overwrite with incomplete data
            if (field === 'ts_id' || field === 'tc_id') {
              // Only update if active doesn't have it and cache has a valid value
              if (!activeValue && cacheValue && cacheValue !== 'undefined' && cacheValue !== null) {
                mergedTest[field] = cacheValue;
              }
              return;
            }

            // For other fields, update if cache has a value and active doesn't, or if cache value is more specific
            if (cacheValue !== undefined && cacheValue !== null && cacheValue !== 'Unknown' && cacheValue !== 'NULL' && cacheValue !== 'undefined') {
              if (activeValue === undefined || activeValue === null || activeValue === 'Unknown' || activeValue === 'NULL') {
                mergedTest[field] = cacheValue;
              }
            }
          });

          console.log(`Test ${tsnId} is older (${activeTestAge.toFixed(1)}s old), using standard smart merge`);
        }
      } else {
        // Use whichever source we have
        mergedTest = activeTestData || cacheData;
      }

      mergedTests.push(mergedTest);
    });

    console.log(`Smart merge: ${rawTests.length} raw tests merged to ${mergedTests.length} unique tests`);
    return mergedTests;
  }

  /**
   * Process the data for a test and ensure all required fields are set
   * @param {Object} testData - The test data to process
   * @returns {Object} - The processed test data
   */
  processTestData(testData) {
    // Create a copy to avoid modifying the original
    const test = { ...testData };

    // Set default values for missing fields to ensure consistent UI display
    if (!test.status) {
      // First check if the API provided a specific status in different fields
      const apiStatus = test.api_status || test.api_state || test.state;
      if (apiStatus) {
        // Convert to lowercase and normalize some common statuses
        const normalizedStatus = apiStatus.toLowerCase();
        test.status = normalizedStatus;
      } else {
        // If we have no API status, base on other indicators
        // Fix: Also check end_ts field from database
        if (test.end_time || test.endTime || test.end_ts) {
          if (test.failed_cases > 0 || test.failed > 0) {
            test.status = 'failed';
          } else if (test.passed_cases > 0 || test.passed > 0) {
            test.status = 'passed';
          } else {
            test.status = 'completed';
          }
        } else {
          // Default for running tests with no status
          test.status = 'running';
        }
      }
    }

    // Fix: Normalize endTime field from various possible sources
    if (!test.endTime) {
      if (test.end_time) {
        test.endTime = test.end_time;
      } else if (test.end_ts) {
        test.endTime = test.end_ts;
      }
    }

    if (!test.startTime && test.creation_time) test.startTime = new Date(test.creation_time);
    if (!test.user && test.initiator_user) test.user = test.initiator_user;
    
    // Fix for issue #3: Determine if this is current user
    const currentUser = this.apiService?.credentials?.uid;
    if (currentUser && (test.initiator_user === currentUser || test.user === currentUser)) {
      test.is_current_user = true;
    }
    
    // Generate appropriate name for test case or test suite if missing or invalid
    if (!test.name || test.name === 'NULL' || test.name === 'Unknown Test') {
      if (test.tc_id) {
        test.name = `Test Case ${test.tc_id}`;
      } else if (test.ts_id) {
        test.name = `Test Suite ${test.ts_id}`;
      } else if (test.tsn_id) {
        test.name = `Test Run ${test.tsn_id}`;  // Fixed: was test.tsnId
      } else {
        test.name = 'Unknown Test';
      }
    }
    
    // Check if this is a test suite by examining its name and type
    const isSuiteName = test.name && (
      test.name.toLowerCase().includes('suite') ||
      test.name.toLowerCase().includes('smoke test') ||
      test.name.toLowerCase().includes('heartbeat') ||
      test.name.toLowerCase().includes('pe2.1')
    );

    // If it looks like a test suite but doesn't have ts_id, set it
    if ((isSuiteName || test.type === 'Test Suite' || test.type === 'suite') && !test.ts_id) {
      // If it's a PE2.1 Smoke Test, use 332 as the ts_id
      if (test.name && test.name.includes('PE2.1 Smoke Test')) {
        test.ts_id = 332;
      }
      // If it's a PE2.1 Heartbeat Test, use 333 as the ts_id
      else if (test.name && test.name.includes('PE2.1 Heartbeat')) {
        test.ts_id = 333;
      }
      // Otherwise use a generic fallback
      else {
        test.ts_id = 332;
      }
    }

    // Make sure we have a valid tsn_id (Test Session Number)
    // Prioritize the tsn_id embedded from the activeTests map key if available
    if (test.tsn_id_from_map_key) {
      test.tsn_id = test.tsn_id_from_map_key;
    } else if (test.run_id) {
      // If run_id is available, it's the canonical TSN ID
      test.tsn_id = test.run_id;
    } else if (test.session_id) {
      // Some APIs use session_id as the run identifier
      test.tsn_id = test.session_id;
    } else if (!test.tsn_id && test.id) { // Fallback if no other source and test.tsn_id is not already set
      // This case handles scenarios where tsn_id might be missing from API responses
      // or if it's a very old test data format.
      test.tsn_id = test.id;
    }
    // Note: If test.tsn_id was already correctly populated (e.g. from API getActiveTests response, or tsn_id_from_map_key),
    // the subsequent conditions might not trigger, which is intended.  }
  
    // For test cases, the tc_id is the ID and session_id/run_id is the TSN ID
    // For test suites, the ts_id is the ID and session_id/run_id is the TSN ID
    if (test.tc_id && !test.test_id) {
      test.test_id = test.tc_id;
    } else if (test.ts_id && !test.test_id) {
      test.test_id = test.ts_id;
    }

    // Set type based on available identifiers and name, but preserve existing valid types
    if (!test.type || test.type === 'Unknown') {
      test.type = isSuiteName || test.ts_id ? 'Test Suite' : 'Test Case';
    } else if (test.type === 'case' || test.tc_id) {
      test.type = 'Test Case';
    } else if (test.type === 'suite' || test.ts_id) {
      test.type = 'Test Suite';
    }
    
    // Set totalTests if missing (mostly for test cases)
    if (!test.totalTests) {
      test.totalTests = test.type === 'Test Case' ? 1 : (test.testCases?.length || 1);
    }
    
    // Set progress to 0 if missing (for newly started tests)
    if (!test.progress) test.progress = 0;
    
    console.log(`Normalized test data for actual TSN_ID ${test.tsn_id} (original map key: ${test.original_tsn_id_key || 'N/A'}):`, test);
    return test;
  }

  /**
   * Format user email into a display name
   * @param {string} email - User email address
   * @returns {string} - Formatted display name
   */
  formatUserEmail(email) {
    if (!email) return 'Unknown';

    try {
      // Parse email in the format <first_name>.<last_name>@<domain>
      const namePart = email.split('@')[0];
      if (!namePart) return email;

      const nameParts = namePart.split('.');
      if (nameParts.length < 2) return email;

      // Capitalize first and last name
      const firstName = nameParts[0].charAt(0).toUpperCase() + nameParts[0].slice(1);
      const lastName = nameParts[1].charAt(0).toUpperCase() + nameParts[1].slice(1);

      return `${firstName} ${lastName}`;
    } catch (error) {
      console.error('Error formatting email:', error);
      return email;
    }
  }

  /**
   * Show test details in modal
   * @param {string} tsnId - Test session ID
   * @param {Object} testData - Test data
   */
  showTestDetails(tsnId, testData) {
    console.log(`Showing test details for session ${tsnId}:`, testData);

    // Get the modal element
    const modal = document.getElementById('report-modal');
    const content = document.getElementById('report-content');

    if (!modal || !content) {
      console.error('Report modal elements not found');
      return;
    }

    // Format the report content
    let reportHtml = '';

    if (testData.report) {
      // If we have a raw report, display it directly
      reportHtml = `
        <div class="ms-report-header">
          <h3>${testData.test_name || 'Test Report'}</h3>
          <div class="ms-report-meta">
            <div><strong>ID:</strong> ${tsnId}</div>
            <div><strong>Test ID:</strong> ${testData.tc_id || testData.ts_id || 'N/A'}</div>
            <div><strong>Initiator:</strong> ${this.formatUserEmail(testData.uid)}</div>
            <div><strong>Status:</strong> <span class="status-${testData.status?.toLowerCase() || 'unknown'}">${testData.status || 'Unknown'}</span></div>
            <div><strong>Started:</strong> ${testData.start_ts || 'N/A'}</div>
            <div><strong>Completed:</strong> ${testData.end_ts || 'N/A'}</div>
            <div><strong>Pass Rate:</strong> ${testData.passed_cases || 0}/${(testData.passed_cases || 0) + (testData.failed_cases || 0)}</div>
          </div>
        </div>
        <div class="ms-report-content">
          ${testData.report}
        </div>
      `;
    } else {
      // Create a basic report from the available data
      reportHtml = `
        <div class="ms-report-header">
          <h3>${testData.test_name || 'Test Report'}</h3>
          <div class="ms-report-meta">
            <div><strong>ID:</strong> ${tsnId}</div>
            <div><strong>Test ID:</strong> ${testData.tc_id || testData.ts_id || 'N/A'}</div>
            <div><strong>Initiator:</strong> ${this.formatUserEmail(testData.uid)}</div>
            <div><strong>Status:</strong> <span class="status-${testData.status?.toLowerCase() || 'unknown'}">${testData.status || 'Unknown'}</span></div>
            <div><strong>Started:</strong> ${testData.start_ts || testData.start_time || 'N/A'}</div>
            <div><strong>Completed:</strong> ${testData.end_ts || testData.end_time || 'N/A'}</div>
          </div>
        </div>
        <div class="ms-report-summary">
          <p>Detailed report not available.</p>
        </div>
      `;
    }

    // Set modal content
    content.innerHTML = reportHtml;

    // Add close functionality
    const closeButtons = modal.querySelectorAll('.ms-modal-close');
    closeButtons.forEach(btn => {
      btn.onclick = () => {
        modal.style.display = 'none';
      };
    });

    // Show modal
    modal.style.display = 'block';

    // Close modal when clicking outside
    window.onclick = (event) => {
      if (event.target === modal) {
        modal.style.display = 'none';
      }
    };
  }

  /**
   * Update the recent runs table with data
   * @param {Array} recentRuns - The recent runs data
   */
  updateRecentRunsTable(recentRuns = []) {
    console.log('Updating recent runs table with data:', recentRuns);

    try {
      // Find the table - try multiple selector strategies
      let recentRunsTable = document.getElementById('reports-table');

      if (!recentRunsTable) {
        // Strategy 1: Look for table with Test ID header
        const tables = document.querySelectorAll('table');
        for (const table of tables) {
          const headers = table.querySelectorAll('th');
          for (const header of headers) {
            if (header.textContent.includes('Test ID')) {
              recentRunsTable = table;
              console.log('Found recent runs table using Test ID header');
              break;
            }
          }
          if (recentRunsTable) break;
        }
      }

      // Strategy 2: Look for table with specific class or id
      if (!recentRunsTable) {
        recentRunsTable = document.querySelector('.recent-runs-table') ||
                          document.querySelector('#recent-runs-table');
        if (recentRunsTable) {
          console.log('Found recent runs table using class/id selector');
        }
      }

      // Strategy 3: Look for any table within the dashboard content area
      if (!recentRunsTable) {
        const dashboardContent = document.querySelector('.dashboard-content') ||
                                document.querySelector('#dashboard-content');
        if (dashboardContent) {
          recentRunsTable = dashboardContent.querySelector('table');
          if (recentRunsTable) {
            console.log('Found recent runs table within dashboard content');
          }
        }
      }

      // Strategy 4: Last resort - take the first table in the document
      if (!recentRunsTable) {
        recentRunsTable = document.querySelector('table');
        if (recentRunsTable) {
          console.log('Using first table as recent runs table (fallback)');
        }
      }

      if (!recentRunsTable) {
        console.error('Recent runs table not found');
        return;
      }

      // Get or create tbody
      let tableBody = recentRunsTable.querySelector('tbody');
      if (!tableBody) {
        tableBody = document.createElement('tbody');
        recentRunsTable.appendChild(tableBody);
      }

      // Clear existing rows
      tableBody.innerHTML = '';

      // Add a "no data" row if no recent runs
      if (!recentRuns || recentRuns.length === 0) {
        const noDataRow = document.createElement('tr');
        const headerRow = recentRunsTable.querySelector('thead tr');
        const columns = headerRow ? headerRow.querySelectorAll('th').length : 10;

        const noDataCell = document.createElement('td');
        noDataCell.setAttribute('colspan', columns);
        noDataCell.textContent = 'No recent test runs found';
        noDataCell.style.textAlign = 'center';
        noDataCell.style.padding = '10px';

        noDataRow.appendChild(noDataCell);
        tableBody.appendChild(noDataRow);
        return;
      }

      // Store the full test data for use in the details view
      this.testDetailsCache = {};

      // Add new rows
      recentRuns.forEach(run => {
        const row = document.createElement('tr');

        // Cache the full test data for the details view
        const tsnId = run.tsn_id || run.id || '';
        this.testDetailsCache[tsnId] = run;

        // Add appropriate class based on status
        if (run.status && run.status.toLowerCase() === 'failed') {
          row.classList.add('table-danger');
        } else if (run.status && run.status.toLowerCase() === 'passed') {
          row.classList.add('table-success');
        }

        // Format the dates
        let startTime = 'N/A';
        let endTime = 'N/A';
        try {
          startTime = this.formatTime(run.start_time || run.created_at || run.timestamp);
          endTime = this.formatTime(run.end_time || '');
        } catch (e) {
          console.error('Error formatting date:', e);
        }

        // Get the test ID and name
        const testId = run.tc_id || run.ts_id || run.test_id || '';
        const testName = run.test_name || run.name || `Test ${testId}`;

        // Format user email to display name
        const userEmail = run.uid || run.user_id || '';
        const userName = this.formatUserEmail(userEmail);

        // Determine passed/failed count
        const passedCount = run.passed_cases || 0;
        const failedCount = run.failed_cases || 0;

        row.innerHTML = `
          <td>${tsnId}</td>
          <td>${testName}</td>
          <td>${testId}</td>
          <td>${run.status || 'Unknown'}</td>
          <td>${startTime}</td>
          <td>${endTime}</td>
          <td title="${userEmail}">${userName}</td>
          <td class="text-success">${passedCount}</td>
          <td class="text-danger">${failedCount}</td>
          <td>
            <button class="ms-Button ms-Button--primary test-details-btn" data-tsn-id="${tsnId}">
              <span class="ms-Button-label">Details</span>
            </button>
          </td>
        `;

        tableBody.appendChild(row);
      });

      // Attach event listeners to the details buttons
      const detailsButtons = recentRunsTable.querySelectorAll('.test-details-btn');
      detailsButtons.forEach(button => {
        button.addEventListener('click', (event) => {
          const tsnId = event.currentTarget.getAttribute('data-tsn-id');
          if (tsnId && this.testDetailsCache[tsnId]) {
            this.showTestDetails(tsnId, this.testDetailsCache[tsnId]);
          } else {
            console.error(`Test data not found for session ${tsnId}`);
          }
        });
      });

      console.log('Recent runs table updated successfully');
    } catch (error) {
      console.error('Error updating recent runs table:', error);
    }
  }
}
# SmartTest API Server

API server component for the SmartTest automation framework.

## Overview

The SmartTest API Server acts as a bridge between the test automation clients and the database, providing RESTful API endpoints for:

- Test session management
- Input query logging
- Test execution status tracking
- Test report generation

## Directory Structure

The server code is organized into a modular structure:

```
frontend/server/
├── api.js                  # Main entry point
├── config/                 # Configuration files
│   ├── app-config.js       # Application configuration
│   └── env-config.js       # Environment variables
├── middleware/             # Middleware functions
│   ├── auth.js             # Authentication middleware
│   ├── error-handler.js    # Error handling middleware
│   ├── logging.js          # Logging middleware
│   └── proxy.js            # Proxy middleware
├── routes/                 # Route handlers
│   ├── index.js            # Route registration
│   ├── test-cases.js       # Test case routes
│   ├── test-suites.js      # Test suite routes
│   ├── test-reports.js     # Report routes
│   ├── active-tests.js     # Active tests routes
│   ├── recent-runs.js      # Recent runs routes
│   ├── case-runner.js      # Case runner routes
│   ├── proxy-routes.js     # External API proxy routes
│   ├── test-sessions.js    # Test session routes
│   └── input-queries.js    # Input query routes
├── services/               # Business logic
│   ├── case-runner.js      # Case runner service
│   ├── test-reports.js     # Report generation service
│   └── test-status.js      # Test status service
└── documentation/          # Documentation
    └── API/                # API documentation
```

Detailed API documentation can be found in the `documentation/API/` directory.

## Requirements

- Node.js (v14 or higher)
- MySQL database (v5.7 or higher)
- SSH access to database server (**required: all DB queries are performed via SSH+MySQL CLI; direct TCP and SSH tunnel are currently disabled**)

## Installation

1. Clone the repository
2. Navigate to the frontend/server directory
3. Install dependencies:

```bash
npm install
```

4. Copy `.env.example` to `.env` and update the configuration
5. Start the server:

```bash
npm start
```

## Configuration

The server can be configured using environment variables or a `.env` file:

### Basic Configuration

- `PORT`: Server port (default: 3000)
- `BASE_URL`: Base URL for the API
- `NODE_ENV`: Environment (development, test, production)

### Database Configuration

- `DB_HOST`: Database host (used for SSH, not direct TCP)
- `DB_PORT`: Database port (default: 3306, used by remote MySQL CLI)
- `DB_USER`: Database username
- `DB_PASSWORD`: Database password
- `DB_NAME`: Database name

### SSH Database Access (Current Mode)

> **All database operations are performed by executing MySQL queries over SSH on the remote server.**
> The SSH tunnel and direct TCP connection options are currently disabled in code (see `db-manager.js`).
> To re-enable them, update the logic in `db-manager.js`.

- `SSH_HOST`: SSH server hostname (same as DB host)
- `SSH_PORT`: SSH server port (default: 22)
- `SSH_USER`: SSH username
- `SSH_KEY_PATH`: Path to SSH private key file

### Environment Switching

- Environments (QA01, QA02, QA03, etc.) are configured in `db-environments.js` and `.env` files.
- The backend will always use SSH for DB access, regardless of environment.

### Cross-Platform Path Handling

The system supports cross-platform path handling for SSH key files:

- Windows paths: `C:\\Users\\<USER>\\.ssh\\id_rsa`
- Unix paths: `/home/<USER>/.ssh/id_rsa`
- Home directory shorthand: `~/.ssh/id_rsa`

The system will:
1. Resolve relative paths against the current working directory
2. Expand tilde (`~`) to the user's home directory
3. Accept both forward slashes and backslashes
4. Fall back to common SSH key locations if not specified

### Security Configuration

- `API_USERNAME`: API Basic Auth username
- `API_PASSWORD`: API Basic Auth password
- `RATE_LIMIT_WINDOW_MS`: Rate limit window in milliseconds
- `RATE_LIMIT_MAX_REQUESTS`: Maximum requests per window

## API Endpoints

### Test Sessions

- `POST /AutoRun/TestSession`: Create a new test session
- `GET /AutoRun/TestSession`: List all test sessions
- `GET /AutoRun/TestSession/:id`: Get a specific test session
- `POST /AutoRun/TestSession/:id/status`: Update session status

### Input Queries

- `POST /AutoRun/InputQuery`: Log a new input query
- `GET /AutoRun/InputQuery/:sessionId`: Get queries for a session

## Development

### Running in Development Mode

```bash
npm run dev
```

### Running Tests

```bash
npm test
```

### Serving Dashboard, Config, and Reports

```bash
npm run dev:all
```

## Troubleshooting

### SSH Connection Issues

1. Verify SSH credentials are correct
2. Ensure SSH key has proper permissions (600 on Unix systems)
3. Check if the SSH server allows connections from your IP
4. Try connecting manually via SSH command line to verify access

### Database Connection Issues

1. Verify database credentials
2. Check if the database is running and accessible from the SSH server
3. Verify firewall rules allow connections to the database port

## License

[MIT](LICENSE)
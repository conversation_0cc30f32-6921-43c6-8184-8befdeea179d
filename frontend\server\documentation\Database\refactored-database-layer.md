# Refactored Database Layer

The SmartTest database layer has been completely refactored to provide a clean, maintainable, and extensible interface for interacting with the database. This document provides an overview of the refactored database layer and links to detailed documentation.

## Overview

The refactored database layer follows a modular architecture with clear separation of concerns:

- **Configuration**: Environment-specific configuration is centralized and loaded from environment variables or configuration files.
- **Connection Methods**: Multiple connection methods (direct SSH and SSH tunnel) are supported, with direct SSH as the primary method.
- **Query Builder**: A flexible SQL query builder is provided for constructing complex queries.
- **Pre-defined Queries**: Common database operations are implemented as pre-defined queries.
- **External API Integration**: The database layer interacts with external APIs for operations like running tests and retrieving test results.
- **AI Integration**: The architecture is designed to be easily extensible for future AI integration.

## Key Features

- **Multiple Connection Methods**: Supports both direct SSH and SSH tunnel connections
- **Environment-Specific Configuration**: Automatically selects the best connection method based on the environment
- **Comprehensive API**: Provides a clean API for common database operations
- **Query Builder**: Includes a flexible SQL query builder for advanced queries
- **External API Integration**: Integrates with external APIs on ports 5080 and 9080 for test execution and reporting
- **AI Integration**: Designed to be easily extensible for future AI integration
- **Error Handling**: Robust error handling with fallback mechanisms

## Documentation

- [Main Documentation](refactored-database-layer/README.md): Comprehensive documentation for the refactored database layer
- [Connection Methods](refactored-database-layer/connection-methods.md): Detailed documentation for the connection methods
- [Query Builder](refactored-database-layer/query-builder.md): Detailed documentation for the query builder
- [Pre-defined Queries](refactored-database-layer/pre-defined-queries.md): Detailed documentation for the pre-defined queries
- [AI Integration](refactored-database-layer/ai-integration.md): Detailed documentation for the AI integration
- [Migration Guide](refactored-database-layer/migration-guide.md): Guide for migrating from the old database layer to the new one

## Directory Structure

```
database/
├── index.js                  # Main entry point
├── config/                   # Configuration
│   ├── index.js              # Configuration loader
│   └── environments.js       # Environment-specific configs
├── connections/              # Connection implementations
│   ├── index.js              # Connection factory
│   ├── direct-ssh.js         # Direct SSH implementation
│   └── ssh-tunnel.js         # SSH tunnel implementation
├── queries/                  # Pre-defined queries
│   ├── index.js              # Query module exports
│   ├── test-cases.js         # Test case queries
│   ├── test-suites.js        # Test suite queries
│   ├── test-sessions.js      # Test session queries
│   └── test-results.js       # Test result queries
├── utils/                    # Database utilities
│   ├── query-builder.js      # SQL query builder
│   └── result-formatter.js   # Result formatting utilities
└── ai/                       # AI integration components
    ├── index.js              # AI module exports
    ├── query-generator.js    # AI-driven query generation
    ├── request-generator.js  # AI-driven HTTP request generation
    └── context-provider.js   # Database context for AI
```

## Basic Usage

```javascript
const db = require('./database');

// Initialize the database connection
await db.init();

// Get test cases
const testCases = await db.getTestCases({ limit: 10 });

// Get test suites
const testSuites = await db.getTestSuites({ projectId: 1 });

// Get active tests
const activeTests = await db.getActiveTests();

// Get test results
const testResults = await db.getTestResults('12345');

// Close the connection when done
await db.close();
```

## Advanced Usage with Query Builder

```javascript
const db = require('./database');
const { QueryBuilder } = db;

// Initialize the database connection
await db.init();

// Create a query builder
const queryBuilder = new QueryBuilder();

// Build a complex query
queryBuilder.select('test_result r', ['r.tsn_id', 'r.tc_id', 'r.outcome', 'r.creation_time', 'o.txt']);
queryBuilder.join('output o', 'r.cnt = o.cnt');
queryBuilder.where('r.tsn_id', '=', '12345');
queryBuilder.orderBy('r.creation_time', 'ASC');

// Execute the query
const { sql, params } = queryBuilder.build();
const results = await db.query(sql, params);

// Close the connection when done
await db.close();
```

## AI Integration (Future)

```javascript
const db = require('./database');

// Initialize the database connection
await db.init();

// Execute a natural language query
const results = await db.executeNaturalLanguageQuery('Get all failed test cases from the last 24 hours');

// Close the connection when done
await db.close();
```

## Migration

If you're migrating from the old database layer to the new one, please refer to the [Migration Guide](refactored-database-layer/migration-guide.md) for detailed instructions.

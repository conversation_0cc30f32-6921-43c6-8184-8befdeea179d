/**
 * Test Status Check Script
 * 
 * This script checks the status of a specific test run without continuous monitoring.
 */

const mysql = require('mysql2/promise');

// Process command line arguments
const args = process.argv.slice(2);
const environment = args[0] || 'qa02';
const tsnId = args[1] || 13579; // Default to the test ID we created

// Configuration
const config = {
  // Environment settings
  environments: {
    qa01: {
      dbHost: 'mprts-qa01.lab.wagerworks.com',
      dbUser: 'rgs_rw',
      dbPassword: 'rgs_rw', 
      dbName: 'rgs_test',
      dbPort: 3306
    },
    qa02: {
      dbHost: 'mprts-qa02.lab.wagerworks.com',
      dbUser: 'rgs_rw',
      dbPassword: 'rgs_rw',
      dbName: 'rgs_test',
      dbPort: 3306
    },
    qa03: {
      dbHost: 'mprts-qa03.lab.wagerworks.com',
      dbUser: 'rgs_rw',
      dbPassword: 'rgs_rw',
      dbName: 'rgs_test',
      dbPort: 3306
    }
  }
};

async function main() {
  let connection;
  
  try {
    if (!config.environments[environment]) {
      throw new Error(`Unknown environment: ${environment}`);
    }
    
    // Connect to database
    const envConfig = config.environments[environment];
    console.log(`Connecting to ${envConfig.dbHost}...`);
    
    connection = await mysql.createConnection({
      host: envConfig.dbHost,
      user: envConfig.dbUser,
      password: envConfig.dbPassword,
      database: envConfig.dbName,
      port: envConfig.dbPort
    });
    
    console.log('Connected to database');
    
    // Check test session status
    console.log(`\nChecking test session for tsn_id: ${tsnId}`);
    const [sessionRows] = await connection.query(`
      SELECT * FROM test_session WHERE tsn_id = ?
    `, [tsnId]);
    
    if (sessionRows.length === 0) {
      console.log(`No test session found for tsn_id: ${tsnId}`);
    } else {
      const session = sessionRows[0];
      console.log('Test Session Details:');
      console.log('---------------------');
      console.log(`ID: ${session.tsn_id}`);
      console.log(`User: ${session.uid || 'Not specified'}`);
      console.log(`Start Time: ${session.start_ts}`);
      console.log(`End Time: ${session.end_ts || 'Still running'}`);
      console.log(`Status: ${session.end_ts ? 'Completed' : 'Running'}`);
      console.log(`Test Case ID: ${session.tc_id || 'Not specified'}`);
      console.log(`Test Suite ID: ${session.ts_id || 'Not specified'}`);
      console.log(`Error Message: ${session.error || 'None'}`);
      
      // Check for test results
      const [resultRows] = await connection.query(`
        SELECT tc_id, outcome, COUNT(*) as count
        FROM test_result
        WHERE tsn_id = ?
        GROUP BY tc_id, outcome
      `, [tsnId]);
      
      console.log('\nTest Results:');
      console.log('-------------');
      
      if (resultRows.length === 0) {
        console.log('No test results found - execution may not have started or no results logged yet.');
      } else {
        const resultsByTestCase = {};
        
        resultRows.forEach(row => {
          if (!resultsByTestCase[row.tc_id]) {
            resultsByTestCase[row.tc_id] = {};
          }
          resultsByTestCase[row.tc_id][row.outcome] = row.count;
        });
        
        for (const [tcId, outcomes] of Object.entries(resultsByTestCase)) {
          console.log(`Test Case ${tcId}:`);
          for (const [outcome, count] of Object.entries(outcomes)) {
            console.log(`  - ${outcome}: ${count}`);
          }
        }
      }
      
      // Check if the test is being executed by looking at activity in the last 5 minutes
      const [recentActivityRows] = await connection.query(`
        SELECT MAX(creation_time) as latest_activity
        FROM test_result
        WHERE tsn_id = ?
      `, [tsnId]);
      
      if (recentActivityRows[0].latest_activity) {
        const latestActivity = new Date(recentActivityRows[0].latest_activity);
        const now = new Date();
        const minutesSinceLastActivity = (now - latestActivity) / (1000 * 60);
        
        console.log('\nActivity Status:');
        console.log('---------------');
        console.log(`Latest activity: ${latestActivity}`);
        console.log(`Minutes since last activity: ${minutesSinceLastActivity.toFixed(2)}`);
        
        if (minutesSinceLastActivity < 5) {
          console.log('Status: ACTIVE - Recent activity detected');
        } else if (!session.end_ts) {
          console.log('Status: STALLED - No recent activity but session not marked as complete');
        } else {
          console.log('Status: COMPLETED - Session marked as complete');
        }
      } else {
        console.log('\nActivity Status: No test result activity found');
      }
    }
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\nDatabase connection closed');
    }
  }
}

main().catch(console.error); 
const axios = require('axios');

async function testApiCall() {
  try {
    console.log('Testing API call to CaseRunner...');
    
    // Create request parameters
    const params = new URLSearchParams();
    params.append('uid', '<EMAIL>');
    params.append('password', 'test'); // Replace with actual password
    params.append('tc_id', '3180');
    params.append('envir', 'qa02');
    params.append('shell_host', 'jps-qa10-app01');
    params.append('file_path', '/home/<USER>/');
    params.append('operatorConfigs', 'operatorNameConfigs');
    params.append('kafka_server', 'kafka-qa-a0.lab.wagerworks.com');
    params.append('dataCenter', 'GU');
    params.append('rgs_env', 'qa02');
    params.append('old_version', '0');
    params.append('networkType1', 'multi-site');
    params.append('networkType2', 'multi-site');
    params.append('sign', '-');
    params.append('rate_src', 'local');
    params.append('user_id', 'Iakov.Volfkovich');
    params.append('username', 'Iakov.Volfkovich');
    
    // Make the API request
    const response = await axios.post(
      'http://mprts-qa02.lab.wagerworks.com:5080/AutoRun/CaseRunner',
      params.toString(),
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      }
    );
    
    // Log the response details
    console.log('Response status:', response.status);
    console.log('Response headers:', response.headers);
    console.log('Response data type:', typeof response.data);
    console.log('Response data:', response.data);
    
    // If the response is HTML, try to extract the tsn_id using different methods
    if (typeof response.data === 'string') {
      // Method 1: Using regex to find tsn_id=number
      const tsnIdMatch = response.data.match(/tsn_id=(\d+)/);
      console.log('\nMethod 1 - Extract using regex tsn_id=number:');
      console.log(tsnIdMatch ? `Found tsn_id: ${tsnIdMatch[1]}` : 'No match found');
      
      // Method 2: Using regex to find ReportSummary?tsn_id=number
      const reportMatch = response.data.match(/ReportSummary\?tsn_id=(\d+)/);
      console.log('\nMethod 2 - Extract using regex ReportSummary?tsn_id=number:');
      console.log(reportMatch ? `Found tsn_id: ${reportMatch[1]}` : 'No match found');
      
      // Method 3: Check for any number after tsn_id
      const anyTsnIdMatch = response.data.match(/tsn_id.*?(\d+)/);
      console.log('\nMethod 3 - Extract using regex tsn_id.*?number:');
      console.log(anyTsnIdMatch ? `Found tsn_id: ${anyTsnIdMatch[1]}` : 'No match found');
      
      // Method 4: Show a snippet of the HTML containing tsn_id
      const tsnIdPosition = response.data.indexOf('tsn_id');
      console.log('\nMethod 4 - Content around tsn_id keyword:');
      if (tsnIdPosition !== -1) {
        const start = Math.max(0, tsnIdPosition - 50);
        const end = Math.min(response.data.length, tsnIdPosition + 150);
        console.log(response.data.substring(start, end));
      } else {
        console.log('tsn_id not found in response');
      }
      
      // Method 5: Locate any URLs in the response
      const urlMatch = response.data.match(/href=['"]([^'"]+)['"]/g);
      console.log('\nMethod 5 - URLs in response:');
      console.log(urlMatch ? urlMatch : 'No URLs found');
    }
    
  } catch (error) {
    console.error('API call error:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
}

testApiCall().catch(console.error); 
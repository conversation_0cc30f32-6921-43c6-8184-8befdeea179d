/**
 * Jest setup file
 * This file is executed before each test file
 */

// Load environment variables from .env.test
require('dotenv').config({ path: '.env.test' });

// Set test timezone to UTC to ensure consistent date handling
process.env.TZ = 'UTC';

// Remove global mock - we'll mock specifically in each test
// jest.mock('../db.js');

// Mock SSH2 client if needed for specific tests
jest.mock('ssh2', () => {
  return {
    Client: jest.fn().mockImplementation(() => ({
      on: jest.fn().mockImplementation(function(event, callback) {
        if (event === 'ready') {
          this.readyCallback = callback;
          return this;
        }
        if (event === 'error') {
          this.errorCallback = callback;
          return this;
        }
        if (event === 'close') {
          this.closeCallback = callback;
          return this;
        }
        return this;
      }),
      connect: jest.fn().mockImplementation(function() {
        if (this.readyCallback) setTimeout(() => this.readyCallback(), 10);
        return this;
      }),
      forwardOut: jest.fn().mockImplementation((srcAddr, srcPort, dstAddr, dstPort, callback) => {
        callback(null, { 
          localPort: 3307,
          on: jest.fn(),
          end: jest.fn() 
        });
      }),
      end: jest.fn()
    }))
  };
});

// Mock mysql2/promise with connection pool for updated implementation
jest.mock('mysql2/promise', () => {
  return {
    createPool: jest.fn().mockReturnValue({
      execute: jest.fn().mockResolvedValue([[{ connection_test: 1 }]]),
      end: jest.fn().mockResolvedValue(undefined)
    })
  };
});

// Mock fs for path resolution testing
jest.mock('fs', () => ({
  ...jest.requireActual('fs'),
  readFileSync: jest.fn().mockReturnValue('mock-private-key-content'),
  existsSync: jest.fn().mockReturnValue(true)
}));

// Set test timeout (30 seconds)
jest.setTimeout(30000);

// Reset mocks between tests
beforeEach(() => {
  jest.clearAllMocks();
});

// Global error handling to catch test errors
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection in tests:', reason);
});

// Export any helper functions we might need across tests
module.exports = {
  // Mock helper functions can go here
  mockSSHConnection: (shouldSucceed = true) => {
    const ssh2 = require('ssh2');
    const mockClient = new ssh2.Client();
    
    if (shouldSucceed) {
      // Trigger ready callback
      setTimeout(() => {
        if (mockClient.readyCallback) mockClient.readyCallback();
      }, 10);
    } else {
      // Trigger error callback
      setTimeout(() => {
        if (mockClient.errorCallback) mockClient.errorCallback(new Error('Mock SSH connection failure'));
      }, 10);
    }
    
    return mockClient;
  }
}; 
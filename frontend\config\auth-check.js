/**
 * Authentication Check for Config Page
 *
 * This script checks if the user is authenticated using the unified auth client.
 * If not authenticated, it redirects to the dashboard for login.
 */

console.log('=== AUTH CHECK SCRIPT ===');

// Use the global unified auth client
function initUnifiedAuth() {
    if (window.unifiedAuthClient) {
        console.log('✅ Config: Connected to unified auth client');
        return true;
    }
    return false;
}

// Try to initialize immediately, or wait for it to be available
if (!initUnifiedAuth()) {
    const checkInterval = setInterval(() => {
        if (initUnifiedAuth()) {
            clearInterval(checkInterval);
            // Re-run auth check with unified client
            performAuthCheck();
        }
    }, 100);

    setTimeout(() => clearInterval(checkInterval), 5000);
}

// Function to check if user is authenticated using JWT-based unified auth client
function checkAuthentication() {
    console.log('Checking JWT-based authentication status...');

    // Use unified auth client for JWT-based authentication
    if (window.unifiedAuthClient) {
        if (window.unifiedAuthClient.isAuthenticated && window.unifiedAuthClient.currentUser) {
            const user = window.unifiedAuthClient.currentUser;
            console.log('✅ Config: JWT authentication valid for user:', user.uid);

            return {
                hasCredentials: true,
                uid: user.uid,
                user: user,
                isJWTAuth: true
            };
        } else {
            console.log('❌ Config: No valid JWT session in unified auth client');
            return {
                hasCredentials: false,
                uid: '',
                user: null,
                isJWTAuth: false
            };
        }
    }

    console.log('⚠️ Config: Unified auth client not available');
    return {
        hasCredentials: false,
        uid: '',
        user: null,
        isJWTAuth: false
    };
}

// Function to redirect to dashboard for login
function redirectToLogin() {
    console.log('Redirecting to dashboard for authentication...');
    
    // Store the current page URL so we can return after login
    try {
        sessionStorage.setItem('smarttest_return_url', window.location.href);
    } catch (error) {
        console.warn('Could not store return URL:', error);
    }
    
    // Redirect to dashboard
    window.location.href = '/dashboard';
}

// Function to show authentication status in UI
function updateAuthUI(uid) {
    const envDisplay = document.getElementById('environment-display');
    if (envDisplay && uid) {
        envDisplay.textContent = `Logged in as: ${uid}`;
    }
}

// Main authentication check using JWT-based unified auth client
function performAuthCheck() {
    // Use unified auth client for JWT-based authentication
    if (window.unifiedAuthClient) {
        // Check if already authenticated
        if (window.unifiedAuthClient.isAuthenticated && window.unifiedAuthClient.currentUser) {
            const user = window.unifiedAuthClient.currentUser;
            console.log('✅ Config: User authenticated via unified auth client:', user.uid);
            updateAuthUI(user.uid);

            // Set credentials in API service using JWT auth
            setCredentialsInApiService(user.uid);
            return true;
        } else {
            console.log('❌ Config: No valid session in unified auth client - redirecting to login');
            redirectToLogin();
            return false;
        }
    }

    console.log('⚠️ Config: Unified auth client not available - redirecting to login');
    redirectToLogin();
    return false;
}

/**
 * Set credentials in API service using JWT authentication
 * @param {string} uid - User ID
 */
function setCredentialsInApiService(uid) {
    function setCredentialsWhenReady() {
        if (window.apiService && typeof window.apiService.setCredentials === 'function') {
            console.log('Setting JWT-based credentials on apiService:', uid);
            // For JWT-based auth, we just need the uid - the API service will use the JWT token
            window.apiService.setCredentials(uid, ''); // Empty password for JWT auth
            return true;
        }
        return false;
    }

    // Try to set credentials immediately
    if (!setCredentialsWhenReady()) {
        // If apiService is not ready yet, wait for it
        console.log('apiService not ready yet, will set JWT credentials when available');

        // Listen for apiservice-ready event
        document.addEventListener('apiservice-ready', function() {
            console.log('apiservice-ready event received, setting JWT credentials');
            setCredentialsWhenReady();
        });

        // Also try periodically in case the event was missed
        const checkInterval = setInterval(() => {
            if (setCredentialsWhenReady()) {
                clearInterval(checkInterval);
            }
        }, 100);

        // Stop trying after 5 seconds
        setTimeout(() => clearInterval(checkInterval), 5000);
    }
}

// Run authentication check when DOM is loaded with retry mechanism
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, performing authentication check...');

    // Give the unified auth client time to initialize and restore session
    let authCheckAttempts = 0;
    const maxAttempts = 10;
    const checkInterval = 500; // Check every 500ms

    function attemptAuthCheck() {
        authCheckAttempts++;
        console.log(`🔍 Config: Auth check attempt ${authCheckAttempts}/${maxAttempts}`);

        const authResult = performAuthCheck();

        if (authResult) {
            console.log('✅ Config: Authentication successful');
            return; // Success, stop checking
        }

        if (authCheckAttempts >= maxAttempts) {
            console.log('❌ Config: Max auth check attempts reached, authentication required');
            // Only show login modal after all attempts failed
            if (window.configAuth && typeof window.configAuth.showLoginModal === 'function') {
                console.log('🔑 Config: Showing login modal via ConfigAuth');
                window.configAuth.showLoginModal();
            } else if (typeof showLoginModal === 'function') {
                showLoginModal();
            } else {
                console.log('⚠️ Config: Login modal not available, initializing ConfigAuth...');
                // Try to initialize ConfigAuth if it's available
                if (typeof ConfigAuth !== 'undefined') {
                    window.configAuth = new ConfigAuth();
                    window.configAuth.init();
                    window.configAuth.showLoginModal();
                } else {
                    console.log('⚠️ Config: ConfigAuth class not available, user needs to refresh');
                }
            }
            return;
        }

        // Try again after a delay
        setTimeout(attemptAuthCheck, checkInterval);
    }

    // Start the authentication check process
    attemptAuthCheck();
});

// Also run check immediately if DOM is already loaded
if (document.readyState === 'loading') {
    // DOM is still loading, event listener will handle it
} else {
    // DOM is already loaded
    console.log('DOM already loaded, performing immediate authentication check...');
    performAuthCheck();
}

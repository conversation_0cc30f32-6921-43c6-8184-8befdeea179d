# SmartTest Dashboard Polling Logic Refactor

**Date:** 2025-07-02

## 1. Objective

The primary goal of this refactor is to stabilize the SmartTest dashboard's frontend, eliminate race conditions, and provide a consistent, flicker-free user experience. This is achieved by establishing a single source of truth for all test status updates.

## 2. The Problem: Race Conditions and UI Flickering

Previously, the dashboard relied on multiple, independent data sources and polling mechanisms:

1.  **`pollRecentRuns`**: Fetched a list of all recent test runs.
2.  **`updateTestStatuses` / `getTestStatus`**: Made individual API calls to `/api/test-status` for each test currently considered "active" by the frontend.

This dual-system approach created significant problems:

*   **Race Conditions**: The two polling loops operated on different schedules. `pollRecentRuns` might update the recent runs list *before* or *after* `updateTestStatuses` received a status for a specific test, leading to conflicting information.
*   **UI Flickering**: A test might be marked "completed" by one API call, only to be briefly shown as "pending" or "running" again based on stale data from the other poller before being corrected.
*   **Inaccurate Notifications**: Completion notifications were sometimes triggered with incorrect pass/fail counts (often showing `0/0`) because the `/api/test-status` endpoint did not have access to the final, structured results available in `/local/recent-runs`.
*   **Stale Active Tests**: Completed tests would sometimes remain in the "Active Tests" panel indefinitely or reappear after disappearing.

## 3. The Solution: A Single Source of Truth

The new architecture is simplified and centralized around a single, authoritative data source: the **`/local/recent-runs` API endpoint**.

### Core Principles:

1.  **One Poller to Rule Them All**: The `pollRecentRuns` function in `api-integration.js` is now the *only* function responsible for fetching test status data from the backend. The `updateTestStatuses` function and its associated `setInterval` have been completely removed.

2.  **`activeTests` Map as Authoritative State**: The frontend maintains an `activeTests` `Map` object. This map holds the state for any test that has been initiated in the current session.

3.  **Data Flow:**
    a. A test is started (e.g., by clicking "Run Suite"). A new entry is added to the `activeTests` map with a status of `running`.
    b. Every 5 seconds, `pollRecentRuns` executes.
    c. It fetches the latest data from `/local/recent-runs`.
    d. It iterates through the local `activeTests` map. For each test, it checks if a corresponding entry exists in the fresh data from `/local/recent-runs`.
    e. If a test in the `activeTests` map is still `running` but the data from `/local/recent-runs` shows it has a completed status (`passed`, `failed`, `completed`, `stopped`) and an `end_time`, the frontend knows the test has finished.

### How State is Managed:

*   **Completion Detection**: A test is considered complete *only* when `pollRecentRuns` finds that its status in the API response is final and an `end_time` is present.
*   **Notifications**: Upon detecting a completed test, a notification is triggered. The pass/fail counts are read directly from the structured `passed_cases` and `failed_cases` fields in the `/local/recent-runs` response, ensuring accuracy.
*   **Graceful Removal**: Once a test is marked as complete, it is kept in the "Active Tests" panel for 10 seconds to allow the user to see the final status. After this delay, it is removed from the `activeTests` map, and the UI is re-rendered, causing it to disappear from the panel permanently.

## 4. Key Code Changes

*   **File**: `c:\Dev\smarttest\frontend\dashboard\api-integration.js`
*   **Removed**: The entire `updateTestStatuses` function and the `setInterval` that called it. A no-op function signature is kept for backward compatibility.
*   **Modified**: `pollRecentRuns` now contains all the logic for updating the status of tests in the `activeTests` map, triggering notifications, and scheduling their removal.
*   **Modified**: `renderActiveTests` is simplified to render directly from the `activeTests` map. It no longer needs complex logic to decide what to show, as the map is the definitive state.
*   **Added**: Two new helper functions that manage the active test lifecycle:

    1. **`isTestSessionActive(run)`** - Determines if a test session should be considered active based on its status data. This function implements the business logic for determining when a test should appear in the Active Tests panel.

       ```javascript
       /**
        * Determines if a test session should be considered active based on its status
        * @param {Object} run - Test run data
        * @returns {boolean} - Whether the test is active
        */
       isTestSessionActive(run) {
         // Tests with an end time/timestamp are generally not active
         if (run.end_time || run.end_ts) {
           const status = (run.status || '').toLowerCase();
           // Exception: keep failed/passed tests briefly for user awareness
           return ['passed', 'failed'].includes(status);
         }
         
         // Use status if available to determine activity
         if (run.status) {
           const status = run.status.toLowerCase();
           return !['completed', 'cancelled', 'canceled', 'error'].includes(status);
         }
         
         // Default to active if we have start time but no end time
         return (run.start_time || run.start_ts) ? true : true;
       }
       ```

    2. **`updateActiveTestsFromRecentRuns(recentRuns)`** - The core function that processes recent runs data and updates the `activeTests` Map. This function:
       - Updates test statuses based on recent runs data
       - Triggers notifications with accurate test results when tests complete
       - Schedules removal of completed tests after a grace period
       - Updates the UI and dashboard counters when needed

       ```javascript
       /**
        * Update the active tests map using data from recent runs
        * This is the single source of truth for test status after refactoring
        * @param {Array} recentRuns - Recent runs data from API
        */
       updateActiveTestsFromRecentRuns(recentRuns) {
         // Process each recent run
         recentRuns.forEach(run => {
           const tsnId = String(run.tsn_id);
           const existingTest = this.activeTests.get(tsnId);
           
           // Determine if this run should be in active tests
           const isActive = this.isTestSessionActive(run);
           
           if (isActive) {
             // Update test data with information from recent run
             // including accurate passed/failed counts
             
             // Show notification when test completes with accurate results
             if (justCompleted) {
               const title = `Suite Finished: ${suiteName}`;
               const message = `Status: ${finalStatusMessage}. Cases Passed: ${passedCount}, Cases Failed: ${failedCount}.`;
               window.showNotification(title, message, notifType);
               
               // Schedule removal after grace period
               setTimeout(() => {
                 this.activeTests.delete(tsnId);
                 this.renderActiveTests();
               }, 10000); // 10 second grace period
             }
           }
         });
       }
       ```

This refactor ensures that the dashboard UI is a direct reflection of a single, reliable data stream, providing a stable and accurate view of test activity with detailed test results included in notifications and a smoother UI experience without flickering or stale data.

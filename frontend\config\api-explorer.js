/**
 * API Explorer for SmartTest
 * 
 * This class provides functionality to explore and test the SmartTest API endpoints.
 */
class ApiExplorer {
  constructor() {
    // Base URL for the API
    this.baseUrl = 'http://localhost:3000/AutoRun/';
    
    // Credentials for authentication (loaded from session)
    this.credentials = {
      uid: '',
      password: ''
    };
    this.loadCredentials();

    // Default parameter values from examples
    this.defaultParams = {
      envir: 'qa02',
      shell_host: 'jps-qa10-app01',
      file_path: '/home/<USER>/',
      operatorConfigs: 'operatorNameConfigs',
      kafka_server: 'kafka-qa-a0.lab.wagerworks.com',
      dataCenter: 'GU',
      rgs_env: 'qa02',
      old_version: '0',
      networkType1: 'multi-site',
      networkType2: 'multi-site',
      sign: '-',
      rate_src: 'local'
    };
  }

  /**
   * Load credentials from session storage
   */
  loadCredentials() {
    try {
      const uid = sessionStorage.getItem('smarttest_uid');
      const password = sessionStorage.getItem('smarttest_pwd');

      if (uid && password) {
        this.credentials = { uid, password };
        console.log('API Explorer: Loaded credentials for', uid);
      } else {
        console.log('API Explorer: No credentials found in session storage');
      }
    } catch (error) {
      console.error('API Explorer: Error loading credentials:', error);
    }
  }

  /**
   * Set credentials
   */
  setCredentials(uid, password) {
    this.credentials = { uid, password };
    try {
      sessionStorage.setItem('smarttest_uid', uid);
      sessionStorage.setItem('smarttest_pwd', password);
    } catch (error) {
      console.warn('API Explorer: Could not save credentials to session storage:', error);
    }
  }

    // Known API endpoints
    this.knownEndpoints = [
      {
        name: 'Create Test Session',
        endpoint: 'TestSession',
        method: 'POST',
        description: 'Create a new test session',
        parameters: [
          { name: 'uid', description: 'Username (email)', required: true, type: 'string' },
          { name: 'password', description: 'Password', required: true, type: 'string' },
          { name: 'test_type', description: 'Type of test', required: true, type: 'string' },
          { name: 'environment', description: 'Test environment', required: false, type: 'string' },
          { name: 'description', description: 'Session description', required: false, type: 'string' }
        ]
      },
      {
        name: 'Get All Test Sessions',
        endpoint: 'TestSession',
        method: 'GET',
        description: 'Get all test sessions',
        parameters: [
          { name: 'uid', description: 'Username (email)', required: true, type: 'string' },
          { name: 'password', description: 'Password', required: true, type: 'string' },
          { name: 'limit', description: 'Maximum number of sessions to retrieve', required: false, type: 'number' },
          { name: 'offset', description: 'Offset for pagination', required: false, type: 'number' },
          { name: 'status', description: 'Filter by status', required: false, type: 'string' }
        ]
      },
      {
        name: 'Get Test Session',
        endpoint: 'TestSession/:id',
        method: 'GET',
        description: 'Get a specific test session',
        parameters: [
          { name: 'id', description: 'Test session ID', required: true, type: 'string' },
          { name: 'uid', description: 'Username (email)', required: true, type: 'string' },
          { name: 'password', description: 'Password', required: true, type: 'string' }
        ]
      },
      {
        name: 'Update Test Session Status',
        endpoint: 'TestSession/:id/status',
        method: 'POST',
        description: 'Update a test session status',
        parameters: [
          { name: 'id', description: 'Test session ID', required: true, type: 'string' },
          { name: 'uid', description: 'Username (email)', required: true, type: 'string' },
          { name: 'password', description: 'Password', required: true, type: 'string' },
          { name: 'status', description: 'New status', required: true, type: 'string' },
          { name: 'progress', description: 'Progress percentage', required: false, type: 'number' }
        ]
      },
      {
        name: 'Get Test Session Report',
        endpoint: 'TestSession/:id/Report',
        method: 'GET',
        description: 'Get a test session report',
        parameters: [
          { name: 'id', description: 'Test session ID', required: true, type: 'string' },
          { name: 'uid', description: 'Username (email)', required: true, type: 'string' },
          { name: 'password', description: 'Password', required: true, type: 'string' }
        ]
      },
      {
        name: 'Log Input Query',
        endpoint: 'InputQuery',
        method: 'POST',
        description: 'Log a new input query',
        parameters: [
          { name: 'uid', description: 'Username (email)', required: true, type: 'string' },
          { name: 'password', description: 'Password', required: true, type: 'string' },
          { name: 'session_id', description: 'Test session ID', required: true, type: 'string' },
          { name: 'query', description: 'Input query', required: true, type: 'string' },
          { name: 'execution_time', description: 'Query execution time in ms', required: true, type: 'number' },
          { name: 'status', description: 'Query status', required: true, type: 'string' },
          { name: 'result', description: 'Query result', required: false, type: 'string' }
        ]
      },
      {
        name: 'Get Input Queries',
        endpoint: 'InputQuery/:sessionId',
        method: 'GET',
        description: 'Get input queries for a session',
        parameters: [
          { name: 'sessionId', description: 'Test session ID', required: true, type: 'string' },
          { name: 'uid', description: 'Username (email)', required: true, type: 'string' },
          { name: 'password', description: 'Password', required: true, type: 'string' },
          { name: 'limit', description: 'Maximum number of queries to retrieve', required: false, type: 'number' },
          { name: 'offset', description: 'Offset for pagination', required: false, type: 'number' },
          { name: 'status', description: 'Filter by status', required: false, type: 'string' }
        ]
      },
      {
        name: 'Get Input Query Stats',
        endpoint: 'InputQuery/:sessionId/Stats',
        method: 'GET',
        description: 'Get query execution stats for a session',
        parameters: [
          { name: 'sessionId', description: 'Test session ID', required: true, type: 'string' },
          { name: 'uid', description: 'Username (email)', required: true, type: 'string' },
          { name: 'password', description: 'Password', required: true, type: 'string' }
        ]
      },
      {
        name: 'Set Up Database Schema',
        endpoint: 'setup',
        method: 'POST',
        description: 'Set up the database schema (admin only)',
        parameters: [
          { name: 'uid', description: 'Admin username (email)', required: true, type: 'string' },
          { name: 'password', description: 'Admin password', required: true, type: 'string' }
        ]
      },
      {
        name: 'Run Test Case',
        endpoint: 'CaseRunner',
        method: 'POST',
        description: 'Run a test case',
        parameters: [
          { name: 'uid', description: 'Username (email)', required: true, type: 'string', defaultValue: '<EMAIL>' },
          { name: 'password', description: 'Password', required: true, type: 'string', defaultValue: 'test' },
          { name: 'tc_id', description: 'Test case ID', required: true, type: 'number', defaultValue: 3180 },
          { name: 'envir', description: 'Environment', required: true, type: 'string', defaultValue: 'qa02' },
          { name: 'shell_host', description: 'Shell host', required: true, type: 'string', defaultValue: 'jps-qa10-app01' },
          { name: 'file_path', description: 'File path', required: true, type: 'string', defaultValue: '/home/<USER>/' },
          { name: 'operatorConfigs', description: 'Operator configurations', required: true, type: 'string', defaultValue: 'operatorNameConfigs' },
          { name: 'kafka_server', description: 'Kafka server', required: true, type: 'string', defaultValue: 'kafka-qa-a0.lab.wagerworks.com' },
          { name: 'dataCenter', description: 'Data center', required: true, type: 'string', defaultValue: 'GU' },
          { name: 'rgs_env', description: 'RGS environment', required: true, type: 'string', defaultValue: 'qa02' },
          { name: 'old_version', description: 'Old version flag', required: true, type: 'string', defaultValue: '0' },
          { name: 'networkType1', description: 'Network type 1', required: true, type: 'string', defaultValue: 'multi-site' },
          { name: 'networkType2', description: 'Network type 2', required: true, type: 'string', defaultValue: 'multi-site' },
          { name: 'sign', description: 'Sign parameter', required: true, type: 'string', defaultValue: '-' },
          { name: 'rate_src', description: 'Rate source', required: true, type: 'string', defaultValue: 'local' }
        ]
      },
      {
        name: 'Run Test Suite',
        endpoint: 'CaseRunner',
        method: 'POST',
        description: 'Run a test suite',
        parameters: [
          { name: 'uid', description: 'Username (email)', required: true, type: 'string', defaultValue: '<EMAIL>' },
          { name: 'password', description: 'Password', required: true, type: 'string', defaultValue: 'test' },
          { name: 'ts_id', description: 'Test suite ID', required: true, type: 'number', defaultValue: 312 },
          { name: 'envir', description: 'Environment', required: true, type: 'string', defaultValue: 'qa02' },
          { name: 'shell_host', description: 'Shell host', required: true, type: 'string', defaultValue: 'jps-qa10-app01' },
          { name: 'file_path', description: 'File path', required: true, type: 'string', defaultValue: '/home/<USER>/' },
          { name: 'operatorConfigs', description: 'Operator configurations', required: true, type: 'string', defaultValue: 'operatorNameConfigs' },
          { name: 'kafka_server', description: 'Kafka server', required: true, type: 'string', defaultValue: 'kafka-qa-a0.lab.wagerworks.com' },
          { name: 'dataCenter', description: 'Data center', required: true, type: 'string', defaultValue: 'GU' },
          { name: 'rgs_env', description: 'RGS environment', required: true, type: 'string', defaultValue: 'qa02' },
          { name: 'old_version', description: 'Old version flag', required: true, type: 'string', defaultValue: '0' },
          { name: 'networkType1', description: 'Network type 1', required: true, type: 'string', defaultValue: 'multi-site' },
          { name: 'networkType2', description: 'Network type 2', required: true, type: 'string', defaultValue: 'multi-site' },
          { name: 'sign', description: 'Sign parameter', required: true, type: 'string', defaultValue: '-' },
          { name: 'rate_src', description: 'Rate source', required: true, type: 'string', defaultValue: 'local' }
        ]
      }
    ];
    
    // Discovered endpoints (will be populated during exploration)
    this.discoveredEndpoints = [];
  }
  
  /**
   * Set the authentication credentials
   * @param {string} username - Username (email)
   * @param {string} password - Password
   */
  setCredentials(username, password) {
    this.credentials.uid = username;
    this.credentials.password = password;
  }
  
  /**
   * Test an endpoint with the given parameters
   * @param {Object} endpoint - Endpoint configuration
   * @param {Object} params - Parameters for the endpoint
   * @returns {Promise<any>} - API response
   */
  async testEndpoint(endpoint, params = {}) {
    // Add credentials to params if not present
    if (!params.uid) params.uid = this.credentials.uid;
    if (!params.password) params.password = this.credentials.password;
    
    // Get the endpoint path with parameter substitution
    let path = endpoint.endpoint;
    
    // Handle path parameters
    path = path.replace(/:(\w+)/g, (match, paramName) => {
      if (params[paramName]) {
        const value = params[paramName];
        delete params[paramName]; // Remove from query params
        return value;
      }
      return match;
    });
    
    // Prepare the URL
    const url = new URL(path, this.baseUrl);
    
    try {
      let response;
      
      if (endpoint.method === 'GET') {
        // Add params to URL for GET requests
        Object.keys(params).forEach(key => {
          if (params[key] !== undefined && params[key] !== '') {
            url.searchParams.append(key, params[key]);
          }
        });
        
        response = await fetch(url, {
          method: 'GET',
          headers: {
            'Accept': 'application/json'
          }
        });
      } else {
        // For POST requests, send params in the body
        const formData = new URLSearchParams();
        Object.keys(params).forEach(key => {
          if (params[key] !== undefined && params[key] !== '') {
            formData.append(key, params[key]);
          }
        });
        
        response = await fetch(url, {
          method: endpoint.method,
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Accept': 'application/json'
          },
          body: formData
        });
      }
      
      // Parse the response
      let result;
      const contentType = response.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        result = await response.json();
      } else {
        result = await response.text();
      }
      
      // Return the result
      return {
        status: response.status,
        ok: response.ok,
        data: result
      };
    } catch (error) {
      console.error('Error testing endpoint:', error);
      return {
        status: 0,
        ok: false,
        error: error.message
      };
    }
  }
  
  /**
   * Explore available API endpoints
   * @returns {Promise<Array>} - Discovered endpoints
   */
  async exploreEndpoints() {
    // Initialize results
    this.discoveredEndpoints = [];
    
    // First, check the known endpoints
    for (const endpoint of this.knownEndpoints) {
      console.log(`Testing known endpoint: ${endpoint.method} ${endpoint.endpoint}`);
      
      // Create basic params with just credentials
      const params = {
        uid: this.credentials.uid,
        password: this.credentials.password
      };
      
      // Add required parameters with dummy values
      endpoint.parameters.forEach(param => {
        if (param.required && param.name !== 'uid' && param.name !== 'password') {
          if (param.defaultValue !== undefined) {
            params[param.name] = param.defaultValue;
          } else if (param.type === 'number') {
            params[param.name] = 1;
          } else if (param.name.includes('sessionId') || param.name.includes('id')) {
            params[param.name] = 'test-session-id';
          } else if (param.name === 'test_type') {
            params[param.name] = 'smoke';
          } else if (param.name === 'tc_id') {
            params[param.name] = 3180; // From example
          } else if (param.name === 'ts_id') {
            params[param.name] = 312; // From example
          } else if (param.name === 'query') {
            params[param.name] = 'SELECT * FROM test';
          } else if (param.name === 'execution_time') {
            params[param.name] = 100;
          } else if (param.name === 'status') {
            params[param.name] = 'success';
          } else {
            params[param.name] = 'test-value';
          }
        }
      });
      
      // Test the endpoint
      const result = await this.testEndpoint(endpoint, params);
      
      // Add to discovered endpoints
      this.discoveredEndpoints.push({
        ...endpoint,
        verified: result.ok,
        lastResult: result
      });
    }
    
    return this.discoveredEndpoints;
  }
  
  /**
   * Generate API service code based on discovered endpoints
   * @returns {string} - Generated code
   */
  generateApiServiceCode() {
    // Filter only verified endpoints
    const verifiedEndpoints = this.discoveredEndpoints.filter(e => e.verified);
    
    // Generate the code
    let code = `/**
 * Generated API Service for SmartTest
 * 
 * This service provides methods to interact with the SmartTest API.
 */
class ApiService {
  constructor() {
    this.baseUrl = '${this.baseUrl}';
    this.credentials = {
      uid: '${this.credentials.uid}',
      password: '${this.credentials.password}'
    };
  }
  
  /**
   * Set the authentication credentials
   * @param {string} username - Username (email)
   * @param {string} password - Password
   */
  setCredentials(username, password) {
    this.credentials.uid = username;
    this.credentials.password = password;
  }
  
`;
    
    // Generate methods for each endpoint
    verifiedEndpoints.forEach(endpoint => {
      // Create method name
      let methodName = endpoint.name
        .toLowerCase()
        .replace(/[^a-z0-9]+(.)/g, (match, char) => char.toUpperCase())
        .replace(/[^a-z0-9]/gi, '');
      methodName = methodName.charAt(0).toLowerCase() + methodName.slice(1);
      
      // Define parameters
      const uniqueParams = endpoint.parameters
        .filter(p => p.name !== 'uid' && p.name !== 'password')
        .filter((p, index, self) => self.findIndex(t => t.name === p.name) === index);
      
      // Create method signature
      code += `  /**
   * ${endpoint.description}
   * ${uniqueParams.map(p => `* @param {${p.type === 'number' ? 'number' : 'string'}} ${p.name} - ${p.description}${p.required ? '' : ' (optional)'}`).join('\n   ')}
   * @returns {Promise<any>} - API response
   */
  async ${methodName}(${uniqueParams.map(p => p.required ? p.name : `${p.name} = undefined`).join(', ')}) {
    const params = {
      uid: this.credentials.uid,
      password: this.credentials.password,
      ${uniqueParams.map(p => `${p.name}: ${p.name}`).join(',\n      ')}
    };
    
    return this.${endpoint.method === 'GET' ? 'getRequest' : 'postRequest'}('${endpoint.endpoint}', params);
  }
  
`;
    });
    
    // Add helper methods
    code += `  /**
   * Make a GET request
   * @param {string} endpoint - API endpoint
   * @param {Object} params - Request parameters
   * @returns {Promise<any>} - API response
   */
  async getRequest(endpoint, params = {}) {
    // Handle path parameters
    let path = endpoint;
    path = path.replace(/:(\w+)/g, (match, paramName) => {
      if (params[paramName]) {
        const value = params[paramName];
        delete params[paramName]; // Remove from query params
        return value;
      }
      return match;
    });
    
    // Add credentials to params if not already present
    if (!params.uid) params.uid = this.credentials.uid;
    if (!params.password) params.password = this.credentials.password;
    
    const url = new URL(path, this.baseUrl);
    
    // Add params to URL
    Object.keys(params).forEach(key => 
      params[key] !== undefined && url.searchParams.append(key, params[key])
    );
    
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Accept': 'application/json, text/html'
      }
    });
    
    return this.handleResponse(response);
  }
  
  /**
   * Make a POST request
   * @param {string} endpoint - API endpoint
   * @param {Object} params - Request parameters
   * @returns {Promise<any>} - API response
   */
  async postRequest(endpoint, params = {}) {
    // Handle path parameters
    let path = endpoint;
    path = path.replace(/:(\w+)/g, (match, paramName) => {
      if (params[paramName]) {
        const value = params[paramName];
        delete params[paramName]; // Remove from query params
        return value;
      }
      return match;
    });
    
    // Add credentials to params if not already present
    if (!params.uid) params.uid = this.credentials.uid;
    if (!params.password) params.password = this.credentials.password;
    
    const url = new URL(path, this.baseUrl);
    
    const formData = new URLSearchParams();
    Object.keys(params).forEach(key => 
      params[key] !== undefined && formData.append(key, params[key])
    );
    
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json, text/html'
      },
      body: formData
    });
    
    return this.handleResponse(response);
  }
  
  /**
   * Handle API response
   * @param {Response} response - Fetch API response
   * @returns {Promise<any>} - Processed response
   */
  async handleResponse(response) {
    if (!response.ok) {
      const error = await response.json().catch(() => ({ message: 'Unknown error' }));
      
      throw new Error(error.message || \`HTTP error \${response.status}\`);
    }
    
    // Try to parse as JSON, fallback to text
    const contentType = response.headers.get('content-type');
    if (contentType && contentType.includes('application/json')) {
      return response.json();
    } else {
      return response.text();
    }
  }
}

// Export a singleton instance
const apiService = new ApiService();
window.apiService = apiService;`;
    
    return code;
  }
}

// Make the class available globally
window.ApiExplorer = ApiExplorer; 
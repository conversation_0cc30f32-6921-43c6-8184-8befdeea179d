/* Test Details Modal Styles - Fluent UI Inspired */

/* Modal overlay */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    animation: fadeIn 0.3s ease-in-out;
    font-family: 'Segoe UI', -apple-system, BlinkMacSystemFont, 'Roboto', 'Helvetica Neue', sans-serif;
}

.modal.show {
    display: block;
}

/* Modal content container */
.modal-content {
    background-color: #ffffff;
    margin: 5% auto;
    padding: 0;
    border: none;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1), 0 8px 24px rgba(0, 0, 0, 0.15);
    max-width: 800px;
    max-height: 85vh;
    overflow-y: auto;
    animation: slideIn 0.3s ease-out;
}

/* Modal header */
.modal-header {
    background: #0078d4;
    color: white;
    padding: 16px 20px;
    border-radius: 8px 8px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    letter-spacing: -0.02em;
}

/* Close button */
.close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 28px;
    font-weight: 300;
    cursor: pointer;
    padding: 0;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
    opacity: 0.8;
}

.close-btn:hover {
    background-color: rgba(255, 255, 255, 0.1);
    opacity: 1;
    transform: scale(1.1);
}

/* Modal body */
.modal-body {
    padding: 16px;
}

/* Test details grid */
.test-details-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 0;
}

/* Detail items */
.detail-item {
    background-color: #f8f9fa;
    padding: 16px;
    border-radius: 6px;
    border-left: 4px solid #0078d4;
    transition: all 0.2s ease;
}

.detail-item:hover {
    background-color: #f3f2f1;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.detail-item.full-width {
    grid-column: 1 / -1;
}

.detail-item strong {
    display: block;
    color: #323130;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 8px;
    opacity: 0.8;
}

.detail-item span {
    display: block;
    color: #201f1e;
    font-size: 14px;
    font-weight: 400;
    line-height: 1.4;
}

/* Status badges */
.status-badge {
    display: inline-block;
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-badge.status-passed {
    background-color: #dff6dd;
    color: #107c10;
    border: 1px solid #107c10;
}

.status-badge.status-failed {
    background-color: #fde7e9;
    color: #e81123;
    border: 1px solid #e81123;
}

.status-badge.status-running {
    background-color: #fff4ce;
    color: #797673;
    border: 1px solid #797673;
}

.status-badge.status-queued {
    background-color: #e1f5fe;
    color: #0078d4;
    border: 1px solid #0078d4;
}

.status-badge.status-unknown {
    background-color: #f3f2f1;
    color: #605e5c;
    border: 1px solid #605e5c;
}

/* Primary button */
.ms-Button--primary {
    background-color: #0078d4;
    border: 1px solid #0078d4;
    color: white;
    padding: 8px 16px;
    border-radius: 4px;
    text-decoration: none;
    font-size: 14px;
    font-weight: 600;
    transition: all 0.2s ease;
    display: inline-block;
}

.ms-Button--primary:hover {
    background-color: #106ebe;
    border-color: #106ebe;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 120, 212, 0.3);
}

/* Loading spinner */
.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #0078d4;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 16px;
}

.loading-text {
    color: #605e5c;
    font-size: 16px;
    margin: 0;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from {
        transform: translateY(-50px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive design */
@media (max-width: 768px) {
    .modal-content {
        margin: 10px;
        max-width: calc(100% - 20px);
        max-height: calc(100vh - 20px);
    }
    
    .test-details-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }
    
    .modal-header {
        padding: 20px 24px 12px;
    }
    
    .modal-body {
        padding: 20px 24px 24px;
    }
    
    .detail-item {
        padding: 12px;
    }
}

/* Enhanced Design Styles */

/* New Enhanced Container */
.test-details-container {
padding: 24px;
}

/* Test header */
.test-header {
display: flex;
justify-content: space-between;
align-items: center;
margin-bottom: 16px;
padding-bottom: 16px;
border-bottom: 1px solid #edebe9;
}

.test-title h4 {
margin: 0 0 4px 0;
font-size: 1.25rem;
font-weight: 600;
color: #323130;
line-height: 1.2;
}

.test-id {
font-size: 0.8125rem;
color: #605e5c;
font-weight: 400;
padding: 2px 8px;
border-radius: 4px;
display: inline-block;
background: #f3f2f1;
}

.status-container {
flex-shrink: 0;
}

/* Status Badge - Refined pill design */
.status-badge {
    padding: 6px 12px;
    border-radius: 16px;
    font-weight: 600;
    font-size: 0.8125rem;
    text-transform: uppercase;
    letter-spacing: 0.4px;
    display: inline-flex;
    align-items: center;
    gap: 6px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
}

.status-badge.passed {
    background-color: #dff6dd;
    color: #107c10;
}

.status-badge.failed {
    background-color: #fde7e9;
    color: #a4262c;
}

.status-badge.running {
    background-color: #fff4ce;
    color: #797673;
}

.status-badge.queued {
    background-color: #e1f5fe;
    color: #0078d4;
}

.status-badge.unknown {
    background-color: #f3f2f1;
    color: #605e5c;
}

.status-icon {
    font-size: 1.1em;
    font-weight: bold;
}

/* Metrics Strip - Horizontal compact design */
.metrics-strip {
    display: flex;
    align-items: center;
    background: #f9f9f9;
    border-radius: 8px;
    padding: 12px 16px;
    margin-bottom: 20px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

.metric-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0 12px;
    flex: 1;
    text-align: center;
}

.metric-divider {
    height: 24px;
    width: 1px;
    background-color: #e1e1e1;
}

.metric-item.duration {
    color: #0078d4;
}

.metric-item.passed {
    color: #107c10;
}

.metric-item.failed {
    color: #a4262c;
}

.metric-item.total {
    color: #605e5c;
}

.metric-icon {
    font-size: 1rem;
    margin-bottom: 2px;
}

.metric-value {
    font-size: 1.125rem;
    font-weight: 600;
    line-height: 1.2;
    margin: 2px 0;
}

.metric-label {
    font-size: 0.75rem;
    color: #605e5c;
    text-transform: uppercase;
    letter-spacing: 0.3px;
}

/* Single column layout for consolidated info */
.details-section {
    margin-bottom: 16px;
}

/* Section Styles */
.details-section,
.timeline-section,
.comments-section {
    background: #ffffff;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 16px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    border: 1px solid #edebe9;
}

.details-section h5,
.timeline-section h5,
.comments-section h5 {
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 0 0 12px 0;
    font-size: 0.9375rem;
    font-weight: 600;
    color: #323130;
    padding-bottom: 8px;
    border-bottom: 1px solid #edebe9;
}

.section-icon {
    font-size: 1.2em;
}

/* Details Grid */
.details-grid {
    display: grid;
    gap: 8px;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: #faf9f8;
    border-radius: 4px;
    border-left: 2px solid #edebe9;
    transition: all 0.2s ease;
}

.detail-row:hover {
    background: #f3f2f1;
    border-left-color: #0078d4;
}

.detail-label {
    font-weight: 600;
    color: #323130;
    font-size: 0.8125rem;
}

.detail-value {
    font-weight: 400;
    color: #323130;
    text-align: right;
    font-size: 0.8125rem;
}

/* Special field formats */
.environment-tag {
    background: #deecf9;
    color: #0078d4;
    padding: 2px 8px;
    border-radius: 2px;
    font-size: 0.75rem;
    font-weight: 600;
}

.session-id {
    font-family: 'Segoe UI Mono', Consolas, 'Courier New', monospace;
    background: #f3f2f1;
    padding: 2px 6px;
    border-radius: 2px;
    font-size: 0.75rem;
}

.user-email {
    color: #0078d4;
}

/* Execution Summary - replaces timeline */
.execution-summary {
    display: flex;
    align-items: center;
    background: #f9f9f9;
    border-radius: 8px;
    padding: 12px 16px;
    margin-bottom: 20px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

.summary-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0 12px;
    flex: 1;
    text-align: center;
}

.summary-divider {
    height: 24px;
    width: 1px;
    background-color: #e1e1e1;
}

.summary-label {
    font-size: 0.75rem;
    color: #605e5c;
    text-transform: uppercase;
    letter-spacing: 0.3px;
    margin-bottom: 4px;
}

.summary-value {
    font-size: 0.875rem;
    font-weight: 600;
    color: #323130;
    line-height: 1.2;
}

/* Comments */
.comment-box {
    background: #faf9f8;
    border: 1px solid #edebe9;
    border-radius: 4px;
    padding: 12px;
    color: #605e5c;
    line-height: 1.5;
    font-style: italic;
    font-size: 0.875rem;
}

/* Actions */
.actions-section {
    text-align: center;
    margin-bottom: 16px;
}

.action-button {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    background: #0078d4;
    color: white;
    text-decoration: none;
    border-radius: 2px;
    font-weight: 600;
    transition: all 0.2s ease;
    font-size: 0.875rem;
}

.action-button:hover {
    background: #106ebe;
    text-decoration: none;
    color: white;
}

/* Add timestamps grid styles */
.timestamps-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
}

.timestamp-item {
    padding: 8px;
    background: #faf9f8;
    border-radius: 4px;
    display: flex;
    flex-direction: column;
}

.timestamp-label {
    font-size: 0.75rem;
    color: #605e5c;
    margin-bottom: 4px;
}

.timestamp-value {
    font-weight: 600;
    font-size: 0.8125rem;
    color: #323130;
}

/* Enhanced Responsive Design */
@media (max-width: 768px) {
    .test-header {
        flex-direction: column;
        gap: 12px;
        align-items: flex-start;
    }
    
    .info-panels {
        grid-template-columns: 1fr;
    }
    
    .metrics-strip {
        flex-wrap: wrap;
    }
    
    .metric-item {
        flex-basis: 50%;
        padding: 8px 4px;
    }
    
    .metric-divider:nth-child(4) {
        display: none;
    }
    
    .test-details-container {
        padding: 12px;
    }
    
    .timestamps-grid {
        grid-template-columns: 1fr;
    }
}

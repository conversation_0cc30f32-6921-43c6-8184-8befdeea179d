/**
 * Config API Service - Unified Implementation
 *
 * Direct replacement of the original config API service
 * using the unified service with config context.
 */

console.log('=== API SERVICE SCRIPT STARTING ===');

// Log the current state before we do anything
console.log('API Service initialization - existing window.apiService:', window.apiService);
console.log('API Service initialization - existing window.UnifiedApiService:', window.UnifiedApiService);

// Wait for UnifiedApiService to be available
function setupApiService() {
  if (window.UnifiedApiService) {
    console.log('Setting up apiService using global UnifiedApiService');

    // Create a new instance if one doesn't exist
    if (!window.apiService) {
      console.log('Creating new UnifiedApiService instance');
      window.apiService = new window.UnifiedApiService();
    }

    // Set the module context
    window.apiService.moduleContext = 'config';

    // Initialize if the method exists
    if (typeof window.apiService.initializeConfiguration === 'function') {
      window.apiService.initializeConfiguration();
    }

    console.log('apiService is now configured and available globally');
    console.log('apiService methods:', Object.keys(window.apiService));
    console.log('searchTestCases available:', typeof window.apiService.searchTestCases === 'function');

    // Dispatch ready event
    document.dispatchEvent(new CustomEvent('apiservice-ready', {
      detail: { apiService: window.apiService }
    }));

    return true;
  }
  return false;
}

// Try to setup immediately
if (!setupApiService()) {
  console.log('UnifiedApiService not ready yet, will retry...');
  // If not ready, wait a bit and try again
  setTimeout(() => {
    if (!setupApiService()) {
      console.error('UnifiedApiService still not available after waiting');
    }
  }, 100);
}

console.log('API Service setup complete - window.apiService should now be available');
function parseReportSummaryHtml(html, testId) {
    try {
        const parser = new DOMParser();
        const doc = parser.parseFromString(html, 'text/html');

        // Extract basic info
        const summary = {
            tsn_id: testId,
            status: 'Unknown',
            start_time: null,
            end_time: null,
            owner: null,
            suite_id: null,
            suite_name: null,
            environment: null,
            passed_cases: 0,
            failed_cases: 0,
            skipped_cases: 0,
            variables: {}
        };

        // Extract text content from the document
        const textContent = doc.body.textContent;
        console.log('Parsing summary HTML for test ID:', testId);

        // Extract test session ID
        const sessionIdMatch = textContent.match(/Test Session ID:\s*([0-9]+)/);
        if (sessionIdMatch) {
            summary.tsn_id = sessionIdMatch[1].trim();
            console.log('Found session ID:', summary.tsn_id);
        }

        // Extract owner
        const ownerMatch = textContent.match(/Session Owner:\s*([^\n]+)/);
        if (ownerMatch) {
            summary.owner = ownerMatch[1].trim();
            console.log('Found owner:', summary.owner);
        }

        // Extract suite ID
        // Look for Test Suite ID in the text, including the link if present
        const suiteIdLink = doc.querySelector('a[href*="SuiteEditor"]');
        if (suiteIdLink) {
            const href = suiteIdLink.getAttribute('href');
            const suiteIdMatch = href.match(/[?&]t[cs]_id=([0-9]+)/);
            if (suiteIdMatch) {
                summary.suite_id = suiteIdMatch[1];
                console.log('Found suite ID from link:', summary.suite_id);
            }
        } else {
            // Fallback to text parsing
            const suiteIdMatch = textContent.match(/Test Suite ID:\s*([0-9]+)/);
            if (suiteIdMatch) {
                summary.suite_id = suiteIdMatch[1].trim();
                console.log('Found suite ID from text:', summary.suite_id);
            }
        }

        // Extract suite name from the report section
        // Look for the suite name in the Report section using the DOM
        const reportSection = textContent.substring(textContent.indexOf('Report'));
        const suiteMatch = reportSection.match(/Suite:\s*[^\n]*?([0-9]+)[^\n]*?\s+([^<\n]+)/);
        if (suiteMatch && suiteMatch.length > 2) {
            summary.suite_name = suiteMatch[2].trim();
            console.log('Found suite name:', summary.suite_name);

            // If we didn't find suite ID earlier, use it from here
            if (!summary.suite_id && suiteMatch[1]) {
                summary.suite_id = suiteMatch[1].trim();
                console.log('Using suite ID from report section:', summary.suite_id);
            }
        }

        // Extract times
        const startTimeMatch = textContent.match(/Start Time:\s*([^\n]+)/);
        if (startTimeMatch) {
            summary.start_time = startTimeMatch[1].trim();
            console.log('Found start time:', summary.start_time);
        }

        const endTimeMatch = textContent.match(/End Time:\s*([^\n]+)/);
        if (endTimeMatch) {
            summary.end_time = endTimeMatch[1].trim();
            console.log('Found end time:', summary.end_time);
        }

        // Extract error information if present
        const errorMatch = textContent.match(/Error:\s*([^\n]+)/);
        if (errorMatch) {
            summary.error = errorMatch[1].trim();
            console.log('Found error info:', summary.error);
        }

        // Determine overall status
        if (textContent.includes('<span style=\'color:green\'>PASS</span>') && !textContent.includes('<span style=\'color:red\'>FAIL</span>')) {
            summary.status = 'Success';
        } else if (textContent.includes('<span style=\'color:red\'>FAIL</span>')) {
            summary.status = 'Failed';
        } else if (textContent.includes('PASS') && !textContent.includes('FAIL')) {
            summary.status = 'Success';
        } else if (textContent.includes('FAIL')) {
            summary.status = 'Failed';
        }
        console.log('Determined status:', summary.status);

        // Extract Variables
        // Use the raw HTML ('html' parameter) for this, not doc.body.textContent, to preserve line breaks and structure
        const variablesSectionMatch = html.match(/Variables:\s*<br\s*\/>([\s\S]*?)(\nID:|<p>\s*ID:|<br\s*\/>\s*<br\s*\/>\s*ID:)/i);
        if (variablesSectionMatch && variablesSectionMatch[1]) {
            const variablesString = variablesSectionMatch[1];
            const lines = variablesString.split(/<br\s*\/?>/i); // Split by <br> or <br/>, case-insensitive
            lines.forEach(line => {
                const trimmedLine = line.trim();
                if (trimmedLine) { // Ensure line is not empty after trimming
                    const parts = trimmedLine.split('=');
                    if (parts.length >= 2) {
                        const key = parts[0].trim();
                        const value = parts.slice(1).join('=').trim();
                        if (key) { // Ensure key is not empty
                            summary.variables[key] = value;
                        }
                    }
                }
            });
            console.log('Found and parsed variables:', summary.variables);
        } else {
            console.log('No "Variables:" section found or it is empty in summary HTML, or terminator not found.');
        }

        // Extract case counts
        const passedMatch = textContent.match(/Case\(s\) passed:\s*([0-9]+)/);
        if (passedMatch) {
            summary.passed_cases = parseInt(passedMatch[1], 10);
            console.log('Found passed cases:', summary.passed_cases);
        }

        const failedMatch = textContent.match(/Case\(s\) failed:\s*([0-9]+)/);
        if (failedMatch) {
            summary.failed_cases = parseInt(failedMatch[1], 10);
            console.log('Found failed cases:', summary.failed_cases);
        }
        
        // Extract individual test case information from the HTML content
        // Look for specific patterns like "PASS Case: 2670" or "FAIL Case: 2671"
        const caseMatches = doc.body.innerHTML.match(/<span style=['|"]color:(?:green|red)['|"]>(PASS|FAIL)<\/span> Case: <a[^>]*>(\d+)<\/a>([^<]+)/g);
        
        if (caseMatches && caseMatches.length > 0) {
            console.log(`Found ${caseMatches.length} test cases in HTML`);
            summary.test_cases = [];
            
            caseMatches.forEach(match => {
                const statusMatch = match.match(/<span style=['|"]color:(green|red)['|"]>(PASS|FAIL)<\/span>/);
                const idMatch = match.match(/<a[^>]*>(\d+)<\/a>/);
                const descMatch = match.match(/<\/a>([^<]+)/);
                
                if (statusMatch && idMatch) {
                    const status = statusMatch[2]; // PASS or FAIL
                    const tc_id = idMatch[1]; // Test case ID
                    const description = descMatch ? descMatch[1].trim() : '';
                    
                    summary.test_cases.push({
                        tc_id: tc_id,
                        status: status === 'PASS' ? 'Success' : 'Failed',
                        description: description,
                        hasFailures: status === 'FAIL'
                    });
                    
                    console.log(`Extracted test case: ID ${tc_id}, Status: ${status}, Description: ${description.substring(0, 30)}...`);
                }
            });
        } else {
            console.log('No individual test cases found in HTML');
        }

        // Extract environment
        const envMatch = textContent.match(/environment=([^\n]+)/);
        if (envMatch) {
            summary.environment = envMatch[1].trim();
            console.log('Found environment:', summary.environment);
        } else {
            // Try to get environment from variables
            const envirMatch = textContent.match(/envir=([^\n]+)/);
            if (envirMatch) {
                summary.environment = envirMatch[1].trim();
                console.log('Found environment from envir variable:', summary.environment);
            }
        }

        // Calculate total cases
        summary.total_cases = summary.passed_cases + summary.failed_cases + summary.skipped_cases;
        console.log('Total cases:', summary.total_cases);

        // Extract variables section
        const variablesMatch = textContent.match(/Variables:([\s\S]*?)(?:ID:|$)/);
        if (variablesMatch && variablesMatch[1]) {
            const variableText = variablesMatch[1];
            const variableLines = variableText.split('\n');

            variableLines.forEach(line => {
                const varMatch = line.match(/([^=]+)=([^\n]+)/);
                if (varMatch && varMatch.length > 2) {
                    const key = varMatch[1].trim();
                    const value = varMatch[2].trim();
                    summary.variables[key] = value;
                }
            });

            // Look for test name in variables if we didn't find it in the suite section
            if (!summary.suite_name && summary.variables['name']) {
                summary.suite_name = summary.variables['name'];
                console.log('Using suite name from variables:', summary.suite_name);
            }
        }

        return summary;
    } catch (error) {
        console.error('Error parsing report summary HTML:', error);
        return { tsn_id: testId, error: error.message };
    }
}

/**
 * Parse HTML from the Report Details endpoint
 * @param {string} html - The HTML content to parse
 * @param {string} testId - The test ID
 * @returns {Object} The parsed details data
 */

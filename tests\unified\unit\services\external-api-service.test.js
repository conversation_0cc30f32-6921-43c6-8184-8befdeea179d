/**
 * Unit Tests for External API Service
 * 
 * Tests the external API service that handles:
 * - Authentication with external systems
 * - Test data retrieval from external APIs
 * - Error handling and retry logic
 * 
 * Migrated and enhanced from:
 * - frontend/reports/tests/external-api-service.*.test.js
 */

const { MockServiceFactory, TestDataGenerator, AssertionHelpers } = require('../../mocks/test-helpers');
const apiResponses = require('../../mocks/api-responses');

// Mock axios
jest.mock('axios');

describe('External API Service', () => {
  let externalApiService;
  let mockAxios;

  beforeEach(() => {
    // Create fresh mocks for each test
    mockAxios = require('axios');
    externalApiService = MockServiceFactory.createExternalApiService();
    
    // Reset all mocks
    jest.clearAllMocks();
  });

  describe('Authentication', () => {
    test('should authenticate successfully', async () => {
      const mockAuthResponse = apiResponses.externalApi.authResponse;
      externalApiService.authenticate.mockResolvedValue(apiResponses.success(mockAuthResponse));

      const response = await externalApiService.authenticate('username', 'password');

      expect(externalApiService.authenticate).toHaveBeenCalledWith('username', 'password');
      AssertionHelpers.assertApiResponse(response, mockAuthResponse);
      
      // Validate auth response structure
      expect(response.data).toHaveProperty('success', true);
      expect(response.data).toHaveProperty('token');
      expect(response.data).toHaveProperty('expires_in');
      expect(response.data).toHaveProperty('user');
    });

    test('should handle authentication failure', async () => {
      const authError = apiResponses.error('Invalid credentials', 401);
      externalApiService.authenticate.mockRejectedValue(authError);

      await expect(externalApiService.authenticate('invalid', 'credentials'))
        .rejects.toMatchObject({
          response: {
            status: 401,
            data: {
              error: 'Invalid credentials'
            }
          }
        });
    });

    test('should validate authentication parameters', async () => {
      await expect(externalApiService.authenticate('', '')).rejects.toThrow();
      await expect(externalApiService.authenticate(null, null)).rejects.toThrow();
    });
  });

  describe('Recent Runs API', () => {
    test('should fetch recent runs successfully', async () => {
      const mockRecentRuns = apiResponses.externalApi.recentRuns;
      externalApiService.getRecentRuns.mockResolvedValue(apiResponses.success(mockRecentRuns));

      const response = await externalApiService.getRecentRuns();

      expect(externalApiService.getRecentRuns).toHaveBeenCalledTimes(1);
      AssertionHelpers.assertApiResponse(response, mockRecentRuns);
      
      // Validate recent runs structure
      expect(Array.isArray(response.data)).toBe(true);
      response.data.forEach(run => {
        expect(run).toHaveProperty('tsn_id');
        expect(run).toHaveProperty('test_id');
        expect(run).toHaveProperty('type');
        expect(run).toHaveProperty('environment');
        expect(run).toHaveProperty('status');
        expect(run).toHaveProperty('start_time');
        expect(run).toHaveProperty('end_time');
        expect(run).toHaveProperty('duration');
        expect(run).toHaveProperty('total_cases');
        expect(run).toHaveProperty('passed_cases');
        expect(run).toHaveProperty('failed_cases');
        expect(run).toHaveProperty('pass_rate');
      });
    });

    test('should fetch recent runs with limit parameter', async () => {
      const limit = 5;
      const limitedRuns = apiResponses.externalApi.recentRuns.slice(0, limit);
      externalApiService.getRecentRuns.mockResolvedValue(apiResponses.success(limitedRuns));

      const response = await externalApiService.getRecentRuns(limit);

      expect(externalApiService.getRecentRuns).toHaveBeenCalledWith(limit);
      expect(response.data).toHaveLength(limit);
    });

    test('should handle recent runs fetch error', async () => {
      const errorMessage = 'External API unavailable';
      externalApiService.getRecentRuns.mockRejectedValue(apiResponses.error(errorMessage, 503));

      await expect(externalApiService.getRecentRuns()).rejects.toMatchObject({
        response: {
          status: 503,
          data: {
            error: errorMessage
          }
        }
      });
    });
  });

  describe('Test Details API', () => {
    test('should fetch test details successfully', async () => {
      const testId = '3180';
      const mockDetails = apiResponses.externalApi.testDetails;
      externalApiService.getTestDetails.mockResolvedValue(apiResponses.success(mockDetails));

      const response = await externalApiService.getTestDetails(testId);

      expect(externalApiService.getTestDetails).toHaveBeenCalledWith(testId);
      AssertionHelpers.assertApiResponse(response, mockDetails);
      
      // Validate test details structure
      expect(response.data).toHaveProperty('test_id', testId);
      expect(response.data).toHaveProperty('test_name');
      expect(response.data).toHaveProperty('description');
      expect(response.data).toHaveProperty('environment');
      expect(response.data).toHaveProperty('status');
      expect(response.data).toHaveProperty('steps');
      expect(Array.isArray(response.data.steps)).toBe(true);
      
      // Validate steps structure
      response.data.steps.forEach(step => {
        expect(step).toHaveProperty('step_id');
        expect(step).toHaveProperty('description');
        expect(step).toHaveProperty('status');
        expect(step).toHaveProperty('duration');
      });
    });

    test('should handle test details not found', async () => {
      const testId = 'nonexistent';
      const notFoundError = apiResponses.error('Test not found', 404);
      externalApiService.getTestDetails.mockRejectedValue(notFoundError);

      await expect(externalApiService.getTestDetails(testId)).rejects.toMatchObject({
        response: {
          status: 404,
          data: {
            error: 'Test not found'
          }
        }
      });
    });
  });

  describe('Summary API', () => {
    test('should fetch summary successfully', async () => {
      const mockSummary = apiResponses.externalApi.testSummary;
      externalApiService.getSummary.mockResolvedValue(apiResponses.success(mockSummary));

      const response = await externalApiService.getSummary();

      expect(externalApiService.getSummary).toHaveBeenCalledTimes(1);
      AssertionHelpers.assertApiResponse(response, mockSummary);
      
      // Validate summary structure
      expect(response.data).toHaveProperty('total_tests');
      expect(response.data).toHaveProperty('passed_tests');
      expect(response.data).toHaveProperty('failed_tests');
      expect(response.data).toHaveProperty('pass_rate');
      expect(response.data).toHaveProperty('last_updated');
      expect(response.data).toHaveProperty('environments');
      
      // Validate numeric values
      expect(typeof response.data.total_tests).toBe('number');
      expect(typeof response.data.passed_tests).toBe('number');
      expect(typeof response.data.failed_tests).toBe('number');
      expect(typeof response.data.pass_rate).toBe('number');
      
      // Validate pass rate calculation
      const expectedPassRate = (response.data.passed_tests / response.data.total_tests) * 100;
      expect(response.data.pass_rate).toBeCloseTo(expectedPassRate, 2);
    });

    test('should handle summary fetch error', async () => {
      const errorMessage = 'Summary calculation failed';
      externalApiService.getSummary.mockRejectedValue(apiResponses.error(errorMessage));

      await expect(externalApiService.getSummary()).rejects.toMatchObject({
        response: {
          data: {
            error: errorMessage
          }
        }
      });
    });
  });

  describe('Connection Management', () => {
    test('should check connection status', () => {
      expect(externalApiService.isConnected()).toBe(true);
    });

    test('should test connection successfully', async () => {
      externalApiService.testConnection.mockResolvedValue(true);

      const result = await externalApiService.testConnection();

      expect(result).toBe(true);
      expect(externalApiService.testConnection).toHaveBeenCalledTimes(1);
    });

    test('should handle connection test failure', async () => {
      externalApiService.testConnection.mockResolvedValue(false);

      const result = await externalApiService.testConnection();

      expect(result).toBe(false);
    });

    test('should handle connection test error', async () => {
      const connectionError = new Error('Connection failed');
      externalApiService.testConnection.mockRejectedValue(connectionError);

      await expect(externalApiService.testConnection()).rejects.toThrow('Connection failed');
    });
  });

  describe('Error Handling and Retry Logic', () => {
    test('should handle rate limiting', async () => {
      const rateLimitError = apiResponses.error('Rate limit exceeded', 429);
      externalApiService.getRecentRuns.mockRejectedValue(rateLimitError);

      await expect(externalApiService.getRecentRuns()).rejects.toMatchObject({
        response: {
          status: 429
        }
      });
    });

    test('should handle server errors', async () => {
      const serverError = apiResponses.error('Internal server error', 500);
      externalApiService.getRecentRuns.mockRejectedValue(serverError);

      await expect(externalApiService.getRecentRuns()).rejects.toMatchObject({
        response: {
          status: 500
        }
      });
    });

    test('should handle network timeouts', async () => {
      const timeoutError = new Error('timeout of 5000ms exceeded');
      timeoutError.code = 'ECONNABORTED';
      externalApiService.getRecentRuns.mockRejectedValue(timeoutError);

      await expect(externalApiService.getRecentRuns()).rejects.toThrow('timeout of 5000ms exceeded');
    });
  });
});

function setupEventListeners() {
    console.log('Setting up event listeners for reports page');

    // Time range dropdown event listeners
    const timeRangeMenu = document.querySelector('.dropdown-menu[aria-labelledby="timeRangeDropdown"]');
    const timeRangeDropdownButton = document.getElementById('timeRangeDropdown');
    const customRangeControls = document.getElementById('custom-range-controls');
    const customStartDateInput = document.getElementById('custom-start-date');
    const customEndDateInput = document.getElementById('custom-end-date');
    const applyCustomRangeBtn = document.getElementById('apply-custom-range');

    if (timeRangeMenu) {
        timeRangeMenu.addEventListener('click', e => {
            if (e.target.classList.contains('dropdown-item')) {
                e.preventDefault();
                const item = e.target;
                const range = item.getAttribute('data-range');
                const text = item.textContent;

                if (!range) return;

                // Update active state for all items
                timeRangeMenu.querySelectorAll('.dropdown-item').forEach(el => el.classList.remove('active'));
                item.classList.add('active');

                // Update button text
                if (timeRangeDropdownButton) {
                    timeRangeDropdownButton.textContent = text;
                }

                // Update global state
                currentState.activeTimeRange = range;

                // Load data for the new time range
                loadReportsData({
                    timeRange: range,
                    forceFullReload: true,
                    showLoading: true
                });
            }
        });
    }

    if (applyCustomRangeBtn && customStartDateInput && customEndDateInput) {
        applyCustomRangeBtn.addEventListener('click', function() {
            const startDate = customStartDateInput.value;
            const endDate = customEndDateInput.value;

            if (startDate && endDate) {
                if (new Date(startDate) > new Date(endDate)) {
                    alert('Start date cannot be after end date.');
                    return;
                }
                currentState.activeTimeRange = { type: 'custom', start: startDate, end: endDate };
                console.log('Custom time range applied:', currentState.activeTimeRange);
                if (timeRangeDropdownButton) {
                    timeRangeDropdownButton.textContent = `${startDate} to ${endDate}`;
                }
                loadReportsData({ forceFullReload: true, showLoading: true });
                if (customRangeControls) customRangeControls.style.display = 'none'; // Optionally hide after apply
            } else {
                alert('Please select both start and end dates for custom range.');
            }
        });
    }

    // Refresh button event listener
    if (elements.refreshBtn) {
        elements.refreshBtn.addEventListener('click', refreshReports);
    }

    // Export button event listener
    if (elements.exportBtn) {
        elements.exportBtn.addEventListener('click', function() {
            // TODO: Implement export functionality
            alert('Export functionality not yet implemented.');
        });
    }

    // Reset button event listener
    if (elements.resetButton) {
        elements.resetButton.addEventListener('click', function() {
            // Reset filters and reload data
            currentState.timeRange = '24h';

            // Update dropdown button text
            const dropdownButton = document.getElementById('timeRangeDropdown');
            if (dropdownButton) {
                dropdownButton.textContent = 'Last 24 Hours';
            }

            // Reload reports data
            loadReportsData();
        });
    }

    // Close details button event listener
    if (elements.closeDetailsBtn) {
        elements.closeDetailsBtn.addEventListener('click', function() {
            if (elements.testDetailsSection) {
                elements.testDetailsSection.classList.add('d-none');
            }
        });
    }

    // Timezone selector event listener
    const timezoneSelector = document.getElementById('timezone-selector');
    if (timezoneSelector) {
        timezoneSelector.addEventListener('change', function() {
            const selectedTimezone = this.value;
            saveSelectedTimezone(selectedTimezone);
            refreshAllTimestamps();
        });
    }

    console.log('Event listeners set up successfully');
}

// Display error message to the user

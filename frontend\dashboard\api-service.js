/**
 * Dashboard API Service - Unified Implementation
 *
 * Direct replacement of the original dashboard API service
 * using the unified service with dashboard context.
 */

// Use the globally available UnifiedApiService class
document.addEventListener('DOMContentLoaded', function() {
  try {
    // Check if UnifiedApiService is available globally
    if (window.UnifiedApiService) {
      console.log('Creating Dashboard API Service with global UnifiedApiService');
      
      // Create dashboard-specific instance
      const apiService = new window.UnifiedApiService();
      apiService.moduleContext = 'dashboard';
      apiService.initializeConfiguration();
      
      // Add test suite selection methods - Using frontend static mapping (no API calls)
      apiService.getTestSuites = async (filters = {}) => {
        console.log('Frontend: Getting test suites with filters (no API call):', filters);
        
        try {
          // Use frontend static mapping instead of API call
          if (window.FrontendTestSuiteService) {
            const suites = window.FrontendTestSuiteService.getTestSuites(filters);
            console.log(`Frontend: Returning ${suites.length} test suites from static mapping`);
            return suites;
          } else {
            console.error('FrontendTestSuiteService not available, falling back to empty array');
            return [];
          }
        } catch (error) {
          console.error('Error getting test suites from static mapping:', error);
          return [];
        }
      };
      
      // Add method to get filtered test suites (multi-select support) - Using frontend static mapping
      apiService.getFilteredTestSuites = async (level = '', version = '') => {
        console.log('Frontend: Getting filtered test suites (no API call):', { level, version });
        
        try {
          // Use frontend static mapping instead of API call
          if (window.FrontendTestSuiteService) {
            const suites = window.FrontendTestSuiteService.getFilteredTestSuites(level, version);
            console.log(`Frontend: Returning ${suites.length} filtered test suites from static mapping`);
            return suites;
          } else {
            console.error('FrontendTestSuiteService not available, falling back to empty array');
            return [];
          }
        } catch (error) {
          console.error('Error getting filtered test suites from static mapping:', error);
          return [];
        }
      };
      
      // Add method to get test suite by ID - Using frontend static mapping
      apiService.getTestSuiteById = async (ts_id) => {
        console.log('Frontend: Getting test suite by ID (no API call):', ts_id);
        
        try {
          // Use frontend static mapping instead of API call
          if (window.FrontendTestSuiteService) {
            const suite = window.FrontendTestSuiteService.getTestSuiteById(ts_id);
            if (suite) {
              console.log(`Frontend: Found test suite ${ts_id} from static mapping`);
              return suite;
            } else {
              throw new Error(`Test suite ${ts_id} not found in static mapping`);
            }
          } else {
            console.error('FrontendTestSuiteService not available');
            throw new Error('FrontendTestSuiteService not available');
          }
        } catch (error) {
          console.error('Error getting test suite by ID from static mapping:', error);
          throw error;
        }
      };
      
      // Add method to get filter options - Using frontend static mapping
      apiService.getFilterOptions = async () => {
        console.log('Frontend: Getting filter options (no API call)');
        
        try {
          // Use frontend static mapping instead of API call
          if (window.FrontendTestSuiteService) {
            const options = window.FrontendTestSuiteService.getFilterOptions();
            console.log('Frontend: Returning filter options from static mapping:', options);
            return options;
          } else {
            console.error('FrontendTestSuiteService not available, falling back to empty options');
            return { levels: [], versions: [] };
          }
        } catch (error) {
          console.error('Error getting filter options from static mapping:', error);
          return { levels: [], versions: [] };
        }
      };
      
      apiService.runTestSuite = async (suiteId) => {
        if (!suiteId) {
          throw new Error('Test suite ID is required');
        }
        
        console.log(`Running test suite with ID: ${suiteId}`);
        
        try {
          // Make the API request using the predefined endpoint
          const data = await apiService.postRequest('/api/suite-runner', { ts_id: suiteId });
          return {
            success: true,
            testId: data.testId,
            message: data.message
          };
        } catch (error) {
          console.error('Error running test suite:', error);
          throw error;
        }
      };
      
      // Add the missing login method that the frontend expects
      apiService.login = async (username, password) => {
        if (!username || !password) {
          throw new Error('Username and password are required');
        }
        
        console.log(`Attempting login for user: ${username}`);
        
        try {
          // Set credentials using the underlying UnifiedApiService method
          apiService.setCredentials(username, password);
          
          // For dashboard module, we consider login successful if credentials are set
          // The actual API authentication happens with each request
          console.log('Login successful, credentials set');
          
          // Dispatch the apiservice-ready event that other parts of the app expect
          setTimeout(() => {
            document.dispatchEvent(new CustomEvent('apiservice-ready', {
              detail: { apiService: apiService, username: username }
            }));
            console.log('Dispatched apiservice-ready event after login');
          }, 50);
          
          return {
            success: true,
            username: username,
            message: 'Login successful'
          };
        } catch (error) {
          console.error('Login failed:', error);
          throw new Error(`Login failed: ${error.message}`);
        }
      };
      
      // Make it globally available (preserving existing interface)
      window.apiService = apiService;
      
      console.log('Dashboard API Service (Unified) initialized successfully');
    } else {
      console.error('UnifiedApiService not found! Make sure unified-api-service.js is loaded before this file.');
    }
  } catch (error) {
    console.error('Error initializing Dashboard API Service:', error);
  }
});

// Legacy export support
if (typeof module !== 'undefined' && typeof module.exports !== 'undefined') {
  module.exports = window.apiService || {};
}

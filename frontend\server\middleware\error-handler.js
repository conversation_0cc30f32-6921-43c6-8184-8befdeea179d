/**
 * Error handling middleware
 */

// Error handling middleware - matching existing error handling pattern
const errorHandler = (err, req, res, next) => {
  console.error('API Error:', err);
  
  // Database connection errors
  if (err.code && (err.code.startsWith('ER_') || err.code.startsWith('ECONNREFUSED'))) {
    return res.status(500).json({ 
      success: false, 
      message: 'Database service unavailable' 
    });
  }
  
  // Default server error
  return res.status(500).json({ 
    success: false, 
    message: err.message || 'Internal server error' 
  });
};

module.exports = errorHandler;

/**
 * Database Environments Configuration
 * 
 * This file contains configuration for different database environments
 * that can be used with the database connection
 */

// Common path for SSH key
const os = require('os');
const path = require('path');
const SSH_KEY_PATH = path.join(os.homedir(), '.ssh', 'id_rsa_dbserver');

// Environment configurations for each server
const environments = {
  // QA01 Environment
  qa01: {
    // Server Configuration
    PORT: 3000,
    BASE_URL: 'http://mprts-qa01.lab.wagerworks.com:9080/AutoRun/',
    
    // Database Configuration
    DB_HOST: 'mprts-qa01.lab.wagerworks.com',
    DB_USER: 'rgs_rw',
    DB_PASSWORD: 'rgs_rw',
    DB_NAME: 'rgs_test',
    DB_PORT: 3306,
    
    // SSH Configuration
    SSH_HOST: 'mprts-qa01.lab.wagerworks.com',
    SSH_USER: process.env.SSH_USER || 'volfkoi',
    SSH_PORT: 22,
    SSH_KEY_PATH: process.env.SSH_KEY_PATH || path.join(process.env.USERPROFILE || os.homedir(), '.ssh', 'id_rsa_dbserver'),
    
    // Connection Preference
    preferredConnectionMethod: 'direct' // 'direct' or 'tunnel'
  },
  
  // QA02 Environment
  qa02: {
    // Server Configuration
    PORT: 3000,
    BASE_URL: 'http://mprts-qa02.lab.wagerworks.com:5080/AutoRun/',
    
    // Database Configuration
    DB_HOST: 'mprts-qa02.lab.wagerworks.com',
    DB_USER: 'rgs_rw',
    DB_PASSWORD: 'rgs_rw',
    DB_NAME: 'rgs_test',
    DB_PORT: 3306,
    
    // SSH Configuration
    SSH_HOST: 'mprts-qa02.lab.wagerworks.com',
    SSH_USER: process.env.SSH_USER || 'volfkoi',
    SSH_PORT: 22,
    SSH_KEY_PATH: process.env.SSH_KEY_PATH || path.join(process.env.USERPROFILE || os.homedir(), '.ssh', 'id_rsa_dbserver'),
    
    // Connection Preference
    preferredConnectionMethod: 'direct' // 'direct' or 'tunnel'
  },
  
  // QA03 Environment
  qa03: {
    // Server Configuration
    PORT: 3000,
    BASE_URL: 'http://mprts-qa03.lab.wagerworks.com:5080/AutoRun/',
    
    // Database Configuration
    DB_HOST: 'mprts-qa03.lab.wagerworks.com',
    DB_USER: 'rgs_rw',
    DB_PASSWORD: 'rgs_rw',
    DB_NAME: 'rgs_test',
    DB_PORT: 3306,
    
    // SSH Configuration
    SSH_HOST: 'mprts-qa03.lab.wagerworks.com',
    SSH_USER: process.env.SSH_USER || 'volfkoi',
    SSH_PORT: 22,
    SSH_KEY_PATH: process.env.SSH_KEY_PATH || path.join(process.env.USERPROFILE || os.homedir(), '.ssh', 'id_rsa_dbserver'),
    
    // Connection Preference
    preferredConnectionMethod: 'direct' // 'direct' or 'tunnel'
  }
};

module.exports = environments;

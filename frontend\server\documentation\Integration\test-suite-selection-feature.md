# Test Suite Selection Feature Documentation

## Overview

This document details the implementation of the Test Suite Selection feature, which enables users to filter, select, and run test suites directly from the dashboard. The feature provides a UI for filtering test suites by project, integration level, and version, with the ability to select and run a specific test suite.

## Components

The feature implementation spans across multiple files in the SmartTest application:

1. **HTML UI Components** (`frontend/dashboard/index.html`)
2. **JavaScript Handlers** (`frontend/dashboard/dashboard.js`)
3. **API Services** (`frontend/dashboard/api-service.js`)
4. **CSS Styling** (`frontend/dashboard/styles.css`)

## Implementation Details

### 1. HTML Components

The Test Suite Selection feature UI is located in the dashboard after the "Predefined Test Suites" section. The UI includes:

- Filter controls (Project, Integration Level, Version)
- Refresh and Run Selected Suite buttons
- A container for displaying the list of available test suites with radio button selection

```html
<!-- Test Suite Selection -->
<div class="ms-content-card ms-test-suite-selection">
    <h2 class="ms-card-title">Test Suite Selection</h2>
    <div class="ms-card-content">
        <div class="ms-suite-filters">
            <div class="ms-suite-filter-group">
                <label for="test-suite-project">Project</label>
                <select id="test-suite-project" class="ms-dropdown">
                    <option value="">All Projects</option>
                </select>
            </div>
            <div class="ms-suite-filter-group">
                <label for="test-suite-level">Integration Level</label>
                <select id="test-suite-level" class="ms-dropdown">
                    <option value="">All Levels</option>
                    <option value="unit">Unit</option>
                    <option value="integration">Integration</option>
                    <option value="system">System</option>
                </select>
            </div>
            <div class="ms-suite-filter-group">
                <label for="test-suite-version">Version</label>
                <select id="test-suite-version" class="ms-dropdown">
                    <option value="">All Versions</option>
                </select>
            </div>
            <div class="ms-suite-actions">
                <button id="refresh-test-suites" class="ms-button ms-button-secondary">
                    <i class="ms-Icon ms-Icon--Refresh"></i> Refresh
                </button>
                <button id="run-selected-suite" class="ms-button ms-button-primary" disabled>
                    <i class="ms-Icon ms-Icon--Play"></i> Run Selected Suite
                </button>
            </div>
        </div>
        <div id="test-suites-container" class="ms-suite-list">
            <div class="ms-empty-message">
                Filter and refresh to view available test suites
            </div>
        </div>
    </div>
</div>
```

### 2. JavaScript Implementation

The JavaScript implementation includes DOM element references, event handlers, and API service calls for the Test Suite Selection feature.

#### 2.1. DOM Element References

```javascript
// Test suite selection elements
const testSuiteProjectFilterEl = document.getElementById('test-suite-project');
const testSuiteLevelFilterEl = document.getElementById('test-suite-level');
const testSuiteVersionFilterEl = document.getElementById('test-suite-version');
const refreshTestSuitesBtn = document.getElementById('refresh-test-suites');
const runSelectedSuiteBtn = document.getElementById('run-selected-suite');
const testSuitesContainer = document.getElementById('test-suites-container');
```

#### 2.2. Setup Handlers

The `setupTestSuiteSelectionHandlers` function sets up event listeners for the filter elements and buttons:

```javascript
// New function to set up test suite selection handlers
function setupTestSuiteSelectionHandlers() {
    // Check if the elements exist (they might not in some versions of the dashboard)
    if (!testSuiteProjectFilterEl || !testSuiteLevelFilterEl || !testSuiteVersionFilterEl || 
        !refreshTestSuitesBtn || !runSelectedSuiteBtn || !testSuitesContainer) {
        return;
    }

    // Add event listeners to filters and buttons
    refreshTestSuitesBtn.addEventListener('click', () => {
        fetchFilteredTestSuites();
    });

    runSelectedSuiteBtn.addEventListener('click', async () => {
        const selectedSuite = document.querySelector('input[name="test-suite"]:checked');
        if (!selectedSuite) return;
        
        const suiteId = selectedSuite.value;
        
        try {
            // Show loading UI
            runSelectedSuiteBtn.disabled = true;
            runSelectedSuiteBtn.innerHTML = '<i class="ms-Icon ms-Icon--Sync ms-Icon--spin"></i> Running...';
            
            await window.apiService.runTestSuite(suiteId);
            
            // Show success notification
            showNotification('Test Suite started successfully', 'success');
            
            // Refresh active tests after a brief delay to allow the backend to start the test
            setTimeout(refreshActiveTests, 1500);
        } catch (error) {
            console.error('Error running test suite:', error);
            showNotification('Failed to run test suite: ' + error.message, 'error');
        } finally {
            // Reset button state
            runSelectedSuiteBtn.disabled = false;
            runSelectedSuiteBtn.innerHTML = '<i class="ms-Icon ms-Icon--Play"></i> Run Selected Suite';
        }
    });

    // Initial fetch of test suites
    fetchFilteredTestSuites();
}
```

#### 2.3. Fetch Filtered Test Suites

The `fetchFilteredTestSuites` function retrieves test suites from the API based on selected filters:

```javascript
// Function to fetch test suites based on the selected filters
async function fetchFilteredTestSuites() {
    // Check if container exists
    if (!testSuitesContainer) return;

    // Show loading state
    testSuitesContainer.innerHTML = '<div class="ms-empty-message"><i class="ms-Icon ms-Icon--Sync ms-Icon--spin"></i> Loading test suites...</div>';
    
    // Disable run button during load
    if (runSelectedSuiteBtn) runSelectedSuiteBtn.disabled = true;
    
    try {
        // Get filter values
        const filters = {
            project: testSuiteProjectFilterEl ? testSuiteProjectFilterEl.value : '',
            level: testSuiteLevelFilterEl ? testSuiteLevelFilterEl.value : '',
            version: testSuiteVersionFilterEl ? testSuiteVersionFilterEl.value : ''
        };
        
        // Call API to get filtered test suites
        const testSuites = await window.apiService.getTestSuites(filters);
        
        // Render the results
        renderTestSuites(testSuites);
    } catch (error) {
        console.error('Error fetching test suites:', error);
        testSuitesContainer.innerHTML = `<div class="ms-empty-message error"><i class="ms-Icon ms-Icon--Error"></i> Error loading test suites: ${error.message}</div>`;
    }
}
```

#### 2.4. Render Test Suites

The `renderTestSuites` function displays the list of test suites:

```javascript
// Function to render the list of test suites
function renderTestSuites(testSuites) {
    // Check if container exists
    if (!testSuitesContainer) return;
    
    // If no test suites or empty array, show empty message
    if (!testSuites || testSuites.length === 0) {
        testSuitesContainer.innerHTML = '<div class="ms-empty-message">No test suites found matching the selected filters</div>';
        return;
    }
    
    // Create HTML elements for each test suite
    let html = '';
    testSuites.forEach(suite => {
        html += `
            <div class="ms-suite-item">
                <div class="ms-suite-select">
                    <input type="radio" name="test-suite" id="suite-${suite.id}" value="${suite.id}">
                </div>
                <label for="suite-${suite.id}" class="ms-suite-info">
                    <div class="ms-suite-name">${escapeHtml(suite.name)}</div>
                    <div class="ms-suite-details">
                        ${suite.project ? `Project: ${escapeHtml(suite.project)}` : ''} 
                        ${suite.level ? `• Level: ${escapeHtml(suite.level)}` : ''} 
                        ${suite.version ? `• Version: ${escapeHtml(suite.version)}` : ''}
                    </div>
                </label>
            </div>
        `;
    });
    
    // Update container with the generated HTML
    testSuitesContainer.innerHTML = html;
    
    // Add event listeners to radio buttons
    const radioButtons = testSuitesContainer.querySelectorAll('input[type="radio"]');
    radioButtons.forEach(radio => {
        radio.addEventListener('change', () => {
            if (runSelectedSuiteBtn) runSelectedSuiteBtn.disabled = false;
        });
    });
}
```

#### 2.5. Integration with Dashboard Initialization

The setup function is called during dashboard initialization:

```javascript
function initDashboard() {
    // ... existing code
    
    // Set up test suite selection handlers
    setupTestSuiteSelectionHandlers();
    
    // ... existing code
}
```

### 3. API Service Implementation

The API service was extended with two new methods to support the Test Suite Selection feature:

```javascript
/**
 * Get test suites with optional filtering
 * @param {Object} filters - Optional filters (project, level, version)
 * @returns {Promise<Array>} - Array of test suite objects
 */
async getTestSuites(filters = {}) {
    try {
        const queryParams = new URLSearchParams();
        
        // Add filters to query parameters if they exist
        if (filters.project) queryParams.append('project', filters.project);
        if (filters.level) queryParams.append('level', filters.level);
        if (filters.version) queryParams.append('version', filters.version);
        
        const queryString = queryParams.toString();
        const endpoint = `/testsuites${queryString ? `?${queryString}` : ''}`;
        
        const response = await this.authenticatedFetch(endpoint);
        
        if (response.success) {
            return response.data || [];
        } else {
            throw new Error(response.message || 'Failed to retrieve test suites');
        }
    } catch (error) {
        console.error('Error fetching test suites:', error);
        throw error;
    }
}

/**
 * Run a selected test suite
 * @param {string} suiteId - ID of the test suite to run
 * @returns {Promise<Object>} - Response containing test session ID
 */
async runTestSuite(suiteId) {
    try {
        const response = await this.authenticatedFetch(`/testsuites/${suiteId}/run`, {
            method: 'POST'
        });
        
        if (response.success) {
            return response.tsn_id;
        } else {
            throw new Error(response.message || 'Failed to run test suite');
        }
    } catch (error) {
        console.error(`Error running test suite ${suiteId}:`, error);
        throw error;
    }
}
```

### 4. CSS Styling

The CSS styling for the Test Suite Selection feature includes styles for filters, buttons, and test suite list items:

```css
/* Test Suite Selection */
.ms-test-suite-selection {
    margin-bottom: 24px;
}

.ms-suite-filters {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    margin-bottom: 16px;
    align-items: flex-end;
}

.ms-suite-filter-group {
    display: flex;
    flex-direction: column;
    min-width: 180px;
}

.ms-suite-filter-group label {
    font-size: 12px;
    margin-bottom: 4px;
    color: #605e5c;
    font-weight: 500;
}

.ms-suite-filter-group select {
    padding: 6px 30px 6px 12px;
    border: 1px solid var(--teams-border);
    border-radius: 4px;
    background-color: white;
    font-size: 14px;
    color: var(--teams-text);
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 16 16'%3E%3Cpath fill='%23605e5c' d='M4.5 6l3.5 3.5 3.5-3.5h-7z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 8px center;
}

.ms-suite-filter-group select:focus {
    outline: 2px solid var(--teams-primary);
    outline-offset: 1px;
}

.ms-suite-actions {
    display: flex;
    gap: 8px;
}

.ms-suite-list {
    border: 1px solid var(--teams-border);
    border-radius: 4px;
    background-color: white;
    max-height: 320px;
    overflow-y: auto;
}

.ms-suite-item {
    display: flex;
    padding: 12px 16px;
    border-bottom: 1px solid var(--teams-border);
    transition: background-color 0.2s;
}

.ms-suite-item:last-child {
    border-bottom: none;
}

.ms-suite-item:hover {
    background-color: rgba(98, 100, 167, 0.05);
}

.ms-suite-select {
    display: flex;
    align-items: center;
    margin-right: 12px;
}

.ms-suite-select input[type="radio"] {
    margin: 0;
    width: 16px;
    height: 16px;
}

.ms-suite-info {
    flex: 1;
}

.ms-suite-name {
    font-weight: 600;
    margin-bottom: 4px;
    color: var(--teams-text);
}

.ms-suite-details {
    font-size: 12px;
    color: #605e5c;
}
```

## API Endpoints

### 1. GET /testsuites

This endpoint retrieves a list of test suites with optional filtering.

**Request Parameters:**
- `project` (optional): Filter test suites by project name
- `level` (optional): Filter test suites by integration level (unit, integration, system)
- `version` (optional): Filter test suites by version

**Response Format:**
```json
{
  "success": true,
  "data": [
    {
      "id": "ts_123",
      "name": "Example Test Suite",
      "project": "ProjectName",
      "level": "integration",
      "version": "1.0.0",
      "description": "Test suite description"
    }
  ],
  "message": "Test suites retrieved successfully"
}
```

### 2. POST /testsuites/{suiteId}/run

This endpoint starts running a specific test suite.

**Path Parameters:**
- `suiteId`: The ID of the test suite to run

**Response Format:**
```json
{
  "success": true,
  "tsn_id": "tsn_456",
  "message": "Test suite started successfully"
}
```

## Data Flow

1. User selects filter criteria for test suites
2. User clicks "Refresh" button
3. Frontend calls API to get test suites matching the filter criteria
4. API returns the filtered test suites
5. Frontend renders the list of test suites
6. User selects a test suite using the radio button
7. User clicks "Run Selected Suite" button
8. Frontend calls API to run the selected test suite
9. API starts the test suite and returns the test session ID
10. Frontend displays a success notification and refreshes the active tests list

## Best Practices

1. **Error Handling**: Ensure proper error handling at both the frontend and API layers.
2. **Loading States**: Show loading indicators during API calls to provide visual feedback to users.
3. **Disable Controls**: Disable controls while actions are in progress to prevent multiple submissions.
4. **Notifications**: Use notifications to inform users of the results of their actions.
5. **Consistent Styling**: Maintain a consistent UI style matching the Microsoft Teams Fluent UI design system.

## Future Enhancements

Potential future enhancements for the Test Suite Selection feature:

1. Add ability to view test suite details before running
2. Implement test suite search functionality
3. Allow customization of test run parameters
4. Support batch execution of multiple test suites
5. Add favorites or recently used test suites for quick access

# SmartTest Database Schema

This document describes the database schema used by the SmartTest API server.

## Overview

The SmartTest database schema consists of several tables that store information about test sessions, test cases, test results, and input queries.

## Tables

### test_session

Stores information about test sessions.

| Column | Type | Description |
|--------|------|-------------|
| id | INT | Auto-incrementing primary key |
| session_id | VARCHAR(36) | Unique session ID (UUID) |
| test_type | VARCHAR(50) | Type of test (TestCase, TestSuite) |
| environment | VARCHAR(50) | Test environment (qa01, qa02, etc.) |
| description | TEXT | Session description |
| status | VARCHAR(20) | Session status (created, running, completed, failed, cancelled) |
| progress | INT | Session progress (0-100) |
| created_by | VARCHAR(100) | User who created the session |
| created_at | DATETIME | Session creation timestamp |
| updated_at | DATETIME | Session update timestamp |
| duration | VARCHAR(50) | Session duration |

Indexes:
- Primary key on `id`
- Unique index on `session_id`
- Index on `status`
- Index on `created_at`

### test_cases

Stores information about test cases within a session.

| Column | Type | Description |
|--------|------|-------------|
| id | INT | Auto-incrementing primary key |
| case_id | VARCHAR(36) | Unique case ID (UUID) |
| session_id | VARCHAR(36) | Session ID (foreign key to test_session.session_id) |
| name | VARCHAR(100) | Test case name |
| description | TEXT | Test case description |
| status | VARCHAR(20) | Test case status (created, running, completed, failed, cancelled) |
| execution_time | INT | Execution time in milliseconds |
| error_message | TEXT | Error message (if any) |
| created_at | DATETIME | Test case creation timestamp |

Indexes:
- Primary key on `id`
- Unique index on `case_id`
- Index on `session_id`
- Index on `status`
- Foreign key on `session_id` referencing `test_session.session_id`

### input_queries

Stores information about input queries within a session.

| Column | Type | Description |
|--------|------|-------------|
| id | INT | Auto-incrementing primary key |
| query_id | VARCHAR(36) | Unique query ID (UUID) |
| session_id | VARCHAR(36) | Session ID (foreign key to test_session.session_id) |
| query | TEXT | SQL query |
| execution_time | INT | Execution time in milliseconds |
| status | VARCHAR(20) | Query status (success, error, warning) |
| result | TEXT | Query result |
| created_at | DATETIME | Query creation timestamp |

Indexes:
- Primary key on `id`
- Unique index on `query_id`
- Index on `session_id`
- Index on `status`
- Foreign key on `session_id` referencing `test_session.session_id`

### test_case

Stores information about test case definitions.

| Column | Type | Description |
|--------|------|-------------|
| tc_id | VARCHAR(36) | Unique test case ID |
| uid | VARCHAR(100) | User who created the test case |
| status | VARCHAR(20) | Test case status (active, inactive) |
| case_driver | VARCHAR(50) | Test case driver (selenium, etc.) |
| tp_id | INT | Test project ID |
| comments | TEXT | Test case comments |
| tickets | VARCHAR(100) | Associated tickets |
| name | VARCHAR(100) | Test case name |

Indexes:
- Primary key on `tc_id`
- Index on `status`
- Index on `tp_id`

### test_case_group

Maps test cases to test suites.

| Column | Type | Description |
|--------|------|-------------|
| ts_id | VARCHAR(36) | Test suite ID |
| tc_id | VARCHAR(36) | Test case ID |
| seq_index | INT | Sequence index |

Indexes:
- Composite primary key on `ts_id` and `tc_id`
- Index on `ts_id`
- Index on `tc_id`

### test_suite

Stores information about test suites.

| Column | Type | Description |
|--------|------|-------------|
| ts_id | VARCHAR(36) | Unique test suite ID |
| status | VARCHAR(20) | Test suite status (active, inactive) |
| uid | VARCHAR(100) | User who created the test suite |
| comments | TEXT | Test suite comments |
| tp_id | INT | Test plan ID |
| name | VARCHAR(100) | Test suite name |

Indexes:
- Primary key on `ts_id`
- Index on `status`
- Index on `uid`

### test_result

Stores test execution results.

| Column | Type | Description |
|--------|------|-------------|
| cnt | INT | Auto-incrementing primary key |
| tsn_id | VARCHAR(36) | Test session ID |
| tc_id | VARCHAR(36) | Test case ID |
| seq_index | INT | Sequence index |
| outcome | CHAR(1) | Outcome (P = Pass, F = Fail) |
| creation_time | DATETIME | Result creation timestamp |

Indexes:
- Primary key on `cnt`
- Index on `tsn_id`
- Index on `tc_id`
- Index on `creation_time`

### output

Stores output data for test results.

| Column | Type | Description |
|--------|------|-------------|
| cnt | INT | Result ID (foreign key to test_result.cnt) |
| txt | TEXT | Output text |

Indexes:
- Primary key on `cnt`
- Foreign key on `cnt` referencing `test_result.cnt`

## Relationships

- `test_cases.session_id` references `test_session.session_id` (many-to-one)
- `input_queries.session_id` references `test_session.session_id` (many-to-one)
- `test_case_group.tc_id` references `test_case.tc_id` (many-to-many)
- `test_case_group.ts_id` references `test_suite.ts_id` (many-to-many)
- `test_result.tc_id` references `test_case.tc_id` (many-to-one)
- `output.cnt` references `test_result.cnt` (one-to-one)

## Database Creation

The database schema can be created using the `/AutoRun/setup` endpoint. This endpoint is restricted to admin users.

```
POST /AutoRun/setup
```

Request body:

```json
{
  "uid": "<EMAIL>",
  "password": "password"
}
```

Response:

```json
{
  "success": true,
  "message": "Database schema created successfully"
}
```

## Database Access

The SmartTest API server accesses the database using one of the following methods:

1. Direct SSH commands
2. SSH tunnel

The database access method is configured in the `database/connections/` directory, which provides a unified interface for different connection methods. The database layer is designed to be extensible and maintainable, with separate modules for different types of queries in the `database/queries/` directory.

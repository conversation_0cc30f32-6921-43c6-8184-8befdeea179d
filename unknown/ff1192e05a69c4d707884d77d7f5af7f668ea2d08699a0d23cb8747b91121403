# External API Integration

## Overview

The database layer integrates with external APIs to perform various operations, such as running tests, stopping tests, and retrieving test results. The external APIs are hosted on two different ports:

1. **Port 5080 Endpoints**: These endpoints use form data authentication with `uid` and `password` parameters.
2. **Port 9080 Endpoints**: These endpoints use cookie-based authentication with a `JSESSIONID` cookie.

## External API Endpoints

### Port 5080 Endpoints

#### Run Test Case / Test Suite

```
POST /AutoRun/CaseRunner
```

Executes a test case or test suite.

**Request Parameters:**

- `uid` (required): User ID
- `password` (required): Password
- `tc_id` or `ts_id` (required): Test case ID or test suite ID
- `envir` (required): Environment (e.g., qa02)
- `shell_host` (required): Shell host (e.g., jps-qa10-app01)
- `file_path` (likely required): File path on shell host
- Various other context-dependent parameters

**Response:**

HTML response containing the test session ID (tsn_id) in the format:
```
Your test session id: 12345
```

### Port 9080 Endpoints

#### Login

```
POST /AutoRun/Login
```

Authenticates a user and returns a JSESSIONID cookie.

**Request Parameters:**

- `uid` (required): User ID
- `password` (required): Password

**Response:**

HTTP 302 redirect with a `Set-Cookie` header containing the JSESSIONID cookie.

#### Stop Test

```
POST /AutoRun/RemoveSession
```

Stops a running test.

**Request Parameters:**

- `tsn_id` (required): Test session ID

**Required Headers:**

- `Cookie: JSESSIONID=<jsessionid>`

**Response:**

Text response containing "Removed" if successful.

#### Get Test Report Summary

```
GET /AutoRun/ReportSummary
```

Retrieves a summary of a test run.

**Request Parameters:**

- `tsn_id` (required): Test session ID

**Required Headers:**

- `Cookie: JSESSIONID=<jsessionid>`

**Response:**

HTML response containing test summary information, including:
- Start Time
- End Time
- Case(s) passed
- Case(s) failed
- Overall status (PASS/FAIL)

#### Get Test Report Details

```
GET /AutoRun/ReportDetails
```

Retrieves detailed information about a test run.

**Request Parameters:**

- `tsn_id` (required): Test session ID
- `index` (optional): Page number for pagination

**Required Headers:**

- `Cookie: JSESSIONID=<jsessionid>`

**Response:**

HTML response containing detailed test steps, including:
- Test case ID
- Sequence index
- Outcome (P/F)
- Description
- Input
- Output

## Cookie Authentication

For endpoints on port 9080, the database layer uses cookie-based authentication. The cookie authentication flow is as follows:

1. The client sends a request with authentication credentials
2. The server uses the credentials to log in to the external API using the `/AutoRun/Login` endpoint
3. The external API returns a `JSESSIONID` cookie
4. The server stores the cookie and uses it for subsequent requests to the external API
5. The server forwards the response from the external API to the client

The cookie authentication service manages cookies for external API authentication:

- Cookies are cached in memory to avoid unnecessary login requests
- Cookies expire after 30 minutes
- If a cookie expires, the service automatically logs in again to get a new cookie

## Integration with Database Layer

The database layer interacts with the external APIs in the following ways:

1. **Test Execution**: When a test case or test suite is executed, the database layer sends a request to the `/AutoRun/CaseRunner` endpoint on port 5080.
2. **Test Status**: To retrieve the status of a test run, the database layer sends a request to the `/AutoRun/ReportSummary` endpoint on port 9080.
3. **Test Results**: To retrieve detailed test results, the database layer sends a request to the `/AutoRun/ReportDetails` endpoint on port 9080.
4. **Stop Test**: To stop a running test, the database layer sends a request to the `/AutoRun/RemoveSession` endpoint on port 9080.

## Hybrid Architecture for Reports Page

The reports page implements a hybrid data access approach that combines direct external API integration with database access:

### Direct External API Integration

The reports page directly integrates with external APIs on port 9080 for the following features:

1. **Test Results Table**: Fetches test results directly from the `/AutoRun/ReportSummary` endpoint
2. **Test Details View**: Fetches test details directly from the `/AutoRun/ReportDetails` endpoint

This direct integration provides several benefits:

- **Performance**: Direct API calls are faster than going through the database layer
- **Reliability**: Reports page works even if the database is unavailable
- **Real-time Data**: 10-second polling interval keeps data current

### Database Integration

The reports page continues to use the database layer for the following features:

1. **Analytics Charts**: Uses database queries for complex analytics (future enhancement)
2. **Historical Data**: Uses database queries for historical data analysis
3. **Fallback Mechanism**: Falls back to database queries if direct API integration fails

For more details on the hybrid approach, see [Hybrid Data Access](../Integration/hybrid-data-access.md).

## Error Handling

The database layer includes robust error handling for external API interactions:

- If the external API returns an error, the database layer will attempt to parse the error message and provide a meaningful error to the caller.
- If the authentication fails, the database layer will attempt to re-authenticate and retry the request.
- If the request fails due to network issues, the database layer will retry the request with exponential backoff.

## Security Considerations

- The database layer does not log sensitive information such as passwords
- The database layer validates the response from the external API before returning it to the caller
- For cookie-based authentication, the database layer uses a dedicated cookie authentication service to manage cookies
- Cookies are cached in memory to avoid unnecessary login requests
- Cookies expire after 30 minutes to reduce the risk of unauthorized access

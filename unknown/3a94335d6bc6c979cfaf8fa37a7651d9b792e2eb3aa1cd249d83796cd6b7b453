# SmartTest API Proxy Functionality

This document describes the proxy functionality used by the SmartTest API server to forward requests to external APIs.

## Overview

The SmartTest API server acts as a proxy for external APIs, forwarding requests from clients to the external API and returning the responses to the clients. This allows the SmartTest API server to provide a unified interface for both local and external APIs.

The SmartTest API server interacts with two different external API endpoints:

1. **Port 5080 Endpoints**: These endpoints use form data authentication with `uid` and `password` parameters.
2. **Port 9080 Endpoints**: These endpoints use cookie-based authentication with a `JSESSIONID` cookie.

## Proxy Middleware

The proxy functionality is implemented in the `middleware/proxy.js` file. This middleware intercepts requests to specific endpoints and forwards them to the corresponding external API endpoints.

## External API Configuration

The external API base URLs are configured in the proxy middleware:

```javascript
// Determine the base URL based on whether cookie auth is required
let baseUrl;
if (requiresCookieAuth) {
  baseUrl = 'http://mprts-qa02.lab.wagerworks.com:9080/AutoRun';
} else {
  baseUrl = (process.env.MPRTS_BASE_URL || 'http://mprts-qa02.lab.wagerworks.com:5080/AutoRun').replace(/\/$/, '');
}
```

The list of endpoints that require cookie-based authentication is also defined in the proxy middleware:

```javascript
// List of endpoints that require cookie-based authentication (port 9080)
const cookieAuthEndpoints = [
  'ReportSummary',
  'ReportDetails',
  'RemoveSession'
];
```

## Proxy Routes

The proxy routes are defined in the `routes/proxy-routes.js` file. This file maps local API endpoints to external API endpoints.

## Proxy Flow

1. Client sends a request to the SmartTest API server
2. Request is intercepted by the proxy middleware
3. Proxy middleware extracts the path and query parameters from the request
4. Proxy middleware constructs a new request to the external API
5. Proxy middleware sends the request to the external API
6. Proxy middleware receives the response from the external API
7. Proxy middleware forwards the response to the client

## Special Cases

### Test Status Endpoint

The `/api/test-status` endpoint is a special case that maps to the external API's `/ReportSummary` endpoint on port 9080:

```javascript
// Special case for test-status endpoint - use ReportSummary instead
let targetPath = apiPath;
if (apiPath === 'test-status') {
  targetPath = 'ReportSummary';
  console.log(`[Proxy] Redirecting test-status request to ReportSummary endpoint`);
}
```

### Stop Test Endpoint

The `/api/stop-test` endpoint is a special case that maps to the external API's `/RemoveSession` endpoint on port 9080:

```javascript
// Stop Test API endpoint - uses RemoveSession with cookie auth
router.post('/stop-test', validateCredentials, async (req, res, next) => {
  try {
    console.log('POST /api/stop-test');
    // Make a copy of request body to avoid consuming it multiple times
    const params = {...req.body};

    // Extract the test session ID and credentials
    const { tsn_id, uid, password } = params;

    try {
      // Stop the test using the stop-test service
      const result = await stopTest(tsn_id, uid, password);

      // Return the result
      return res.json({
        success: true,
        ...result
      });
    } catch (error) {
      console.error(`[/api/stop-test] Error stopping test:`, error);
      return res.status(500).json({
        success: false,
        message: `Failed to stop test: ${error.message}`
      });
    }
  } catch (error) {
    console.error('Error in stop-test endpoint:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
});
```

## Request Transformation

The proxy middleware transforms the request before forwarding it to the external API:

1. Extracts the path from the request URL
2. Determines if the endpoint requires cookie-based authentication
3. Constructs a new URL for the external API based on the authentication type
4. Copies query parameters from the request
5. Prepares headers based on the authentication type
6. For cookie-based authentication, obtains a JSESSIONID cookie and adds it to the headers
7. Prepares the request body based on the authentication type and request method

## Response Transformation

The proxy middleware transforms the response before returning it to the client:

1. Forwards the HTTP status code from the external API response
2. Forwards headers from the external API response (excluding problematic headers)
3. Processes the response body based on the content type
4. For JSON responses, parses the JSON and returns it as-is
5. For non-JSON responses, returns the response as text

## Error Handling

The proxy middleware handles errors that occur during the proxy process:

```javascript
try {
  // Proxy logic
} catch (error) {
  console.error(`[Proxy] Error in proxy middleware:`, error);
  res.status(500).json({
    success: false,
    message: `Proxy error: ${error.message}`
  });
}
```

## Logging

The proxy middleware logs information about the proxy process:

```javascript
console.log(`[Proxy] ${req.method} request from ${req.ip} to ${url}`);
console.log(`[Proxy] Preparing to fetch: ${finalUrl}`);
console.log(`[Proxy] Fetch call completed. Status: ${response.status}`);
```

## Local Endpoints

Some endpoints are handled locally and not proxied to the external API:

```javascript
const localEndpoints = [
  '/api/test-reports',
  '/api/local/active-tests',
  '/api/local/recent-runs'
];

if (localEndpoints.includes(req.path)) {
  console.log(`[Proxy] Skipping proxy for local endpoint: ${req.path}`);
  return next();
}
```

## Security Considerations

- The proxy middleware does not log sensitive information such as passwords
- The proxy middleware does not forward problematic headers that might cause issues with the external API
- The proxy middleware validates the response from the external API before returning it to the client
- For cookie-based authentication, the proxy middleware uses a dedicated cookie authentication service to manage cookies
- Cookies are cached in memory to avoid unnecessary login requests
- Cookies expire after 30 minutes to reduce the risk of unauthorized access

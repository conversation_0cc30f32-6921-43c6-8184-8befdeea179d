/**
 * QA01 SSH Tunnel Test Script
 *
 * This script tests connection to QA01 through an SSH tunnel
 * It establishes the tunnel and verifies it's working correctly
 */

// Import modules
const fs = require('fs');
const path = require('path');
const { Client } = require('ssh2');
require('dotenv').config({ path: path.resolve(__dirname, '.env.01') }); // Load environment variables from .env.01

// Default SSH connection settings if not provided in environment
const sshConfig = {
  host: process.env.SSH_HOST || 'mprts-qa01.lab.wagerworks.com',
  port: parseInt(process.env.SSH_PORT || '22', 10),
  username: process.env.SSH_USER || 'volfkoi',
  privateKey: fs.readFileSync(process.env.SSH_KEY_PATH || path.join(process.env.USERPROFILE || process.env.HOME, '.ssh', 'id_rsa_dbserver')),
  algorithms: {
    serverHostKey: ['ssh-rsa', 'ssh-dss', 'ecdsa-sha2-nistp256', 'ecdsa-sha2-nistp384', 'ecdsa-sha2-nistp521']
  }
};

console.log('QA01 SSH Connection Test');
console.log('-----------------');
console.log(`Host: ${sshConfig.host}`);
console.log(`Port: ${sshConfig.port}`);
console.log(`Username: ${sshConfig.username}`);
console.log(`Key: ${process.env.SSH_KEY_PATH ? 'Loaded' : 'Using default location'}`);
console.log('-----------------\n');

// Create SSH client
const conn = new Client();

// Connection events
conn.on('ready', () => {
  console.log('✅ SSH connection established successfully!');
  console.log('Running a simple command to verify connectivity...');
  
  // Execute a simple command to verify connection
  conn.exec('echo "SSH connection test successful" && hostname && uptime', (err, stream) => {
    if (err) {
      console.error('❌ Error executing command:', err);
      conn.end();
      process.exit(1);
      return;
    }
    
    // Handle command output
    let output = '';
    stream.on('data', (data) => {
      output += data.toString();
    });
    
    stream.stderr.on('data', (data) => {
      console.error('STDERR:', data.toString());
    });
    
    stream.on('close', (code) => {
      console.log('==== Command Output ====');
      console.log(output);
      console.log('=======================');
      
      if (code === 0) {
        console.log('✅ Remote command executed successfully');
        conn.end();
        process.exit(0);
      } else {
        console.error(`❌ Command failed with exit code ${code}`);
        conn.end();
        process.exit(1);
      }
    });
  });
});

conn.on('error', (err) => {
  console.error('❌ SSH connection error:', err);
  process.exit(1);
});

conn.on('end', () => {
  console.log('SSH connection ended');
});

conn.on('close', () => {
  console.log('SSH connection closed');
  process.exit(0);
});

// Attempt connection
console.log('Connecting to SSH server...');
conn.connect(sshConfig);
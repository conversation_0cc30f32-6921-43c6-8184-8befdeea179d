# Direct build script that bypasses npm
# This implements the equivalent of 'npm run build' directly

$nodeExe = Join-Path (Get-Location).Path "nodejs\node\node.exe"

# Check if node exists
if (-not (Test-Path $nodeExe)) {
    Write-Host "Error: Node.js executable not found at: $nodeExe" -ForegroundColor Red
    exit 1
}

Write-Host "Using Node.js from: $nodeExe" -ForegroundColor Cyan
Write-Host "Starting build process..." -ForegroundColor Green

# Create the public directory structure if it doesn't exist
$publicDir = "frontend/server/public"
$dirs = @(
    "$publicDir/dashboard",
    "$publicDir/config",
    "$publicDir/reports"
)

foreach ($dir in $dirs) {
    if (-not (Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
        Write-Host "Created directory: $dir" -ForegroundColor Gray
    }
}

# Function to copy files (equivalent to copyfiles in package.json)
function Copy-Files {
    param (
        [string]$sourcePath,
        [string]$targetPath,
        [int]$upLevels = 0
    )
    
    Write-Host "Copying files from $sourcePath to $targetPath" -ForegroundColor Cyan
    
    # Get all files from source
    $sourceFiles = Get-ChildItem -Path $sourcePath -Recurse -File
    
    foreach ($file in $sourceFiles) {
        # Calculate relative path based on upLevels
        $relativePath = $file.FullName
        for ($i = 0; $i -lt $upLevels; $i++) {
            $relativePath = Split-Path -Path $relativePath -Leaf
            $parentPath = Split-Path -Path $file.FullName -Parent
            for ($j = 0; $j -lt $i; $j++) {
                $parentPath = Split-Path -Path $parentPath -Parent
            }
            $relativePath = Join-Path (Split-Path -Path $parentPath -Leaf) $relativePath
        }
        
        # Create target path
        $targetFilePath = Join-Path $targetPath $relativePath
        $targetDir = Split-Path -Path $targetFilePath -Parent
        
        # Create directory if it doesn't exist
        if (-not (Test-Path $targetDir)) {
            New-Item -ItemType Directory -Path $targetDir -Force | Out-Null
        }
        
        # Copy file
        Copy-Item -Path $file.FullName -Destination $targetFilePath -Force
    }
    
    $count = $sourceFiles.Count
    Write-Host "Copied $count files" -ForegroundColor Green
}

# Implement build:dashboard - equivalent to "copyfiles -u 2 frontend/dashboard/**/* frontend/server/public/dashboard"
Write-Host "`n[1/3] Building dashboard..." -ForegroundColor Yellow
Copy-Files -sourcePath "frontend/dashboard" -targetPath "frontend/server/public/dashboard" -upLevels 2

# Implement build:config - equivalent to "copyfiles -u 2 frontend/config/**/* frontend/server/public/config"
Write-Host "`n[2/3] Building config..." -ForegroundColor Yellow
Copy-Files -sourcePath "frontend/config" -targetPath "frontend/server/public/config" -upLevels 2

# Implement build:reports - equivalent to "copyfiles -u 2 frontend/reports/**/* frontend/server/public/reports"
Write-Host "`n[3/3] Building reports..." -ForegroundColor Yellow
Copy-Files -sourcePath "frontend/reports" -targetPath "frontend/server/public/reports" -upLevels 2

Write-Host "`nBuild completed successfully!" -ForegroundColor Green
Write-Host "You can now start the application with: $nodeExe frontend/server/api.js" -ForegroundColor Cyan

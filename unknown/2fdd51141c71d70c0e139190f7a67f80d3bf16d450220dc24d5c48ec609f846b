#  SSH Key Setup Instructions for SmartTest Database Connection

These instructions guide you through generating a NEW SSH key pair and configuring it for the SmartTest application to connect to the database server (e.g., mprts-qa02) via SSH.

**DO NOT SHARE YOUR PRIVATE KEY.** Each user should generate their own.

## Step 1: Generate a New SSH Key Pair (Client-Side)
Open PowerShell on your Windows machine and run:
```powershell
# Using the standard filename 'id_rsa_dbserver' for this connection.
# IMPORTANT: Even though the filename is the same, each user generates their OWN UNIQUE key pair.
# You will be prompted to enter a passphrase - this is optional but recommended for security.
ssh-keygen -t rsa -b 4096 -f "$env:USERPROFILE\.ssh\id_rsa_dbserver"
```
This creates two files in your `C:\Users\<USER>\.ssh\` directory:
*   `id_rsa_dbserver` (Your PRIVATE key - Keep this safe!)
*   `id_rsa_dbserver.pub` (Your PUBLIC key - This goes on the server)

## Step 2: Get Your Public Key Content (Client-Side)
Display the content of your **public** key file. You will need to copy this entire output (starting with `ssh-rsa` and ending with your username/host).
```powershell
Get-Content "$env:USERPROFILE\.ssh\id_rsa_dbserver.pub"
```

## Step 3: Connect to the Database Server (Manual SSH)
Connect to the target server using your designated SSH username (e.g., `volfkoi`) and the specific `-o HostKeyAlgorithms` option needed for this older server. You might need an existing key or password for this initial connection.
```powershell
# Replace volfkoi and the server address if different
# You might need -i path/to/existing_key if password login is disabled
ssh -o HostKeyAlgorithms=+ssh-rsa <EMAIL>
```

## Step 4: Add Your Public Key to Server's authorized_keys (Server-Side)
Once logged into the server via SSH (from Step 3), run these commands carefully:
```bash
# Ensure the .ssh directory exists and has correct permissions
mkdir -p ~/.ssh
chmod 700 ~/.ssh

# IMPORTANT: Use '>>' to APPEND your key. Do NOT use '>'.
# Paste the PUBLIC key content you copied in Step 2, then press Enter, then press Ctrl+D
cat >> ~/.ssh/authorized_keys

# Ensure the authorized_keys file has correct permissions
chmod 600 ~/.ssh/authorized_keys

# Exit the server SSH session
exit
```
This adds your new public key to the list of keys allowed to log in as the `volfkoi` user.

## Step 5: Configure SmartTest Application (Client-Side)
Edit the `.env` file in the root of the `smarttest` project directory:
*   Set `SSH_KEY_PATH` to the **full path** of your **PRIVATE** key file generated in Step 1 (e.g., `id_rsa_dbserver`). 
    *   **CRITICAL:** Ensure this path includes your specific user profile directory. Each user's path will be different!
    *   Use double backslashes in the path.
    Example: `SSH_KEY_PATH=C:\\Users\\<USER>\\.ssh\\id_rsa_dbserver`
*   **REMOVE or COMMENT OUT** the `SSH_PASSWORD` line entirely to ensure key authentication is used.
    Example: `# SSH_PASSWORD=`
*   Ensure `SSH_HOST`, `SSH_PORT`, and `SSH_USER` are set correctly for the target environment.

## Step 6: Restart SmartTest Application
Stop the Node.js server (if running) and restart it to apply the `.env` changes:
```bash
# In your SmartTest project directory terminal
node frontend/server/api.js
```

The application should now be able to connect to the database using your new, unique SSH key.
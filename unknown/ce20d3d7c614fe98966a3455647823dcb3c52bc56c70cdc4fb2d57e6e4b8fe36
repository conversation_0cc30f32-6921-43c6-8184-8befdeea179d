/**
 * Direct SSH Connection
 * Implements database access through direct SSH commands
 */
const { Client } = require('ssh2');
const fs = require('fs');
const path = require('path');
const util = require('util');

class DirectSshConnection {
  constructor(config) {
    this.config = config;
    this.sshClient = null;
    this.isConnected = false;
    this.debugMode = process.env.DB_DEBUG === 'true';
  }

  /**
   * Log debug information if debug mode is enabled
   * @param {string} message - Message to log
   */
  log(message) {
    if (this.debugMode) {
      console.log(`[DirectSSH] ${message}`);
    }
  }

  /**
   * Initialize the connection
   * @returns {Promise<boolean>} - True if initialization was successful
   */
  async init() {
    try {
      this.log('Initializing direct SSH connection...');

      // Create SSH client
      this.sshClient = new Client();

      // Connect to SSH server
      await this._connectSsh();

      // Test MySQL connectivity
      await this._testMysqlConnectivity();

      this.isConnected = true;
      this.log('Direct SSH connection initialized successfully');
      return true;
    } catch (error) {
      console.error(`Failed to initialize direct SSH connection: ${error.message}`);

      // Clean up any partial connections
      if (this.sshClient) {
        try {
          this.sshClient.end();
        } catch (closeError) {
          // Ignore errors during cleanup
        }
        this.sshClient = null;
      }

      this.isConnected = false;
      throw error;
    }
  }

  /**
   * Connect to SSH server
   * @returns {Promise<void>}
   */
  async _connectSsh() {
    return new Promise((resolve, reject) => {
      // Get SSH configuration
      const sshHost = this.config.SSH_HOST;
      const sshPort = parseInt(this.config.SSH_PORT || '22', 10);
      const sshUsername = this.config.SSH_USER;
      const sshKeyPath = this.config.SSH_KEY_PATH;

      // Validate SSH configuration
      if (!sshHost) {
        return reject(new Error('SSH_HOST is required'));
      }

      if (!sshUsername) {
        return reject(new Error('SSH_USER is required'));
      }

      // Prepare SSH configuration
      const sshConfig = {
        host: sshHost,
        port: sshPort,
        username: sshUsername,
        readyTimeout: 30000, // 30 seconds
        algorithms: {
          kex: [
            'diffie-hellman-group1-sha1',
            'diffie-hellman-group14-sha1',
            'diffie-hellman-group-exchange-sha1',
            'diffie-hellman-group-exchange-sha256',
            'ecdh-sha2-nistp256',
            'ecdh-sha2-nistp384',
            'ecdh-sha2-nistp521',
            '<EMAIL>'
          ],
          cipher: [
            'aes128-ctr',
            'aes192-ctr',
            'aes256-ctr',
            'aes128-gcm',
            '<EMAIL>',
            'aes256-gcm',
            '<EMAIL>',
            'aes256-cbc',
            'aes192-cbc',
            'aes128-cbc',
            '3des-cbc'
          ],
          serverHostKey: [
            'ssh-rsa',
            'ssh-dss',
            'ecdsa-sha2-nistp256',
            'ecdsa-sha2-nistp384',
            'ecdsa-sha2-nistp521',
            'rsa-sha2-256',
            'rsa-sha2-512'
          ],
          hmac: [
            'hmac-sha2-256',
            'hmac-sha2-512',
            'hmac-sha1',
            'hmac-md5',
            'hmac-sha2-256-96',
            'hmac-sha2-512-96',
            'hmac-sha1-96',
            'hmac-md5-96'
          ]
        }
      };

      // Add private key if available
      if (sshKeyPath && fs.existsSync(sshKeyPath)) {
        this.log(`Using SSH key: ${sshKeyPath}`);
        sshConfig.privateKey = fs.readFileSync(sshKeyPath);
      } else {
        console.warn(`SSH key not found at ${sshKeyPath}, falling back to password authentication`);
        // Use password authentication as fallback
        sshConfig.password = this.config.SSH_PASSWORD || 'password';
      }

      // Connect to SSH server
      this.sshClient.on('ready', () => {
        this.log(`SSH connection established to ${sshHost}:${sshPort}`);
        resolve();
      });

      this.sshClient.on('error', (err) => {
        reject(new Error(`SSH connection error: ${err.message}`));
      });

      this.log(`Connecting to SSH server ${sshHost}:${sshPort} as ${sshUsername}...`);
      this.sshClient.connect(sshConfig);
    });
  }

  /**
   * Test MySQL connectivity
   * @returns {Promise<void>}
   */
  async _testMysqlConnectivity() {
    this.log('Testing MySQL connectivity...');

    try {
      // Execute a simple query to test connectivity
      const result = await this.query('SELECT 1 AS connection_test');

      if (result.length > 0 && result[0].connection_test === 1) {
        this.log('MySQL connectivity test successful');
      } else {
        throw new Error('MySQL connectivity test failed: unexpected result');
      }
    } catch (error) {
      throw new Error(`MySQL connectivity test failed: ${error.message}`);
    }
  }

  /**
   * Execute a SQL query
   * @param {string} sql - SQL query
   * @param {Array} params - Query parameters
   * @returns {Promise<Array>} - Query results
   */
  async query(sql, params = []) {
    if (!this.isConnected || !this.sshClient) {
      throw new Error('Not connected to SSH server');
    }

    // Apply parameters to the SQL query
    let preparedSql = sql;
    if (params.length > 0) {
      // Simple parameter substitution
      params.forEach((param, index) => {
        const paramValue = param === null ? 'NULL' :
                          typeof param === 'string' ? `'${param.replace(/'/g, "''")}'` :
                          param;
        preparedSql = preparedSql.replace('?', paramValue);
      });
    }

    this.log(`Executing query: ${preparedSql}`);

    // Get MySQL credentials
    const dbUser = this.config.DB_USER || 'rgs_rw';
    const dbPassword = this.config.DB_PASSWORD || 'rgs_rw';
    const dbName = this.config.DB_NAME || 'rgs_test';

    // Escape the SQL command for shell execution
    const escapedSql = preparedSql.replace(/'/g, "'\\''");

    // Build the MySQL command line with JSON output format for easier parsing
    const mysqlCommand = `mysql --user='${dbUser}' --password='${dbPassword}' '${dbName}' --execute='${escapedSql}' --skip-column-names --batch`;

    // Execute the command through SSH
    return new Promise((resolve, reject) => {
      this.sshClient.exec(mysqlCommand, (err, stream) => {
        if (err) {
          return reject(new Error(`Failed to execute MySQL command: ${err.message}`));
        }

        let data = '';
        let errorData = '';

        stream.on('data', (chunk) => {
          data += chunk.toString('utf8');
        });

        stream.stderr.on('data', (chunk) => {
          errorData += chunk.toString('utf8');
        });

        stream.on('close', (code) => {
          if (code !== 0) {
            return reject(new Error(`MySQL command failed with code ${code}: ${errorData}`));
          }

          // Parse the result
          const rows = this._parseResult(data);
          resolve(rows);
        });
      });
    });
  }

  /**
   * Parse MySQL result
   * @param {string} data - Raw MySQL result
   * @returns {Array} - Parsed rows
   */
  _parseResult(data) {
    // Split the result into rows
    const rows = data.trim().split('\n');

    // If no rows, return empty array
    if (rows.length === 0 || (rows.length === 1 && rows[0] === '')) {
      return [];
    }

    // Get column names from the first row
    const firstRow = rows[0].split('\t');

    // Parse each row
    return rows.map(row => {
      const values = row.split('\t');
      const rowObject = {};

      // Map values to column names
      values.forEach((value, index) => {
        // Use index as column name if no column names available
        const columnName = `column${index + 1}`;

        // Convert value to appropriate type
        let parsedValue = value;

        // Try to parse as number
        if (!isNaN(value) && value !== '') {
          parsedValue = Number(value);
        }

        // Handle NULL values
        if (value === 'NULL' || value === '\\N') {
          parsedValue = null;
        }

        rowObject[columnName] = parsedValue;
      });

      return rowObject;
    });
  }

  /**
   * Close the connection
   * @returns {Promise<void>}
   */
  async close() {
    this.log('Closing SSH connection...');

    if (this.sshClient) {
      try {
        this.sshClient.end();
      } catch (error) {
        console.error(`Error closing SSH connection: ${error.message}`);
      }

      this.sshClient = null;
    }

    this.isConnected = false;
    this.log('SSH connection closed');
  }
}

module.exports = DirectSshConnection;

/**
 * Unit tests for the External API Service - Authentication
 *
 * These tests verify that the External API Service correctly:
 * - Authenticates with the external API
 * - Manages session cookies
 * - Handles authentication errors
 */

describe('ExternalApiService - Authentication', () => {
  let service;
  let fetchMock;

  // Mock credentials
  const mockCredentials = {
    uid: '<EMAIL>',
    password: 'test123'
  };

  // Setup before each test
  beforeEach(() => {
    // Use the global service instance
    service = window.externalApiService;

    // Mock the fetch function
    global.fetch = jest.fn();
    fetchMock = global.fetch;
  });

  // Cleanup after each test
  afterEach(() => {
    jest.resetAllMocks();
  });

  describe('login', () => {
    test('should authenticate and extract JSE<PERSON>ION<PERSON> from cookie', async () => {
      // Mock successful login response with realistic JSESSIONID
      fetchMock.mockResolvedValueOnce({
        ok: true,
        headers: {
          get: jest.fn().mockReturnValue('JSESSIONID=58A7C523CB9EE2B9F6C822C475C74139; Path=/AutoRun; HttpOnly')
        }
      });

      // Call the login method
      const result = await service.login(mockCredentials.uid, mockCredentials.password);

      // Verify fetch was called with correct parameters
      expect(fetchMock).toHaveBeenCalledWith(
        `${service.baseUrl}/Login`,
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8'
          }),
          credentials: 'include'
        })
      );

      // Verify the body contains the credentials
      const callArgs = fetchMock.mock.calls[0][1];
      expect(callArgs.body.toString()).toContain(`uid=${mockCredentials.uid}`);
      expect(callArgs.body.toString()).toContain(`password=${mockCredentials.password}`);

      // Verify the result is the JSESSIONID
      expect(result).toBe('58A7C523CB9EE2B9F6C822C475C74139');

      // Verify the service stored the JSESSIONID
      expect(service.jsessionId).toBe('58A7C523CB9EE2B9F6C822C475C74139');

      // Verify the service set an expiry time
      expect(service.jsessionExpiry).toBeGreaterThan(Date.now());
    });

    test('should throw an error if login fails', async () => {
      // Mock failed login response
      fetchMock.mockResolvedValueOnce({
        ok: false,
        status: 401
      });

      // Expect the login method to throw an error
      await expect(service.login(mockCredentials.uid, mockCredentials.password))
        .rejects.toThrow('Login failed with status 401');
    });

    test('should throw an error if no Set-Cookie header', async () => {
      // Mock response with no Set-Cookie header
      fetchMock.mockResolvedValueOnce({
        ok: true,
        headers: {
          get: jest.fn().mockReturnValue(null)
        }
      });

      // Expect the login method to throw an error
      await expect(service.login(mockCredentials.uid, mockCredentials.password))
        .rejects.toThrow('No Set-Cookie header in login response');
    });

    test('should throw an error if no JSESSIONID in cookie', async () => {
      // Mock response with invalid cookie
      fetchMock.mockResolvedValueOnce({
        ok: true,
        headers: {
          get: jest.fn().mockReturnValue('Path=/; HttpOnly')
        }
      });

      // Expect the login method to throw an error
      await expect(service.login(mockCredentials.uid, mockCredentials.password))
        .rejects.toThrow('No JSESSIONID in Set-Cookie header');
    });
  });

  describe('getValidSession', () => {
    test('should return existing session if valid', async () => {
      // Set a valid session
      service.jsessionId = 'abc123';
      service.jsessionExpiry = Date.now() + 1000000; // Far in the future

      // Call getValidSession
      const result = await service.getValidSession(mockCredentials.uid, mockCredentials.password);

      // Verify login was not called
      expect(fetchMock).not.toHaveBeenCalled();

      // Verify the result is the existing JSESSIONID
      expect(result).toBe('abc123');
    });

    test('should login if session is expired', async () => {
      // Set an expired session
      service.jsessionId = 'abc123';
      service.jsessionExpiry = Date.now() - 1000; // In the past

      // Mock successful login response
      fetchMock.mockResolvedValueOnce({
        ok: true,
        headers: {
          get: jest.fn().mockReturnValue('JSESSIONID=new456; Path=/; HttpOnly')
        }
      });

      // Call getValidSession
      const result = await service.getValidSession(mockCredentials.uid, mockCredentials.password);

      // Verify login was called
      expect(fetchMock).toHaveBeenCalled();

      // Verify the result is the new JSESSIONID
      expect(result).toBe('new456');
    });

    test('should login if no session exists', async () => {
      // No existing session
      service.jsessionId = null;
      service.jsessionExpiry = null;

      // Mock successful login response
      fetchMock.mockResolvedValueOnce({
        ok: true,
        headers: {
          get: jest.fn().mockReturnValue('JSESSIONID=new456; Path=/; HttpOnly')
        }
      });

      // Call getValidSession
      const result = await service.getValidSession(mockCredentials.uid, mockCredentials.password);

      // Verify login was called
      expect(fetchMock).toHaveBeenCalled();

      // Verify the result is the new JSESSIONID
      expect(result).toBe('new456');
    });
  });

  describe('isSessionValid', () => {
    test('should return true if session is valid', () => {
      // Set a valid session
      service.jsessionId = 'abc123';
      service.jsessionExpiry = Date.now() + 1000000; // Far in the future

      // Call isSessionValid
      const result = service.isSessionValid();

      // Verify the result is true
      expect(result).toBe(true);
    });

    test('should return false if session is expired', () => {
      // Set an expired session
      service.jsessionId = 'abc123';
      service.jsessionExpiry = Date.now() - 1000; // In the past

      // Call isSessionValid
      const result = service.isSessionValid();

      // Verify the result is false
      expect(result).toBe(false);
    });

    test('should return false if no session exists', () => {
      // No existing session
      service.jsessionId = null;
      service.jsessionExpiry = null;

      // Call isSessionValid
      const result = service.isSessionValid();

      // Verify the result is false
      expect(result).toBe(false);
    });
  });
});

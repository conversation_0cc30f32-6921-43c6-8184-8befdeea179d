/**
 * Test Direct SSH Connection to QA02
 */
const { Client } = require('ssh2');
const fs = require('fs');
const path = require('path');

// Load environment configuration
const dbEnvironments = require('./db-environments');
const qa02Config = dbEnvironments.environments.qa02;

// Set environment variables
Object.entries(qa02Config).forEach(([key, value]) => {
  process.env[key] = String(value);
});

console.log('QA02 Environment Configuration:');
console.log(JSON.stringify(qa02Config, null, 2));

// SSH connection parameters
const sshConfig = {
  host: qa02Config.SSH_HOST,
  port: parseInt(qa02Config.SSH_PORT || '22', 10),
  username: qa02Config.SSH_USER,
  privateKey: fs.readFileSync(qa02Config.SSH_KEY_PATH),
  readyTimeout: 30000,
  // Add debug option to see detailed SSH connection logs
  debug: true,
  algorithms: {
    kex: [
      'diffie-hellman-group1-sha1',
      'diffie-hellman-group14-sha1',
      'diffie-hellman-group-exchange-sha1',
      'diffie-hellman-group-exchange-sha256',
      'ecdh-sha2-nistp256',
      'ecdh-sha2-nistp384',
      'ecdh-sha2-nistp521',
      '<EMAIL>'
    ],
    cipher: [
      'aes128-ctr',
      'aes192-ctr',
      'aes256-ctr',
      'aes128-gcm',
      '<EMAIL>',
      'aes256-gcm',
      '<EMAIL>',
      'aes256-cbc',
      'aes192-cbc',
      'aes128-cbc',
      'blowfish-cbc',
      '3des-cbc'
    ],
    serverHostKey: [
      'ssh-rsa',
      'ssh-dss',
      'ecdsa-sha2-nistp256',
      'ecdsa-sha2-nistp384',
      'ecdsa-sha2-nistp521',
      'rsa-sha2-256',
      'rsa-sha2-512'
    ],
    hmac: [
      'hmac-sha2-256',
      'hmac-sha2-512',
      'hmac-sha1',
      'hmac-md5',
      'hmac-sha2-256-96',
      'hmac-sha2-512-96',
      'hmac-sha1-96',
      'hmac-md5-96'
    ]
  }
};

// Create SSH client
const sshClient = new Client();

// Connect to SSH server
console.log(`Connecting to SSH server ${sshConfig.host}:${sshConfig.port} as ${sshConfig.username}...`);

sshClient.on('ready', () => {
  console.log('✅ SSH connection established successfully!');
  
  // Test MySQL connectivity
  console.log(`Testing MySQL connectivity to ${qa02Config.DB_HOST}:${qa02Config.DB_PORT}...`);
  
  // Test if MySQL port is open
  const mysqlPortCommand = `nc -z -v -w5 ${qa02Config.DB_HOST} ${qa02Config.DB_PORT}`;
  console.log(`Executing command: ${mysqlPortCommand}`);
  
  sshClient.exec(mysqlPortCommand, (err, stream) => {
    if (err) {
      console.error(`Error executing MySQL port test: ${err.message}`);
      sshClient.end();
      return;
    }
    
    let output = '';
    stream.on('data', (data) => {
      output += data.toString();
    });
    
    stream.stderr.on('data', (data) => {
      output += data.toString();
    });
    
    stream.on('close', (code) => {
      console.log(`MySQL port test output: ${output.trim()}`);
      console.log(`Exit code: ${code}`);
      
      if (code === 0) {
        console.log('✅ MySQL port is open and accessible');
        
        // Try a simple MySQL query
        console.log('Testing MySQL query execution...');
        
        const mysqlCommand = `mysql -h ${qa02Config.DB_HOST} -u ${qa02Config.DB_USER} -p${qa02Config.DB_PASSWORD} ${qa02Config.DB_NAME} -e "SELECT 1 AS connection_test"`;
        console.log(`Executing MySQL command (password hidden)...`);
        
        sshClient.exec(mysqlCommand, (err, stream) => {
          if (err) {
            console.error(`Error executing MySQL command: ${err.message}`);
            sshClient.end();
            return;
          }
          
          let queryOutput = '';
          stream.on('data', (data) => {
            queryOutput += data.toString();
          });
          
          stream.stderr.on('data', (data) => {
            queryOutput += data.toString();
          });
          
          stream.on('close', (code) => {
            console.log(`MySQL query output: ${queryOutput.trim()}`);
            console.log(`Exit code: ${code}`);
            
            if (code === 0) {
              console.log('✅ MySQL query executed successfully');
            } else {
              console.error('❌ MySQL query execution failed');
            }
            
            // Close the SSH connection
            sshClient.end();
          });
        });
      } else {
        console.error('❌ MySQL port is not accessible');
        
        // Check if MySQL is running
        console.log('Checking if MySQL is running on the server...');
        
        sshClient.exec('ps aux | grep mysql', (err, stream) => {
          if (err) {
            console.error(`Error checking MySQL process: ${err.message}`);
            sshClient.end();
            return;
          }
          
          let processOutput = '';
          stream.on('data', (data) => {
            processOutput += data.toString();
          });
          
          stream.stderr.on('data', (data) => {
            processOutput += data.toString();
          });
          
          stream.on('close', (code) => {
            console.log('MySQL processes:');
            console.log(processOutput.trim());
            
            // Close the SSH connection
            sshClient.end();
          });
        });
      }
    });
  });
});

sshClient.on('error', (err) => {
  console.error(`SSH connection error: ${err.message}`);
  process.exit(1);
});

sshClient.on('end', () => {
  console.log('SSH connection ended');
});

sshClient.on('close', () => {
  console.log('SSH connection closed');
});

// Connect to SSH server
sshClient.connect(sshConfig);

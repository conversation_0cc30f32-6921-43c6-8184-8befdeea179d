/**
 * SSH Connection Test Script
 * 
 * This script tests only the SSH connection to verify it works
 * independently of the MySQL connection
 */

const Client = require('ssh2').Client;
const fs = require('fs');
const path = require('path');
require('dotenv').config(); // Load environment variables from .env file

// Configuration
const sshConfig = {
  host: process.env.SSH_HOST || 'mprts-qa03.lab.wagerworks.com',
  port: parseInt(process.env.SSH_PORT || '22', 10),
  username: process.env.SSH_USER || 'volfkoi',
  privateKey: fs.readFileSync(process.env.SSH_KEY_PATH || path.join(process.env.USERPROFILE || process.env.HOME, '.ssh', 'id_rsa_dbserver'))
};

console.log('SSH Connection Test');
console.log('-----------------');
console.log(`Host: ${sshConfig.host}`);
console.log(`Port: ${sshConfig.port}`);
console.log(`Username: ${sshConfig.username}`);
console.log(`Key: ${sshConfig.privateKey}`);
console.log('-----------------\n');

// Create SSH client
const conn = new Client();

// Setup connection handlers
conn.on('ready', () => {
  console.log('✅ SSH connection established successfully!');
  
  // Test executing a remote command
  console.log('\nExecuting remote command: hostname...');
  conn.exec('hostname', (err, stream) => {
    if (err) {
      console.error('❌ Failed to execute command:', err);
      conn.end();
      return;
    }
    
    let output = '';
    stream.on('data', (data) => {
      output += data.toString();
    });
    
    stream.on('close', (code) => {
      console.log(`Command output: ${output.trim()}`);
      console.log(`Exit code: ${code}`);
      
      // Test port 3306 on localhost
      console.log('\nTesting MySQL port on remote server...');
      conn.exec('nc -zv 127.0.0.1 3306 || echo "Port 3306 is not open"', (err, stream) => {
        if (err) {
          console.error('❌ Failed to test port:', err);
          conn.end();
          return;
        }
        
        let portOutput = '';
        stream.on('data', (data) => {
          portOutput += data.toString();
        });
        
        stream.on('close', (code) => {
          console.log(`Port test output: ${portOutput.trim()}`);
          console.log(`Exit code: ${code}`);
          
          // List running MySQL processes
          console.log('\nChecking for MySQL processes on remote server...');
          conn.exec('ps aux | grep mysql', (err, stream) => {
            if (err) {
              console.error('❌ Failed to check processes:', err);
              conn.end();
              return;
            }
            
            let processOutput = '';
            stream.on('data', (data) => {
              processOutput += data.toString();
            });
            
            stream.on('close', () => {
              console.log('MySQL processes:');
              console.log(processOutput.trim());
              
              console.log('\n✅ SSH tests completed successfully!');
              conn.end();
            });
          });
        });
      });
    });
  });
});

conn.on('error', (err) => {
  console.error('❌ SSH connection error:', err);
  process.exit(1);
});

conn.on('end', () => {
  console.log('SSH connection ended');
});

conn.on('close', () => {
  console.log('SSH connection closed');
  process.exit(0);
});

// Connect to SSH server
console.log('Connecting to SSH server...');
conn.connect(sshConfig); 
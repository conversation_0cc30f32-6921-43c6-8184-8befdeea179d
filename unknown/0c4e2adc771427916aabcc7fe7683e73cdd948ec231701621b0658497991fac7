# AI Integration

## Overview

The refactored database layer is designed to be easily extensible for future AI integration. The AI integration components are currently placeholders, but they provide a foundation for future development. This document describes the AI integration architecture and how it can be used to enhance the database layer.

## AI Integration Architecture

The AI integration architecture consists of three main components:

1. **Query Generator**: Generates SQL queries from natural language
2. **Request Generator**: Generates HTTP requests from natural language
3. **Context Provider**: Provides database schema and context information to AI

These components are located in the `ai` directory:

```
database/ai/
├── index.js              # AI module exports
├── query-generator.js    # AI-driven query generation
├── request-generator.js  # AI-driven HTTP request generation
└── context-provider.js   # Database context for AI
```

## Query Generator

The Query Generator is responsible for generating SQL queries from natural language descriptions. It uses the database schema provided by the Context Provider to understand the database structure and generate appropriate queries.

### Implementation

The Query Generator is implemented in `ai/query-generator.js`:

```javascript
/**
 * AI-driven Query Generator
 * Generates SQL queries based on natural language input
 */
const QueryBuilder = require('../utils/query-builder');

/**
 * Generate a SQL query from natural language
 * @param {string} description - Natural language query description
 * @param {Object} dbSchema - Database schema information
 * @returns {Object} - SQL query and parameters
 */
async function generateQuery(description, dbSchema) {
  // This is a placeholder for future AI integration
  console.log(`[AI] Generating query from: "${description}"`);
  
  // For now, return a simple query based on keywords
  const queryBuilder = new QueryBuilder();
  
  if (description.includes('test case') || description.includes('test cases')) {
    queryBuilder.select('test_case', ['tc_id', 'name', 'status']);
    queryBuilder.limit(10);
  } else if (description.includes('test suite') || description.includes('test suites')) {
    queryBuilder.select('test_case_group', ['ts_id', 'name', 'status']);
    queryBuilder.limit(10);
  } else if (description.includes('active test') || description.includes('running test')) {
    queryBuilder.select('test_session', ['tsn_id', 'tc_id', 'uid', 'start_ts']);
    queryBuilder.where('end_ts', 'IS', null);
    queryBuilder.limit(10);
  } else if (description.includes('test result') || description.includes('test results')) {
    queryBuilder.select('test_result r', ['r.tsn_id', 'r.tc_id', 'r.outcome', 'r.creation_time']);
    queryBuilder.join('output o', 'r.cnt = o.cnt');
    queryBuilder.limit(10);
  } else {
    // Default query
    queryBuilder.select('test_result', ['tsn_id', 'tc_id', 'outcome', 'creation_time']);
    queryBuilder.limit(10);
  }
  
  return queryBuilder.build();
}

module.exports = {
  generateQuery
};
```

### Usage

```javascript
const { queryGenerator } = require('./ai');
const { getDatabaseSchema } = require('./ai/context-provider');

// Get database schema
const dbSchema = getDatabaseSchema();

// Generate a query from natural language
const description = 'Get all active test cases';
const { sql, params } = await queryGenerator.generateQuery(description, dbSchema);

console.log(`Generated SQL: ${sql}`);
console.log(`Parameters: ${JSON.stringify(params)}`);
```

## Request Generator

The Request Generator is responsible for generating HTTP requests from natural language descriptions. It uses the API schema to understand the available endpoints and generate appropriate requests.

### Implementation

The Request Generator is implemented in `ai/request-generator.js`:

```javascript
/**
 * AI-driven HTTP Request Generator
 * Generates HTTP requests based on natural language input
 */

/**
 * Generate an HTTP request from natural language
 * @param {string} description - Natural language request description
 * @param {Object} apiSchema - API schema information
 * @returns {Object} - HTTP request configuration
 */
async function generateRequest(description, apiSchema) {
  // This is a placeholder for future AI integration
  console.log(`[AI] Generating HTTP request from: "${description}"`);
  
  // For now, return a simple request based on keywords
  if (description.includes('test case') || description.includes('test cases')) {
    return {
      method: 'GET',
      endpoint: '/local/test-cases',
      params: {
        limit: 10
      }
    };
  } else if (description.includes('test suite') || description.includes('test suites')) {
    return {
      method: 'GET',
      endpoint: '/local/test-suites',
      params: {
        limit: 10
      }
    };
  } else if (description.includes('active test') || description.includes('running test')) {
    return {
      method: 'GET',
      endpoint: '/local/active-tests',
      params: {}
    };
  } else if (description.includes('test result') || description.includes('test results')) {
    return {
      method: 'GET',
      endpoint: '/api/test-reports',
      params: {
        limit: 10
      }
    };
  } else if (description.includes('run') && description.includes('test case')) {
    return {
      method: 'POST',
      endpoint: '/api/case-runner',
      body: {
        tc_id: '3180', // Example test case ID
        envir: 'qa02',
        shell_host: 'jps-qa10-app01'
      }
    };
  } else if (description.includes('run') && description.includes('test suite')) {
    return {
      method: 'POST',
      endpoint: '/api/run-suite',
      body: {
        ts_id: '101', // Example test suite ID
        envir: 'qa02',
        shell_host: 'jps-qa10-app01'
      }
    };
  } else {
    // Default request
    return {
      method: 'GET',
      endpoint: '/api/test-status',
      params: {}
    };
  }
}

module.exports = {
  generateRequest
};
```

### Usage

```javascript
const { requestGenerator } = require('./ai');

// Generate a request from natural language
const description = 'Run test case 3180';
const request = await requestGenerator.generateRequest(description);

console.log(`Generated request: ${JSON.stringify(request)}`);
```

## Context Provider

The Context Provider is responsible for providing database schema and context information to AI components. It provides information about the database tables, columns, relationships, and common query patterns.

### Implementation

The Context Provider is implemented in `ai/context-provider.js`:

```javascript
/**
 * Database Context Provider for AI
 * Provides database schema and context information to AI components
 */

/**
 * Get database schema information
 * @returns {Object} - Database schema information
 */
function getDatabaseSchema() {
  // This would be implemented to provide schema information to AI
  return {
    tables: {
      test_case: {
        columns: ['tc_id', 'uid', 'status', 'case_driver', 'tp_id', 'comments', 'tickets', 'name'],
        primaryKey: 'tc_id'
      },
      test_case_group: {
        columns: ['tcg_id', 'ts_id', 'tc_id', 'seq_index', 'uid', 'status', 'pj_id', 'name', 'comments', 'tickets', 'tag'],
        primaryKey: 'tcg_id'
      },
      test_result: {
        columns: ['cnt', 'tsn_id', 'tc_id', 'seq_index', 'outcome', 'creation_time'],
        primaryKey: 'cnt'
      },
      test_session: {
        columns: ['tsn_id', 'tc_id', 'uid', 'start_ts', 'end_ts'],
        primaryKey: 'tsn_id'
      },
      output: {
        columns: ['cnt', 'txt'],
        primaryKey: 'cnt'
      }
    },
    relationships: [
      { from: 'test_case_group', fromColumn: 'tc_id', to: 'test_case', toColumn: 'tc_id' },
      { from: 'test_result', fromColumn: 'tc_id', to: 'test_case', toColumn: 'tc_id' },
      { from: 'test_result', fromColumn: 'cnt', to: 'output', toColumn: 'cnt' }
    ]
  };
}

/**
 * Get common query patterns
 * @returns {Object} - Common query patterns
 */
function getQueryPatterns() {
  return {
    getTestCases: {
      description: 'Get test cases with optional filtering',
      template: 'SELECT tc_id, name, status FROM test_case WHERE {conditions} LIMIT {limit}'
    },
    getTestSuites: {
      description: 'Get test suites with optional filtering',
      template: 'SELECT ts_id, name, status FROM test_case_group WHERE {conditions} LIMIT {limit}'
    },
    getActiveTests: {
      description: 'Get active test sessions',
      template: 'SELECT tsn_id, tc_id, uid, start_ts FROM test_session WHERE end_ts IS NULL LIMIT {limit}'
    },
    getTestResults: {
      description: 'Get test results for a specific test session',
      template: 'SELECT r.tsn_id, r.tc_id, r.outcome, r.creation_time, o.txt FROM test_result r JOIN output o ON r.cnt = o.cnt WHERE r.tsn_id = {tsn_id}'
    }
  };
}

module.exports = {
  getDatabaseSchema,
  getQueryPatterns
};
```

### Usage

```javascript
const { contextProvider } = require('./ai');

// Get database schema
const dbSchema = contextProvider.getDatabaseSchema();
console.log(`Database schema: ${JSON.stringify(dbSchema)}`);

// Get common query patterns
const queryPatterns = contextProvider.getQueryPatterns();
console.log(`Query patterns: ${JSON.stringify(queryPatterns)}`);
```

## Integration with the Database Layer

The AI integration components are integrated with the database layer through the `executeNaturalLanguageQuery` method in the main database module:

```javascript
/**
 * Execute a query generated from natural language
 * @param {string} description - Natural language query description
 * @returns {Promise<Array>} - Query results
 */
async function executeNaturalLanguageQuery(description) {
  if (!isInitialized) {
    await init();
  }
  
  try {
    // Get database schema for context
    const dbSchema = getDatabaseSchema();
    
    // Generate query from natural language
    const { sql, params } = await generateQuery(description, dbSchema);
    
    // Log the generated query
    log(`[AI] Generated SQL: ${sql}`);
    log(`[AI] Parameters: ${JSON.stringify(params)}`);
    
    // Execute the query
    return await query(sql, params);
  } catch (error) {
    console.error(`[AI] Error executing natural language query: ${error.message}`);
    throw error;
  }
}
```

### Usage

```javascript
const db = require('./database');

// Initialize the database connection
await db.init();

// Execute a natural language query
const results = await db.executeNaturalLanguageQuery('Get all active test cases');
console.log(`Results: ${JSON.stringify(results)}`);

// Close the connection
await db.close();
```

## Future Development

The AI integration components are currently placeholders, but they provide a foundation for future development. Here are some ideas for future development:

1. **Natural Language Query Processing**: Implement a more sophisticated natural language query processor that can understand complex queries.
2. **Query Generation**: Implement a more sophisticated query generator that can generate complex queries based on natural language.
3. **Request Generation**: Implement a more sophisticated request generator that can generate complex HTTP requests based on natural language.
4. **Context Awareness**: Implement a more sophisticated context provider that can provide more detailed information about the database schema and API.
5. **Learning**: Implement a learning system that can improve query and request generation based on user feedback.

## Integration with External AI Services

The AI integration components can be integrated with external AI services such as OpenAI's GPT models or other natural language processing services. Here's an example of how this could be implemented:

```javascript
/**
 * Generate a SQL query from natural language using an external AI service
 * @param {string} description - Natural language query description
 * @param {Object} dbSchema - Database schema information
 * @returns {Object} - SQL query and parameters
 */
async function generateQueryWithExternalAI(description, dbSchema) {
  // Prepare the prompt for the AI service
  const prompt = `
    Given the following database schema:
    ${JSON.stringify(dbSchema)}
    
    Generate a SQL query for the following request:
    ${description}
    
    Return the SQL query and parameters as a JSON object with the following structure:
    {
      "sql": "SELECT ... FROM ... WHERE ...",
      "params": [param1, param2, ...]
    }
  `;
  
  // Call the external AI service
  const response = await callExternalAIService(prompt);
  
  // Parse the response
  const { sql, params } = JSON.parse(response);
  
  return { sql, params };
}
```

## Conclusion

The AI integration components provide a foundation for future AI integration with the database layer. They are currently placeholders, but they demonstrate how AI can be used to enhance the database layer with natural language query processing and request generation. With further development, these components could provide a powerful way to interact with the database using natural language.

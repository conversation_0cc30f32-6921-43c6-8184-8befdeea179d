# Parameter Mapping

This document provides a detailed mapping of parameters between the frontend, API, and database layers of the SmartTest application.

## Table of Contents

1. [Frontend to API Parameters](#frontend-to-api-parameters)
2. [API to Database Parameters](#api-to-database-parameters)
3. [Database to API Response Parameters](#database-to-api-response-parameters)
4. [API to Frontend Response Parameters](#api-to-frontend-response-parameters)
5. [Parameter Naming Conventions](#parameter-naming-conventions)

## Frontend to API Parameters

### Running a Test Suite

| Frontend Parameter | API Parameter | Type | Description | Required |
|-------------------|---------------|------|-------------|----------|
| `tsId` | `ts_id` | Number | Test suite ID | Yes |
| `environment` | `envir` | String | Environment (qa01, qa02, qa03) | No (default: 'qa02') |
| `shellHost` | `shell_host` | String | Shell host for test execution | No (default: 'jps-qa10-app01') |
| `credentials.uid` | `uid` | String | User ID | Yes |
| `credentials.password` | `password` | String | User password | Yes |

### Getting Test Cases

| Frontend Parameter | API Parameter | Type | Description | Required |
|-------------------|---------------|------|-------------|----------|
| `suiteId` | `ts_id` | Number | Test suite ID | No |
| `status` | `status` | String | Test case status | No |
| `limit` | `limit` | Number | Maximum number of results | No (default: 100) |

### Getting Test Suites

| Frontend Parameter | API Parameter | Type | Description | Required |
|-------------------|---------------|------|-------------|----------|
| `name` | `name` | String | Test suite name (partial match) | No |
| `status` | `status` | String | Test suite status | No |
| `userId` | `uid` | String | User ID | No |
| `limit` | `limit` | Number | Maximum number of results | No (default: 100) |

### Getting Active Tests

| Frontend Parameter | API Parameter | Type | Description | Required |
|-------------------|---------------|------|-------------|----------|
| `credentials.uid` | Extracted from authentication | String | User ID | Yes |

### Getting Test Results

| Frontend Parameter | API Parameter | Type | Description | Required |
|-------------------|---------------|------|-------------|----------|
| `tsnId` | `tsn_id` (in URL path) | String | Test session ID | Yes |

### Searching Test Cases

| Frontend Parameter | API Parameter | Type | Description | Required |
|-------------------|---------------|------|-------------|----------|
| `name` | `name` | String | Test case name (partial match) | No |
| `status` | `status` | String | Test case status | No |
| `minId` | `min_id` | Number | Minimum test case ID | No |
| `maxId` | `max_id` | Number | Maximum test case ID | No |
| `limit` | `limit` | Number | Maximum number of results | No (default: 20) |

## API to Database Parameters

### Getting Test Suite Info

| API Parameter | Database Parameter | Type | Description | Required |
|--------------|-------------------|------|-------------|----------|
| `ts_id` | `ts_id` | Number/String | Test suite ID | Yes |

### Getting Active Tests

| API Parameter | Database Parameter | Type | Description | Required |
|--------------|-------------------|------|-------------|----------|
| `req.user.uid` | `uid` | String | User ID | No |
| `req.query.limit` | `limit` | Number | Maximum number of results | No (default: 20) |

### Getting Test Results

| API Parameter | Database Parameter | Type | Description | Required |
|--------------|-------------------|------|-------------|----------|
| `req.params.tsn_id` | `tsn_id` | String/Number | Test session ID | Yes |

### Getting Test Cases

| API Parameter | Database Parameter | Type | Description | Required |
|--------------|-------------------|------|-------------|----------|
| `req.query.ts_id` | `ts_id` | Number | Test suite ID | No |
| `req.query.status` | `status` | String | Test case status | No |
| `req.query.limit` | `limit` | Number | Maximum number of results | No (default: 100) |

### Getting Test Suites

| API Parameter | Database Parameter | Type | Description | Required |
|--------------|-------------------|------|-------------|----------|
| `req.query.name` | `name` | String | Test suite name (partial match) | No |
| `req.query.status` | `status` | String | Test suite status | No |
| `req.query.uid` | `uid` | String | User ID | No |
| `req.query.limit` | `limit` | Number | Maximum number of results | No (default: 100) |

### Searching Test Cases

| API Parameter | Database Parameter | Type | Description | Required |
|--------------|-------------------|------|-------------|----------|
| `req.query.name` | `name` | String | Test case name (partial match) | No |
| `req.query.status` | `status` | String | Test case status | No |
| `req.query.min_id` | `min_id` | Number | Minimum test case ID | No |
| `req.query.max_id` | `max_id` | Number | Maximum test case ID | No |
| `req.query.limit` | `limit` | Number | Maximum number of results | No (default: 20) |

## Database to API Response Parameters

### Test Suite Info Response

| Database Response | API Response | Type | Description |
|------------------|--------------|------|-------------|
| `ts_id` | `data.ts_id` | Number | Test suite ID |
| `uid` | `data.uid` | String | User ID |
| `status` | `data.status` | String | Test suite status |
| `tp_id` | `data.tp_id` | Number | Test plan ID |
| `name` | `data.name` | String | Test suite name |
| `comments` | `data.comments` | String | Test suite comments |
| `testCaseCount` | `data.testCaseCount` | Number | Number of test cases in the suite |
| `testCases` | `data.testCases` | Array | Test cases in the suite |

### Active Tests Response

| Database Response | API Response | Type | Description |
|------------------|--------------|------|-------------|
| `tsn_id` | `data[].tsn_id` | String | Test session ID |
| `tc_id` | `data[].tc_id` | String | Test case ID |
| `initiator_user` | `data[].initiator_user` | String | User ID of the initiator |
| `creation_time` | `data[].creation_time` | String | Creation time |
| `is_current_user` | `data[].is_current_user` | Boolean | Whether the session belongs to the current user |

### Test Results Response

| Database Response | API Response | Type | Description |
|------------------|--------------|------|-------------|
| `tsn_id` | `data.tsn_id` | String | Test session ID |
| `total_results` | `data.total_results` | Number | Total number of results |
| `pass_count` | `data.pass_count` | Number | Number of passed results |
| `fail_count` | `data.fail_count` | Number | Number of failed results |
| `start_time` | `data.start_time` | String | Start time |
| `end_time` | `data.end_time` | String | End time |
| `duration` | `data.duration` | String | Duration |
| `results` | `data.results` | Array | Test case results |
| `results[].tc_id` | `data.results[].tc_id` | String | Test case ID |
| `results[].total_steps` | `data.results[].total_steps` | Number | Total number of steps |
| `results[].passed_steps` | `data.results[].passed_steps` | Number | Number of passed steps |
| `results[].failed_steps` | `data.results[].failed_steps` | Number | Number of failed steps |
| `results[].steps` | `data.results[].steps` | Array | Step results |
| `results[].steps[].seq_index` | `data.results[].steps[].seq_index` | Number | Sequence index |
| `results[].steps[].outcome` | `data.results[].steps[].outcome` | String | Outcome (P for pass, F for fail) |
| `results[].steps[].creation_time` | `data.results[].steps[].creation_time` | String | Creation time |
| `results[].steps[].output` | `data.results[].steps[].output` | String | Output text |

### Test Cases Response

| Database Response | API Response | Type | Description |
|------------------|--------------|------|-------------|
| `tc_id` | `data[].tc_id` | Number | Test case ID |
| `uid` | `data[].uid` | String | User ID |
| `status` | `data[].status` | String | Test case status |
| `case_driver` | `data[].case_driver` | String | Case driver |
| `tp_id` | `data[].tp_id` | Number | Test plan ID |
| `comments` | `data[].comments` | String | Test case comments |
| `tickets` | `data[].tickets` | String | Test case tickets |
| `name` | `data[].name` | String | Test case name |

### Test Suites Response

| Database Response | API Response | Type | Description |
|------------------|--------------|------|-------------|
| `ts_id` | `data[].ts_id` | Number | Test suite ID |
| `uid` | `data[].uid` | String | User ID |
| `status` | `data[].status` | String | Test suite status |
| `tp_id` | `data[].tp_id` | Number | Test plan ID |
| `name` | `data[].name` | String | Test suite name |
| `comments` | `data[].comments` | String | Test suite comments |

## API to Frontend Response Parameters

All API responses follow a consistent format:

```javascript
{
  success: true,
  data: {...}, // or []
  message: "Operation successful"
}
```

In case of an error:

```javascript
{
  success: false,
  message: "Error message",
  error: "Detailed error message"
}
```

The `data` field contains the actual response data, which is passed directly to the frontend.

### Run Test Suite Response

| API Response | Frontend Parameter | Type | Description |
|--------------|-------------------|------|-------------|
| `success` | `response.success` | Boolean | Whether the operation was successful |
| `tsn_id` | `response.tsn_id` | String | Test session ID |
| `message` | `response.message` | String | Success message |

### Active Tests Response

| API Response | Frontend Parameter | Type | Description |
|--------------|-------------------|------|-------------|
| `success` | `response.success` | Boolean | Whether the operation was successful |
| `data` | `response.data` | Array | Active tests |
| `message` | `response.message` | String | Success message |

### Test Results Response

| API Response | Frontend Parameter | Type | Description |
|--------------|-------------------|------|-------------|
| `success` | `response.success` | Boolean | Whether the operation was successful |
| `data` | `response.data` | Object | Test results |
| `message` | `response.message` | String | Success message |

### Test Cases Response

| API Response | Frontend Parameter | Type | Description |
|--------------|-------------------|------|-------------|
| `success` | `response.success` | Boolean | Whether the operation was successful |
| `data` | `response.data` | Array | Test cases |
| `message` | `response.message` | String | Success message |

### Test Suites Response

| API Response | Frontend Parameter | Type | Description |
|--------------|-------------------|------|-------------|
| `success` | `response.success` | Boolean | Whether the operation was successful |
| `data` | `response.data` | Array | Test suites |
| `message` | `response.message` | String | Success message |

## Parameter Naming Conventions

### Frontend

The frontend uses camelCase for parameter names:

- `tsId`
- `tsnId`
- `projectId`
- `suiteId`
- `userId`
- `shellHost`

### API

The API uses snake_case for both request and response parameters:

- Request: `ts_id`, `tsn_id`, `tp_id`, `tc_id`, `uid`, `shell_host`
- Response: `ts_id`, `tsn_id`, `tp_id`, `tc_id`, `uid`, `shell_host`

### Database

The database layer uses snake_case for parameter names:

- `ts_id`
- `tsn_id`
- `tp_id`
- `tc_id`
- `uid`
- `shell_host`

### Migration Notes

When migrating from the old database layer to the new one, parameter names have been standardized to use snake_case consistently:

- Old: `suiteId`, `projectId`, `userId`, `tsnId`, `tcId`, `tsId`
- New: `ts_id`, `tp_id`, `uid`, `tsn_id`, `tc_id`, `ts_id`

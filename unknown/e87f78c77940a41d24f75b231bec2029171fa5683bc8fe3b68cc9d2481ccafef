# Database Connection Methods

## Overview

The refactored database layer supports two connection methods:

1. **Direct SSH**: Executes SQL queries through direct SSH commands (primary method)
2. **SSH Tunnel**: Creates an SSH tunnel and connects to the database through the tunnel (fallback method)

This document provides detailed information about these connection methods, including their implementation, configuration, and usage.

## Connection Factory

The connection factory is responsible for creating the appropriate connection based on the environment and options:

```javascript
const connection = ConnectionFactory.createConnection(environment, options);
```

By default, the connection factory prioritizes direct SSH connections, but SSH tunnel can be forced if needed:

```javascript
// Force SSH tunnel connection
const connection = ConnectionFactory.createConnection(environment, { forceTunnel: true });
```

### Implementation

The connection factory is implemented in `connections/index.js`:

```javascript
/**
 * Connection Factory
 * Creates database connections with direct SSH as the primary method
 */
const DirectSshConnection = require('./direct-ssh');
const SshTunnelConnection = require('./ssh-tunnel');
const config = require('../config');

/**
 * Create a database connection
 * @param {string} environment - Environment name
 * @param {Object} options - Connection options
 * @returns {Object} - Database connection
 */
function createConnection(environment, options = {}) {
  const { forceTunnel } = options;
  const envConfig = config.getConfig(environment);
  
  // Always use direct SSH unless tunnel is explicitly forced
  if (forceTunnel) {
    console.log('Using SSH tunnel connection as requested');
    return new SshTunnelConnection(envConfig);
  } else {
    console.log('Using direct SSH connection (primary method)');
    return new DirectSshConnection(envConfig);
  }
}

module.exports = {
  createConnection
};
```

## Direct SSH Connection

The direct SSH connection method executes SQL queries through direct SSH commands. This is the primary connection method used by the database layer.

### Implementation

The direct SSH connection is implemented in `connections/direct-ssh.js`. It uses the `ssh2` library to establish an SSH connection and execute MySQL commands through the SSH connection.

### Key Features

- **SSH Authentication**: Supports both key-based and password-based authentication
- **Query Execution**: Executes SQL queries through SSH commands
- **Result Parsing**: Parses the results of SQL queries into JavaScript objects
- **Error Handling**: Includes robust error handling for SSH and MySQL errors

### Usage

```javascript
const DirectSshConnection = require('./connections/direct-ssh');
const config = require('./config').getConfig('qa02');

// Create a direct SSH connection
const connection = new DirectSshConnection(config);

// Initialize the connection
await connection.init();

// Execute a query
const results = await connection.query('SELECT * FROM test_case LIMIT 10');

// Close the connection
await connection.close();
```

### Configuration

The direct SSH connection requires the following configuration:

- **SSH_HOST**: SSH server hostname
- **SSH_PORT**: SSH server port (default: 22)
- **SSH_USER**: SSH username
- **SSH_KEY_PATH**: Path to SSH private key file
- **SSH_PASSWORD**: SSH password (used if key file is not available)
- **DB_USER**: Database username
- **DB_PASSWORD**: Database password
- **DB_NAME**: Database name

## SSH Tunnel Connection

The SSH tunnel connection method creates an SSH tunnel and connects to the database through the tunnel. This is the fallback connection method used by the database layer.

### Implementation

The SSH tunnel connection is implemented in `connections/ssh-tunnel.js`. It uses the `ssh2` library to establish an SSH tunnel and the `mysql2/promise` library to connect to the database through the tunnel.

### Key Features

- **SSH Authentication**: Supports both key-based and password-based authentication
- **Tunnel Creation**: Creates an SSH tunnel to the database server
- **Connection Pooling**: Uses a connection pool for better performance
- **Error Handling**: Includes robust error handling for SSH and MySQL errors

### Usage

```javascript
const SshTunnelConnection = require('./connections/ssh-tunnel');
const config = require('./config').getConfig('qa02');

// Create an SSH tunnel connection
const connection = new SshTunnelConnection(config);

// Initialize the connection
await connection.init();

// Execute a query
const results = await connection.query('SELECT * FROM test_case LIMIT 10');

// Close the connection
await connection.close();
```

### Configuration

The SSH tunnel connection requires the following configuration:

- **SSH_HOST**: SSH server hostname
- **SSH_PORT**: SSH server port (default: 22)
- **SSH_USER**: SSH username
- **SSH_KEY_PATH**: Path to SSH private key file
- **SSH_PASSWORD**: SSH password (used if key file is not available)
- **DB_HOST**: Database server hostname
- **DB_PORT**: Database server port (default: 3306)
- **DB_USER**: Database username
- **DB_PASSWORD**: Database password
- **DB_NAME**: Database name

## Connection Interface

Both connection methods implement the same interface:

- `init()`: Initialize the connection
- `query(sql, params)`: Execute a SQL query
- `close()`: Close the connection

This allows the database layer to use either connection method interchangeably.

## Error Handling

Both connection methods include robust error handling:

- **Connection Errors**: If a connection fails, it will throw an error with details about the failure
- **Query Errors**: If a query fails, it will throw an error with details about the failure
- **Cleanup**: If an error occurs during initialization, any partial connections will be cleaned up

## Best Practices

1. **Use Direct SSH by Default**: The direct SSH connection method is more reliable and should be used by default
2. **Provide SSH Key**: Always provide an SSH key file for authentication if possible
3. **Handle Connection Errors**: Always handle connection errors in your code
4. **Close Connections**: Always close connections when you're done with them
5. **Use Connection Pooling**: The SSH tunnel connection method uses connection pooling for better performance

## Troubleshooting

### Common Issues

1. **SSH Connection Refused**: Make sure the SSH server is running and accessible
2. **SSH Authentication Failed**: Make sure the SSH username and key/password are correct
3. **MySQL Connection Failed**: Make sure the MySQL server is running and accessible
4. **Query Execution Failed**: Make sure the SQL query is valid and the user has the necessary permissions

### Debugging

To enable debug logging, set the `DB_DEBUG` environment variable to `true`:

```
DB_DEBUG=true
```

This will log detailed information about the connection process and query execution, which can be helpful for troubleshooting.

## Performance Considerations

1. **Connection Initialization**: Initializing a connection can take some time, especially for SSH tunnel connections. Initialize connections once and reuse them.
2. **Query Execution**: Executing queries through SSH can be slower than direct database connections. Use connection pooling and optimize your queries.
3. **Result Parsing**: Parsing large result sets can be slow. Limit the number of rows returned by your queries.
4. **Connection Cleanup**: Always close connections when you're done with them to free up resources.

## Security Considerations

1. **SSH Keys**: Use SSH keys instead of passwords for better security
2. **Database Credentials**: Store database credentials securely (e.g., in environment variables)
3. **Query Parameters**: Always use parameterized queries to prevent SQL injection
4. **Error Messages**: Be careful not to expose sensitive information in error messages

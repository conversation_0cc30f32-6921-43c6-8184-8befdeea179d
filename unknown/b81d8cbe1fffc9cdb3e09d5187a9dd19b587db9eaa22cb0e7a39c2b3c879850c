# Pre-defined Queries

## Overview

The database layer includes pre-defined queries for common operations. These queries are implemented in the `queries` directory and provide a convenient way to perform common database operations without having to write SQL queries manually.

## Query Modules

The pre-defined queries are organized into the following modules:

- **Test Cases**: Queries related to test cases
- **Test Suites**: Queries related to test suites
- **Test Sessions**: Queries related to test sessions
- **Test Results**: Queries related to test results

Each module provides a set of functions that execute specific queries and return the results in a consistent format.

## Test Case Queries

The test case queries are implemented in `queries/test-cases.js` and provide functions for querying test cases.

### getTestCases

Gets test cases with optional filtering.

```javascript
/**
 * Get test cases with optional filtering
 * @param {Object} connection - Database connection
 * @param {Object} filters - Optional filters
 * @returns {Promise<Array>} - Test cases
 */
async function getTestCases(connection, filters = {})
```

#### Parameters

- `connection`: Database connection
- `filters`: Optional filters
  - `ts_id`: Filter by test suite ID
  - `status`: Filter by status
  - `limit`: Maximum number of results to return (default: 100)

#### Returns

An array of test cases, each with the following properties:

- `tc_id`: Test case ID
- `uid`: User ID
- `status`: Status
- `case_driver`: Case driver
- `tp_id`: Test plan ID
- `comments`: Comments
- `tickets`: Tickets
- `name`: Name

#### Example

```javascript
const testCases = await testCaseQueries.getTestCases(connection, {
  ts_id: 123,
  status: 'active',
  limit: 10
});
```

### getTestCaseById

Gets a test case by ID.

```javascript
/**
 * Get a test case by ID
 * @param {Object} connection - Database connection
 * @param {number|string} tcId - Test case ID
 * @returns {Promise<Object>} - Test case
 */
async function getTestCaseById(connection, tcId)
```

#### Parameters

- `connection`: Database connection
- `tc_id`: Test case ID

#### Returns

A test case object with the following properties:

- `tc_id`: Test case ID
- `uid`: User ID
- `status`: Status
- `case_driver`: Case driver
- `tp_id`: Test plan ID
- `comments`: Comments
- `tickets`: Tickets
- `name`: Name

#### Example

```javascript
const testCase = await testCaseQueries.getTestCaseById(connection, 123);
```

### searchTestCases

Searches for test cases based on criteria.

```javascript
/**
 * Search test cases
 * @param {Object} connection - Database connection
 * @param {Object} criteria - Search criteria
 * @returns {Promise<Array>} - Test cases
 */
async function searchTestCases(connection, criteria = {})
```

#### Parameters

- `connection`: Database connection
- `criteria`: Search criteria
  - `name`: Filter by name (partial match)
  - `status`: Filter by status
  - `min_id`: Minimum test case ID
  - `max_id`: Maximum test case ID
  - `limit`: Maximum number of results to return (default: 20)

#### Returns

An array of test cases, each with the following properties:

- `tc_id`: Test case ID
- `uid`: User ID
- `status`: Status
- `case_driver`: Case driver
- `tp_id`: Test plan ID
- `comments`: Comments
- `tickets`: Tickets
- `name`: Name

#### Example

```javascript
const testCases = await testCaseQueries.searchTestCases(connection, {
  name: 'login',
  status: 'active',
  min_id: 1000,
  max_id: 2000,
  limit: 10
});
```

## Test Suite Queries

The test suite queries are implemented in `queries/test-suites.js` and provide functions for querying test suites.

### getTestSuites

Gets test suites with optional filtering.

```javascript
/**
 * Get test suites with optional filtering
 * @param {Object} connection - Database connection
 * @param {Object} filters - Optional filters
 * @returns {Promise<Array>} - Test suites
 */
async function getTestSuites(connection, filters = {})
```

#### Parameters

- `connection`: Database connection
- `filters`: Optional filters
  - `name`: Filter by name (partial match)
  - `status`: Filter by status
  - `uid`: Filter by user ID
  - `limit`: Maximum number of results to return (default: 100)

#### Returns

An array of test suites, each with the following properties:

- `ts_id`: Test suite ID
- `uid`: User ID
- `status`: Status
- `tp_id`: Test plan ID
- `name`: Name
- `comments`: Comments

#### Example

```javascript
const testSuites = await testSuiteQueries.getTestSuites(connection, {
  name: 'regression',
  status: 'active',
  uid: 'test_user',
  limit: 10
});
```

### getTestSuiteById

Gets a test suite by ID.

```javascript
/**
 * Get a test suite by ID
 * @param {Object} connection - Database connection
 * @param {number|string} tsId - Test suite ID
 * @returns {Promise<Object>} - Test suite
 */
async function getTestSuiteById(connection, tsId)
```

#### Parameters

- `connection`: Database connection
- `ts_id`: Test suite ID

#### Returns

A test suite object with the following properties:

- `ts_id`: Test suite ID
- `uid`: User ID
- `status`: Status
- `tp_id`: Test plan ID
- `name`: Name
- `comments`: Comments

#### Example

```javascript
const testSuite = await testSuiteQueries.getTestSuiteById(connection, 123);
```

### getTestSuiteInfo

Gets test cases in a test suite.

```javascript
/**
 * Get test cases in a test suite
 * @param {Object} connection - Database connection
 * @param {number|string} tsId - Test suite ID
 * @returns {Promise<Object>} - Test suite with test cases
 */
async function getTestSuiteInfo(connection, tsId)
```

#### Parameters

- `connection`: Database connection
- `ts_id`: Test suite ID

#### Returns

A test suite object with the following properties:

- `ts_id`: Test suite ID
- `uid`: User ID
- `status`: Status
- `tp_id`: Test plan ID
- `name`: Name
- `comments`: Comments
- `testCaseCount`: Number of test cases in the suite
- `testCases`: Array of test cases in the suite, each with the following properties:
  - `tc_id`: Test case ID
  - `seq_index`: Sequence index in the suite

#### Example

```javascript
const testSuiteInfo = await testSuiteQueries.getTestSuiteInfo(connection, 123);
```

## Test Session Queries

The test session queries are implemented in `queries/test-sessions.js` and provide functions for querying test sessions.

### getActiveTests

Gets active test sessions.

```javascript
/**
 * Get active test sessions
 * @param {Object} connection - Database connection
 * @param {Object} filters - Optional filters
 * @returns {Promise<Array>} - Active test sessions
 */
async function getActiveTests(connection, filters = {})
```

#### Parameters

- `connection`: Database connection
- `filters`: Optional filters
  - `uid`: Filter by user ID
  - `limit`: Maximum number of results to return (default: 20)

#### Returns

An array of active test sessions, each with the following properties:

- `tsn_id`: Test session ID
- `tc_id`: Test case ID
- `initiator_user`: User ID of the initiator
- `creation_time`: Creation time
- `is_current_user`: Whether the session belongs to the current user

#### Example

```javascript
const activeTests = await testSessionQueries.getActiveTests(connection, {
  uid: 'test_user',
  limit: 10
});
```

### getRecentRuns

Gets recent test sessions.

```javascript
/**
 * Get recent test sessions
 * @param {Object} connection - Database connection
 * @param {Object} filters - Optional filters
 * @returns {Promise<Array>} - Recent test sessions
 */
async function getRecentRuns(connection, filters = {})
```

#### Parameters

- `connection`: Database connection
- `filters`: Optional filters
  - `limit`: Maximum number of results to return (default: 20)

#### Returns

An array of recent test sessions, each with the following properties:

- `tsn_id`: Test session ID
- `start_time`: Start time
- `end_time`: End time
- `type`: Session type (TestCase, TestSuite, Project)
- `envir`: Environment
- `tc_id`: Test case ID
- `ts_id`: Test suite ID
- `pj_id`: Project ID

#### Example

```javascript
const recentRuns = await testSessionQueries.getRecentRuns(connection, {
  limit: 10
});
```

### getTestSessionDetails

Gets test session details.

```javascript
/**
 * Get test session details
 * @param {Object} connection - Database connection
 * @param {string|number} tsn_id - Test session ID
 * @returns {Promise<Object>} - Test session details
 */
async function getTestSessionDetails(connection, tsn_id)
```

#### Parameters

- `connection`: Database connection
- `tsn_id`: Test session ID

#### Returns

A test session object with the following properties:

- `tsn_id`: Test session ID
- `tc_id`: Test case ID
- `uid`: User ID
- `start_ts`: Start time
- `end_ts`: End time
- `pj_id`: Project ID
- `ts_id`: Test suite ID
- `duration`: Duration (if start_ts and end_ts are available)

#### Example

```javascript
const sessionDetails = await testSessionQueries.getTestSessionDetails(connection, 12345);
```

## Test Result Queries

The test result queries are implemented in `queries/test-results.js` and provide functions for querying test results.

### getTestResults

Gets test results for a specific test session.

```javascript
/**
 * Get test results for a specific test session
 * @param {Object} connection - Database connection
 * @param {string|number} tsn_id - Test session ID
 * @returns {Promise<Object>} - Test results
 */
async function getTestResults(connection, tsn_id)
```

#### Parameters

- `connection`: Database connection
- `tsn_id`: Test session ID

#### Returns

A test results object with the following properties:

- `tsn_id`: Test session ID
- `total_results`: Total number of results
- `pass_count`: Number of passed results
- `fail_count`: Number of failed results
- `start_time`: Start time
- `end_time`: End time
- `duration`: Duration
- `results`: Array of test case results, each with the following properties:
  - `tc_id`: Test case ID
  - `total_steps`: Total number of steps
  - `passed_steps`: Number of passed steps
  - `failed_steps`: Number of failed steps
  - `steps`: Array of step results, each with the following properties:
    - `seq_index`: Sequence index
    - `outcome`: Outcome (P for pass, F for fail)
    - `creation_time`: Creation time
    - `output`: Output text

#### Example

```javascript
const testResults = await testResultQueries.getTestResults(connection, 12345);
```

### getTestResultSummary

Gets test result summary for a specific test session.

```javascript
/**
 * Get test result summary for a specific test session
 * @param {Object} connection - Database connection
 * @param {string|number} tsn_id - Test session ID
 * @returns {Promise<Object>} - Test result summary
 */
async function getTestResultSummary(connection, tsn_id)
```

#### Parameters

- `connection`: Database connection
- `tsn_id`: Test session ID

#### Returns

A test result summary object with the following properties:

- `tsn_id`: Test session ID
- `total_results`: Total number of results
- `pass_count`: Number of passed results
- `fail_count`: Number of failed results
- `start_time`: Start time
- `end_time`: End time
- `duration`: Duration

#### Example

```javascript
const resultSummary = await testResultQueries.getTestResultSummary(connection, 12345);
```

### getTestCaseResults

Gets test case results for a specific test session.

```javascript
/**
 * Get test case results for a specific test session
 * @param {Object} connection - Database connection
 * @param {string|number} tsn_id - Test session ID
 * @returns {Promise<Array>} - Test case results
 */
async function getTestCaseResults(connection, tsn_id)
```

#### Parameters

- `connection`: Database connection
- `tsn_id`: Test session ID

#### Returns

An array of test case results, each with the following properties:

- `tc_id`: Test case ID
- `step_count`: Number of steps
- `pass_steps`: Number of passed steps
- `fail_steps`: Number of failed steps
- `status`: Status (passed or failed)

#### Example

```javascript
const caseResults = await testResultQueries.getTestCaseResults(connection, 12345);
```

## Result Formatting

The pre-defined queries use the `result-formatter.js` utility to format the results in a consistent way. This utility provides the following functions:

- `formatTestCases(rows)`: Formats test case results
- `formatTestSuites(rows)`: Formats test suite results
- `formatActiveTests(rows, currentUser)`: Formats active test results
- `formatTestResults(rows)`: Formats test result results
- `calculateDuration(start, end)`: Calculates duration between two timestamps

## Error Handling

The pre-defined queries include robust error handling with fallback mechanisms. If a query fails, it will attempt to execute a simpler fallback query. If the fallback query also fails, it will return an empty result.

## Usage in the Main Database Module

The pre-defined queries are used in the main database module to implement the high-level API:

```javascript
/**
 * Get test cases with optional filtering
 * @param {Object} filters - Optional filters
 * @returns {Promise<Array>} - Test cases
 */
async function getTestCases(filters = {}) {
  if (!isInitialized) {
    await init();
  }

  return await queries.testCases.getTestCases(connection, filters);
}
```

## Conclusion

The pre-defined queries provide a convenient way to perform common database operations without having to write SQL queries manually. They include robust error handling and result formatting, making them a reliable foundation for the database layer's API.

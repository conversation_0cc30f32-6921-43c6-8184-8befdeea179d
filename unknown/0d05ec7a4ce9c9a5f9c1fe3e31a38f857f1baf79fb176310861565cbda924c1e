# ======================================================
# SSH Key Permissions Fix Script
# ======================================================
# This script attempts to fix common SSH key permission issues
# for the SmartTest application's database connection

# Function to read environment variables from .env file
function Get-EnvVariable {
    param (
        [string]$VariableName,
        [string]$DefaultValue = ""
    )
    
    $envFiles = @(
        (Join-Path (Split-Path -Parent $MyInvocation.MyCommand.Path) "../frontend/server/.env"),
        (Join-Path (Split-Path -Parent $MyInvocation.MyCommand.Path) "../frontend/server/.env.01"),
        (Join-Path (Split-Path -Parent $MyInvocation.MyCommand.Path) "../frontend/server/.env.03")
    )
    
    foreach ($envFile in $envFiles) {
        if (Test-Path $envFile) {
            $envContent = Get-Content $envFile -ErrorAction SilentlyContinue
            foreach ($line in $envContent) {
                if ($line -match "^\s*$VariableName\s*=\s*(.+)$") {
                    return $Matches[1].Trim('"''')
                }
            }
        }
    }
    
    # If not found in env files, check system environment variables
    if (Test-Path env:$VariableName) {
        return (Get-Item env:$VariableName).Value
    }
    
    return $DefaultValue
}

# Get credentials from environment variables
$sshKeyFile = Get-EnvVariable -VariableName "SSH_KEY_PATH" -DefaultValue "$env:USERPROFILE\.ssh\id_rsa_dbserver"
$server = Get-EnvVariable -VariableName "SSH_HOST" -DefaultValue "mprts-qa02.lab.wagerworks.com"
$sshUser = Get-EnvVariable -VariableName "SSH_USER" -DefaultValue "volfkoi"

# Check if key exists
Write-Host "Checking for SSH key..." -ForegroundColor Cyan
if (-not (Test-Path "$sshKeyFile")) {
    Write-Host "ERROR: SSH key does not exist at $sshKeyFile" -ForegroundColor Red
    exit 1
}

Write-Host "SSH private key exists at $sshKeyFile" -ForegroundColor Green

# Check if public key exists
if (-not (Test-Path "$sshKeyFile.pub")) {
    Write-Host "ERROR: Public key does not exist at $sshKeyFile.pub" -ForegroundColor Red
    exit 1
}

Write-Host "SSH public key exists at $sshKeyFile.pub" -ForegroundColor Green

# Display the public key
$publicKey = Get-Content "$sshKeyFile.pub"
Write-Host "Your public key is:" -ForegroundColor Cyan
Write-Host $publicKey -ForegroundColor Yellow
Write-Host ""

# Create a temporary file with the authorized_keys setup commands
$tempFile = Join-Path $env:TEMP "ssh_fix_commands.sh"
@"
#!/bin/bash
# Create .ssh directory if it doesn't exist
mkdir -p ~/.ssh
chmod 700 ~/.ssh

# Create or overwrite authorized_keys with the new key
cat > ~/.ssh/authorized_keys << 'EOF'
$publicKey
EOF

# Fix permissions
chmod 600 ~/.ssh/authorized_keys

# Display setup information
echo "SSH directory:"
ls -la ~/.ssh
echo ""
echo "authorized_keys content:"
cat ~/.ssh/authorized_keys
echo ""
echo "SSH server configuration (AllowPublicKeyAuthentication):"
grep -i "PubkeyAuthentication" /etc/ssh/sshd_config
"@ | Out-File -FilePath $tempFile -Encoding ASCII

Write-Host "Created temporary script file for server-side setup." -ForegroundColor Green
Write-Host "We'll now connect to the server and run these commands to properly set up your key." -ForegroundColor Cyan
Write-Host "When prompted, enter your SSH password." -ForegroundColor Yellow
Write-Host ""

# Copy the script to the server and execute it
Write-Host "Copying and executing setup script on server..." -ForegroundColor Cyan
$scpCommand = "scp -o HostKeyAlgorithms=+ssh-rsa `"$tempFile`" ${sshUser}@${server}:~/ssh_fix.sh"
$execCommand = "ssh -o HostKeyAlgorithms=+ssh-rsa ${sshUser}@${server} 'chmod +x ~/ssh_fix.sh && ~/ssh_fix.sh && rm ~/ssh_fix.sh'"

Write-Host "Running: $scpCommand" -ForegroundColor DarkGray
Invoke-Expression $scpCommand

Write-Host "`nRunning: $execCommand" -ForegroundColor DarkGray
Invoke-Expression $execCommand

Write-Host "`nSetup script executed. Now testing SSH key authentication..." -ForegroundColor Cyan
$testCommand = "ssh -o StrictHostKeyChecking=no -i `"$sshKeyFile`" -o HostKeyAlgorithms=+ssh-rsa ${sshUser}@${server} 'echo `"SSH KEY AUTHENTICATION SUCCESSFUL!`"'"

Write-Host "Running: $testCommand" -ForegroundColor DarkGray
Invoke-Expression $testCommand

# Cleanup
Remove-Item -Path $tempFile -Force

Write-Host "`nIf you did not see 'SSH KEY AUTHENTICATION SUCCESSFUL!' above, then the key setup failed." -ForegroundColor Yellow
Write-Host "Possible issues:" -ForegroundColor Yellow
Write-Host "1. The server's SSH configuration may not allow public key authentication" -ForegroundColor Yellow
Write-Host "2. There may be permission issues with your .ssh directory or authorized_keys file" -ForegroundColor Yellow
Write-Host "3. The format of your key may not be compatible with the server" -ForegroundColor Yellow
Write-Host "`nIf you continue to have issues, please contact your system administrator." -ForegroundColor Yellow
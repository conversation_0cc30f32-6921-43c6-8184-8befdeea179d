# ======================================================
# Interactive SSH Key Setup Script for SmartTest
# ======================================================

# Show welcome message
Write-Host "===================================================" -ForegroundColor Cyan
Write-Host "        SMARTTEST SSH KEY SETUP UTILITY" -ForegroundColor Cyan
Write-Host "===================================================" -ForegroundColor Cyan
Write-Host "This script will help you set up SSH key authentication" -ForegroundColor White
Write-Host "for secure database connections with the SmartTest app." -ForegroundColor White
Write-Host ""

# Server details
$server = "mprts-qa02.lab.wagerworks.com"

# Prompt for username
$user = Read-Host "Enter your server username (e.g., username)"
if ([string]::IsNullOrWhiteSpace($user)) {
    Write-Host "Username cannot be empty. Exiting script." -ForegroundColor Red
    exit 1
}

# Define key paths
$keyPath = "$env:USERPROFILE\.ssh\id_rsa_dbserver"
$publicKeyPath = "$keyPath.pub"

# Function to check if an SSH key already exists
function Test-SshKeyExists {
    if ((Test-Path $keyPath) -and (Test-Path $publicKeyPath)) {
        Write-Host "SSH key pair found at $keyPath" -ForegroundColor Green
        return $true
    }
    else {
        Write-Host "No SSH key pair found at $keyPath" -ForegroundColor Yellow
        return $false
    }
}

# Function to generate a new SSH key pair
function New-SshKeyPair {
    # Create the .ssh directory if it doesn't exist
    $sshDir = "$env:USERPROFILE\.ssh"
    if (-not (Test-Path $sshDir)) {
        New-Item -ItemType Directory -Path $sshDir | Out-Null
        Write-Host "Created SSH directory at $sshDir" -ForegroundColor Gray
    }
    
    # Generate the key pair
    Write-Host "`nGenerating new SSH key pair..." -ForegroundColor Cyan
    
    # Check if ssh-keygen is available
    try {
        $null = Get-Command ssh-keygen -ErrorAction Stop
        
        # Use ssh-keygen to create a key
        $command = "ssh-keygen -t rsa -b 4096 -f `"$keyPath`" -N `"`""
        Write-Host "Running: $command" -ForegroundColor DarkGray
        Invoke-Expression $command
        
        if ((Test-Path $keyPath) -and (Test-Path $publicKeyPath)) {
            Write-Host "Key pair successfully generated!" -ForegroundColor Green
            return $true
        }
        else {
            Write-Host "Failed to generate key pair." -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "ssh-keygen not found. Please install OpenSSH or Git for Windows." -ForegroundColor Red
        return $false
    }
}

# Function to deploy the SSH key to the server
function Deploy-SshKey {
    param (
        [string]$Username,
        [string]$ServerName,
        [string]$PublicKeyPath
    )
    
    # Create a temporary file containing the public key
    $tempKeyFile = Join-Path $env:TEMP "temp_pubkey.txt"
    $publicKey = Get-Content $PublicKeyPath
    $publicKey | Out-File -FilePath $tempKeyFile -Encoding ASCII
    
    Write-Host "`nWe'll now set up your SSH key on the server." -ForegroundColor Cyan
    Write-Host "You'll be prompted for your password twice:" -ForegroundColor Yellow
    Write-Host "1. First when copying the key file" -ForegroundColor Yellow
    Write-Host "2. Then when executing the setup command" -ForegroundColor Yellow
    Write-Host ""
    
    try {
        # Copy the key file to the server
        Write-Host "Copying public key to server..." -ForegroundColor Cyan
        $scpCommand = "scp -o HostKeyAlgorithms=+ssh-rsa `"$tempKeyFile`" ${Username}@${ServerName}:~/temp_key.txt"
        Write-Host "Running: $scpCommand" -ForegroundColor DarkGray
        Invoke-Expression $scpCommand
        
        # Set up the authorized_keys file with proper permissions
        $sshCommand = "ssh -o HostKeyAlgorithms=+ssh-rsa ${Username}@${ServerName} 'mkdir -p ~/.ssh && chmod 700 ~/.ssh && cat ~/temp_key.txt >> ~/.ssh/authorized_keys && chmod 600 ~/.ssh/authorized_keys && rm ~/temp_key.txt && echo Key setup complete!'"
        Write-Host "`nRunning: $sshCommand" -ForegroundColor DarkGray
        Invoke-Expression $sshCommand
        
        Write-Host "`nNow testing the key authentication..." -ForegroundColor Cyan
        $testCommand = "ssh -o HostKeyAlgorithms=+ssh-rsa -i `"$keyPath`" ${Username}@${ServerName} 'echo SSH KEY AUTHENTICATION SUCCESSFUL!'"
        Write-Host "Running: $testCommand" -ForegroundColor DarkGray
        Invoke-Expression $testCommand
        
        Write-Host "`nIf you see 'SSH KEY AUTHENTICATION SUCCESSFUL!' above, the setup was successful." -ForegroundColor Green
        return $true
    } 
    catch {
        Write-Host "An error occurred: $_" -ForegroundColor Red
        return $false
    }
    finally {
        # Clean up
        if (Test-Path $tempKeyFile) {
            Remove-Item -Path $tempKeyFile -Force
        }
    }
}

# Function to update the database connection script
function Update-ConnectionScript {
    $dbScriptPath = Join-Path (Get-Location).Path "frontend\server\database\connection\db_connection.ps1"
    
    if (-not (Test-Path $dbScriptPath)) {
        Write-Host "Database connection script not found at $dbScriptPath" -ForegroundColor Yellow
        Write-Host "You may need to manually update your connection script. See instructions below." -ForegroundColor Yellow
        return
    }
    
    Write-Host "`nUpdating database connection script..." -ForegroundColor Cyan
    
    # Read the content of the script
    $content = Get-Content -Path $dbScriptPath -Raw
    
    # Update SSH command
    $oldSshPattern = '(\$sshCmd\s*=\s*if\s*\(\$UseConfigFile\)\s*\{[\s\S]*?\}\s*else\s*\{[\s\S]*?ssh\s+-o\s+HostKeyAlgorithms=\+ssh-rsa[\s\S]*?\}\s*)'
    $newSshContent = @'
$sshCmd = if ($UseConfigFile) {
    "ssh -i `"$env:USERPROFILE\.ssh\id_rsa_dbserver`" -F `"$sshConfigPath`""
} else {
    "ssh -i `"$env:USERPROFILE\.ssh\id_rsa_dbserver`" -o HostKeyAlgorithms=+ssh-rsa"
}
'@
    
    # Update SCP command
    $oldScpPattern = '(\$scpCmd\s*=\s*"scp\s+-o\s+HostKeyAlgorithms=\+ssh-rsa\s+`"\$queryFilePath`"\s+)'
    $newScpContent = '$scpCmd = "scp -i `"$env:USERPROFILE\.ssh\id_rsa_dbserver`" -o HostKeyAlgorithms=+ssh-rsa `"$queryFilePath`" '
    
    # Perform the replacements
    $updatedContent = $content
    try {
        if ($content -match $oldSshPattern) {
            $updatedContent = $updatedContent -replace $oldSshPattern, $newSshContent
            $sshUpdated = $true
        } else {
            $sshUpdated = $false
        }
        
        if ($content -match $oldScpPattern) {
            $updatedContent = $updatedContent -replace $oldScpPattern, $newScpContent
            $scpUpdated = $true
        } else {
            $scpUpdated = $false
        }
        
        # Save the updated content
        if ($sshUpdated -or $scpUpdated) {
            # Backup the original file
            $backupPath = "$dbScriptPath.bak"
            Copy-Item -Path $dbScriptPath -Destination $backupPath -Force
            Write-Host "Created backup of original script at $backupPath" -ForegroundColor Gray
            
            # Write the updated content
            $updatedContent | Set-Content -Path $dbScriptPath -Encoding UTF8
            
            if ($sshUpdated -and $scpUpdated) {
                Write-Host "Successfully updated both SSH and SCP commands in the script." -ForegroundColor Green
            } elseif ($sshUpdated) {
                Write-Host "Updated SSH command, but couldn't locate SCP command pattern." -ForegroundColor Yellow
            } elseif ($scpUpdated) {
                Write-Host "Updated SCP command, but couldn't locate SSH command pattern." -ForegroundColor Yellow
            }
        } else {
            Write-Host "No matching patterns found in the connection script." -ForegroundColor Yellow
            Write-Host "You may need to manually update the script. See instructions below." -ForegroundColor Yellow
        }
    } catch {
        Write-Host "An error occurred while updating the script: $_" -ForegroundColor Red
    }
}

# Main script flow
try {
    # Step 1: Check if SSH key exists, generate if needed
    $keyExists = Test-SshKeyExists
    
    if (-not $keyExists) {
        $generate = Read-Host "Would you like to generate a new SSH key? (Y/N)"
        if ($generate -eq "Y" -or $generate -eq "y") {
            $keyExists = New-SshKeyPair
            if (-not $keyExists) {
                Write-Host "Failed to generate SSH key. Exiting script." -ForegroundColor Red
                exit 1
            }
        } else {
            Write-Host "SSH key is required for setup. Exiting script." -ForegroundColor Yellow
            exit 0
        }
    }
    
    # Step 2: Deploy key to server
    $deploy = Read-Host "`nWould you like to deploy the key to the server? (Y/N)"
    if ($deploy -eq "Y" -or $deploy -eq "y") {
        $success = Deploy-SshKey -Username $user -ServerName $server -PublicKeyPath $publicKeyPath
        if (-not $success) {
            Write-Host "Key deployment had issues. Review the output above." -ForegroundColor Yellow
        }
    } else {
        Write-Host "Skipping key deployment." -ForegroundColor Yellow
    }
    
    # Step 3: Update connection script - COMMENTED OUT as per user request
    <# 
    $update = Read-Host "`nWould you like to update the database connection script? (Y/N)"
    if ($update -eq "Y" -or $update -eq "y") {
        Update-ConnectionScript
    } else {
        Write-Host "Skipping connection script update." -ForegroundColor Yellow
    }
    #>
} catch {
    Write-Host "An error occurred: $_" -ForegroundColor Red
}

# Final instructions
Write-Host "`n===================================================" -ForegroundColor Cyan
Write-Host "      MANUAL CONFIGURATION INSTRUCTIONS" -ForegroundColor Cyan
Write-Host "===================================================" -ForegroundColor Cyan
Write-Host "If automatic configuration didn't work, here's how to" -ForegroundColor White
Write-Host "manually update db_connection.ps1:" -ForegroundColor White
Write-Host ""
Write-Host "1. Find these lines in db_connection.ps1:" -ForegroundColor Yellow
Write-Host "   `$sshCmd = if (`$UseConfigFile) {" -ForegroundColor Gray
Write-Host "       `"ssh -F `\"`$sshConfigPath`\"`"" -ForegroundColor Gray
Write-Host "   } else {" -ForegroundColor Gray
Write-Host "       `"ssh -o HostKeyAlgorithms=+ssh-rsa`"" -ForegroundColor Gray
Write-Host "   }" -ForegroundColor Gray
Write-Host ""
Write-Host "2. Change them to:" -ForegroundColor Yellow
Write-Host "   `$sshCmd = if (`$UseConfigFile) {" -ForegroundColor Gray
Write-Host "       `"ssh -i `\"`$env:USERPROFILE\.ssh\id_rsa_dbserver`\" -F `\"`$sshConfigPath`\"`"" -ForegroundColor Green
Write-Host "   } else {" -ForegroundColor Gray
Write-Host "       `"ssh -i `\"`$env:USERPROFILE\.ssh\id_rsa_dbserver`\" -o HostKeyAlgorithms=+ssh-rsa`"" -ForegroundColor Green
Write-Host "   }" -ForegroundColor Gray
Write-Host ""
Write-Host "3. Find the SCP command line and update it similarly:" -ForegroundColor Yellow
Write-Host "   `$scpCmd = `"scp -o HostKeyAlgorithms=+ssh-rsa `\"`$queryFilePath`\" `"" -ForegroundColor Gray
Write-Host "   Change to:" -ForegroundColor Yellow
Write-Host "   `$scpCmd = `"scp -i `\"`$env:USERPROFILE\.ssh\id_rsa_dbserver`\" -o HostKeyAlgorithms=+ssh-rsa `\"`$queryFilePath`\" `"" -ForegroundColor Green
Write-Host ""
Write-Host "These changes will make the database connection script use your SSH key." -ForegroundColor Cyan
Write-Host "===================================================" -ForegroundColor Cyan 
Write-Host "`nSetup process complete. Thank you for using SmartTest!" -ForegroundColor Green
# Frontend-API-Database Integration Guide

## Overview

This guide provides detailed instructions for integrating the frontend, API, and database layers in the SmartTest application. It focuses on how data flows between these layers and how to ensure proper communication between them.

## Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [Integration Flow](#integration-flow)
3. [Frontend to API Integration](#frontend-to-api-integration)
4. [API to Database Integration](#api-to-database-integration)
5. [Implementation Details](#implementation-details)
6. [Error Handling](#error-handling)
7. [Best Practices](#best-practices)

## Architecture Overview

The SmartTest application consists of three main layers:

1. **Frontend Layer**: User interface components for running test suites and viewing results
   - Located in `frontend/dashboard` and `frontend/components`
   - Uses API service to communicate with the API layer

2. **API Layer**: RESTful API endpoints for handling test execution and data retrieval
   - Located in `frontend/server/routes`
   - Processes requests from the frontend and communicates with the database layer

3. **Database Layer**: Data access layer for interacting with the database
   - Located in `frontend/server/database`
   - Provides a clean interface for querying the database

The new database layer has been refactored to provide a more modular and maintainable architecture with direct SSH connections as the primary method and support for future AI integration.

## Integration Flow

### Data Flow Diagram

```
┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│   Frontend  │      │     API     │      │  Database   │
│    Layer    │◄────►│    Layer    │◄────►│    Layer    │
└─────────────┘      └─────────────┘      └─────────────┘
       ▲                    ▲                    ▲
       │                    │                    │
       ▼                    ▼                    ▼
┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│    User     │      │   External  │      │   MySQL     │
│  Interface  │      │    APIs     │      │  Database   │
└─────────────┘      └─────────────┘      └─────────────┘
```

### Request-Response Flow

1. Frontend sends a request to the API layer
2. API layer validates the request and processes it
3. If database access is needed, API layer calls the database layer
4. Database layer executes the query and returns the results to the API layer
5. API layer formats the response and returns it to the frontend
6. Frontend displays the results to the user

## Frontend to API Integration

### API Service

The frontend communicates with the API layer through an API service that handles HTTP requests and responses. The API service is implemented in `frontend/config/services/api-service.js` and `frontend/dashboard/api-service.js`.

#### API Endpoints

The API service defines the following endpoints:

```javascript
this.endpoints = {
  caseRunner: '/case-runner',         // -> /api/case-runner
  testStatus: '/test-status',         // -> /api/test-status
  testReport: '/test-report',         // -> /api/test-report
  testSuites: '/local/test-suites',   // -> /local/test-suites
  testCases: '/local/test-cases',     // -> /local/test-cases
  stopTest: '/stop-test',             // -> /api/stop-test
  activeTests: '/local/active-tests', // -> /local/active-tests
  recentRuns: '/local/recent-runs',   // -> /local/recent-runs
  testReports: '/test-reports'        // -> /api/test-reports
};
```

#### Making API Requests

The API service provides methods for making HTTP requests to the API layer:

```javascript
/**
 * Make a GET request
 * @param {string} endpoint - API endpoint
 * @param {Object} params - Query parameters
 * @returns {Promise<Object>} - API response
 */
async getRequest(endpoint, params = {}) {
  // Implementation
}

/**
 * Make a POST request
 * @param {string} endpoint - API endpoint
 * @param {Object} data - Request body
 * @returns {Promise<Object>} - API response
 */
async postRequest(endpoint, data = {}) {
  // Implementation
}
```

### Example: Running a Test Suite

#### Frontend Component

```javascript
/**
 * Run a test suite
 * @param {number} tsId - Test suite ID
 * @param {Object} params - Additional parameters
 * @returns {Promise<string>} - Test session ID
 */
async runTestSuite(tsId, params = {}) {
  try {
    // Build request body
    const requestBody = {
      ts_id: tsId,
      ...params,
      uid: this.credentials.uid,
      password: this.credentials.password,
      envir: params.environment || 'qa02',
      shell_host: params.shellHost || 'jps-qa10-app01'
    };

    // Make POST request to case-runner endpoint
    const response = await this.postRequest(this.endpoints.caseRunner, requestBody);

    if (response.success) {
      return response.tsn_id;
    } else {
      throw new Error(response.message || 'Failed to run test suite');
    }
  } catch (error) {
    console.error(`Error running test suite ${tsId}:`, error);
    throw error;
  }
}
```

#### API Route Handler

```javascript
// Run a test case or test suite
router.post('/case-runner', validateCredentials, async (req, res) => {
  try {
    // Extract parameters
    const { ts_id, tc_id, envir = 'qa02', shell_host = 'jps-qa10-app01', uid, password } = req.body;

    // Validate parameters
    if (!ts_id && !tc_id) {
      return res.status(400).json({
        success: false,
        message: 'Missing required parameter: either ts_id or tc_id must be provided'
      });
    }

    // Forward request to external API
    const result = await caseRunnerService.runTest({
      ts_id,
      tc_id,
      envir,
      shell_host,
      uid,
      password
    });

    return res.json({
      success: true,
      tsn_id: result.tsn_id,
      message: result.message
    });
  } catch (error) {
    console.error('Error running test:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to run test',
      error: error.message
    });
  }
});
```

## API to Database Integration

### Database Module

The API layer communicates with the database layer through the database module. The database module is implemented in `frontend/server/database/index.js`.

#### Database Functions

The database module provides the following functions:

```javascript
// Core functions
init(environment, options)
query(sql, params)
close()
getConnectionInfo()

// Test case functions
getTestCases(filters)
getTestCaseById(tc_id)
searchTestCases(criteria)

// Test suite functions
getTestSuites(filters)
getTestSuiteById(ts_id)
getTestSuiteInfo(ts_id)

// Test session functions
getActiveTests(filters)
getRecentRuns(filters)
getTestSessionDetails(tsn_id)

// Test result functions
getTestResults(tsn_id)
getTestResultSummary(tsn_id)
getTestCaseResults(tsn_id)
```

### Example: Getting Active Tests

#### API Route Handler

```javascript
// Get active tests
router.get('/active-tests', validateCredentials, async (req, res) => {
  try {
    console.log('GET /local/active-tests');

    // Use the database module to fetch active tests
    const activeTests = await db.getActiveTests({
      uid: req.user.uid
    });

    // Return as JSON with success flag
    return res.json({
      success: true,
      data: activeTests || [],
      message: 'Active tests retrieved successfully'
    });
  } catch (error) {
    console.error('Error retrieving active tests:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to retrieve active tests',
      error: error.message
    });
  }
});
```

#### Database Function

```javascript
/**
 * Get active test sessions
 * @param {Object} filters - Optional filters
 * @returns {Promise<Array>} - Active test sessions
 */
async function getActiveTests(filters = {}) {
  if (!isInitialized) {
    await init();
  }

  return await queries.testSessions.getActiveTests(connection, filters);
}
```

#### Database Query

```javascript
/**
 * Get active test sessions
 * @param {Object} connection - Database connection
 * @param {Object} filters - Optional filters
 * @returns {Promise<Array>} - Active test sessions
 */
async function getActiveTests(connection, filters = {}) {
  const { uid, limit = 20 } = filters;

  // Create query builder
  const queryBuilder = new QueryBuilder();

  // Base query
  queryBuilder.select('test_session s', [
    's.tsn_id',
    'COALESCE(s.tc_id, 0) as tc_id',
    's.uid as initiator_user',
    's.start_ts as creation_time'
  ]);

  // Filter for active tests (end_ts is NULL)
  queryBuilder.where('s.end_ts', 'IS', null);

  // Filter by user ID if provided
  if (uid) {
    queryBuilder.where('s.uid', '=', uid);
  }

  // Add ordering and limit
  queryBuilder.orderBy('s.start_ts', 'DESC');
  queryBuilder.limit(limit);

  // Build and execute query
  const { sql, params } = queryBuilder.build();

  try {
    const rows = await connection.query(sql, params);
    return formatter.formatActiveTests(rows, uid);
  } catch (error) {
    console.error('Error executing getActiveTests query:', error);

    // Try a simpler fallback query if the main query fails
    try {
      console.log('Attempting fallback query for active tests');
      const fallbackSql = `
        SELECT tsn_id, tc_id, uid, start_ts
        FROM test_session
        WHERE end_ts IS NULL
        ORDER BY start_ts DESC
        LIMIT ?
      `;
      const fallbackRows = await connection.query(fallbackSql, [limit]);
      return formatter.formatActiveTests(fallbackRows, uid);
    } catch (fallbackError) {
      console.error('Fallback query also failed:', fallbackError);
      return [];
    }
  }
}
```

## Implementation Details

### Updating API Routes to Use the New Database Layer

To update the API routes to use the new database layer, you need to:

1. Update the import statement to use the new database module:

```javascript
// Old
const db = require('../db-manager');

// New
const db = require('../database');
```

2. Update the function calls to use the new parameter names:

```javascript
// Old
const testCases = await db.getTestCases({ suiteId: req.query.suiteId });

// New
const testCases = await db.getTestCases({ ts_id: req.query.ts_id });
```

3. Update the error handling to match the new error handling approach:

```javascript
// Old
try {
  const testCases = await db.getTestCases({ suiteId: req.query.suiteId });
  // ...
} catch (error) {
  console.error('Error retrieving test cases:', error);
  // ...
}

// New
try {
  const testCases = await db.getTestCases({ ts_id: req.query.ts_id });
  // ...
} catch (error) {
  console.error('Error retrieving test cases:', error);
  // ...
}
```

### Updating Frontend Components to Use the New API Responses

The frontend components should already be compatible with the new API responses, as the API layer maintains the same response format. However, if there are any changes in the response structure, you may need to update the frontend components accordingly.

## Error Handling

### Frontend Error Handling

The frontend should handle API errors by displaying appropriate error messages to the user:

```javascript
try {
  const testResults = await apiService.getTestResults(tsnId);
  // Display test results
} catch (error) {
  // Display error message
  console.error('Error getting test results:', error);
  showErrorMessage('Failed to get test results. Please try again later.');
}
```

### API Error Handling

The API layer should handle database errors by returning appropriate error responses to the frontend:

```javascript
try {
  const testResults = await db.getTestResults(tsnId);
  return res.json({
    success: true,
    data: testResults || {},
    message: 'Test results retrieved successfully'
  });
} catch (error) {
  console.error('Error retrieving test results:', error);
  return res.status(500).json({
    success: false,
    message: 'Failed to retrieve test results',
    error: error.message
  });
}
```

### Database Error Handling

The database layer includes robust error handling with fallback mechanisms:

```javascript
try {
  const rows = await connection.query(sql, params);
  return formatter.formatTestResults(rows);
} catch (error) {
  console.error(`Error getting test results for session ID ${tsnId}:`, error);

  // Try a simpler fallback query if the main query fails
  try {
    console.log('Attempting fallback query for test results');
    const fallbackSql = `
      SELECT r.tsn_id, r.tc_id, r.seq_index, r.outcome, r.creation_time
      FROM test_result r
      WHERE r.tsn_id = ?
      ORDER BY r.creation_time ASC
    `;
    const fallbackRows = await connection.query(fallbackSql, [tsnId]);

    // Create a simplified result without output data
    return {
      tsn_id: tsnId,
      total_results: fallbackRows.length,
      pass_count: fallbackRows.filter(row => row.outcome === 'P').length,
      fail_count: fallbackRows.filter(row => row.outcome === 'F').length,
      results: []
    };
  } catch (fallbackError) {
    console.error('Fallback query also failed:', fallbackError);
    return {
      tsn_id: tsnId,
      total_results: 0,
      pass_count: 0,
      fail_count: 0,
      results: []
    };
  }
}
```

## Best Practices

1. **Use Parameter Validation**: Always validate parameters before passing them to the database layer.

2. **Handle Errors Gracefully**: Implement proper error handling at all layers of the application.

3. **Use Consistent Parameter Names**: Use consistent parameter names across all layers of the application.

4. **Document API Endpoints**: Document all API endpoints with their parameters and response formats.

5. **Use Connection Pooling**: Use connection pooling to improve performance and reliability.

6. **Close Connections**: Always close database connections when they are no longer needed.

7. **Use Prepared Statements**: Use prepared statements to prevent SQL injection attacks.

8. **Limit Query Results**: Always limit the number of rows returned by queries to prevent performance issues.

9. **Use Transactions**: Use transactions for operations that require multiple queries to maintain data consistency.

10. **Monitor Performance**: Monitor the performance of database queries and optimize them as needed.

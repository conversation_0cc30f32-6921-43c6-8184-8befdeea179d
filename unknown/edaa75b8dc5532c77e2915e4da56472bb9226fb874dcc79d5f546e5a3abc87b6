# ======================================================
# DATABASE CONNECTION SCRIPT
# ======================================================
# This script provides reliable methods to connect to the test database
# server via SSH with support for legacy algorithms

# Script Parameters
param(
    [Parameter(Mandatory=$true)]
    [ValidateSet('qa1', 'qa2', 'qa3')]
    [string]$Environment,

    [Parameter(Mandatory=$false)]
    [string]$Query
)

# Enable verbose output for easier debugging
$VerbosePreference = "Continue"
$DebugPreference = "Continue"

# Function to read environment variables from .env file
function Get-EnvVariable {
    param (
        [string]$VariableName,
        [string]$DefaultValue = ""
    )
    
    $envFiles = @(
        (Join-Path (Split-Path -Parent $MyInvocation.MyCommand.Path) "../frontend/server/.env"),
        (Join-Path (Split-Path -Parent $MyInvocation.MyCommand.Path) "../frontend/server/.env.01"),
        (Join-Path (Split-Path -Parent $MyInvocation.MyCommand.Path) "../frontend/server/.env.03")
    )
    
    foreach ($envFile in $envFiles) {
        if (Test-Path $envFile) {
            $envContent = Get-Content $envFile -ErrorAction SilentlyContinue
            foreach ($line in $envContent) {
                if ($line -match "^\s*$VariableName\s*=\s*(.+)$") {
                    return $Matches[1].Trim('"''')
                }
            }
        }
    }
    
    # If not found in env files, check system environment variables
    if (Test-Path env:$VariableName) {
        return (Get-Item env:$VariableName).Value
    }
    
    return $DefaultValue
}

# Server Mapping
$serverMapping = @{
    'qa1' = 'mprts-qa01.lab.wagerworks.com'
    'qa2' = 'mprts-qa02.lab.wagerworks.com'
    'qa3' = 'mprts-qa03.lab.wagerworks.com'
}

# Set credentials and connection details
$server = $serverMapping[$Environment]
if ([string]::IsNullOrEmpty($server)) {
    Write-Error "Invalid environment specified: $Environment"
    exit 1
}

$database = "rgs_test"

# Default credentials (can be changed via parameters)
$dbUser = "rgs_rw"
$dbPassword = "rgs_rw"
$sshUser = Get-EnvVariable -VariableName "SSH_USER" -DefaultValue "volfkoi"

# SSH key path - using environment variables instead of hardcoded path
$sshKeyPath = Get-EnvVariable -VariableName "SSH_KEY_PATH" -DefaultValue "$env:USERPROFILE\.ssh\id_rsa_dbserver"
Write-Verbose "Using SSH key at: $sshKeyPath"

# Validate SSH key exists
if (Test-Path $sshKeyPath) {
    Write-Host "SSH private key found at $sshKeyPath" -ForegroundColor Green
    
    # Check permissions on the SSH key file
    $acl = Get-Acl $sshKeyPath
    Write-Host "SSH key file permissions:" -ForegroundColor Cyan
    $acl.Access | Format-Table IdentityReference, FileSystemRights, AccessControlType -AutoSize
} else {
    Write-Host "WARNING: SSH private key not found at $sshKeyPath" -ForegroundColor Red
    Write-Host "Key-based authentication will likely fail" -ForegroundColor Red
}

# Create a temporary SSH config file to enable legacy algorithms
$sshConfigPath = Join-Path $env:TEMP "temp_ssh_config"
@"
Host $server
    HostKeyAlgorithms +ssh-rsa
    PubkeyAcceptedAlgorithms +ssh-rsa
    LogLevel DEBUG3
"@ | Out-File -FilePath $sshConfigPath -Encoding ascii

Write-Host "Created temporary SSH config at $sshConfigPath" -ForegroundColor Green

# Function to execute a MySQL query through SSH
function Invoke-MySqlQuery {
    param (
        [Parameter(Mandatory=$true)]
        [string]$Query,
        
        [Parameter(Mandatory=$true)]
        [ValidateSet('qa1', 'qa2', 'qa3')]
        [string]$Environment,
        
        [Parameter(Mandatory=$false)]
        [string]$SSHUser = $sshUser,
        
        [Parameter(Mandatory=$false)]
        [string]$DBUser = $dbUser,
        
        [Parameter(Mandatory=$false)]
        [string]$DBPassword = $dbPassword,
        
        [Parameter(Mandatory=$false)]
        [switch]$UseConfigFile,
        
        [Parameter(Mandatory=$false)]
        [switch]$SaveToFile,
        
        [Parameter(Mandatory=$false)]
        [string]$OutputFile = "query_result.txt"
    )
    
    # Determine the server based on the environment passed to the function
    $functionServer = $serverMapping[$Environment]
    if ([string]::IsNullOrEmpty($functionServer)) {
        Write-Error "Invalid environment specified in function call: $Environment"
        return # Or throw, depending on desired error handling
    }

    Write-Host "`n==== Executing Query on $Environment ($functionServer) ====" -ForegroundColor Cyan
    Write-Host "Query: $Query" -ForegroundColor Gray
    
    # Ensure temp SSH config uses the correct server for this specific call
    $tempSshConfigPath = Join-Path $env:TEMP "temp_ssh_config_$($functionServer)" # Unique name per server
    @"
Host $functionServer
    HostKeyAlgorithms +ssh-rsa
    PubkeyAcceptedAlgorithms +ssh-rsa
    LogLevel DEBUG3
"@ | Out-File -FilePath $tempSshConfigPath -Encoding ascii

    # Determine SSH command based on parameters
    $sshCmd = if ($UseConfigFile) {
        "ssh -F `"$tempSshConfigPath`""
    } else {
        "ssh -i `"$sshKeyPath`" -o HostKeyAlgorithms=+ssh-rsa"
    }
    
    Write-Verbose "SSH command base: $sshCmd"
    
    # Add user if specified
    if ($SSHUser) {
        $sshCmd += " ${SSHUser}@$functionServer"
    } else {
        $sshCmd += " $functionServer"
    }
    
    Write-Verbose "Complete SSH command: $sshCmd"
    
    # Create a temporary file for the SQL query to avoid escaping issues
    $queryFilePath = Join-Path $env:TEMP "temp_query.sql"
    $Query | Out-File -FilePath $queryFilePath -Encoding ascii
    
    Write-Verbose "SQL query saved to: $queryFilePath"
    
    # Create a different approach - using a temporary SQL file
    try {
        Write-Host "Executing command..." -ForegroundColor Yellow
        
        # Method 1: Copy the SQL file to the remote server then execute it
        $remoteTempFile = "/tmp/temp_query_$([guid]::NewGuid().ToString('N')).sql"
        
        # Step 1: SCP the query file to the remote server (Removed -v for less noise)
        $scpCmd = "scp -i `"$sshKeyPath`" -o HostKeyAlgorithms=+ssh-rsa `"$queryFilePath`" "
        if ($SSHUser) {
            $scpCmd += "${SSHUser}@$functionServer"
        } else {
            $scpCmd += "$functionServer"
        }
        $scpCmd += ":$remoteTempFile"
        
        Write-Host "Copying query file to server..." -ForegroundColor Yellow
        # Write-Verbose "SCP command: $scpCmd" # Keep verbose log if needed, but command runs without -v
        
        try {
            # Write-Host "--- SCP DEBUG OUTPUT START ---" -ForegroundColor Magenta # Removed verbose output block
            $scpOutput = Invoke-Expression $scpCmd
            # Write-Host $scpOutput
            # Write-Host "--- SCP DEBUG OUTPUT END ---" -ForegroundColor Magenta
            
            # Step 2: Run the query from the remote file
            # Build the base SSH command (Removed -v for less noise)
            $sshCmdBase = if ($UseConfigFile) {
                "ssh -F `"$tempSshConfigPath`""
            } else {
                "ssh -i `"$sshKeyPath`" -o HostKeyAlgorithms=+ssh-rsa"
            }
            
            # Add user and server
            $sshCmdFull = $sshCmdBase
            if ($SSHUser) {
                $sshCmdFull += " ${SSHUser}@$functionServer"
            } else {
                $sshCmdFull += " $functionServer"
            }

            $mysqlCmd = "mysql --user=$DBUser --password='$DBPassword' $database < $remoteTempFile"
            $executeCmd = "$sshCmdFull `"$mysqlCmd`""
            
            # Write-Verbose "MySQL execution command: $executeCmd" # Keep verbose log if needed
            
            if ($SaveToFile) {
                Write-Host "Results will be saved to $OutputFile" -ForegroundColor Magenta
                # Write-Host "--- SSH DEBUG OUTPUT START ---" -ForegroundColor Magenta # Removed verbose output block
                $sshOutput = Invoke-Expression "$executeCmd > `"$OutputFile`" 2>&1"
                # Write-Host $sshOutput
                # Write-Host "--- SSH DEBUG OUTPUT END ---" -ForegroundColor Magenta
                
                # Check if the output file exists and has content
                if ((Test-Path $OutputFile) -and ((Get-Item $OutputFile).Length -gt 0)) {
                    Write-Host "Query executed and results saved to $OutputFile" -ForegroundColor Green
                    Get-Content $OutputFile | Select-Object -First 10 | ForEach-Object { Write-Host $_ }
                    if ((Get-Content $OutputFile).Count -gt 10) {
                        Write-Host "... (more results in file)" -ForegroundColor Gray
                    }
                } else {
                    Write-Host "Query may have failed. Check the output file for errors." -ForegroundColor Yellow
                }
            } else {
                # Write-Host "--- SSH DEBUG OUTPUT START ---" -ForegroundColor Magenta # Removed verbose output block
                $result = Invoke-Expression $executeCmd
                Write-Host "Exit code: $LASTEXITCODE" -ForegroundColor Cyan
                # Write-Host "--- SSH DEBUG OUTPUT END ---" -ForegroundColor Magenta
                
                if ($LASTEXITCODE -eq 0) {
                    Write-Host "Query executed successfully!" -ForegroundColor Green
                    if ($result) {
                        Write-Host "Result:" -ForegroundColor Cyan
                        $result | ForEach-Object { Write-Host $_ }
                    } else {
                        Write-Host "Query executed but returned no results." -ForegroundColor Yellow
                    }
                } else {
                    Write-Host "Query execution failed with exit code $LASTEXITCODE" -ForegroundColor Red
                }
            }
            
            # Step 3: Clean up the remote file (using the same non-verbose $sshCmdFull)
            $cleanupCmd = "$sshCmdFull `"rm -f $remoteTempFile`""
            # Write-Verbose "Clean-up command: $cleanupCmd" # Keep verbose log if needed
            Write-Host "Cleaning up remote file..." -ForegroundColor Gray
            Invoke-Expression $cleanupCmd | Out-Null
        } catch {
            Write-Host "Error with file transfer approach: $_" -ForegroundColor Red
            Write-Host "Full exception details: $($_.Exception.Message)" -ForegroundColor Red
            Write-Host "Trying alternative method..." -ForegroundColor Yellow
            
            # Alternative method: Try direct query execution with careful escaping
            # Build the base SSH command (Removed -v for less noise)
            $sshCmdBaseAlt = if ($UseConfigFile) {
                "ssh -F `"$tempSshConfigPath`""
            } else {
                "ssh -i `"$sshKeyPath`" -o HostKeyAlgorithms=+ssh-rsa"
            }
            
            # Add user and server
            $sshCmdFullAlt = $sshCmdBaseAlt
            if ($SSHUser) {
                $sshCmdFullAlt += " ${SSHUser}@$functionServer"
            } else {
                $sshCmdFullAlt += " $functionServer"
            }

            $escapedQuery = $Query.Replace('"', '\"').Replace('$', '\$')
            $mysqlCmd = "mysql --user=$DBUser --password='$DBPassword' $database --execute=`"$escapedQuery`""
            $executeCmd = "$sshCmdFullAlt '$mysqlCmd'"
            
            # Write-Verbose "Alternative execution command: $executeCmd" # Keep verbose log if needed
            
            if ($SaveToFile) {
                # Write-Host "--- SSH DEBUG OUTPUT START ---" -ForegroundColor Magenta # Removed verbose output block
                $sshOutput = Invoke-Expression "$executeCmd > `"$OutputFile`" 2>&1"
                # Write-Host $sshOutput
                # Write-Host "--- SSH DEBUG OUTPUT END ---" -ForegroundColor Magenta
                
                if ((Test-Path $OutputFile) -and ((Get-Item $OutputFile).Length -gt 0)) {
                    Write-Host "Query executed and results saved to $OutputFile" -ForegroundColor Green
                    Get-Content $OutputFile | Select-Object -First 10 | ForEach-Object { Write-Host $_ }
                } else {
                    Write-Host "Query execution failed. Check the output file for errors." -ForegroundColor Red
                }
            } else {
                # Write-Host "--- SSH DEBUG OUTPUT START ---" -ForegroundColor Magenta # Removed verbose output block
                $result = Invoke-Expression $executeCmd
                Write-Host "Exit code: $LASTEXITCODE" -ForegroundColor Cyan
                # Write-Host "--- SSH DEBUG OUTPUT END ---" -ForegroundColor Magenta
                
                if ($result) {
                    Write-Host "Result:" -ForegroundColor Cyan
                    $result | ForEach-Object { Write-Host $_ }
                } else {
                    Write-Host "Query execution failed or returned no results." -ForegroundColor Red
                }
            }
        }
    } catch {
        Write-Host "Error executing query: $_" -ForegroundColor Red
        Write-Host "Full exception details: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "Stack trace: $($_.ScriptStackTrace)" -ForegroundColor DarkGray
    } finally {
        # Clean up the local temporary file
        if (Test-Path $queryFilePath) {
            Remove-Item -Path $queryFilePath -Force
            Write-Verbose "Removed temporary query file: $queryFilePath"
        }
        
        # Clean up temporary SSH config file if it exists
        if (Test-Path $tempSshConfigPath) {
            Remove-Item -Path $tempSshConfigPath -Force
            Write-Verbose "Removed temporary SSH config file: $tempSshConfigPath"
        }
    }
}

# Function to execute predefined queries
function Invoke-PredefinedQuery {
    param (
        [Parameter(Mandatory=$true)]
        [ValidateSet("Count", "LatestRun", "LatestFailures")]
        [string]$QueryType,
        
        [Parameter(Mandatory=$false)]
        [switch]$SaveToFile,
        
        [Parameter(Mandatory=$false)]
        [string]$OutputFile
    )
    
    $query = switch ($QueryType) {
        "Count" { 
            "SELECT COUNT(*) FROM rgs_test.test_result" 
        }
        "LatestRun" { 
            "SELECT tsn_id, tc_id, outcome, count(*) FROM rgs_test.test_result r, rgs_test.output i WHERE i.cnt=r.cnt AND r.tsn_id=(SELECT max(tsn_id) FROM rgs_test.test_result) GROUP BY tc_id, outcome, tsn_id ORDER BY creation_time ASC" 
        }
        "LatestFailures" { 
            "SELECT r.cnt, seq_index, tsn_id, tc_id, outcome, txt FROM rgs_test.test_result r, rgs_test.output i WHERE i.cnt=r.cnt AND r.tsn_id=(SELECT max(tsn_id) FROM rgs_test.test_result) AND outcome='F'" 
        }
    }
    
    $outputFileName = if ($OutputFile) { 
        $OutputFile 
    } else { 
        "$($QueryType.ToLower())_results.txt" 
    }
    
    if ($SaveToFile) {
        Invoke-MySqlQuery -Query $query -Environment $Environment -SaveToFile -OutputFile $outputFileName
    } else {
        Invoke-MySqlQuery -Query $query -Environment $Environment
    }
}

# Interactive menu
function Show-Menu {
    Clear-Host
    Write-Host "========== DATABASE CONNECTION TOOL ==========" -ForegroundColor Cyan
    Write-Host "1: Execute Count Query (Simple Test)"
    Write-Host "2: Get Latest Test Run Results"
    Write-Host "3: Get Latest Test Failures"
    Write-Host "4: Execute Custom Query"
    Write-Host "5: Change Credentials"
    Write-Host "6: Test SSH Key Authentication"
    Write-Host "Q: Quit"
    Write-Host "=============================================="
    
    $choice = Read-Host "Enter your choice"
    
    switch ($choice) {
        "1" { Invoke-PredefinedQuery -QueryType "Count" }
        "2" { Invoke-PredefinedQuery -QueryType "LatestRun" -SaveToFile }
        "3" { Invoke-PredefinedQuery -QueryType "LatestFailures" -SaveToFile }
        "4" { 
            $query = Read-Host "Enter your custom SQL query"
            $saveToFile = (Read-Host "Save results to file? (y/n)") -eq "y"
            
            if ($saveToFile) {
                $outputFile = Read-Host "Enter output file name (press Enter for default)"
                if ([string]::IsNullOrWhiteSpace($outputFile)) {
                    $outputFile = "custom_query_results.txt"
                }
                Invoke-MySqlQuery -Query $query -Environment $Environment -SaveToFile -OutputFile $outputFile
            } else {
                Invoke-MySqlQuery -Query $query -Environment $Environment
            }
        }
        "5" {
            $dbUser = Read-Host "Enter database username (press Enter for '$dbUser')"
            $dbPassword = Read-Host "Enter database password (press Enter for current password)" -AsSecureString
            if ($dbPassword.Length -gt 0) {
                $BSTR = [System.Runtime.InteropServices.Marshal]::SecureStringToBSTR($dbPassword)
                $dbPassword = [System.Runtime.InteropServices.Marshal]::PtrToStringAuto($BSTR)
                [System.Runtime.InteropServices.Marshal]::ZeroFreeBSTR($BSTR)
            } else {
                $dbPassword = $dbPassword
            }
            $sshUser = Read-Host "Enter SSH username (press Enter for '$sshUser')"
            Write-Host "Credentials updated." -ForegroundColor Green
        }
        "6" {
            Write-Host "Testing SSH key authentication..." -ForegroundColor Cyan
            $testCmd = "ssh -v -i `"$sshKeyPath`" -o HostKeyAlgorithms=+ssh-rsa ${sshUser}@${server} 'echo SSH KEY AUTHENTICATION SUCCESS!'"
            Write-Host "Running: $testCmd" -ForegroundColor DarkGray
            
            # Add additional SSH troubleshooting information
            Write-Host "`nSSH key details:" -ForegroundColor Cyan
            if (Test-Path $sshKeyPath) {
                $keyInfo = Get-Item $sshKeyPath
                Write-Host "  File: $($keyInfo.FullName)" -ForegroundColor White
                Write-Host "  Size: $($keyInfo.Length) bytes" -ForegroundColor White
                Write-Host "  Created: $($keyInfo.CreationTime)" -ForegroundColor White
                Write-Host "  Modified: $($keyInfo.LastWriteTime)" -ForegroundColor White
                
                # Try to check if the public key exists
                $publicKeyPath = "$sshKeyPath.pub"
                if (Test-Path $publicKeyPath) {
                    Write-Host "  Public key: Found" -ForegroundColor Green
                    Write-Host "  Public key content (first 50 chars):" -ForegroundColor White
                    $pubKey = Get-Content $publicKeyPath -Raw
                    Write-Host "    $($pubKey.Substring(0, [Math]::Min(50, $pubKey.Length)))..." -ForegroundColor Gray
                } else {
                    Write-Host "  Public key: Not found" -ForegroundColor Red
                    Write-Host "  Note: A missing public key file doesn't affect authentication, but it's useful for reference" -ForegroundColor Yellow
                }
            }
            
            # Run the test command with clearer output formatting
            Write-Host "`nAttempting connection..." -ForegroundColor Cyan
            Invoke-Expression $testCmd
            
            # Provide guidance based on the result
            if ($LASTEXITCODE -eq 0) {
                Write-Host "`nSSH key authentication successful!" -ForegroundColor Green
            } else {
                Write-Host "`nSSH key authentication failed." -ForegroundColor Red
                Write-Host "Common issues to check:" -ForegroundColor Yellow
                Write-Host "1. Make sure your public key is in ~/.ssh/authorized_keys on the server" -ForegroundColor White
                Write-Host "2. Check permissions on both the key file and authorized_keys file" -ForegroundColor White
                Write-Host "3. The server might not accept your key type or algorithm" -ForegroundColor White
                Write-Host "4. Your username might be incorrect" -ForegroundColor White
            }
            
            Read-Host "`nPress Enter to continue"
        }
        "Q" { return $false }
        "q" { return $false }
        default { Write-Host "Invalid choice. Please try again." -ForegroundColor Red }
    }
    
    Read-Host "`nPress Enter to continue"
    return $true
}

# Main execution loop
Write-Host "==================================================" -ForegroundColor Green
Write-Host "DATABASE CONNECTION TOOL WITH DEBUG LOGGING" -ForegroundColor Green 
Write-Host "==================================================" -ForegroundColor Green
Write-Host "Script started with the following configuration:" -ForegroundColor Cyan
Write-Host "Server: $server" -ForegroundColor White
Write-Host "SSH User: $sshUser" -ForegroundColor White
Write-Host "DB User: $dbUser" -ForegroundColor White
Write-Host "SSH Key: $sshKeyPath" -ForegroundColor White
Write-Host "SSH Config: $sshConfigPath" -ForegroundColor White
Write-Host "==================================================" -ForegroundColor Green

# Direct execution if -Query parameter is provided
if ($PSBoundParameters.ContainsKey('Query')) {
    Write-Host "Direct execution requested for environment: $Environment" -ForegroundColor Yellow
    Write-Host "Executing provided query..." -ForegroundColor Cyan
    Invoke-MySqlQuery -Query $Query -Environment $Environment
    Write-Host "`nDirect query execution finished." -ForegroundColor Green
    exit # Exit after direct execution
} 

# Interactive Menu (if -Query was not provided)
$continue = $true
while ($continue) {
    $continue = Show-Menu
}

# Clean up
if (Test-Path $sshConfigPath) {
    Remove-Item -Path $sshConfigPath -Force
    Write-Host "Temporary config file removed." -ForegroundColor Green
}

Write-Host "Database connection script ended. Goodbye!" -ForegroundColor Cyan 

/**
 * Simple CORS proxy server for testing the reports page with real data
 */
const express = require('express');
const cors = require('cors');
const { createProxyMiddleware } = require('http-proxy-middleware');

const app = express();

// Enable CORS for all routes
app.use(cors());

// Configure proxy middleware
const apiProxy = createProxyMiddleware({
  target: 'http://mprts-qa02.lab.wagerworks.com:9080',
  changeOrigin: true,
  pathRewrite: {
    '^/api': '' // Remove /api prefix when forwarding
  },
  onProxyRes: (proxyRes, req, res) => {
    // Forward cookies to the client
    console.log('Received response from API');
    if (proxyRes.headers['set-cookie']) {
      const cookies = proxyRes.headers['set-cookie'].map(
        cookie => cookie.replace('Secure;', '')
      );
      proxyRes.headers['set-cookie'] = cookies;
      console.log('Forwarding cookies:', cookies);
    }
  }
});

// Use the proxy for all /api routes
app.use('/api', apiProxy);

// Serve static files for the reports page
app.use('/reports', express.static('frontend/reports'));

// Root route
app.get('/', (req, res) => {
  res.redirect('/reports');
});

// Start the server
const PORT = 3000;
app.listen(PORT, () => {
  console.log(`Proxy server running on http://localhost:${PORT}`);
  console.log(`Access reports at http://localhost:${PORT}/reports`);
  console.log(`API requests will be proxied to http://mprts-qa02.lab.wagerworks.com:9080`);
});

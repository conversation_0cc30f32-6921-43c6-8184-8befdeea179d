/**
 * Database Environments Configuration
 * 
 * This file contains configuration for different database environments
 * that can be used with the SSH tunnel connection
 */

const path = require('path');

// Environment configurations for each server
const environments = {
  // QA01 Environment
  qa01: {
    // Server Configuration
    PORT: 3000,
    BASE_URL: 'http://mprts-qa01.lab.wagerworks.com:9080/AutoRun/',
    
    // Database Configuration
    DB_HOST: 'mprts-qa01.lab.wagerworks.com',
    DB_USER: 'rgs_rw',
    DB_PASSWORD: 'rgs_rw',
    DB_NAME: 'rgs_test',
    DB_PORT: 3306,
    
    // SSH Tunnel Configuration
    SSH_ENABLED: true,
    SSH_HOST: 'mprts-qa01.lab.wagerworks.com',
    SSH_USER: process.env.SSH_USER || 'volfkoi',
    SSH_PORT: 22,
    SSH_KEY_PATH: process.env.SSH_KEY_PATH || path.join(process.env.USERPROFILE || process.env.HOME, '.ssh', 'id_rsa_dbserver'),
    SSH_LOCAL_HOST: '127.0.0.1',
    SSH_LOCAL_PORT: 3307
  },
  
  // QA02 Environment
  qa02: {
    // Server Configuration
    PORT: 3000,
    BASE_URL: 'http://mprts-qa02.lab.wagerworks.com:5080/AutoRun/',
    
    // Database Configuration
    DB_HOST: 'mprts-qa02.lab.wagerworks.com',
    DB_USER: 'rgs_rw',
    DB_PASSWORD: 'rgs_rw',
    DB_NAME: 'rgs_test',
    DB_PORT: 3306,
    
    // SSH Tunnel Configuration
    SSH_ENABLED: true,
    SSH_HOST: 'mprts-qa02.lab.wagerworks.com',
    SSH_USER: process.env.SSH_USER || 'volfkoi',
    SSH_PORT: 22,
    SSH_KEY_PATH: process.env.SSH_KEY_PATH || path.join(process.env.USERPROFILE || process.env.HOME, '.ssh', 'id_rsa_dbserver'),
    SSH_LOCAL_HOST: '127.0.0.1',
    SSH_LOCAL_PORT: 3307
  },
  
  // QA03 Environment
  qa03: {
    // Server Configuration
    PORT: 3000,
    BASE_URL: 'http://mprts-qa03.lab.wagerworks.com:5080/AutoRun/',
    
    // Database Configuration
    DB_HOST: 'mprts-qa03.lab.wagerworks.com',
    DB_USER: 'rgs_rw',
    DB_PASSWORD: 'rgs_rw',
    DB_NAME: 'rgs_test',
    DB_PORT: 3306,
    
    // SSH Tunnel Configuration
    SSH_ENABLED: true,
    SSH_HOST: 'mprts-qa03.lab.wagerworks.com',
    SSH_USER: process.env.SSH_USER || 'volfkoi',
    SSH_PORT: 22,
    SSH_KEY_PATH: process.env.SSH_KEY_PATH || path.join(process.env.USERPROFILE || process.env.HOME, '.ssh', 'id_rsa_dbserver'),
    SSH_LOCAL_HOST: '127.0.0.1',
    SSH_LOCAL_PORT: 3307
  }
};

/**
 * Sets environment variables for the specified environment
 * @param {string} env - Environment name (qa01, qa02, qa03)
 * @returns {Object} - The environment configuration that was applied
 */
function setEnvironment(env) {
  if (!environments[env]) {
    throw new Error(`Unknown environment: ${env}. Available environments: ${Object.keys(environments).join(', ')}`);
  }
  
  const config = environments[env];
  
  // Apply environment variables
  Object.entries(config).forEach(([key, value]) => {
    process.env[key] = String(value);
  });
  
  console.log(`Environment set to ${env}`);
  
  return config;
}

/**
 * Gets the current configuration based on environment variables
 * @returns {Object} - The current environment configuration
 */
function getCurrentConfig() {
  // Create a configuration object from current environment variables
  return {
    // Server Configuration
    PORT: process.env.PORT,
    BASE_URL: process.env.BASE_URL,
    
    // Database Configuration
    DB_HOST: process.env.DB_HOST,
    DB_USER: process.env.DB_USER,
    DB_PASSWORD: process.env.DB_PASSWORD,
    DB_NAME: process.env.DB_NAME,
    DB_PORT: process.env.DB_PORT,
    
    // SSH Tunnel Configuration
    SSH_ENABLED: process.env.SSH_ENABLED,
    SSH_HOST: process.env.SSH_HOST,
    SSH_USER: process.env.SSH_USER,
    SSH_PORT: process.env.SSH_PORT,
    SSH_KEY_PATH: process.env.SSH_KEY_PATH,
    SSH_LOCAL_HOST: process.env.SSH_LOCAL_HOST,
    SSH_LOCAL_PORT: process.env.SSH_LOCAL_PORT
  };
}

/**
 * Detect the current environment based on the DB_HOST value
 * @returns {string|null} - The detected environment name or null if not detected
 */
function detectCurrentEnvironment() {
  if (!process.env.DB_HOST) return null;
  
  const dbHost = process.env.DB_HOST.toLowerCase();
  
  for (const [env, config] of Object.entries(environments)) {
    if (dbHost.includes(env)) {
      return env;
    }
  }
  
  return null;
}

module.exports = {
  environments,
  setEnvironment,
  getCurrentConfig,
  detectCurrentEnvironment
}; 
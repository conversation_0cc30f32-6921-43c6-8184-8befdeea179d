<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="1744875117905" clover="3.2.0">
  <project timestamp="1744875117905" name="All files">
    <metrics statements="276" coveredstatements="155" conditionals="157" coveredconditionals="47" methods="33" coveredmethods="18" elements="466" coveredelements="220" complexity="0" loc="276" ncloc="276" packages="2" files="2" classes="2"/>
    <package name="services">
      <metrics statements="190" coveredstatements="150" conditionals="80" coveredconditionals="46" methods="20" coveredmethods="18"/>
      <file name="external-api-service.js" path="C:\Dev\smarttest\frontend\reports\services\external-api-service.js">
        <metrics statements="190" coveredstatements="150" conditionals="80" coveredconditionals="46" methods="20" coveredmethods="18"/>
        <line num="11" count="1" type="stmt"/>
        <line num="14" count="1" type="stmt"/>
        <line num="15" count="1" type="stmt"/>
        <line num="18" count="1" type="stmt"/>
        <line num="20" count="1" type="stmt"/>
        <line num="28" count="12" type="cond" truecount="3" falsecount="1"/>
        <line num="29" count="0" type="stmt"/>
        <line num="32" count="12" type="stmt"/>
        <line num="42" count="1" type="stmt"/>
        <line num="43" count="1" type="stmt"/>
        <line num="46" count="1" type="stmt"/>
        <line num="47" count="1" type="stmt"/>
        <line num="48" count="1" type="stmt"/>
        <line num="51" count="1" type="stmt"/>
        <line num="62" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="63" count="0" type="stmt"/>
        <line num="67" count="1" type="stmt"/>
        <line num="68" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="69" count="0" type="stmt"/>
        <line num="73" count="1" type="stmt"/>
        <line num="74" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="75" count="0" type="stmt"/>
        <line num="79" count="1" type="stmt"/>
        <line num="80" count="1" type="stmt"/>
        <line num="82" count="1" type="stmt"/>
        <line num="83" count="1" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="97" count="11" type="cond" truecount="1" falsecount="1"/>
        <line num="98" count="11" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="114" count="6" type="stmt"/>
        <line num="116" count="6" type="stmt"/>
        <line num="119" count="6" type="stmt"/>
        <line num="120" count="6" type="stmt"/>
        <line num="121" count="10" type="stmt"/>
        <line num="125" count="6" type="stmt"/>
        <line num="135" count="6" type="cond" truecount="2" falsecount="0"/>
        <line num="136" count="1" type="stmt"/>
        <line num="137" count="1" type="stmt"/>
        <line num="138" count="4" type="stmt"/>
        <line num="140" count="1" type="stmt"/>
        <line num="141" count="1" type="stmt"/>
        <line num="144" count="6" type="stmt"/>
        <line num="147" count="6" type="stmt"/>
        <line num="149" count="6" type="cond" truecount="1" falsecount="1"/>
        <line num="150" count="0" type="stmt"/>
        <line num="153" count="6" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="168" count="4" type="stmt"/>
        <line num="169" count="4" type="stmt"/>
        <line num="172" count="4" type="stmt"/>
        <line num="180" count="4" type="stmt"/>
        <line num="183" count="4" type="stmt"/>
        <line num="185" count="4" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="188" count="0" type="stmt"/>
        <line num="201" count="1" type="stmt"/>
        <line num="202" count="1" type="stmt"/>
        <line num="205" count="1" type="stmt"/>
        <line num="213" count="1" type="stmt"/>
        <line num="216" count="1" type="stmt"/>
        <line num="218" count="1" type="stmt"/>
        <line num="220" count="0" type="stmt"/>
        <line num="221" count="0" type="stmt"/>
        <line num="233" count="0" type="stmt"/>
        <line num="234" count="0" type="stmt"/>
        <line num="237" count="0" type="stmt"/>
        <line num="246" count="0" type="stmt"/>
        <line num="249" count="0" type="stmt"/>
        <line num="251" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="252" count="0" type="stmt"/>
        <line num="254" count="0" type="stmt"/>
        <line num="255" count="0" type="stmt"/>
        <line num="266" count="4" type="stmt"/>
        <line num="268" count="4" type="stmt"/>
        <line num="269" count="4" type="stmt"/>
        <line num="272" count="4" type="stmt"/>
        <line num="273" count="4" type="stmt"/>
        <line num="274" count="4" type="cond" truecount="1" falsecount="1"/>
        <line num="275" count="0" type="stmt"/>
        <line num="279" count="4" type="stmt"/>
        <line num="280" count="4" type="stmt"/>
        <line num="281" count="4" type="stmt"/>
        <line num="282" count="4" type="stmt"/>
        <line num="283" count="20" type="stmt"/>
        <line num="284" count="20" type="cond" truecount="2" falsecount="0"/>
        <line num="285" count="4" type="stmt"/>
        <line num="286" count="16" type="cond" truecount="2" falsecount="0"/>
        <line num="287" count="4" type="stmt"/>
        <line num="292" count="4" type="stmt"/>
        <line num="293" count="4" type="stmt"/>
        <line num="294" count="4" type="stmt"/>
        <line num="295" count="20" type="stmt"/>
        <line num="296" count="20" type="cond" truecount="2" falsecount="0"/>
        <line num="297" count="4" type="cond" truecount="1" falsecount="1"/>
        <line num="298" count="16" type="cond" truecount="2" falsecount="0"/>
        <line num="299" count="4" type="cond" truecount="2" falsecount="0"/>
        <line num="304" count="4" type="stmt"/>
        <line num="305" count="4" type="stmt"/>
        <line num="306" count="4" type="stmt"/>
        <line num="307" count="4" type="stmt"/>
        <line num="308" count="8" type="cond" truecount="2" falsecount="0"/>
        <line num="309" count="8" type="cond" truecount="1" falsecount="1"/>
        <line num="310" count="0" type="stmt"/>
        <line num="311" count="0" type="stmt"/>
        <line num="312" count="8" type="cond" truecount="2" falsecount="0"/>
        <line num="313" count="4" type="stmt"/>
        <line num="314" count="4" type="stmt"/>
        <line num="319" count="4" type="stmt"/>
        <line num="320" count="4" type="stmt"/>
        <line num="321" count="20" type="stmt"/>
        <line num="323" count="4" type="cond" truecount="1" falsecount="1"/>
        <line num="324" count="4" type="stmt"/>
        <line num="325" count="4" type="stmt"/>
        <line num="326" count="4" type="cond" truecount="1" falsecount="1"/>
        <line num="327" count="4" type="stmt"/>
        <line num="332" count="4" type="stmt"/>
        <line num="333" count="4" type="cond" truecount="3" falsecount="1"/>
        <line num="334" count="4" type="stmt"/>
        <line num="335" count="4" type="stmt"/>
        <line num="336" count="4" type="stmt"/>
        <line num="337" count="4" type="stmt"/>
        <line num="338" count="4" type="stmt"/>
        <line num="339" count="4" type="stmt"/>
        <line num="340" count="4" type="stmt"/>
        <line num="344" count="4" type="stmt"/>
        <line num="360" count="4" type="stmt"/>
        <line num="361" count="4" type="stmt"/>
        <line num="363" count="0" type="stmt"/>
        <line num="364" count="0" type="stmt"/>
        <line num="379" count="1" type="stmt"/>
        <line num="381" count="1" type="stmt"/>
        <line num="382" count="1" type="stmt"/>
        <line num="385" count="1" type="stmt"/>
        <line num="386" count="1" type="stmt"/>
        <line num="389" count="1" type="stmt"/>
        <line num="390" count="1" type="stmt"/>
        <line num="391" count="1" type="stmt"/>
        <line num="393" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="395" count="1" type="stmt"/>
        <line num="396" count="1" type="stmt"/>
        <line num="399" count="1" type="stmt"/>
        <line num="400" count="1" type="stmt"/>
        <line num="401" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="402" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="403" count="1" type="stmt"/>
        <line num="404" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="405" count="0" type="stmt"/>
        <line num="409" count="1" type="stmt"/>
        <line num="410" count="1" type="stmt"/>
        <line num="413" count="1" type="stmt"/>
        <line num="414" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="416" count="0" type="stmt"/>
        <line num="417" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="418" count="0" type="stmt"/>
        <line num="423" count="1" type="stmt"/>
        <line num="435" count="1" type="stmt"/>
        <line num="436" count="1" type="stmt"/>
        <line num="437" count="1" type="stmt"/>
        <line num="438" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="439" count="1" type="stmt"/>
        <line num="440" count="1" type="stmt"/>
        <line num="441" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="442" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="443" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="448" count="1" type="stmt"/>
        <line num="457" count="1" type="stmt"/>
        <line num="458" count="1" type="stmt"/>
        <line num="460" count="0" type="stmt"/>
        <line num="461" count="0" type="stmt"/>
        <line num="475" count="4" type="cond" truecount="1" falsecount="1"/>
        <line num="478" count="4" type="stmt"/>
        <line num="479" count="4" type="cond" truecount="1" falsecount="1"/>
        <line num="480" count="0" type="stmt"/>
        <line num="484" count="4" type="stmt"/>
        <line num="496" count="1" type="stmt"/>
        <line num="497" count="1" type="stmt"/>
        <line num="500" count="1" type="stmt"/>
        <line num="503" count="1" type="stmt"/>
        <line num="504" count="3" type="stmt"/>
        <line num="506" count="0" type="stmt"/>
        <line num="507" count="0" type="stmt"/>
        <line num="516" count="1" type="stmt"/>
        <line num="518" count="1" type="stmt"/>
        <line num="519" count="1" type="stmt"/>
        <line num="521" count="0" type="stmt"/>
        <line num="522" count="0" type="stmt"/>
        <line num="528" count="1" type="stmt"/>
      </file>
    </package>
    <package name="tests.mocks">
      <metrics statements="86" coveredstatements="5" conditionals="77" coveredconditionals="1" methods="13" coveredmethods="0"/>
      <file name="reports.js" path="C:\Dev\smarttest\frontend\reports\tests\mocks\reports.js">
        <metrics statements="86" coveredstatements="5" conditionals="77" coveredconditionals="1" methods="13" coveredmethods="0"/>
        <line num="6" count="1" type="stmt"/>
        <line num="7" count="1" type="stmt"/>
        <line num="8" count="1" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="29" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="78" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="79" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="84" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="85" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="88" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="97" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="101" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="103" count="0" type="stmt"/>
        <line num="108" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="109" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="120" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="122" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="169" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="170" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="175" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="176" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="179" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="186" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="187" count="0" type="stmt"/>
        <line num="194" count="0" type="stmt"/>
        <line num="197" count="0" type="stmt"/>
        <line num="199" count="0" type="stmt"/>
        <line num="200" count="0" type="stmt"/>
        <line num="203" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="206" count="0" type="stmt"/>
        <line num="208" count="0" type="stmt"/>
        <line num="223" count="0" type="stmt"/>
        <line num="224" count="0" type="stmt"/>
        <line num="225" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="228" count="0" type="stmt"/>
        <line num="236" count="0" type="stmt"/>
        <line num="238" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="239" count="0" type="stmt"/>
        <line num="240" count="0" type="stmt"/>
        <line num="241" count="0" type="stmt"/>
        <line num="245" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="248" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="249" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="252" count="0" type="stmt"/>
        <line num="255" count="0" type="stmt"/>
        <line num="288" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="289" count="0" type="stmt"/>
        <line num="296" count="0" type="stmt"/>
        <line num="299" count="0" type="stmt"/>
        <line num="301" count="0" type="stmt"/>
        <line num="302" count="0" type="stmt"/>
        <line num="305" count="0" type="stmt"/>
        <line num="307" count="0" type="stmt"/>
        <line num="317" count="0" type="stmt"/>
        <line num="322" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="323" count="1" type="stmt"/>
      </file>
    </package>
  </project>
</coverage>

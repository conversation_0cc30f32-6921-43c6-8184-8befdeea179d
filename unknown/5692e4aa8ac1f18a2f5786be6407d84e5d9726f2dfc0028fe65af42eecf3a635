// diagnose_column_mapping.js
// Run this script with: node frontend/server/tests/diagnose_column_mapping.js
// It will print out the raw rows and column keys for the latest test sessions.

const path = require('path');
const db = require('../database'); // Use the main database entry point

async function diagnoseColumnMapping() {
  try {
    // Initialize the database connection
    await db.init();

    // Run the same query as getRecentRuns (without any CASE/alias logic)
    const sql = `SELECT * FROM test_session ORDER BY tsn_id DESC LIMIT 5`;
    const rows = await db.query(sql);

    console.log('--- RAW ROWS FROM DATABASE (first 5) ---');
    rows.forEach((row, idx) => {
      console.log(`Row ${idx + 1}:`, row);
      console.log('Keys:', Object.keys(row));
    });

    // Print column order as received
    if (rows.length > 0) {
      console.log('\nColumn order as received:', Object.keys(rows[0]));
    } else {
      console.log('No rows returned.');
    }

    // Clean up
    await db.close();
    process.exit(0);
  } catch (error) {
    console.error('Error during column mapping diagnosis:', error);
    process.exit(1);
  }
}

diagnoseColumnMapping();

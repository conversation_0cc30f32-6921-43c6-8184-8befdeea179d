/**
 * QA01 Direct Database Connection Test Script
 *
 * This script tests direct database access through SSH to QA01
 * It executes queries by running MySQL commands over SSH
 */

// Import modules
const fs = require('fs');
const path = require('path');
require('dotenv').config({ path: path.resolve(__dirname, '.env.01') }); // Load environment variables from .env.01

// Default values if environment variables are not set
const defaultEnvValues = {
  // Server Configuration
  PORT: 3000,
  BASE_URL: 'http://mprts-qa01.lab.wagerworks.com:9080/AutoRun/',

  // Database Configuration
  DB_HOST: 'mprts-qa01.lab.wagerworks.com',
  DB_USER: 'rgs_rw',
  DB_PASSWORD: 'rgs_rw',
  DB_NAME: 'rgs_test',
  DB_PORT: 3306,

  // SSH Configuration
  SSH_ENABLED: true,
  SSH_HOST: 'mprts-qa01.lab.wagerworks.com',
  SSH_USER: process.env.SSH_USER || 'defaultuser',
  SSH_PORT: 22,
  SSH_KEY_PATH: path.join(process.env.USERPROFILE || process.env.HOME, '.ssh', 'id_rsa_dbserver'),

  // Enable debug logging
  DB_DEBUG: 'true'
};

// Apply default values only if environment variables are not set
Object.entries(defaultEnvValues).forEach(([key, value]) => {
  if (!process.env[key]) {
    process.env[key] = String(value);
  }
});

// Print loaded SSH and DB variables for verification
console.log('QA01 Environment variables loaded:');
['SSH_ENABLED', 'SSH_HOST', 'SSH_USER', 'SSH_PORT', 'SSH_KEY_PATH',
  'DB_HOST', 'DB_USER', 'DB_PASSWORD', 'DB_NAME', 'DB_PORT'].forEach(key => {
  // Mask password value in logs for security
  const value = key === 'DB_PASSWORD'
    ? (process.env[key] ? '********' : '(not set)')
    : (process.env[key] || '(not set)');
  console.log(`${key}: ${value}`);
});

// Import the database module
const db = require('./database');

// Main function to run the test
async function runTest() {
  try {
    console.log('\n--- STARTING QA01 DIRECT DATABASE CONNECTION TEST ---\n');

    // Step 1: Initialize database connection
    console.log('Step 1: Initializing direct database connection to QA01...');
    await db.init();
    console.log('✅ Database connection initialized successfully!\n');

    // Step 2: Execute a simple query to test the connection
    console.log('Step 2: Testing basic connectivity...');
    console.log('Running test query: SELECT 1 AS test_value');
    const result = await db.query('SELECT 1 AS test_value');
    console.log('Query result:', JSON.stringify(result));
    console.log('✅ Basic connectivity test successful!\n');

    // Step 3: Execute a query to get MySQL version
    console.log('Step 3: Getting MySQL version...');
    const versionQuery = 'SELECT VERSION() AS version';
    const versionResult = await db.query(versionQuery);
    console.log('Version result:', JSON.stringify(versionResult));
    console.log('✅ Version query successful!\n');

    // Step 4: Get information about test case 1279
    console.log('Step 4: Getting information about test case 1279...');
    const testCaseQuery = `
      SELECT tc_id, COUNT(*) AS execution_count
      FROM test_result
      WHERE tc_id = 1279
      GROUP BY tc_id
    `;
    const testCaseInfo = await db.query(testCaseQuery);
    console.log('Test case info:', JSON.stringify(testCaseInfo));
    console.log('✅ Test case query successful!\n');

    // Step 5: Get information about test suite 82
    console.log('Step 5: Getting information about test suite 82...');
    const testSuiteQuery = `
      SELECT COUNT(DISTINCT tc_id) AS test_case_count
      FROM test_case_group
      WHERE ts_id = 82
    `;
    const testSuiteInfo = await db.query(testSuiteQuery);
    console.log('Test suite info:', JSON.stringify(testSuiteInfo));
    console.log('✅ Test suite query successful!\n');

    console.log('\n--- TEST COMPLETED SUCCESSFULLY ---');
    return true;
  } catch (error) {
    console.error('\n❌ TEST FAILED:', error);
    return false;
  } finally {
    // Always attempt to close the database connection
    try {
      console.log('\nClosing database connection...');
      await db.close();
      console.log('✅ Database connection closed successfully');
    } catch (closeError) {
      console.error('❌ Error closing database connection:', closeError);
    }
  }
}

// Run the test and exit with appropriate code
runTest()
  .then(success => {
    if (success) {
      console.log('\nAll tests passed! Direct database connection to QA01 is working properly.');
      process.exit(0);
    } else {
      console.error('\nTests failed! Please check the error messages above.');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('\nUnexpected error in the test runner:', error);
    process.exit(1);
  });
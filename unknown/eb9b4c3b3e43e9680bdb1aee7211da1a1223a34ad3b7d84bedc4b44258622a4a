# Refactored Database Layer Documentation

## Overview

The SmartTest database layer has been completely refactored to provide a clean, maintainable, and extensible interface for interacting with the database. This document provides comprehensive documentation for the new database layer, including its architecture, API, and usage examples.

## Table of Contents

1. [Architecture](#architecture)
2. [Directory Structure](#directory-structure)
3. [Configuration](#configuration)
4. [Connection Methods](#connection-methods)
5. [API Reference](#api-reference)
6. [Query Builder](#query-builder)
7. [Pre-defined Queries](#pre-defined-queries)
8. [External API Integration](#external-api-integration)
9. [AI Integration](#ai-integration)
10. [Error Handling](#error-handling)
11. [Usage Examples](#usage-examples)
12. [Migration Guide](#migration-guide)

## Architecture

The refactored database layer follows a modular architecture with clear separation of concerns:

- **Configuration**: Environment-specific configuration is centralized and loaded from environment variables or configuration files.
- **Connection Methods**: Multiple connection methods (direct SSH and SSH tunnel) are supported, with direct SSH as the primary method.
- **Query Builder**: A flexible SQL query builder is provided for constructing complex queries.
- **Pre-defined Queries**: Common database operations are implemented as pre-defined queries.
- **External API Integration**: The database layer interacts with external APIs for operations like running tests and retrieving test results.
- **AI Integration**: The architecture is designed to be easily extensible for future AI integration.

### Key Design Principles

1. **Modularity**: Each component has a single responsibility and can be tested in isolation.
2. **Extensibility**: The architecture is designed to be easily extended with new functionality.
3. **Robustness**: Error handling is implemented at all levels, with fallback mechanisms for when operations fail.
4. **Performance**: Connection pooling and query optimization are implemented to improve performance.
5. **Security**: Database credentials are centralized and loaded from environment variables.

## Directory Structure

```
database/
├── index.js                  # Main entry point
├── config/                   # Configuration
│   ├── index.js              # Configuration loader
│   └── environments.js       # Environment-specific configs
├── connections/              # Connection implementations
│   ├── index.js              # Connection factory
│   ├── direct-ssh.js         # Direct SSH implementation
│   └── ssh-tunnel.js         # SSH tunnel implementation
├── queries/                  # Pre-defined queries
│   ├── index.js              # Query module exports
│   ├── test-cases.js         # Test case queries
│   ├── test-suites.js        # Test suite queries
│   ├── test-sessions.js      # Test session queries
│   └── test-results.js       # Test result queries
├── utils/                    # Database utilities
│   ├── query-builder.js      # SQL query builder
│   └── result-formatter.js   # Result formatting utilities
└── ai/                       # AI integration components
    ├── index.js              # AI module exports
    ├── query-generator.js    # AI-driven query generation
    ├── request-generator.js  # AI-driven HTTP request generation
    └── context-provider.js   # Database context for AI
```

## Configuration

The configuration module provides a centralized way to manage environment-specific configuration. It loads configuration from environment variables or configuration files and provides methods for getting and setting the current environment.

### Environment Configuration

Each environment (qa01, qa02, qa03) has its own configuration, including:

- **Server Configuration**: Port, base URL
- **Database Configuration**: Host, user, password, database name, port
- **SSH Configuration**: Host, user, port, key path
- **Connection Preference**: Preferred connection method (direct or tunnel)

### Configuration Methods

- `getConfig(environment)`: Get configuration for a specific environment
- `getCurrentConfig()`: Get configuration for the current environment
- `detectEnvironment()`: Detect the current environment from environment variables
- `setEnvironment(environment)`: Set environment variables for a specific environment

## Connection Methods

The database layer supports two connection methods:

1. **Direct SSH**: Executes SQL queries through direct SSH commands
2. **SSH Tunnel**: Creates an SSH tunnel and connects to the database through the tunnel

### Connection Factory

The connection factory creates the appropriate connection based on the environment and options:

```javascript
const connection = ConnectionFactory.createConnection(environment, options);
```

By default, the connection factory prioritizes direct SSH connections, but SSH tunnel can be forced if needed:

```javascript
// Force SSH tunnel connection
const connection = ConnectionFactory.createConnection(environment, { forceTunnel: true });
```

## API Reference

### Core Functions

- `init(environment, options)`: Initialize the database connection
  - `environment`: Environment name (qa01, qa02, qa03)
  - `options`: Connection options (forceDirect, forceTunnel)
  - Returns: Promise<boolean> - True if initialization was successful

- `query(sql, params)`: Execute a raw SQL query
  - `sql`: SQL query
  - `params`: Query parameters
  - Returns: Promise<Array> - Query results

- `close()`: Close the database connection
  - Returns: Promise<void>

- `getConnectionInfo()`: Get information about the current connection
  - Returns: Object - Connection information

### Test Case Functions

- `getTestCases(filters)`: Get test cases with optional filtering
  - `filters`: Optional filters (ts_id, status, limit)
  - Returns: Promise<Array> - Test cases

- `getTestCaseById(tc_id)`: Get a test case by ID
  - `tc_id`: Test case ID
  - Returns: Promise<Object> - Test case

- `searchTestCases(criteria)`: Search test cases
  - `criteria`: Search criteria (name, status, min_id, max_id, limit)
  - Returns: Promise<Array> - Test cases

### Test Suite Functions

- `getTestSuites(filters)`: Get test suites with optional filtering
  - `filters`: Optional filters (name, status, uid, limit)
  - Returns: Promise<Array> - Test suites

- `getTestSuiteById(ts_id)`: Get a test suite by ID
  - `ts_id`: Test suite ID
  - Returns: Promise<Object> - Test suite

- `getTestSuiteInfo(ts_id)`: Get test cases in a test suite
  - `ts_id`: Test suite ID
  - Returns: Promise<Object> - Test suite with test cases

### Test Session Functions

- `getActiveTests(filters)`: Get active test sessions
  - `filters`: Optional filters (uid, limit)
  - Returns: Promise<Array> - Active test sessions

- `getRecentRuns(filters)`: Get recent test sessions
  - `filters`: Optional filters (limit)
  - Returns: Promise<Array> - Recent test sessions

- `getTestSessionDetails(tsn_id)`: Get test session details
  - `tsn_id`: Test session ID
  - Returns: Promise<Object> - Test session details

### Test Result Functions

- `getTestResults(tsn_id)`: Get test results for a specific test session
  - `tsn_id`: Test session ID
  - Returns: Promise<Object> - Test results

- `getTestResultSummary(tsn_id)`: Get test result summary for a specific test session
  - `tsn_id`: Test session ID
  - Returns: Promise<Object> - Test result summary

- `getTestCaseResults(tsn_id)`: Get test case results for a specific test session
  - `tsn_id`: Test session ID
  - Returns: Promise<Array> - Test case results

### AI-Related Functions

- `executeNaturalLanguageQuery(description)`: Execute a query generated from natural language
  - `description`: Natural language query description
  - Returns: Promise<Array> - Query results

### Environment Utilities

- `setEnvironment(environment)`: Set environment variables for the specified environment
  - `environment`: Environment name
  - Returns: Object - The environment configuration that was applied

- `detectEnvironment()`: Detect current environment from environment variables
  - Returns: string|null - Detected environment name

- `environments`: Environment configurations

## Query Builder

The query builder provides a fluent interface for constructing SQL queries:

```javascript
const queryBuilder = new QueryBuilder();
queryBuilder.select('test_case', ['tc_id', 'name', 'status']);
queryBuilder.where('status', '=', 'active');
queryBuilder.limit(10);

const { sql, params } = queryBuilder.build();
// sql: "SELECT tc_id, name, status FROM test_case WHERE status = ? LIMIT ?"
// params: ["active", 10]
```

### Query Builder Methods

- `select(table, columns)`: Select columns from a table
- `where(column, operator, value)`: Add a WHERE condition
- `join(table, condition, type)`: Add a JOIN clause
- `leftJoin(table, condition)`: Add a LEFT JOIN clause
- `rightJoin(table, condition)`: Add a RIGHT JOIN clause
- `orderBy(column, direction)`: Add an ORDER BY clause
- `groupBy(columns)`: Add a GROUP BY clause
- `having(condition)`: Add a HAVING clause
- `limit(limit)`: Add a LIMIT clause
- `build()`: Build the SQL query

### AI-Driven Query Building

The query builder also supports AI-driven query construction:

```javascript
// Create a query builder from a structured query object
const queryBuilder = QueryBuilder.fromSpec({
  type: 'SELECT',
  table: 'test_case',
  columns: ['tc_id', 'name', 'status'],
  where: [{ column: 'status', operator: '=', value: 'active' }],
  limit: 10
});

// Create a query builder from a natural language description (future)
const queryBuilder = QueryBuilder.fromDescription('Get all active test cases');
```

## Pre-defined Queries

The database layer includes pre-defined queries for common operations:

### Test Case Queries

- `getTestCases(connection, filters)`: Get test cases with optional filtering
- `getTestCaseById(connection, tcId)`: Get a test case by ID
- `searchTestCases(connection, criteria)`: Search test cases

### Test Suite Queries

- `getTestSuites(connection, filters)`: Get test suites with optional filtering
- `getTestSuiteById(connection, tsId)`: Get a test suite by ID
- `getTestSuiteInfo(connection, tsId)`: Get test cases in a test suite

### Test Session Queries

- `getActiveTests(connection, filters)`: Get active test sessions
- `getRecentRuns(connection, filters)`: Get recent test sessions
- `getTestSessionDetails(connection, tsnId)`: Get test session details

### Test Result Queries

- `getTestResults(connection, tsnId)`: Get test results for a specific test session
- `getTestResultSummary(connection, tsnId)`: Get test result summary for a specific test session
- `getTestCaseResults(connection, tsnId)`: Get test case results for a specific test session

## External API Integration

The database layer integrates with external APIs to perform various operations, such as running tests, stopping tests, and retrieving test results. The external APIs are hosted on two different ports:

1. **Port 5080 Endpoints**: These endpoints use form data authentication with `uid` and `password` parameters.
2. **Port 9080 Endpoints**: These endpoints use cookie-based authentication with a `JSESSIONID` cookie.

### Hybrid Data Access Architecture

The SmartTest application implements a hybrid data access approach for the reports page that combines direct external API integration with database access:

- **Test Results Table**: Uses direct external API integration to fetch test results from port 9080 endpoints
- **Test Details View**: Uses direct external API integration to fetch test details from port 9080 endpoints
- **Analytics Charts**: Uses database queries for complex analytics (future enhancement)

This hybrid approach provides several benefits:

- **Performance**: Direct API calls avoid the overhead of the database layer
- **Reliability**: Reports page works even if the database is unavailable
- **Flexibility**: Complex analytics can still use the database when needed

For detailed information about the external API integration, see [External API Integration](external-api-integration.md) and [Hybrid Data Access](../Integration/hybrid-data-access.md).

## AI Integration

The database layer is designed to be easily extensible for future AI integration. The AI integration components are currently placeholders, but they provide a foundation for future development.

### AI Components

- **Query Generator**: Generates SQL queries from natural language
- **Request Generator**: Generates HTTP requests from natural language
- **Context Provider**: Provides database schema and context information to AI

### AI Integration Methods

- `executeNaturalLanguageQuery(description)`: Execute a query generated from natural language
- `generateQuery(description, dbSchema)`: Generate a SQL query from natural language
- `generateRequest(description, apiSchema)`: Generate an HTTP request from natural language

## Error Handling

The database layer includes robust error handling with fallback mechanisms:

1. **Connection Errors**: If a connection fails, it will attempt to reconnect or use a fallback connection method.
2. **Query Errors**: If a query fails, it will attempt to execute a simpler fallback query.
3. **Initialization Errors**: If initialization fails, it will clean up any partial connections and throw an error.

### Error Handling Example

```javascript
try {
  const testCases = await db.getTestCases({ suiteId: 123 });
  console.log(testCases);
} catch (error) {
  console.error('Error getting test cases:', error);
  // Handle the error
}
```

## Usage Examples

### Basic Usage

```javascript
const db = require('./database');

// Initialize the database connection
await db.init();

// Get test cases
const testCases = await db.getTestCases({ limit: 10 });

// Get test suites
const testSuites = await db.getTestSuites({ projectId: 1 });

// Get active tests
const activeTests = await db.getActiveTests();

// Get test results
const testResults = await db.getTestResults('12345');

// Close the connection when done
await db.close();
```

### Advanced Usage with Query Builder

```javascript
const db = require('./database');
const { QueryBuilder } = db;

// Initialize the database connection
await db.init();

// Create a query builder
const queryBuilder = new QueryBuilder();

// Build a complex query
queryBuilder.select('test_result r', ['r.tsn_id', 'r.tc_id', 'r.outcome', 'r.creation_time', 'o.txt']);
queryBuilder.join('output o', 'r.cnt = o.cnt');
queryBuilder.where('r.tsn_id', '=', '12345');
queryBuilder.orderBy('r.creation_time', 'ASC');

// Execute the query
const { sql, params } = queryBuilder.build();
const results = await db.query(sql, params);

// Close the connection when done
await db.close();
```

### Environment-Specific Configuration

```javascript
const db = require('./database');

// Initialize with a specific environment
await db.init('qa02');

// Force direct SSH connection
await db.init('qa02', { forceDirect: true });

// Force SSH tunnel connection
await db.init('qa02', { forceTunnel: true });

// Get connection information
const info = db.getConnectionInfo();
console.log(`Connected to ${info.environment} using ${info.connectionMethod} method`);
```

### AI Integration (Future)

```javascript
const db = require('./database');

// Initialize the database connection
await db.init();

// Execute a natural language query
const results = await db.executeNaturalLanguageQuery('Get all failed test cases from the last 24 hours');

// Close the connection when done
await db.close();
```

## Migration Guide

### Migrating from the Old Database Layer

To migrate from the old database layer to the new one, follow these steps:

1. **Update Imports**: Replace imports of the old database module with the new one:

   ```javascript
   // Old
   const db = require('../db-manager');

   // New
   const db = require('../database');
   ```

2. **Update Initialization**: Update the initialization code:

   ```javascript
   // Old
   await db.init(environment);

   // New
   await db.init(environment);
   ```

3. **Update Query Calls**: Update query calls to use the new API:

   ```javascript
   // Old
   const testCases = await db.getTestCases({ suite_id: 123 });

   // New
   const testCases = await db.getTestCases({ suiteId: 123 });
   ```

4. **Update Error Handling**: Update error handling to use the new error handling approach:

   ```javascript
   // Old
   try {
     const testCases = await db.getTestCases({ suite_id: 123 });
     console.log(testCases);
   } catch (error) {
     console.error('Error getting test cases:', error);
   }

   // New
   try {
     const testCases = await db.getTestCases({ suiteId: 123 });
     console.log(testCases);
   } catch (error) {
     console.error('Error getting test cases:', error);
   }
   ```

5. **Update Connection Management**: Update connection management to use the new approach:

   ```javascript
   // Old
   await db.close();

   // New
   await db.close();
   ```

### API Mapping

| Old API | New API |
|---------|---------|
| `db.init(environment)` | `db.init(environment)` |
| `db.query(sql, params)` | `db.query(sql, params)` |
| `db.getTestCases(filters)` | `db.getTestCases(filters)` |
| `db.getTestSuites(filters)` | `db.getTestSuites(filters)` |
| `db.getActiveTests(filters)` | `db.getActiveTests(filters)` |
| `db.getTestResults(tsnId)` | `db.getTestResults(tsnId)` |
| `db.close()` | `db.close()` |

### Parameter Mapping

| Old Parameter | New Parameter |
|---------------|---------------|
| `suiteId` | `ts_id` |
| `projectId` | `tp_id` |
| `userId` | `uid` |
| `tsnId` | `tsn_id` |
| `tcId` | `tc_id` |
| `tsId` | `ts_id` |

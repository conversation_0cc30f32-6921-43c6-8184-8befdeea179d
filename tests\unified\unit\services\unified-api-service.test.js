/**
 * Unit Tests for Unified API Service
 * 
 * Tests the unified API service that consolidates:
 * - External API integration
 * - Database operations
 * - Internal API endpoints
 * 
 * Migrated and enhanced from:
 * - frontend/reports/tests/external-api-service.*.test.js
 * - frontend/server/tests/api.test.js
 */

const { MockServiceFactory, TestDataGenerator, AssertionHelpers } = require('../../mocks/test-helpers');
const apiResponses = require('../../mocks/api-responses');

// Mock the unified API service
jest.mock('../../../../frontend/shared/services/unified-api-service');

describe('Unified API Service', () => {
  let unifiedApiService;
  let mockAxios;

  beforeEach(() => {
    // Create fresh mocks for each test
    mockAxios = {
      get: jest.fn(),
      post: jest.fn(),
      put: jest.fn(),
      delete: jest.fn(),
      request: jest.fn()
    };

    // Mock the service
    unifiedApiService = MockServiceFactory.createUnifiedApiService();
    
    // Reset all mocks
    jest.clearAllMocks();
  });

  describe('Service Initialization', () => {
    test('should initialize with default configuration', () => {
      expect(unifiedApiService.isConfigured()).toBe(true);
      expect(unifiedApiService.getConfig()).toEqual(apiResponses.internalApi.configResponse);
    });

    test('should handle configuration errors gracefully', () => {
      unifiedApiService.isConfigured.mockReturnValue(false);
      expect(unifiedApiService.isConfigured()).toBe(false);
    });
  });

  describe('Test Runs API', () => {
    test('should fetch test runs successfully', async () => {
      const mockTestRuns = TestDataGenerator.testRuns(3);
      unifiedApiService.getTestRuns.mockResolvedValue(apiResponses.success(mockTestRuns));

      const response = await unifiedApiService.getTestRuns();

      expect(unifiedApiService.getTestRuns).toHaveBeenCalledTimes(1);
      AssertionHelpers.assertApiResponse(response, mockTestRuns);
      
      // Validate each test run structure
      response.data.forEach(testRun => {
        AssertionHelpers.assertTestRun(testRun);
      });
    });

    test('should handle test runs fetch error', async () => {
      const errorMessage = 'Failed to fetch test runs';
      unifiedApiService.getTestRuns.mockRejectedValue(apiResponses.error(errorMessage));

      await expect(unifiedApiService.getTestRuns()).rejects.toMatchObject({
        response: {
          data: {
            error: errorMessage
          }
        }
      });
    });

    test('should fetch test runs with filters', async () => {
      const filters = { environment: 'QA01', status: 'COMPLETED' };
      const filteredRuns = TestDataGenerator.testRuns(2, filters);
      unifiedApiService.getTestRuns.mockResolvedValue(apiResponses.success(filteredRuns));

      const response = await unifiedApiService.getTestRuns(filters);

      expect(unifiedApiService.getTestRuns).toHaveBeenCalledWith(filters);
      AssertionHelpers.assertApiResponse(response, filteredRuns);
    });
  });

  describe('Test Details API', () => {
    test('should fetch test details successfully', async () => {
      const testId = '3180';
      const mockDetails = apiResponses.externalApi.testDetails;
      unifiedApiService.getTestDetails.mockResolvedValue(apiResponses.success(mockDetails));

      const response = await unifiedApiService.getTestDetails(testId);

      expect(unifiedApiService.getTestDetails).toHaveBeenCalledWith(testId);
      AssertionHelpers.assertApiResponse(response, mockDetails);
      
      // Validate test details structure
      expect(response.data).toHaveProperty('test_id');
      expect(response.data).toHaveProperty('test_name');
      expect(response.data).toHaveProperty('status');
      expect(response.data).toHaveProperty('steps');
      expect(Array.isArray(response.data.steps)).toBe(true);
    });

    test('should handle test details fetch error', async () => {
      const testId = 'invalid-id';
      const errorMessage = 'Test not found';
      unifiedApiService.getTestDetails.mockRejectedValue(apiResponses.error(errorMessage, 404));

      await expect(unifiedApiService.getTestDetails(testId)).rejects.toMatchObject({
        response: {
          status: 404,
          data: {
            error: errorMessage
          }
        }
      });
    });

    test('should validate test ID parameter', async () => {
      const invalidTestId = null;
      
      await expect(unifiedApiService.getTestDetails(invalidTestId)).rejects.toThrow();
    });
  });

  describe('Test Summary API', () => {
    test('should fetch test summary successfully', async () => {
      const mockSummary = apiResponses.externalApi.testSummary;
      unifiedApiService.getTestSummary.mockResolvedValue(apiResponses.success(mockSummary));

      const response = await unifiedApiService.getTestSummary();

      expect(unifiedApiService.getTestSummary).toHaveBeenCalledTimes(1);
      AssertionHelpers.assertApiResponse(response, mockSummary);
      
      // Validate summary structure
      expect(response.data).toHaveProperty('total_tests');
      expect(response.data).toHaveProperty('passed_tests');
      expect(response.data).toHaveProperty('failed_tests');
      expect(response.data).toHaveProperty('pass_rate');
      expect(response.data).toHaveProperty('environments');
      expect(typeof response.data.pass_rate).toBe('number');
    });

    test('should handle summary fetch error', async () => {
      const errorMessage = 'Summary service unavailable';
      unifiedApiService.getTestSummary.mockRejectedValue(apiResponses.error(errorMessage, 503));

      await expect(unifiedApiService.getTestSummary()).rejects.toMatchObject({
        response: {
          status: 503,
          data: {
            error: errorMessage
          }
        }
      });
    });
  });

  describe('HTTP Methods', () => {
    test('should handle GET requests', async () => {
      const endpoint = '/api/test-runs';
      const mockData = TestDataGenerator.testRuns(2);
      unifiedApiService.get.mockResolvedValue(apiResponses.success(mockData));

      const response = await unifiedApiService.get(endpoint);

      expect(unifiedApiService.get).toHaveBeenCalledWith(endpoint);
      AssertionHelpers.assertApiResponse(response, mockData);
    });

    test('should handle POST requests', async () => {
      const endpoint = '/api/test-runs';
      const postData = { name: 'New Test Run', environment: 'QA01' };
      const mockResponse = TestDataGenerator.testRun(postData);
      unifiedApiService.post.mockResolvedValue(apiResponses.success(mockResponse));

      const response = await unifiedApiService.post(endpoint, postData);

      expect(unifiedApiService.post).toHaveBeenCalledWith(endpoint, postData);
      AssertionHelpers.assertApiResponse(response, mockResponse);
    });

    test('should handle PUT requests', async () => {
      const endpoint = '/api/test-runs/1001';
      const updateData = { status: 'COMPLETED' };
      const mockResponse = TestDataGenerator.testRun({ id: 1001, ...updateData });
      unifiedApiService.put.mockResolvedValue(apiResponses.success(mockResponse));

      const response = await unifiedApiService.put(endpoint, updateData);

      expect(unifiedApiService.put).toHaveBeenCalledWith(endpoint, updateData);
      AssertionHelpers.assertApiResponse(response, mockResponse);
    });

    test('should handle DELETE requests', async () => {
      const endpoint = '/api/test-runs/1001';
      const mockResponse = { deleted: true, id: 1001 };
      unifiedApiService.delete.mockResolvedValue(apiResponses.success(mockResponse));

      const response = await unifiedApiService.delete(endpoint);

      expect(unifiedApiService.delete).toHaveBeenCalledWith(endpoint);
      AssertionHelpers.assertApiResponse(response, mockResponse);
    });
  });

  describe('Error Handling', () => {
    test('should handle network errors', async () => {
      const networkError = new Error('Network Error');
      unifiedApiService.getTestRuns.mockRejectedValue(networkError);

      await expect(unifiedApiService.getTestRuns()).rejects.toThrow('Network Error');
    });

    test('should handle timeout errors', async () => {
      const timeoutError = apiResponses.error('Request timeout', 408);
      unifiedApiService.getTestRuns.mockRejectedValue(timeoutError);

      await expect(unifiedApiService.getTestRuns()).rejects.toMatchObject({
        response: {
          status: 408
        }
      });
    });

    test('should handle authentication errors', async () => {
      const authError = apiResponses.error('Unauthorized', 401);
      unifiedApiService.getTestRuns.mockRejectedValue(authError);

      await expect(unifiedApiService.getTestRuns()).rejects.toMatchObject({
        response: {
          status: 401
        }
      });
    });
  });
});

# Test Parameter System Documentation

## Overview

This document details the parameter inheritance system used in the testing framework. Parameters allow test configurations to be defined at different levels of the test hierarchy and inherited by child elements.

## Access Restriction Notice

**Important**: During our database exploration, we discovered that all parameter-related tables have restricted access for the standard database read-only user (`rgs_ro`). This indicates:

1. Parameter management is likely considered a sensitive or privileged operation
2. The system implements a tiered access model with elevated permissions for configuration changes
3. SmartTest implementation will need to consider these access restrictions

While direct database access to these tables is restricted, alternative access methods (such as API endpoints) may be available for parameter management.

## Parameter Hierarchy

The testing framework implements a hierarchical parameter system that follows the organizational structure:

1. **Global Parameters**: System-wide parameters defined at the top level.
2. **Project Parameters**: Parameters defined at the project level, stored in `project_parameter` table (access restricted).
3. **Suite Parameters**: Parameters defined at the test suite level, stored in `suite_parameter` table (access restricted).
4. **Case Parameters**: Parameters defined at the test case level, stored in `case_parameter` table (access restricted).
5. **Step Parameters**: Most granular parameter level, defined for individual test steps, stored in `step_parameter` table (access restricted).

Parameters defined at higher levels cascade down and can be overridden at lower levels.

## Parameter Storage

Based on our findings, parameters are stored in separate tables at each level of the hierarchy:

1. **Test Parameter**: Core parameter definitions (access restricted)
2. **Project Parameter**: Project-level parameter values (access restricted)
3. **Suite Parameter**: Suite-level parameter values (access restricted)
4. **Case Parameter**: Case-level parameter values (access restricted)
5. **Step Parameter**: Step-level parameter values (access restricted)

While we cannot directly view these tables, their existence confirms the hierarchical parameter system.

## Parameter Usage

Parameters are used throughout the testing system to:

1. **Configure Test Environment**: Specify environment-specific settings.
2. **Define Test Data**: Provide data inputs for test cases.
3. **Control Test Flow**: Determine execution paths and conditions.
4. **Set Validation Criteria**: Define expected results and validation rules.

## Parameter Types

Common parameter types may include:

1. **String**: Text values for configuration.
2. **Numeric**: Integer or floating-point values.
3. **Boolean**: True/false flags.
4. **Date/Time**: Temporal values.
5. **JSON/Object**: Structured data values.
6. **Reference**: Pointers to other system objects.

## Parameter Examples

TBD

## Common Parameter Patterns

The system likely implements several common patterns for parameter management:

1. **Inheritance Override**: Lower-level parameters override higher-level ones.
2. **Default Values**: Parameters may have system defaults if not specified.
3. **Reference Resolution**: Parameters may refer to other parameters.
4. **Dynamic Evaluation**: Parameters may contain expressions evaluated at runtime.
5. **Conditional Parameters**: Some parameters may only apply under specific conditions.

## Access Strategies for SmartTest

Given the access restrictions, SmartTest implementation should consider:

1. **API-Based Access**: Utilize existing API endpoints that may provide parameter management
2. **Permission Elevation**: Request enhanced database access for parameter management
3. **Cache-Based Approach**: Initialize parameters from executions and cache known values
4. **Proxy Implementation**: Create a service layer that accesses parameters through authorized channels

## Implementation Strategies

When implementing parameter management in SmartTest:

1. **Account for Restriction**: Design the architecture anticipating limited direct database access
2. **Progressive Enhancement**: Implement basic test execution first, then add parameter management
3. **Service Abstraction**: Create a parameter service that can adapt to different access methods
4. **User Experience**: Provide UI for parameter management that aligns with actual access capabilities
5. **Documentation**: Clearly document parameter inheritance rules and limitations

## Conclusion

The parameter management system is a powerful feature of the test automation framework that enables flexible, reusable, and maintainable tests. By understanding and properly utilizing the parameter hierarchy, test developers can create tests that are both robust and adaptable to different environments and scenarios.

In our SmartTest application, we should provide intuitive interfaces for managing parameters while maintaining compatibility with the existing parameter storage and resolution mechanisms in the database. 
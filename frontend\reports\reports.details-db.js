async function loadTestDetailsFromDatabaseApi(testId, credentials) {
    try {
        console.log(`Loading test details from database API for test ${testId}...`);

        let resultTestDetails = null;

        // Use ApiService if available
        if (window.apiService) {
            console.log('Using ApiService to load test details');

            // Set credentials if they're not already set
            if (!window.apiService.credentials.uid) {
                window.apiService.setCredentials(credentials.uid, credentials.password);
            }

            // Use the getTestDetails method we added to ApiService
            const testDetails = await window.apiService.getTestDetails(testId);
            resultTestDetails = testDetails;

            // Store the test details in current state (with safety check)
            if (typeof window.currentState !== 'undefined') {
                window.currentState.currentTestDetails = testDetails;
            } else if (typeof currentState !== 'undefined') {
                currentState.currentTestDetails = testDetails;
            } else {
                console.warn('currentState not available, skipping state storage');
            }

            console.log('Test details loaded from database API via ApiService:', testDetails);

            // Get reference to current state (with safety check)
            const state = window.currentState || (typeof currentState !== 'undefined' ? currentState : null);

            // If we don't have the full report, try to get it from the API
            if (state && state.currentTestDetails &&
                (!state.currentTestDetails.report || state.currentTestDetails.report === '') &&
                state.currentTestDetails.tsn_id) {

                console.log('Test details loaded but no report found, trying to get full data...');

                // Use ApiService to get the complete test report
                const recentRuns = await window.apiService.getRecentRuns({
                    limit: 1,
                    tsn_id: state.currentTestDetails.tsn_id
                });

                if (recentRuns && recentRuns.length > 0 && recentRuns[0].report) {
                    console.log('Full test report found in recent runs:', recentRuns[0]);
                    state.currentTestDetails.report = recentRuns[0].report;

                    // Copy over any other missing data
                    if (!state.currentTestDetails.test_name && recentRuns[0].test_name) {
                        state.currentTestDetails.test_name = recentRuns[0].test_name;
                    }
                    if (!state.currentTestDetails.status && recentRuns[0].status) {
                        state.currentTestDetails.status = recentRuns[0].status;
                    }
                }
            }
        } else {
            // Fallback to direct fetch for backward compatibility
            console.log('ApiService not available, using direct fetch');

            // Build the URL with path parameter and credentials
            const url = `${config.testDetailsEndpoint}/${testId}?uid=${encodeURIComponent(credentials.uid)}&password=${encodeURIComponent(credentials.password)}`;

            // Make the request with timeout
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

            let response;
            try {
                response = await fetch(url, {
                    signal: controller.signal
                });
                clearTimeout(timeoutId);

                // Check for success
                if (!response.ok) {
                    throw new Error(`API error: ${response.status} ${response.statusText}`);
                }
            } catch (error) {
                clearTimeout(timeoutId);
                if (error.name === 'AbortError') {
                    throw new Error('Database API request timed out after 5 seconds');
                }
                throw error;
            }

            // Parse the response
            const data = await response.json();

            // Check if we have valid data
            if (!data || !data.success) {
                throw new Error(data?.message || 'Invalid response from API');
            }

            console.log('Test details loaded from database API:', data);

            // Store the test details in current state (with safety check)
            const testDetailsData = data.test || data;
            resultTestDetails = testDetailsData;
            const state = window.currentState || (typeof currentState !== 'undefined' ? currentState : null);

            if (state) {
                state.currentTestDetails = testDetailsData;
            } else {
                console.warn('currentState not available, skipping state storage');
            }

            // If we don't have the full report, try to get it from the API
            if (state && state.currentTestDetails &&
                (!state.currentTestDetails.report || state.currentTestDetails.report === '') &&
                state.currentTestDetails.tsn_id) {

                console.log('Test details loaded but no report found, trying to get full data...');

                // Try to get the complete test report from the recent runs endpoint
                const fullDataUrl = `${config.reportingEndpoint}?limit=1&tsn_id=${state.currentTestDetails.tsn_id}&uid=${encodeURIComponent(credentials.uid)}&password=${encodeURIComponent(credentials.password)}`;

                const fullDataResponse = await fetch(fullDataUrl);
                if (fullDataResponse.ok) {
                    const fullData = await fullDataResponse.json();
                    if (fullData && fullData.length > 0 && fullData[0].report) {
                        console.log('Full test report found in recent runs:', fullData[0]);
                        state.currentTestDetails.report = fullData[0].report;

                        // Copy over any other missing data
                        if (!state.currentTestDetails.test_name && fullData[0].test_name) {
                            state.currentTestDetails.test_name = fullData[0].test_name;
                        }
                        if (!state.currentTestDetails.status && fullData[0].status) {
                            state.currentTestDetails.status = fullData[0].status;
                        }
                    }
                }
            }
        }

        // Return the test details
        return resultTestDetails;
    } catch (error) {
        console.error('Error loading test details from database API:', error);
        throw error;
    }
}

/**
 * Format environment variables for better readability
 * @param {string} envStr - The environment string to format
 * @returns {string} Formatted HTML
 */
function formatEnvironmentVariables(envStr) {
    if (!envStr) return 'N/A';
    
    // Split by <br/> or similar tags if they exist
    const variables = envStr.split(/<br\s*\/?>/i);
    if (variables.length === 1) {
        // Try splitting by newline if no <br/> tags
        const parts = envStr.split(/\r?\n/);
        if (parts.length > 1) {
            return formatEnvironmentArrays(parts);
        }
        
        // Try splitting by spaces if it's a single line
        const spaceParts = envStr.split(/\s+/);
        if (spaceParts.length > 1) {
            return formatEnvironmentArrays(spaceParts);
        }
        
        return envStr; // Return as is if we can't parse it
    }
    
    return formatEnvironmentArrays(variables);
}

/**
 * Format environment variables from array of strings
 * @param {string[]} variables - Array of environment variable strings
 * @returns {string} Formatted HTML
 */
function formatEnvironmentArrays(variables) {
    // Important variables to show first
    const priorityVars = ['envir', 'rgs_env', 'shell_host', 'dataCenter', 'environment'];
    const importantVars = [];
    const otherVars = [];
    
    variables.forEach(varStr => {
        const trimmed = varStr.trim();
        if (!trimmed) return;
        
        // Check if it's a priority variable
        const isPriority = priorityVars.some(pVar => 
            trimmed.startsWith(pVar + '=') || trimmed.startsWith(pVar + ' ='));
        
        if (isPriority) {
            importantVars.push(formatSingleVariable(trimmed));
        } else {
            otherVars.push(formatSingleVariable(trimmed));
        }
    });
    
    // Create a formatted display
    let html = '';
    
    // Add important vars first with highlighting
    if (importantVars.length > 0) {
        html += '<div class="important-env-vars">';
        html += importantVars.join('<br/>');
        html += '</div>';
    }
    
    // Add other vars in a collapsible section if there are many
    if (otherVars.length > 0) {
        if (otherVars.length > 5) {
            html += '<div class="env-vars-toggle"><a href="#" onclick="$(this).next().toggle(); return false;">Show/Hide All Variables (' + otherVars.length + ')</a>';
            html += '<div class="all-env-vars" style="display:none;">' + otherVars.join('<br/>') + '</div></div>';
        } else {
            html += '<div class="all-env-vars">' + otherVars.join('<br/>') + '</div>';
        }
    }
    
    return html;
}

/**
 * Format a single environment variable
 * @param {string} varStr - The variable string (e.g., "key=value")
 * @returns {string} Formatted HTML
 */

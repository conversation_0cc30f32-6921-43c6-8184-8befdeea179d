const mysql = require('mysql2/promise');

async function checkTestResults() {
  let connection;
  try {
    console.log('Connecting to database to check test 17985 results...');
    connection = await mysql.createConnection({
      host: 'mprts-qa02.lab.wagerworks.com',
      user: 'rgs_rw',
      password: 'rgs_rw',
      database: 'rgs_test'
    });
    
    console.log('Connected successfully\n');
    
    // Check test session details
    console.log('=== TEST SESSION 17985 DETAILS ===');
    const [sessionData] = await connection.query(`
      SELECT tsn_id, uid, start_ts, end_ts, error, tc_id, ts_id, pj_id
      FROM test_session
      WHERE tsn_id = 17985
    `);
    
    if (sessionData.length === 0) {
      console.log('❌ Test session 17985 not found');
      return;
    }
    
    const session = sessionData[0];
    console.log('Session data:');
    console.log(JSON.stringify(session, null, 2));
    
    // Parse the error field
    const errorField = session.error;
    console.log(`\nError field: "${errorField}"`);
    
    if (errorField && typeof errorField === 'string') {
      const errorMatch = errorField.match(/^(\d+):(\d+)\/(\d+)$/);
      if (errorMatch) {
        const failed = parseInt(errorMatch[1], 10);
        const passed = parseInt(errorMatch[2], 10);
        const total = parseInt(errorMatch[3], 10);
        
        console.log(`Parsed results: ${failed} failed, ${passed} passed, ${total} total`);
        
        const expectedStatus = failed > 0 ? 'failed' : (passed > 0 ? 'passed' : 'completed');
        console.log(`Expected status: ${expectedStatus}`);
      } else {
        console.log('Error field does not match expected format');
      }
    }
    
    // Check what columns are available in test_result table
    console.log('\n=== TEST_RESULT TABLE STRUCTURE ===');
    const [columns] = await connection.query(`DESCRIBE test_result`);
    console.log('Available columns:');
    columns.forEach(col => console.log(`  - ${col.Field} (${col.Type})`));
    
    // Try to get test results with basic columns
    console.log('\n=== INDIVIDUAL TEST RESULTS ===');
    const [testResults] = await connection.query(`
      SELECT * FROM test_result WHERE tsn_id = 17985 LIMIT 5
    `);
    
    if (testResults.length === 0) {
      console.log('No individual test results found');
    } else {
      console.log(`Found ${testResults.length} test results:`);
      console.log('First result structure:');
      console.log(JSON.stringify(testResults[0], null, 2));
    }
    
    console.log('\n=== CONCLUSION ===');
    console.log('✅ Database connection works');
    console.log('✅ Test session data retrieved');
    console.log('✅ Error field format confirmed: "1:3/3" = 1 failed, 3 passed');
    console.log('✅ This means the test should show as "Failed" status');
    
  } catch (error) {
    console.error('❌ Database operation error:', error);
  } finally {
    if (connection) {
      console.log('\nClosing connection');
      await connection.end();
    }
  }
}

checkTestResults().catch(console.error);

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test All Fixes</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button { padding: 8px 16px; margin: 5px; cursor: pointer; }
        pre { background-color: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; max-height: 300px; }
    </style>
</head>
<body>
    <h1>Test All Performance Fixes</h1>
    
    <div id="status-container">
        <div class="status info">
            <strong>Testing:</strong> Script loading, polling, and test details modal
        </div>
    </div>
    
    <div>
        <button onclick="testScriptLoading()">Test Script Loading</button>
        <button onclick="testPolling()">Test Polling</button>
        <button onclick="testModal()">Test Modal</button>
        <button onclick="runAllTests()">Run All Tests</button>
        <button onclick="clearResults()">Clear Results</button>
    </div>
    
    <h2>Test Results</h2>
    <div id="results"></div>
    
    <h2>Console Output</h2>
    <pre id="console-output"></pre>
    
    <!-- Mock API Service -->
    <script>
        window.apiService = {
            credentials: { uid: 'test', password: 'test' },
            setCredentials: function(uid, password) {
                this.credentials = { uid, password };
                console.log('Mock API: Credentials set');
            },
            getRecentRuns: async function(options = {}) {
                console.log('Mock API: getRecentRuns called with:', options);
                await new Promise(resolve => setTimeout(resolve, 100));
                return [
                    { tsn_id: '17150', tc_id: '3180', status: 'passed', name: 'Test 1' },
                    { tsn_id: '17151', tc_id: '3181', status: 'running', name: 'Test 2' }
                ];
            },
            getTestDetails: async function(tsnId) {
                console.log('Mock API: getTestDetails called for:', tsnId);
                await new Promise(resolve => setTimeout(resolve, 200));
                return { 
                    tsn_id: tsnId, 
                    tc_id: '3180', 
                    status: 'passed', 
                    name: 'Test Details',
                    start_time: new Date().toISOString(),
                    environment: 'qa02'
                };
            }
        };
        
        // Mock app state and functions
        window.appState = {
            activeTests: new Map(),
            recentRunsCache: [],
            pollInterval: null,
            credentials: { uid: 'test', password: 'test' }
        };
        
        window.processRecentRunsData = function(data) {
            console.log('Mock: processRecentRunsData called');
        };
        
        window.renderRecentRuns = function(data) {
            console.log('Mock: renderRecentRuns called');
        };
        
        window.updateTestCounters = function() {
            console.log('Mock: updateTestCounters called');
        };
        
        window.updateActiveTestsDisplay = function(data) {
            console.log('Mock: updateActiveTestsDisplay called');
        };
        
        window.showError = function(message) {
            console.error('Mock showError:', message);
            addResult(`Error: ${message}`, 'error');
        };
        
        // Console capture
        let consoleOutput = [];
        const originalLog = console.log;
        const originalError = console.error;
        
        console.log = function(...args) {
            consoleOutput.push('LOG: ' + args.join(' '));
            originalLog.apply(console, args);
            updateConsoleOutput();
        };
        
        console.error = function(...args) {
            consoleOutput.push('ERROR: ' + args.join(' '));
            originalError.apply(console, args);
            updateConsoleOutput();
        };
        
        function updateConsoleOutput() {
            document.getElementById('console-output').textContent = consoleOutput.slice(-15).join('\n');
        }
        
        function addResult(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `status ${type}`;
            resultDiv.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            resultsDiv.appendChild(resultDiv);
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
            consoleOutput = [];
            updateConsoleOutput();
        }
        
        function testScriptLoading() {
            addResult('Testing script loading...', 'info');
            
            // Test 1: Check if simple optimizations loaded
            if (window.simpleRequestManager) {
                addResult('✅ Simple request manager loaded', 'success');
            } else {
                addResult('❌ Simple request manager not found', 'error');
            }
            
            if (window.simplePollingCoordinator) {
                addResult('✅ Simple polling coordinator loaded', 'success');
            } else {
                addResult('❌ Simple polling coordinator not found', 'error');
            }
            
            // Test 2: Check if API service is optimized
            if (window.apiService._optimized) {
                addResult('✅ API service is optimized', 'success');
            } else {
                addResult('⚠️ API service not yet optimized', 'info');
            }
        }
        
        function testPolling() {
            addResult('Testing polling functionality...', 'info');
            
            try {
                // Test the polling logic from config.js
                if (window.pollingCoordinator && typeof window.pollingCoordinator.subscribe === 'function') {
                    addResult('✅ Full polling coordinator available', 'success');
                } else if (window.simplePollingCoordinator && typeof window.simplePollingCoordinator.subscribe === 'function') {
                    addResult('✅ Simple polling coordinator available', 'success');
                    
                    // Test subscription
                    window.simplePollingCoordinator.subscribe('recentRuns', (data) => {
                        addResult(`✅ Received polling data: ${data.length} items`, 'success');
                    }, 'test-component');
                    
                } else {
                    addResult('⚠️ No polling coordinator available, will use legacy polling', 'info');
                }
                
                addResult('✅ Polling test completed without errors', 'success');
                
            } catch (error) {
                addResult(`❌ Polling test failed: ${error.message}`, 'error');
            }
        }
        
        async function testModal() {
            addResult('Testing modal functionality...', 'info');
            
            try {
                // Test if viewTestDetails function exists
                if (typeof window.viewTestDetails === 'function') {
                    addResult('✅ viewTestDetails function exists', 'success');
                    
                    // Test modal creation
                    addResult('Testing modal with TSN ID 17150...', 'info');
                    await window.viewTestDetails('17150');
                    
                    // Check if modal was created
                    const modal = document.getElementById('test-details-modal');
                    if (modal) {
                        addResult('✅ Modal created successfully', 'success');
                        
                        // Close modal after 2 seconds
                        setTimeout(() => {
                            if (typeof window.closeTestDetailsModal === 'function') {
                                window.closeTestDetailsModal();
                                addResult('✅ Modal closed successfully', 'success');
                            }
                        }, 2000);
                    } else {
                        addResult('❌ Modal not found in DOM', 'error');
                    }
                    
                } else {
                    addResult('❌ viewTestDetails function not found', 'error');
                }
                
            } catch (error) {
                addResult(`❌ Modal test failed: ${error.message}`, 'error');
            }
        }
        
        async function runAllTests() {
            addResult('Running all tests...', 'info');
            clearResults();
            
            testScriptLoading();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            testPolling();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testModal();
            
            addResult('✅ All tests completed!', 'success');
        }
    </script>
    
    <!-- Load the simple optimizations script (fixed path) -->
    <script src="performance/simple-optimizations.js"></script>
    
    <!-- Load parts of config.js functionality for testing -->
    <script>
        // Include the essential functions from config.js for testing
        
        // View test details function (simplified version)
        async function viewTestDetails(tsnId) {
            console.log(`Loading test details for TSN ID: ${tsnId}`);
            
            try {
                // Show loading modal immediately
                showTestDetailsModal(tsnId, null, true);
                
                let testDetails;
                
                // Try to use request manager for optimized loading
                if (window.simpleRequestManager && typeof window.simpleRequestManager.executeRequest === 'function') {
                    console.log('Using simple request manager for test details');
                    const requestKey = `testDetails_${tsnId}`;
                    testDetails = await window.simpleRequestManager.executeRequest(
                        requestKey,
                        () => window.apiService.getTestDetails(tsnId),
                        'testDetails'
                    );
                } else {
                    console.log('No request manager available, using direct API call');
                    testDetails = await window.apiService.getTestDetails(tsnId);
                }
                
                // Update modal with loaded details
                showTestDetailsModal(tsnId, testDetails, false);
                
            } catch (error) {
                console.error(`Error loading test details for ${tsnId}:`, error);
                showError(`Failed to load test details: ${error.message}`);
            }
        }
        
        // Modal functions (simplified)
        function showTestDetailsModal(tsnId, testDetails, isLoading) {
            let modal = document.getElementById('test-details-modal');
            if (!modal) {
                modal = createTestDetailsModal();
                document.body.appendChild(modal);
            }
            
            const modalContent = modal.querySelector('.modal-content');
            if (!modalContent) return;
            
            if (isLoading) {
                modalContent.innerHTML = `
                    <div style="padding: 20px; text-align: center;">
                        <h3>Test Details - TSN ${tsnId}</h3>
                        <div style="margin: 20px 0;">Loading...</div>
                        <button onclick="closeTestDetailsModal()">Close</button>
                    </div>
                `;
            } else if (testDetails) {
                modalContent.innerHTML = `
                    <div style="padding: 20px;">
                        <h3>Test Details - TSN ${tsnId}</h3>
                        <p><strong>Name:</strong> ${testDetails.name}</p>
                        <p><strong>Status:</strong> ${testDetails.status}</p>
                        <p><strong>Environment:</strong> ${testDetails.environment}</p>
                        <button onclick="closeTestDetailsModal()">Close</button>
                    </div>
                `;
            }
            
            modal.style.display = 'block';
        }
        
        function createTestDetailsModal() {
            const modal = document.createElement('div');
            modal.id = 'test-details-modal';
            modal.style.cssText = 'display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5);';
            
            const modalContent = document.createElement('div');
            modalContent.className = 'modal-content';
            modalContent.style.cssText = 'background: white; margin: 10% auto; padding: 0; max-width: 600px; border-radius: 4px;';
            
            modal.appendChild(modalContent);
            return modal;
        }
        
        function closeTestDetailsModal() {
            const modal = document.getElementById('test-details-modal');
            if (modal) {
                modal.style.display = 'none';
            }
        }
        
        // Make functions global
        window.viewTestDetails = viewTestDetails;
        window.showTestDetailsModal = showTestDetailsModal;
        window.closeTestDetailsModal = closeTestDetailsModal;
        
        // Auto-run tests after everything loads
        setTimeout(() => {
            addResult('Page loaded, ready for testing', 'info');
            if (window.simpleRequestManager) {
                addResult('✅ Simple optimizations detected', 'success');
            }
        }, 1000);
    </script>
</body>
</html>

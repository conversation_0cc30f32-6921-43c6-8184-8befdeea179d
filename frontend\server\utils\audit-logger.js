/**
 * Audit Logger
 * Comprehensive logging system for authentication events and security incidents
 */

const fs = require('fs');
const path = require('path');

class AuditLogger {
  constructor() {
    this.logDir = path.join(__dirname, '..', 'logs');
    this.auditLogFile = path.join(this.logDir, 'audit.log');
    this.securityLogFile = path.join(this.logDir, 'security.log');
    this.errorLogFile = path.join(this.logDir, 'error.log');
    
    // Ensure log directory exists
    this.ensureLogDirectory();
    
    // Log levels
    this.levels = {
      INFO: 'INFO',
      WARN: 'WARN',
      ERROR: 'ERROR',
      SECURITY: 'SECURITY',
      AUDIT: 'AUDIT'
    };
    
    console.log('✅ Audit Logger initialized');
  }

  /**
   * Ensure log directory exists
   */
  ensureLogDirectory() {
    try {
      if (!fs.existsSync(this.logDir)) {
        fs.mkdirSync(this.logDir, { recursive: true });
        console.log(`Created log directory: ${this.logDir}`);
      }
    } catch (error) {
      console.error('Failed to create log directory:', error);
    }
  }

  /**
   * Format log entry
   * @param {string} level - Log level
   * @param {string} event - Event type
   * @param {Object} data - Event data
   * @returns {string} Formatted log entry
   */
  formatLogEntry(level, event, data) {
    const timestamp = new Date().toISOString();
    const logEntry = {
      timestamp,
      level,
      event,
      ...data
    };
    
    return JSON.stringify(logEntry) + '\n';
  }

  /**
   * Write to log file
   * @param {string} filePath - Log file path
   * @param {string} entry - Log entry
   */
  writeToFile(filePath, entry) {
    try {
      fs.appendFileSync(filePath, entry, 'utf8');
    } catch (error) {
      console.error(`Failed to write to log file ${filePath}:`, error);
    }
  }

  /**
   * Log authentication success
   * @param {Object} data - Authentication data
   */
  logAuthSuccess(data) {
    const entry = this.formatLogEntry(this.levels.AUDIT, 'AUTH_SUCCESS', {
      uid: data.uid,
      ip: data.ip,
      userAgent: data.userAgent,
      sessionId: data.sessionId,
      role: data.role,
      permissions: data.permissions
    });
    
    this.writeToFile(this.auditLogFile, entry);
    console.log(`✅ AUTH_SUCCESS: ${data.uid} from ${data.ip}`);
  }

  /**
   * Log authentication failure
   * @param {Object} data - Authentication data
   */
  logAuthFailure(data) {
    const entry = this.formatLogEntry(this.levels.SECURITY, 'AUTH_FAILURE', {
      uid: data.uid,
      ip: data.ip,
      userAgent: data.userAgent,
      reason: data.reason,
      failedAttempts: data.failedAttempts
    });
    
    this.writeToFile(this.securityLogFile, entry);
    console.log(`❌ AUTH_FAILURE: ${data.uid} from ${data.ip} - ${data.reason}`);
  }

  /**
   * Log account lockout
   * @param {Object} data - Lockout data
   */
  logAccountLockout(data) {
    const entry = this.formatLogEntry(this.levels.SECURITY, 'ACCOUNT_LOCKOUT', {
      uid: data.uid,
      ip: data.ip,
      lockoutDuration: data.lockoutDuration,
      failedAttempts: data.failedAttempts,
      lockoutCount: data.lockoutCount
    });
    
    this.writeToFile(this.securityLogFile, entry);
    console.log(`🔒 ACCOUNT_LOCKOUT: ${data.uid} locked for ${data.lockoutDuration}s`);
  }

  /**
   * Log suspicious activity
   * @param {Object} data - Activity data
   */
  logSuspiciousActivity(data) {
    const entry = this.formatLogEntry(this.levels.SECURITY, 'SUSPICIOUS_ACTIVITY', {
      ip: data.ip,
      activityType: data.activityType,
      details: data.details,
      userAgent: data.userAgent,
      attempts: data.attempts
    });
    
    this.writeToFile(this.securityLogFile, entry);
    console.log(`🚨 SUSPICIOUS_ACTIVITY: ${data.activityType} from ${data.ip}`);
  }

  /**
   * Log session events
   * @param {Object} data - Session data
   */
  logSessionEvent(data) {
    const entry = this.formatLogEntry(this.levels.AUDIT, data.event, {
      sessionId: data.sessionId,
      uid: data.uid,
      ip: data.ip,
      details: data.details
    });
    
    this.writeToFile(this.auditLogFile, entry);
    console.log(`📝 ${data.event}: ${data.uid} (${data.sessionId})`);
  }

  /**
   * Log admin actions
   * @param {Object} data - Admin action data
   */
  logAdminAction(data) {
    const entry = this.formatLogEntry(this.levels.AUDIT, 'ADMIN_ACTION', {
      adminUid: data.adminUid,
      action: data.action,
      targetUid: data.targetUid,
      ip: data.ip,
      details: data.details,
      success: data.success
    });
    
    this.writeToFile(this.auditLogFile, entry);
    console.log(`👑 ADMIN_ACTION: ${data.adminUid} performed ${data.action} on ${data.targetUid}`);
  }

  /**
   * Log security configuration changes
   * @param {Object} data - Configuration change data
   */
  logConfigChange(data) {
    const entry = this.formatLogEntry(this.levels.AUDIT, 'CONFIG_CHANGE', {
      adminUid: data.adminUid,
      configType: data.configType,
      changes: data.changes,
      ip: data.ip,
      previousValue: data.previousValue,
      newValue: data.newValue
    });
    
    this.writeToFile(this.auditLogFile, entry);
    console.log(`⚙️ CONFIG_CHANGE: ${data.configType} changed by ${data.adminUid}`);
  }

  /**
   * Log CSRF attacks
   * @param {Object} data - CSRF attack data
   */
  logCSRFAttack(data) {
    const entry = this.formatLogEntry(this.levels.SECURITY, 'CSRF_ATTACK', {
      ip: data.ip,
      userAgent: data.userAgent,
      url: data.url,
      method: data.method,
      referer: data.referer,
      uid: data.uid
    });
    
    this.writeToFile(this.securityLogFile, entry);
    console.log(`🛡️ CSRF_ATTACK: Blocked request from ${data.ip} to ${data.url}`);
  }

  /**
   * Log rate limit violations
   * @param {Object} data - Rate limit data
   */
  logRateLimitViolation(data) {
    const entry = this.formatLogEntry(this.levels.SECURITY, 'RATE_LIMIT_VIOLATION', {
      ip: data.ip,
      endpoint: data.endpoint,
      attempts: data.attempts,
      windowMs: data.windowMs,
      userAgent: data.userAgent
    });
    
    this.writeToFile(this.securityLogFile, entry);
    console.log(`⚡ RATE_LIMIT_VIOLATION: ${data.ip} exceeded limit on ${data.endpoint}`);
  }

  /**
   * Log permission violations
   * @param {Object} data - Permission violation data
   */
  logPermissionViolation(data) {
    const entry = this.formatLogEntry(this.levels.SECURITY, 'PERMISSION_VIOLATION', {
      uid: data.uid,
      ip: data.ip,
      requiredPermission: data.requiredPermission,
      userPermissions: data.userPermissions,
      endpoint: data.endpoint,
      method: data.method
    });
    
    this.writeToFile(this.securityLogFile, entry);
    console.log(`🚫 PERMISSION_VIOLATION: ${data.uid} lacks ${data.requiredPermission} for ${data.endpoint}`);
  }

  /**
   * Log system errors
   * @param {Object} data - Error data
   */
  logError(data) {
    const entry = this.formatLogEntry(this.levels.ERROR, 'SYSTEM_ERROR', {
      error: data.error,
      stack: data.stack,
      context: data.context,
      uid: data.uid,
      ip: data.ip,
      timestamp: data.timestamp
    });
    
    this.writeToFile(this.errorLogFile, entry);
    console.error(`💥 SYSTEM_ERROR: ${data.error} in ${data.context}`);
  }

  /**
   * Get audit logs for a specific time range
   * @param {Date} startDate - Start date
   * @param {Date} endDate - End date
   * @param {string} logType - Log type (audit, security, error)
   * @returns {Array} Array of log entries
   */
  getLogs(startDate, endDate, logType = 'audit') {
    try {
      const logFile = logType === 'security' ? this.securityLogFile : 
                     logType === 'error' ? this.errorLogFile : this.auditLogFile;
      
      if (!fs.existsSync(logFile)) {
        return [];
      }

      const logContent = fs.readFileSync(logFile, 'utf8');
      const lines = logContent.split('\n').filter(line => line.trim());
      
      const logs = lines.map(line => {
        try {
          return JSON.parse(line);
        } catch (error) {
          return null;
        }
      }).filter(log => log !== null);

      // Filter by date range if provided
      if (startDate || endDate) {
        return logs.filter(log => {
          const logDate = new Date(log.timestamp);
          if (startDate && logDate < startDate) return false;
          if (endDate && logDate > endDate) return false;
          return true;
        });
      }

      return logs;
    } catch (error) {
      console.error('Error reading logs:', error);
      return [];
    }
  }

  /**
   * Get security statistics
   * @param {Date} startDate - Start date
   * @param {Date} endDate - End date
   * @returns {Object} Security statistics
   */
  getSecurityStats(startDate, endDate) {
    const securityLogs = this.getLogs(startDate, endDate, 'security');
    
    const stats = {
      totalEvents: securityLogs.length,
      authFailures: 0,
      accountLockouts: 0,
      suspiciousActivities: 0,
      csrfAttacks: 0,
      rateLimitViolations: 0,
      permissionViolations: 0,
      uniqueIPs: new Set(),
      topFailedUsers: {},
      topSuspiciousIPs: {}
    };

    securityLogs.forEach(log => {
      switch (log.event) {
        case 'AUTH_FAILURE':
          stats.authFailures++;
          stats.topFailedUsers[log.uid] = (stats.topFailedUsers[log.uid] || 0) + 1;
          break;
        case 'ACCOUNT_LOCKOUT':
          stats.accountLockouts++;
          break;
        case 'SUSPICIOUS_ACTIVITY':
          stats.suspiciousActivities++;
          stats.topSuspiciousIPs[log.ip] = (stats.topSuspiciousIPs[log.ip] || 0) + 1;
          break;
        case 'CSRF_ATTACK':
          stats.csrfAttacks++;
          break;
        case 'RATE_LIMIT_VIOLATION':
          stats.rateLimitViolations++;
          break;
        case 'PERMISSION_VIOLATION':
          stats.permissionViolations++;
          break;
      }
      
      if (log.ip) {
        stats.uniqueIPs.add(log.ip);
      }
    });

    stats.uniqueIPs = stats.uniqueIPs.size;
    
    return stats;
  }

  /**
   * Rotate log files (for production use)
   * @param {number} maxSizeMB - Maximum file size in MB
   */
  rotateLogs(maxSizeMB = 10) {
    const maxSizeBytes = maxSizeMB * 1024 * 1024;
    
    [this.auditLogFile, this.securityLogFile, this.errorLogFile].forEach(logFile => {
      try {
        if (fs.existsSync(logFile)) {
          const stats = fs.statSync(logFile);
          if (stats.size > maxSizeBytes) {
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const rotatedFile = `${logFile}.${timestamp}`;
            fs.renameSync(logFile, rotatedFile);
            console.log(`📁 Log rotated: ${logFile} -> ${rotatedFile}`);
          }
        }
      } catch (error) {
        console.error(`Error rotating log ${logFile}:`, error);
      }
    });
  }
}

// Export singleton instance
module.exports = new AuditLogger();

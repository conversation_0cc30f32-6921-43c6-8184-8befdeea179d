# Test External CaseRunner API with All Configuration Parameters
# This PowerShell script tests the external API with all 12 configuration parameters
# to verify that the enhanced parameter support will work

Write-Host "Testing External CaseRunner API with All Configuration Parameters" -ForegroundColor Green
Write-Host "=================================================================" -ForegroundColor Green

# Configuration - REPLACE THESE VALUES WITH YOUR ACTUAL CREDENTIALS
$TEST_CASE_ID = "3180"  # Replace with a valid test case ID
$USERNAME = "<EMAIL>"  # Replace with your username
$PASSWORD = "test"  # Replace with your password
$ENVIRONMENT = "qa02"  # Environment to test

Write-Host "Test Case ID: $TEST_CASE_ID"
Write-Host "Username: $USERNAME"
Write-Host "Environment: $ENVIRONMENT"
Write-Host ""

# Create the request body with all parameters
$body = @{
    uid = $USERNAME
    password = $PASSWORD
    tc_id = $TEST_CASE_ID
    envir = $ENVIRONMENT
    shell_host = "jps-qa10-app01"
    file_path = "/home/<USER>/"
    operatorConfigs = "operatorNameConfigs"
    kafka_server = "kafka-qa-a0.lab.wagerworks.com"
    dataCenter = "GU"
    rgs_env = $ENVIRONMENT
    old_version = "0"
    networkType1 = "multi-site"
    networkType2 = "multi-site"
    sign = "-"
    rate_src = "local"
}

Write-Host "Executing API request..." -ForegroundColor Yellow
Write-Host ""

try {
    # Make the API request
    $response = Invoke-RestMethod -Uri "http://mprts-qa02.lab.wagerworks.com:5080/AutoRun/CaseRunner" `
                                  -Method POST `
                                  -Body $body `
                                  -ContentType "application/x-www-form-urlencoded" `
                                  -Verbose

    Write-Host "SUCCESS!" -ForegroundColor Green
    Write-Host "Response:" -ForegroundColor Green
    Write-Host $response
    
    # Try to extract TSN ID
    if ($response -match "Your test session id: (\d+)") {
        $tsnId = $matches[1]
        Write-Host ""
        Write-Host "✅ Test Session ID extracted: $tsnId" -ForegroundColor Green
        Write-Host "✅ External API accepts all configuration parameters!" -ForegroundColor Green
    }
}
catch {
    Write-Host "ERROR!" -ForegroundColor Red
    Write-Host "Error details: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "Possible issues:" -ForegroundColor Yellow
    Write-Host "- Invalid credentials (check USERNAME and PASSWORD)" -ForegroundColor Yellow
    Write-Host "- Invalid test case ID (check TEST_CASE_ID)" -ForegroundColor Yellow
    Write-Host "- Network connectivity issues" -ForegroundColor Yellow
    Write-Host "- External API server issues" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "=================================================================" -ForegroundColor Green
Write-Host "Parameters Used (from app-config.js DEFAULT_PARAMS):" -ForegroundColor Cyan
Write-Host "1. envir: $ENVIRONMENT (currently configurable)" -ForegroundColor White
Write-Host "2. shell_host: jps-qa10-app01 (currently configurable)" -ForegroundColor White
Write-Host "3. file_path: /home/<USER>/ (NEW)" -ForegroundColor Yellow
Write-Host "4. operatorConfigs: operatorNameConfigs (NEW)" -ForegroundColor Yellow
Write-Host "5. kafka_server: kafka-qa-a0.lab.wagerworks.com (NEW)" -ForegroundColor Yellow
Write-Host "6. dataCenter: GU (NEW)" -ForegroundColor Yellow
Write-Host "7. rgs_env: $ENVIRONMENT (NEW - matches envir)" -ForegroundColor Yellow
Write-Host "8. old_version: 0 (NEW)" -ForegroundColor Yellow
Write-Host "9. networkType1: multi-site (NEW)" -ForegroundColor Yellow
Write-Host "10. networkType2: multi-site (NEW)" -ForegroundColor Yellow
Write-Host "11. sign: - (NEW)" -ForegroundColor Yellow
Write-Host "12. rate_src: local (NEW)" -ForegroundColor Yellow
Write-Host ""
Write-Host "If this test succeeds, it confirms that the external API supports" -ForegroundColor Cyan
Write-Host "all the additional parameters we want to expose in the UI." -ForegroundColor Cyan

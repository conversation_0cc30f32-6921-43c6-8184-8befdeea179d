/**
 * Test Setup
 * Global test configuration and utilities
 */

// Set test environment
process.env.NODE_ENV = 'test';

// Mock console methods to reduce noise during tests
const originalConsoleLog = console.log;
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;

// Only show console output if TEST_VERBOSE is set
if (!process.env.TEST_VERBOSE) {
  console.log = jest.fn();
  console.error = jest.fn();
  console.warn = jest.fn();
}

// Global test utilities
global.testUtils = {
  // Restore console for debugging
  enableConsole: () => {
    console.log = originalConsoleLog;
    console.error = originalConsoleError;
    console.warn = originalConsoleWarn;
  },
  
  // Disable console
  disableConsole: () => {
    console.log = jest.fn();
    console.error = jest.fn();
    console.warn = jest.fn();
  },
  
  // Generate test user data
  generateTestUser: (overrides = {}) => ({
    uid: '<EMAIL>',
    password: 'TestPassword123!',
    role: 'tester',
    name: 'Test User',
    active: true,
    ...overrides
  }),
  
  // Generate invalid test data
  generateInvalidData: () => ({
    sqlInjection: "'; DROP TABLE users; --",
    xssAttempt: '<script>alert("xss")</script>',
    nullBytes: 'test\x00data',
    controlChars: 'test\r\ndata',
    oversized: 'a'.repeat(10000),
    emptyString: '',
    nullValue: null,
    undefinedValue: undefined
  }),
  
  // Wait for async operations
  wait: (ms = 100) => new Promise(resolve => setTimeout(resolve, ms)),
  
  // Mock request object
  mockRequest: (overrides = {}) => ({
    ip: '127.0.0.1',
    headers: {
      'user-agent': 'Test-Agent/1.0',
      'x-forwarded-for': '127.0.0.1'
    },
    connection: {
      remoteAddress: '127.0.0.1'
    },
    originalUrl: '/test',
    method: 'GET',
    body: {},
    query: {},
    params: {},
    cookies: {},
    ...overrides
  }),
  
  // Mock response object
  mockResponse: () => {
    const res = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
      send: jest.fn().mockReturnThis(),
      setHeader: jest.fn().mockReturnThis(),
      getHeader: jest.fn(),
      clearCookie: jest.fn().mockReturnThis(),
      cookie: jest.fn().mockReturnThis(),
      removeHeader: jest.fn().mockReturnThis()
    };
    return res;
  },
  
  // Mock next function
  mockNext: () => jest.fn()
};

// Global test constants
global.testConstants = {
  VALID_EMAIL: '<EMAIL>',
  INVALID_EMAIL: 'not-an-email',
  VALID_PASSWORD: 'TestPassword123!',
  WEAK_PASSWORD: '123',
  VALID_ROLE: 'tester',
  INVALID_ROLE: 'invalid-role',
  TEST_IP: '127.0.0.1',
  TEST_USER_AGENT: 'Test-Agent/1.0'
};

// Setup and teardown
beforeEach(() => {
  // Clear all mocks before each test
  jest.clearAllMocks();
});

afterEach(() => {
  // Clean up any test artifacts
  jest.restoreAllMocks();
});

// Global error handler for unhandled promises
process.on('unhandledRejection', (reason, promise) => {
  if (process.env.TEST_VERBOSE) {
    console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  }
});

// Increase timeout for integration tests
jest.setTimeout(10000);

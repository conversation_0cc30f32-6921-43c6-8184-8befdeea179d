@echo off
echo ===== Database Connection Tool =====
echo.
echo This batch file will launch the PowerShell database connection script.
echo.

REM Check if PowerShell exists
where powershell >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo PowerShell is not installed or not in your PATH.
    echo Please install PowerShell to use this tool.
    pause
    exit /b 1
)

echo Launching PowerShell script...
echo.

REM Run the PowerShell script
powershell -ExecutionPolicy Bypass -File "%~dp0db_connection.ps1"

echo.
echo Exiting Database Connection Tool.
pause 
/**
 * Tests for path utilities used in the database module
 */

const path = require('path');
const fs = require('fs');
const db = require('../db');

// Extract the resolvePath function for testing
const { resolvePath } = db;

describe('Path Utilities', () => {
  // Store original environment
  const originalEnv = process.env;
  
  beforeEach(() => {
    // Reset environment variables before each test
    process.env = { ...originalEnv };
    
    // Reset fs mock
    if (fs.existsSync.mockReset) {
      fs.existsSync.mockReset();
      fs.existsSync.mockReturnValue(true);
    }
  });
  
  afterEach(() => {
    // Restore original environment after each test
    process.env = originalEnv;
  });
  
  test('should handle absolute paths correctly', () => {
    // Test with Windows-style absolute path
    const winPath = 'C:\\Users\\<USER>\\.ssh\\id_rsa';
    expect(resolvePath(winPath)).toBe(winPath);
    
    // Test with Unix-style absolute path
    const unixPath = '/home/<USER>/.ssh/id_rsa';
    expect(resolvePath(unixPath)).toBe(unixPath);
  });
  
  test('should resolve relative paths to absolute paths', () => {
    // Get the current working directory
    const cwd = process.cwd();
    
    // Test with relative path
    const relativePath = '.ssh/id_rsa';
    expect(resolvePath(relativePath)).toBe(path.resolve(cwd, relativePath));
    
    // Test with path containing parent directory reference
    const parentDirPath = '../.ssh/id_rsa';
    expect(resolvePath(parentDirPath)).toBe(path.resolve(cwd, parentDirPath));
  });
  
  test('should expand tilde to home directory on Unix systems', () => {
    // Mock home directory environment variables
    delete process.env.USERPROFILE;
    process.env.HOME = '/home/<USER>';
    
    // Test with tilde path
    const tildePathWithSlash = '~/.ssh/id_rsa';
    const expandedPath = path.join('/home/<USER>', '.ssh/id_rsa');
    
    expect(resolvePath(tildePathWithSlash)).toBe(expandedPath);
  });
  
  test('should expand tilde to home directory on Windows systems', () => {
    // Mock home directory environment variables
    process.env.USERPROFILE = 'C:\\Users\\<USER>\\Users\\testuser', '.ssh/id_rsa');
    
    expect(resolvePath(tildePathWithSlash)).toBe(expandedPath);
  });
  
  test('should handle empty or null paths', () => {
    // Test with empty path - should resolve to current working directory
    expect(resolvePath('')).toBe(path.resolve(process.cwd(), ''));
    
    // Test with null path - should not throw error and return reasonable default
    expect(() => resolvePath(null)).not.toThrow();
    expect(resolvePath(null)).toBe(path.resolve(process.cwd(), ''));
  });
  
  test('should handle paths with environment variables in values', () => {
    // Set test environment variable
    process.env.SSH_PATH = '/custom/ssh/path';
    
    // Test with environment variable in path
    const sshKeyPath = process.env.SSH_PATH + '/id_rsa';
    
    // Should maintain absolute path 
    expect(resolvePath(sshKeyPath)).toBe(sshKeyPath);
  });
}); 
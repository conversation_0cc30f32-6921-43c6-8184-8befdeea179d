{"users": [{"uid": "<EMAIL>", "password": "$2b$12$TVTKhfcJw9zcKwMPBgpa3eoefch0ZK5siYcgsdr/q2tq5RmbDn9sa", "role": "admin", "name": "<PERSON>", "active": true, "createdAt": "2024-01-01T00:00:00Z", "lastLogin": null}, {"uid": "<EMAIL>", "password": "test", "role": "tester", "name": "<PERSON>", "active": true, "created": "2024-01-01T00:00:00Z"}, {"uid": "Artur<PERSON>@IGT.com", "password": "test", "role": "tester", "name": "<PERSON><PERSON>", "active": true, "created": "2024-01-01T00:00:00Z"}, {"uid": "<PERSON><PERSON>@IGT.com", "password": "test", "role": "tester", "name": "<PERSON>", "active": true, "created": "2024-01-01T00:00:00Z"}, {"uid": "<EMAIL>", "password": "test", "role": "admin", "name": "<PERSON><PERSON><PERSON>", "active": true, "created": "2024-01-01T00:00:00Z"}, {"uid": "<EMAIL>", "password": "$2b$12$TVTKhfcJw9zcKwMPBgpa3eoefch0ZK5siYcgsdr/q2tq5RmbDn9sa", "role": "admin", "name": "Test User", "active": true, "createdAt": "2025-07-22T07:34:42.079Z", "lastLogin": null}], "roles": {"admin": {"permissions": ["read", "write", "delete", "manage_users", "view_logs"], "description": "Full access to all features"}, "tester": {"permissions": ["read", "write"], "description": "Can run tests and view reports"}, "viewer": {"permissions": ["read"], "description": "Read-only access to reports"}}, "config": {"version": "1.0", "lastUpdated": "2024-01-01T00:00:00Z", "passwordPolicy": {"minLength": 4, "requireSpecialChars": false, "maxAge": 0}, "sessionTimeout": 3600}, "lastModified": "2025-07-22T07:34:55.066Z"}
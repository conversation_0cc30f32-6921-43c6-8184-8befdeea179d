/**
 * Database Configuration Module
 * 
 * Loads and manages environment-specific configuration
 */
const environments = require('./environments');
const path = require('path');

// Environment variables are loaded by api.js before this module is required
// No need to load them again here

/**
 * Get configuration for an environment
 * @param {string} environment - Environment name (qa01, qa02, qa03)
 * @returns {Object} - Environment configuration
 */
function getConfig(environment) {
  // If no environment specified, try to detect from environment variables
  if (!environment) {
    environment = detectEnvironment();
  }
  
  // If still no environment, use default
  if (!environment || !environments[environment]) {
    console.log(`No valid environment specified, using default environment: qa02`);
    environment = 'qa02';
  }
  
  const config = { ...environments[environment] }; // Create a copy to avoid modifying the original

  // Override with environment variables if they exist
  Object.keys(config).forEach(key => {
    if (process.env[key]) {
      config[key] = process.env[key];
    }
  });

  // Set SSH_USER from environment variable if not already set
  if (!config.SSH_USER && process.env.SSH_USER) {
    config.SSH_USER = process.env.SSH_USER;
    console.log(`Set SSH_USER from environment: ${config.SSH_USER}`);
  } else {
    console.log(`SSH_USER not set. config.SSH_USER: ${config.SSH_USER}, process.env.SSH_USER: ${process.env.SSH_USER}`);
  }

  // Set SSH_KEY_PATH from environment variable or use default if not already set
  if (!config.SSH_KEY_PATH || config.SSH_KEY_PATH === null) {
    if (process.env.SSH_KEY_PATH) {
      // Handle Windows environment variable expansion
      let keyPath = process.env.SSH_KEY_PATH;
      console.log(`Original SSH_KEY_PATH from env: ${keyPath}`);
      if (keyPath.includes('%USERPROFILE%')) {
        keyPath = keyPath.replace('%USERPROFILE%', process.env.USERPROFILE || '');
        console.log(`Expanded SSH_KEY_PATH: ${keyPath}`);
      }
      config.SSH_KEY_PATH = keyPath;
    } else {
      // Use default SSH key path
      const homeDir = process.env.USERPROFILE || process.env.HOME || require('os').homedir();
      config.SSH_KEY_PATH = path.join(homeDir, '.ssh', 'id_rsa_dbserver');
    }
    console.log(`Final SSH_KEY_PATH: ${config.SSH_KEY_PATH}`);
  } else if (config.SSH_KEY_PATH && config.SSH_KEY_PATH.includes('%USERPROFILE%')) {
    // Handle case where SSH_KEY_PATH is already set but contains unexpanded variables
    console.log(`SSH_KEY_PATH already set but needs expansion: ${config.SSH_KEY_PATH}`);
    config.SSH_KEY_PATH = config.SSH_KEY_PATH.replace('%USERPROFILE%', process.env.USERPROFILE || '');
    console.log(`Expanded existing SSH_KEY_PATH: ${config.SSH_KEY_PATH}`);
  }

  return config;
}

/**
 * Get current environment configuration based on environment variables
 * @returns {Object} - Current environment configuration
 */
function getCurrentConfig() {
  const environment = detectEnvironment();
  return getConfig(environment);
}

/**
 * Detect current environment from environment variables
 * @returns {string|null} - Detected environment name
 */
function detectEnvironment() {
  // Try to detect from DB_HOST
  if (process.env.DB_HOST) {
    const dbHost = process.env.DB_HOST.toLowerCase();
    
    for (const [env, config] of Object.entries(environments)) {
      if (dbHost.includes(env)) {
        return env;
      }
    }
  }
  
  // Try to detect from explicit environment variable
  if (process.env.DB_ENVIRONMENT) {
    return process.env.DB_ENVIRONMENT;
  }
  
  return null;
}

/**
 * Set environment variables for the specified environment
 * @param {string} environment - Environment name (qa01, qa02, qa03)
 * @returns {Object} - The environment configuration that was applied
 */
function setEnvironment(environment) {
  if (!environments[environment]) {
    throw new Error(`Unknown environment: ${environment}. Available environments: ${Object.keys(environments).join(', ')}`);
  }
  
  const config = environments[environment];
  
  // Apply environment variables
  Object.entries(config).forEach(([key, value]) => {
    process.env[key] = String(value);
  });
  
  console.log(`Environment set to ${environment}`);
  
  return config;
}

module.exports = {
  getConfig,
  getCurrentConfig,
  detectEnvironment,
  setEnvironment,
  environments
};

/**
 * Global Test Teardown for Unified SmartTest Architecture
 * 
 * This file handles cleanup after all tests have completed:
 * - Close database connections
 * - Clean up temporary files
 * - Generate final reports
 * - Restore environment state
 */

const fs = require('fs');
const path = require('path');

module.exports = async () => {
  console.log('🧹 Starting global test teardown...');
  
  try {
    // Clean up temporary test files
    const tempDir = path.join(__dirname, 'temp');
    if (fs.existsSync(tempDir)) {
      fs.rmSync(tempDir, { recursive: true, force: true });
      console.log('✅ Cleaned up temporary test files');
    }
    
    // Clean up test cache
    const cacheDir = path.join(__dirname, '.jest-cache');
    if (fs.existsSync(cacheDir)) {
      fs.rmSync(cacheDir, { recursive: true, force: true });
      console.log('✅ Cleaned up test cache');
    }
    
    // Close any remaining database connections
    if (global.testDbConnections) {
      for (const connection of global.testDbConnections) {
        try {
          await connection.end();
        } catch (error) {
          console.warn('⚠️ Error closing database connection:', error.message);
        }
      }
      console.log('✅ Closed database connections');
    }
    
    // Close any remaining HTTP servers
    if (global.testServers) {
      for (const server of global.testServers) {
        try {
          await new Promise((resolve) => {
            server.close(resolve);
          });
        } catch (error) {
          console.warn('⚠️ Error closing test server:', error.message);
        }
      }
      console.log('✅ Closed test servers');
    }
    
    // Generate test summary report
    const summaryPath = path.join(__dirname, 'coverage', 'test-summary.json');
    const summary = {
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV,
      testMode: process.env.TEST_MODE,
      totalTestFiles: global.testFileCount || 0,
      completedAt: new Date().toISOString()
    };
    
    // Ensure coverage directory exists
    const coverageDir = path.dirname(summaryPath);
    if (!fs.existsSync(coverageDir)) {
      fs.mkdirSync(coverageDir, { recursive: true });
    }
    
    fs.writeFileSync(summaryPath, JSON.stringify(summary, null, 2));
    console.log('✅ Generated test summary report');
    
    // Restore original environment variables
    if (global.originalEnv) {
      Object.keys(global.originalEnv).forEach(key => {
        if (global.originalEnv[key] === undefined) {
          delete process.env[key];
        } else {
          process.env[key] = global.originalEnv[key];
        }
      });
      console.log('✅ Restored environment variables');
    }
    
    // Final cleanup
    delete global.testDbConnections;
    delete global.testServers;
    delete global.testFileCount;
    delete global.originalEnv;
    
    console.log('🎉 Global test teardown completed successfully');
    
  } catch (error) {
    console.error('❌ Error during global test teardown:', error);
    process.exit(1);
  }
};

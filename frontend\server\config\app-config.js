/**
 * App configuration
 */

// App configuration
const PORT = process.env.PORT || 3000;
const BASE_URL = process.env.BASE_URL || 'http://mprts-qa02.lab.wagerworks.com:5080/AutoRun/';

// Authentication configuration
const AUTH_CONFIG = {
  // Path to allowed users configuration file
  ALLOWED_USERS_FILE: process.env.ALLOWED_USERS_FILE || './config/allowed-users.json',

  // Session configuration
  SESSION_TIMEOUT: parseInt(process.env.SESSION_TIMEOUT) || 3600, // 1 hour
  SESSION_SECRET: process.env.SESSION_SECRET || 'smarttest-default-secret-change-in-production',

  // Development mode settings
  DEVELOPMENT_MODE: process.env.NODE_ENV === 'development',
  SKIP_AUTH: process.env.SKIP_AUTH === 'true' && process.env.NODE_ENV === 'development'
};

// Default test parameters
const DEFAULT_PARAMS = {
  environment: process.env.DEFAULT_ENVIRONMENT || 'qa02',
  shell_host: process.env.DEFAULT_SHELL_HOST || 'jps-qa10-app01',
  file_path: process.env.DEFAULT_FILE_PATH || '/home/<USER>/',
  operatorConfigs: process.env.DEFAULT_OPERATOR_CONFIGS || 'operatorNameConfigs',
  kafka_server: process.env.DEFAULT_KAFKA_SERVER || 'kafka-qa-a0.lab.wagerworks.com',
  dataCenter: process.env.DEFAULT_DATA_CENTER || 'GU',
  rgs_env: process.env.DEFAULT_RGS_ENV || 'qa02',
  old_version: process.env.DEFAULT_OLD_VERSION || '0',
  networkType1: process.env.DEFAULT_NETWORK_TYPE1 || 'multi-site',
  networkType2: process.env.DEFAULT_NETWORK_TYPE2 || 'multi-site',
  sign: process.env.DEFAULT_SIGN || '-',
  rate_src: process.env.DEFAULT_RATE_SRC || 'local'
};

// Helper function to validate IDs
const isValidId = (id) => {
  return id && !isNaN(parseInt(id, 10));
};

module.exports = {
  PORT,
  BASE_URL,
  AUTH_CONFIG,
  DEFAULT_PARAMS,
  isValidId
};

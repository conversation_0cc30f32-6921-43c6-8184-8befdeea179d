/**
 * Performance Optimization Loader
 * Simple script to load all performance optimization modules
 * Include this single script instead of individual modules
 */

(function() {
    'use strict';
    
    console.log('🚀 Loading SmartTest performance optimizations...');
    
    // Base path for performance scripts
    const basePath = './performance/';
    
    // Scripts to load in order
    const scripts = [
        'request-manager.js',
        'polling-coordinator.js', 
        'performance-monitor.js',
        'init-performance.js'
    ];
    
    // CSS files to load
    const stylesheets = [
        'optimized-styles.css'
    ];
    
    let loadedScripts = 0;
    let totalScripts = scripts.length;
    
    /**
     * Load a script dynamically
     */
    function loadScript(src, callback) {
        const script = document.createElement('script');
        script.src = basePath + src;
        script.onload = callback;
        script.onerror = function() {
            console.error(`Failed to load performance script: ${src}`);
            callback();
        };
        document.head.appendChild(script);
    }
    
    /**
     * Load a stylesheet dynamically
     */
    function loadStylesheet(href) {
        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.href = basePath + href;
        link.onerror = function() {
            console.warn(`Failed to load performance stylesheet: ${href}`);
        };
        document.head.appendChild(link);
    }
    
    /**
     * Check if all scripts are loaded and initialize
     */
    function checkComplete() {
        loadedScripts++;
        console.log(`Performance script loaded (${loadedScripts}/${totalScripts})`);
        
        if (loadedScripts >= totalScripts) {
            console.log('✅ All performance scripts loaded');
            
            // Wait a bit for scripts to initialize their global objects
            setTimeout(() => {
                if (typeof window.initializePerformanceOptimizations === 'function') {
                    window.initializePerformanceOptimizations();
                    console.log('✅ Performance optimizations initialized');
                    
                    // Dispatch event to notify other scripts
                    document.dispatchEvent(new CustomEvent('performance-ready', {
                        detail: {
                            requestManager: !!window.requestManager,
                            pollingCoordinator: !!window.pollingCoordinator,
                            performanceMonitor: !!window.performanceMonitor
                        }
                    }));
                } else {
                    console.error('❌ Performance initialization function not available');
                }
            }, 100);
        }
    }
    
    /**
     * Load all performance scripts
     */
    function loadPerformanceScripts() {
        // Load stylesheets first
        stylesheets.forEach(loadStylesheet);
        
        // Load scripts in sequence
        scripts.forEach(script => {
            loadScript(script, checkComplete);
        });
    }
    
    /**
     * Initialize when DOM is ready
     */
    function initialize() {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', loadPerformanceScripts);
        } else {
            loadPerformanceScripts();
        }
    }
    
    // Start loading
    initialize();
    
    // Expose loader status
    window.performanceLoader = {
        isLoaded: function() {
            return loadedScripts >= totalScripts;
        },
        getStatus: function() {
            return {
                loaded: loadedScripts,
                total: totalScripts,
                complete: loadedScripts >= totalScripts,
                modules: {
                    requestManager: !!window.requestManager,
                    pollingCoordinator: !!window.pollingCoordinator,
                    performanceMonitor: !!window.performanceMonitor,
                    initFunction: typeof window.initializePerformanceOptimizations === 'function'
                }
            };
        }
    };
    
})();

# SmartTest Debugging Tools

This folder contains a collection of scripts for testing, debugging, and managing the SmartTest application's database connections, API interactions, and test execution workflows.

## Script Organization

The scripts are organized into the following categories:

### 1. Database Connection Tools

- **db-connector.js** - Unified database interface for connecting to different environments (QA01, QA02, QA03)
- **query_db_ai.ps1** - PowerShell utility for executing SQL queries against the test database via SSH

### 2. Test Execution Tools

- **run_test.js** - Execute a test case or suite through the API with proper formatting
- **monitor_test.js** - Monitor the status and results of a running test
- **test_report.js** - Generate a detailed report for a completed test

### 3. API Testing Tools

- **api_test.js** - Test API endpoints with various parameters
- **api_compare.js** - Compare different API request formats to identify issues

### 4. Environment Verification Tools

- **verify_connection.js** - Verify SSH and database connections to different environments
- **verify_schema.js** - Verify database schema structure

## Database Schema Overview

The SmartTest application uses the following core database tables:

- `test_session` - Contains test session execution details
  - `tsn_id` - Test suite run ID
  - `uid` - User ID of the initiator
  - `start_ts` - Execution start time
  - `end_ts` - Execution end time
  - `tc_id` - Test case ID (if testing a single test case)

- `test_result` - Records test execution results
  - `tc_id` - Test case ID
  - `tsn_id` - Test suite run ID
  - `outcome` - Result status ('P' for pass, 'F' for fail)
  - `cnt` - Counter linking to output

- `output` - Contains detailed test output logs
  - `cnt` - Foreign key linking to test_result
  - `txt` - The actual output text content

## Usage Instructions

### Connecting to a Database

```javascript
// Example using db-connector.js
const dbConnector = require('./db-connector');

// Connect to QA02 environment
await dbConnector.init('qa02');

// Execute a query
const results = await dbConnector.query('SELECT * FROM test_session LIMIT 10');
console.log(results);

// Close the connection
await dbConnector.close();
```

### Running a Test

```javascript
// Example using run_test.js
const runTest = require('./run_test');

// Run test case 3180 on QA02 environment as the specified user
const result = await runTest.executeTest({
  testCaseId: 3180,
  environment: 'qa02',
  username: '<EMAIL>'
});

console.log(`Test started with tsn_id: ${result.tsnId}`);
```

### Monitoring a Test

```javascript
// Example using monitor_test.js
const monitorTest = require('./monitor_test');

// Monitor test with tsn_id 12345 on QA02 environment
await monitorTest.monitorTestRun({
  tsnId: 12345,
  environment: 'qa02',
  intervalSeconds: 5,
  timeoutMinutes: 10
});
```

### Using PowerShell for Quick Queries

```powershell
# Example using query_db_ai.ps1
powershell.exe -ExecutionPolicy Bypass -File .\scripts\query_db_ai.ps1 -Query "SELECT * FROM test_session WHERE uid='<EMAIL>' LIMIT 5"
```

## Troubleshooting

### Common Issues

1. **SSH Connection Failures**
   - Ensure your SSH key is properly set up at `~/.ssh/id_rsa_dbserver`
   - Verify network connectivity to the server

2. **API Connection Issues**
   - Double-check the URL format
   - Ensure parameters are properly formatted (especially the double-ampersand)
   - Verify credentials are correct

3. **Database Query Errors**
   - Ensure you're connecting to the correct environment
   - Verify table and column names
   - Check your query syntax

### Getting Help

For additional assistance, refer to the detailed documentation in the `/documentation/Database/` folder or contact the SmartTest support team. 
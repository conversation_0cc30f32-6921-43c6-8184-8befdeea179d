/**
 * Active Tests Routes
 */
const express = require('express');
const router = express.Router();
const db = require('../database');

// Active Tests endpoint
router.get('/active-tests', async (req, res) => {
  try {
    console.log('GET /local/active-tests');
    // Make a copy of query params to avoid consuming them multiple times
    const params = {...req.query};

    // Log parameters
    console.log('[/local/active-tests] Received parameters:', params);

    // Use the database module to fetch active tests
    const activeTests = await db.getActiveTests(params);

    // Return with success flag and data
    return res.json({
      success: true,
      data: activeTests,
      message: 'Active tests retrieved successfully'
    });
  } catch (error) {
    console.error('Error retrieving active tests:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to retrieve active tests',
      error: error.message
    });
  }
});

module.exports = router;

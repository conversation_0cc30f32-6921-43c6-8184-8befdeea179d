<!-- 
Performance Optimization Scripts Include Template
Add these script tags to your main config HTML file BEFORE loading config.js
-->

<!-- Performance Optimization Scripts - Load in order -->
<script src="./performance/request-manager.js"></script>
<script src="./performance/polling-coordinator.js"></script>
<script src="./performance/performance-monitor.js"></script>
<script src="./performance/init-performance.js"></script>

<!-- Optional: Performance Testing (only for development) -->
<script src="./performance/test-performance.js"></script>

<!-- Load optimized styles -->
<link rel="stylesheet" href="./performance/optimized-styles.css">

<!-- 
Example of complete HTML structure:
-->
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SmartTest Config - Performance Optimized</title>
    
    <!-- Existing styles -->
    <link rel="stylesheet" href="your-existing-styles.css">
    
    <!-- Performance optimized styles -->
    <link rel="stylesheet" href="./performance/optimized-styles.css">
</head>
<body>
    <!-- Your existing HTML content -->
    
    <!-- Performance Scripts - Load BEFORE config.js -->
    <script src="./performance/request-manager.js"></script>
    <script src="./performance/polling-coordinator.js"></script>
    <script src="./performance/performance-monitor.js"></script>
    <script src="./performance/init-performance.js"></script>
    
    <!-- Your existing scripts -->
    <script src="your-api-service.js"></script>
    
    <!-- Main config script - Load AFTER performance scripts -->
    <script src="config.js"></script>
    
    <!-- Optional: Performance testing (development only) -->
    <!-- <script src="./performance/test-performance.js"></script> -->
    
    <script>
        // Optional: Manual performance monitoring controls
        document.addEventListener('DOMContentLoaded', function() {
            // Show performance monitor in development
            if (window.location.search.includes('debug=true')) {
                setTimeout(() => {
                    if (window.performanceMonitor) {
                        window.performanceMonitor.show();
                        console.log('Performance monitor shown (Ctrl+Shift+P to toggle)');
                    }
                }, 2000);
            }
            
            // Add performance test button (development only)
            if (window.location.hostname === 'localhost' && window.PerformanceTest) {
                const testButton = document.createElement('button');
                testButton.textContent = 'Run Performance Tests';
                testButton.style.position = 'fixed';
                testButton.style.top = '10px';
                testButton.style.right = '10px';
                testButton.style.zIndex = '9999';
                testButton.onclick = () => window.PerformanceTest.run();
                document.body.appendChild(testButton);
            }
        });
    </script>
</body>
</html>

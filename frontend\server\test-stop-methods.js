/**
 * Test script to compare different stop test methods
 * Run this to test the difference between cached and fresh login approaches
 */

const fetch = require('node-fetch');

// Configuration
const BASE_URL = 'http://localhost:3000';
const TEST_SESSION_ID = '17714'; // Change this to your test session ID
const ACCESS_TOKEN = 'YOUR_ACCESS_TOKEN_HERE'; // Replace with actual token

/**
 * Make authenticated request to API
 */
async function makeAuthenticatedRequest(endpoint, data) {
  const response = await fetch(`${BASE_URL}${endpoint}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Cookie': `accessToken=${ACCESS_TOKEN}`
    },
    body: JSON.stringify(data)
  });

  const result = await response.json();
  return { status: response.status, result };
}

/**
 * Test different stop methods
 */
async function testStopMethods() {
  console.log('🧪 Testing Different Stop Test Methods');
  console.log('=====================================\n');

  const testData = { tsn_id: TEST_SESSION_ID };

  // Test 1: Fresh Login (Default)
  console.log('1️⃣ Testing FRESH LOGIN method (/api/stop-test)');
  console.log('   - Forces fresh authentication');
  console.log('   - Bypasses cache');
  console.log('   - Should be most reliable\n');
  
  try {
    const { status, result } = await makeAuthenticatedRequest('/api/stop-test', testData);
    console.log(`   Status: ${status}`);
    console.log(`   Result:`, JSON.stringify(result, null, 2));
  } catch (error) {
    console.error(`   Error:`, error.message);
  }

  console.log('\n' + '='.repeat(50) + '\n');

  // Test 2: Cached Login
  console.log('2️⃣ Testing CACHED LOGIN method (/api/stop-test-cached)');
  console.log('   - Uses cached authentication');
  console.log('   - Faster but potentially stale');
  console.log('   - May fail if session expired\n');
  
  try {
    const { status, result } = await makeAuthenticatedRequest('/api/stop-test-cached', testData);
    console.log(`   Status: ${status}`);
    console.log(`   Result:`, JSON.stringify(result, null, 2));
  } catch (error) {
    console.error(`   Error:`, error.message);
  }

  console.log('\n' + '='.repeat(50) + '\n');

  // Test 3: Database Method
  console.log('3️⃣ Testing DATABASE method (/api/stop-test-db)');
  console.log('   - Bypasses external API completely');
  console.log('   - Updates database directly');
  console.log('   - Most reliable fallback\n');
  
  try {
    const { status, result } = await makeAuthenticatedRequest('/api/stop-test-db', testData);
    console.log(`   Status: ${status}`);
    console.log(`   Result:`, JSON.stringify(result, null, 2));
  } catch (error) {
    console.error(`   Error:`, error.message);
  }

  console.log('\n' + '='.repeat(50) + '\n');
  console.log('🏁 Test completed!');
  console.log('\n📊 Analysis:');
  console.log('- If fresh login works but cached fails → Session cache issue');
  console.log('- If both external methods fail but DB works → External API issue');
  console.log('- If all methods fail → Check authentication/permissions');
}

/**
 * Main execution
 */
async function main() {
  if (ACCESS_TOKEN === 'YOUR_ACCESS_TOKEN_HERE') {
    console.error('❌ Please update ACCESS_TOKEN in the script with your actual token');
    console.log('💡 You can get it from browser cookies or login response');
    return;
  }

  if (TEST_SESSION_ID === '17714') {
    console.warn('⚠️  Using default test session ID. Update TEST_SESSION_ID if needed.');
  }

  await testStopMethods();
}

// Run the test
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { testStopMethods, makeAuthenticatedRequest };

# SmartTest Application Setup Script
# This script prepares the SmartTest application environment
# for new users by setting up environment files and SSH keys

# Set script to stop on errors
$ErrorActionPreference = "Stop"

# Display header
Write-Host "=======================================================" -ForegroundColor Cyan
Write-Host "            SmartTest Application Setup                " -ForegroundColor Cyan  
Write-Host "=======================================================" -ForegroundColor Cyan
Write-Host ""

# Verify required tools are installed
function Test-Prerequisites {
    # Check Node.js
    $nodeVersion = & node -v 2>&1
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Node.js is not installed or not in PATH. Please install Node.js before proceeding." -ForegroundColor Red
        Write-Host "Download from: https://nodejs.org/" -ForegroundColor Yellow
        return $false
    }
    Write-Host "Node.js detected: $nodeVersion" -ForegroundColor Green

    # Check npm
    $npmVersion = & npm -v 2>&1
    if ($LASTEXITCODE -ne 0) {
        Write-Host "npm is not installed or not in PATH. Please install npm before proceeding." -ForegroundColor Red
        return $false
    }
    Write-Host "npm detected: $npmVersion" -ForegroundColor Green
    
    return $true
}

# Create environment files from templates
function New-EnvFiles {
    Write-Host "`nSetting up environment files..." -ForegroundColor Cyan

    # Get username for environment configuration
    $sshUser = Read-Host -Prompt "Enter your SSH username (leave blank to use current user)"
    if ([string]::IsNullOrEmpty($sshUser)) {
        $sshUser = $env:USERNAME
    }
    Write-Host "Using SSH username: $sshUser" -ForegroundColor Yellow

    # Define file paths
    $envFiles = @(
        @{
            Sample = ".\frontend\server\.env.sample"
            Target = ".\frontend\server\.env"
        },
        @{
            Sample = ".\frontend\server\.env.01.sample"
            Target = ".\frontend\server\.env.01"
        },
        @{
            Sample = ".\frontend\server\.env.03.sample"
            Target = ".\frontend\server\.env.03"
        }
    )

    # Process each environment file
    foreach ($fileInfo in $envFiles) {
        $samplePath = $fileInfo.Sample
        $targetPath = $fileInfo.Target
        
        if ((Test-Path -Path $targetPath)) {
            Write-Host "$targetPath already exists." -ForegroundColor Yellow
            continue
        }
        
        if (-not (Test-Path -Path $samplePath)) {
            Write-Host "Warning: Sample file not found at $samplePath" -ForegroundColor Red
            continue
        }
        
        # Create from sample and update username
        Write-Host "Creating $targetPath from sample..." -ForegroundColor Yellow
        $content = Get-Content -Path $samplePath -Raw
        $content = $content -replace "SSH_USER=.*", "SSH_USER=$sshUser"
        Set-Content -Path $targetPath -Value $content
        Write-Host "$targetPath created successfully." -ForegroundColor Green
    }
}

# Set up SSH keys if needed
function Initialize-SshKeys {
    Write-Host "`nChecking SSH key setup..." -ForegroundColor Cyan
    
    $sshKeyPath = "$env:USERPROFILE\.ssh\id_rsa_dbserver"
    
    if (-not (Test-Path -Path $sshKeyPath)) {
        Write-Host "SSH key not found at $sshKeyPath" -ForegroundColor Yellow
        
        $setupKeys = Read-Host "Would you like to set up SSH keys now? (y/n)"
        if ($setupKeys -eq "y") {
            $sshSetupScript = ".\utils\fix_ssh_keys.ps1"
            if (Test-Path -Path $sshSetupScript) {
                & $sshSetupScript
            }
            else {
                Write-Host "SSH setup script not found at: $sshSetupScript" -ForegroundColor Red
            }
        }
        else {
            Write-Host "Skipping SSH key setup. You will need to set up SSH keys manually." -ForegroundColor Yellow
        }
    }
    else {
        Write-Host "SSH key found at $sshKeyPath" -ForegroundColor Green
    }
}

# Install npm dependencies
function Install-AppDependencies {
    Write-Host "`nInstalling npm dependencies..." -ForegroundColor Cyan
    
    $originalLocation = Get-Location
    
    try {
        # Install frontend dependencies
        Set-Location -Path ".\frontend"
        Write-Host "Installing frontend dependencies..." -ForegroundColor Yellow
        & npm install
        if ($LASTEXITCODE -ne 0) {
            Write-Host "Error installing frontend dependencies." -ForegroundColor Red
            Set-Location -Path $originalLocation
            return $false
        }
        
        # Install server dependencies
        Set-Location -Path ".\server"
        Write-Host "Installing server dependencies..." -ForegroundColor Yellow
        & npm install
        if ($LASTEXITCODE -ne 0) {
            Write-Host "Error installing server dependencies." -ForegroundColor Red
            Set-Location -Path $originalLocation
            return $false
        }
        
        Write-Host "Dependencies installed successfully." -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "Error installing dependencies: $_" -ForegroundColor Red
        return $false
    }
    finally {
        # Always return to original directory
        Set-Location -Path $originalLocation
    }
}

# Main script execution
try {
    # Check prerequisites
    if (-not (Test-Prerequisites)) {
        exit 1
    }
    
    # Create environment files
    New-EnvFiles
    
    # Set up SSH keys
    Initialize-SshKeys
    
    # Offer to install dependencies
    $installDeps = Read-Host "`nWould you like to install dependencies? (y/n)"
    if ($installDeps -eq "y") {
        $result = Install-AppDependencies
        if (-not $result) {
            Write-Host "There were issues installing dependencies. See errors above." -ForegroundColor Yellow
        }
    }
    else {
        Write-Host "Skipping dependency installation." -ForegroundColor Yellow
    }
    
    # Show completion message
    Write-Host "`nSetup completed successfully!" -ForegroundColor Green
    Write-Host "You can now run the application by:" -ForegroundColor Cyan
    Write-Host "1. Starting the frontend: cd frontend && npm start" -ForegroundColor Yellow
    Write-Host "2. Starting the server: cd frontend/server && node api.js" -ForegroundColor Yellow
}
catch {
    Write-Host "Error during setup: $_" -ForegroundColor Red
    exit 1
}

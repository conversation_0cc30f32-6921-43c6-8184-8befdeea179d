async function triggerTestCaseRerun(tcId, tsnId, uid, password, originalParameters = {}) {
    console.log(`[DEBUG] triggerTestCaseRerun: Received TC_ID: ${tcId}, Parent_TSN_ID: ${tsnId}`);
    console.log('[DEBUG] triggerTestCaseRerun: Received originalParameters:', JSON.stringify(originalParameters, null, 2));

    const formData = new URLSearchParams();

    // Add essential parameters
    formData.append('tc_id', tcId);
    formData.append('uid', uid);
    formData.append('password', password);

    // Parameters to pick from originalParameters, if they exist.
    // Note: 'environment' from originalParameters maps to 'envir' for the API.
    if (originalParameters && typeof originalParameters === 'object') {
        // Explicitly handle 'envir' (maps from 'environment' or 'envir')
        let envirValue = originalParameters.envir || originalParameters.environment;
        if (envirValue !== undefined && envirValue !== null && envirValue !== '') {
            console.log(`[DEBUG] triggerTestCaseRerun: Adding 'envir': "${envirValue}"`);
            formData.append('envir', envirValue);
        } else {
            console.log(`[DEBUG] triggerTestCaseRerun: 'envir' (or 'environment') not found or is empty/undefined in originalParameters.`);
        }

        // Explicitly handle 'shell_host'
        if (originalParameters.shell_host !== undefined && originalParameters.shell_host !== null && originalParameters.shell_host !== '') {
            console.log(`[DEBUG] triggerTestCaseRerun: Adding 'shell_host': "${originalParameters.shell_host}"`);
            formData.append('shell_host', originalParameters.shell_host);
        } else {
            console.log(`[DEBUG] triggerTestCaseRerun: 'shell_host' not found or is empty/undefined in originalParameters.`);
        }

        // Define other relevant keys that might come from originalParameters
        // Exclude 'envir', 'environment', and 'shell_host' as they are handled explicitly
        const otherRelevantKeys = [
            'file_path', 'operatorConfigs', 'kafka_server',
            'dataCenter', 'rgs_env', 'old_version', 'networkType1',
            'networkType2', 'sign', 'rate_src'
            // Add any other keys that should be passed if present and not handled above
        ];

        console.log(`[DEBUG] triggerTestCaseRerun: Processing other relevant keys: ${otherRelevantKeys.join(', ')}`);
        otherRelevantKeys.forEach(key => {
            if (originalParameters.hasOwnProperty(key) && originalParameters[key] !== undefined && originalParameters[key] !== null && originalParameters[key] !== '') {
                console.log(`[DEBUG] triggerTestCaseRerun: Adding other key '${key}': "${originalParameters[key]}"`);
                formData.append(key, originalParameters[key]);
            }
        });
    } else {
        console.log('[DEBUG] triggerTestCaseRerun: originalParameters is null or not an object. Skipping parameter addition from it.');
    }

    // Log the FormData (converted to an object for easier reading)
    const payloadForLogging = {};
    for (const [key, value] of formData.entries()) {
        payloadForLogging[key] = value;
    }
    console.log('[DEBUG] triggerTestCaseRerun: Final payload for /api/case-runner:', JSON.stringify(payloadForLogging, null, 2));

    try {
        const response = await fetch('/api/case-runner', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: formData, // URLSearchParams object is directly usable as body
        });

        if (!response.ok) {
            let errorDetail;
            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
                const errorData = await response.json();
                errorDetail = errorData.message || JSON.stringify(errorData);
            } else {
                errorDetail = await response.text();
            }
            throw new Error(`API request failed with status ${response.status}: ${errorDetail}`);
        }
        // Assuming successful responses from /api/case-runner are JSON as per original code's expectation
        return await response.json();
    } catch (error) {
        console.error('Error in triggerTestCaseRerun:', error.message);
        const errorMessage = error.message || 'An unknown error occurred during test case rerun.';
        // Display error to user more visibly if possible
        if (window.showToast) { // Check if showToast utility is available
            window.showToast(`Error rerunning test: ${errorMessage}`, 'error', { duration: 7000 });
        } else {
            alert(`Error rerunning test: ${errorMessage}`); // Fallback to alert
        }
        return { success: false, message: errorMessage, error: true }; // Add error flag for consistent handling
    }
}

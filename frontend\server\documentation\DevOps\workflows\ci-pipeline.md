# Continuous Integration Pipeline Documentation

This document explains our comprehensive CI pipeline implemented in `.github/workflows/ci.yml` and how it ensures code quality and security for SmartTest.

## 🎯 Pipeline Overview

Our CI pipeline runs on **every push and pull request** to main, develop, and feature branches, providing comprehensive quality assurance through automated testing, security scanning, and build validation.

## 🔄 Workflow Stages

### Stage 1: Test Matrix (Parallel Execution)
**File**: `.github/workflows/ci.yml`
**Trigger**: Push/PR to main, develop, feature/* branches
**Duration**: ~5-8 minutes

```yaml
strategy:
  matrix:
    node-version: [16.x, 18.x, 20.x]
```

**What happens**:
1. **Environment Setup**: Creates Ubuntu runners with Node.js 16, 18, and 20
2. **Dependency Installation**: Runs `npm ci` for clean, reproducible installs
3. **Linting**: Executes `npm run lint` (if present) for code style consistency
4. **Unit Tests**: Runs `npm test` with coverage reporting
5. **Integration Tests**: Executes `npm run test:unified` (if present)
6. **Coverage Upload**: Sends coverage data to Codecov

**Success Criteria**:
- ✅ All tests pass on all Node.js versions
- ✅ Code coverage meets minimum thresholds
- ✅ No linting errors
- ✅ No critical security vulnerabilities

### Stage 2: Security Analysis (Parallel)
**Duration**: ~3-5 minutes

**Components**:

#### **NPM Security Audit**
```bash
npm audit --audit-level high
```
- Scans for known vulnerabilities in dependencies
- Fails pipeline if HIGH or CRITICAL vulnerabilities found
- Generates audit report for review

#### **CodeQL Analysis**
```yaml
- uses: github/codeql-action/init@v3
  with:
    languages: javascript
```
- **Purpose**: Deep static analysis for security vulnerabilities
- **Coverage**: JavaScript/TypeScript code throughout the repository
- **Detection**: SQL injection, XSS, authentication bypass, etc.
- **Reporting**: Results appear in GitHub Security tab

### Stage 3: Build Validation
**Duration**: ~2-3 minutes
**Dependency**: Requires successful test and security stages

**Process**:
1. **Clean Environment**: Fresh Ubuntu runner
2. **Dependency Installation**: `npm ci` for production dependencies
3. **Build Execution**: `npm run build` creates production assets
4. **Artifact Upload**: Stores build files for deployment workflows

**Build Process**:
```bash
npm run build
# Executes:
# - npm run clean (removes old build files)  
# - npm run build:dashboard (copies dashboard files)
# - npm run build:config (copies config files)
# - npm run build:reports (copies report files)
```

## 📊 Quality Gates

### **Required Checks**
Before any code can be merged, the following must pass:

| Check | Requirement | Failure Action |
|-------|-------------|----------------|
| **Unit Tests** | All tests pass on all Node.js versions | ❌ Block merge |
| **Code Coverage** | Minimum coverage threshold met | ❌ Block merge |
| **Security Audit** | No HIGH/CRITICAL vulnerabilities | ❌ Block merge |
| **Build Success** | Production build completes successfully | ❌ Block merge |
| **CodeQL Scan** | No security vulnerabilities detected | ⚠️ Review required |

### **Branch Protection Rules**
Configured in GitHub Settings → Branches:
- ✅ Require status checks to pass before merging
- ✅ Require branches to be up to date before merging
- ✅ Require review from code owners
- ✅ Dismiss stale reviews when new commits are pushed

## 🚨 Handling CI Failures

### **Test Failures**
**Symptoms**: ❌ Red X on PR, "Tests failing" message
**Investigation**:
1. Click on failed workflow in GitHub Actions
2. Expand the failing test job
3. Look for specific test failures in logs
4. Check if failure is environment-specific (Node.js version)

**Common Causes**:
- New code breaks existing functionality
- Environment-specific issues (Node.js version compatibility)
- Flaky tests (timing issues, external dependencies)
- Missing test data or configuration

**Resolution**:
```bash
# Run tests locally to reproduce
npm test

# Run specific test file
npm test -- --testPathPattern=failing-test.js

# Debug with verbose output
npm test -- --verbose

# Check Node.js version compatibility
nvm use 16 && npm test
nvm use 18 && npm test
nvm use 20 && npm test
```

### **Security Failures**
**Symptoms**: 🔒 Security alerts in PR, CodeQL findings

**NPM Audit Issues**:
```bash
# Check local vulnerabilities
npm audit

# Attempt automatic fixes
npm audit fix

# Force fixes (use with caution)
npm audit fix --force

# Update specific package
npm update package-name
```

**CodeQL Issues**:
1. Review finding details in GitHub Security tab
2. Assess severity and impact
3. Implement fix following security best practices
4. Re-run CodeQL to verify fix

### **Build Failures**
**Symptoms**: 🏗️ Build step fails, missing assets

**Common Causes**:
- Missing dependencies in package.json
- Incorrect file paths in build scripts
- Environment-specific build tools missing
- TypeScript compilation errors

**Debugging**:
```bash
# Test build locally
npm run build

# Check build script components
npm run clean
npm run build:dashboard
npm run build:config  
npm run build:reports

# Verify file structure
ls -la frontend/server/public/
```

## 📈 Performance & Optimization

### **Current Performance**
- **Average Pipeline Duration**: 8-12 minutes
- **Node.js Matrix**: 3 parallel jobs (16.x, 18.x, 20.x)
- **Security Scan**: 3-5 minutes
- **Build Time**: 2-3 minutes

### **Optimization Strategies**
1. **Dependency Caching**: npm cache reduces install time
2. **Parallel Execution**: Matrix strategy runs multiple Node.js versions simultaneously
3. **Conditional Steps**: Security scans skip on documentation changes
4. **Artifact Reuse**: Build artifacts shared between workflows

### **Future Improvements**
- **Test Parallelization**: Split test suites for faster execution
- **Incremental Builds**: Only rebuild changed components
- **Advanced Caching**: Cache test results and build outputs
- **Selective Testing**: Run only tests affected by changes

## 🔍 Monitoring & Alerts

### **GitHub Actions Dashboard**
**Location**: Repository → Actions tab
**Monitoring**:
- Workflow success/failure rates
- Average execution times
- Failed job details and logs
- Resource usage patterns

### **Notification Setup**
**Team Notifications**:
- 📧 Email alerts for workflow failures
- 💬 Slack/Discord integration (if configured)
- 📱 GitHub mobile app notifications
- 🔔 Browser notifications for assigned reviews

### **Metrics to Track**
- **Pipeline Success Rate**: Target >95%
- **Average Execution Time**: Target <10 minutes
- **Security Finding Resolution Time**: Target <24 hours
- **Build Artifact Size**: Monitor for unexpected growth

## 🛠️ Troubleshooting Common Issues

### **"npm ci failed" Error**
```bash
# Clear npm cache
npm cache clean --force

# Delete package-lock.json and node_modules
rm -rf package-lock.json node_modules

# Reinstall dependencies
npm install
```

### **"Tests timeout" Error**
```bash
# Increase Jest timeout in package.json
{
  "jest": {
    "testTimeout": 30000
  }
}

# Or in specific test files
jest.setTimeout(30000);
```

### **"CodeQL initialization failed"**
- Usually indicates JavaScript syntax errors
- Review recent changes for syntax issues
- Check for missing semicolons or brackets
- Verify all files are valid JavaScript/TypeScript

### **"Build artifacts missing"**
- Check build script execution order
- Verify source file paths exist
- Ensure copyfiles patterns are correct
- Check for file permission issues

## 📚 Related Documentation

- **[Security Scanning](security-scanning.md)**: Detailed security workflow documentation
- **[Deployment Environments](deployment-environments.md)**: How CI connects to deployments
- **[Troubleshooting](../operations/troubleshooting.md)**: Comprehensive troubleshooting guide
- **[Daily Routines](../operations/daily-routines.md)**: Monitoring CI health daily

---

**Remember**: The CI pipeline is your first line of defense against bugs and security issues. Always investigate and fix failures promptly to maintain code quality and team productivity.

function refreshReports() {
    console.log('Refreshing reports (incremental)...');
    loadReportsData({
        isIncrementalRefresh: true,
        showLoading: true
    });
}

// Get the currently selected time range
function getSelectedTimeRange() {
    return currentState.activeTimeRange || '24h'; // Fallback to 24h if not set
}

// Get the currently selected timezone with security error handling
function getSelectedTimezone() {
    try {
        return localStorage.getItem('selected_timezone') || 'local';
    } catch (e) {
        console.log('Cannot access localStorage for timezone settings due to security restrictions');
        return 'local'; // Default to local timezone if access is restricted
    }
}

// Save the selected timezone to localStorage with security error handling
function saveSelectedTimezone(timezone) {
    try {
        localStorage.setItem('selected_timezone', timezone);
        return true;
    } catch (e) {
        console.log('Cannot save timezone settings to localStorage due to security restrictions');
        return false;
    }
}

/**
 * Formats a duration in total seconds into a human-readable string (e.g., "1d 2h 30m 15s").
 * @param {number} totalSeconds - The total duration in seconds.
 * @returns {string} A human-readable duration string.
 */
function formatDurationForChart(totalSeconds) {
    if (totalSeconds === null || typeof totalSeconds === 'undefined' || isNaN(totalSeconds) || totalSeconds < 0) {
        return 'N/A'; // Handle undefined, null, NaN, or negative values
    }
    if (totalSeconds === 0) {
        return '0s';
    }

    const days = Math.floor(totalSeconds / 86400);
    const hours = Math.floor((totalSeconds % 86400) / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = Math.floor(totalSeconds % 60);

    let parts = [];
    if (days > 0) parts.push(days + 'd');
    if (hours > 0) parts.push(hours + 'h');
    if (minutes > 0) parts.push(minutes + 'm');
    // Always show seconds if it's the only unit or if it's non-zero and other units are present,
    // or if totalSeconds is less than 60
    if (seconds > 0 || parts.length === 0 || totalSeconds < 60) {
        parts.push(seconds + 's');
    }
    
    return parts.length > 0 ? parts.join(' ') : '0s'; // Ensure '0s' if all parts were zero but totalSeconds wasn't (e.g. 0.5s)
}

// Initialize charts
function initializeCharts() {
    console.log('Initializing charts...');

    // Get the chart canvases
    const successRateCanvas = elements.successRateChart;
    const durationTrendCanvas = elements.durationTrendChart;

    if (!successRateCanvas || !durationTrendCanvas) {
        console.error('Chart canvases not found');
        return;
    }

    // Destroy existing charts if they exist to prevent errors
    if (successRateChart) {
        successRateChart.destroy();
        successRateChart = null;
    }

    if (durationTrendChart) {
        durationTrendChart.destroy();
        durationTrendChart = null;
    }

    // Create success rate chart if the element exists
    if (elements.successRateChart) {
        successRateChart = new Chart(elements.successRateChart.getContext('2d'), {
            type: 'doughnut',
            data: {
                labels: ['Passed', 'Failed', 'Skipped'],
                datasets: [{
                    data: [0, 0, 0], // Initial data will be updated later
                    backgroundColor: ['#28a745', '#dc3545', '#ffc107'],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                legend: {
                    position: 'bottom'
                }
            }
        });
    }

    // Create duration trend chart if the element exists
    if (elements.durationTrendChart) {
        durationTrendChart = new Chart(elements.durationTrendChart.getContext('2d'), {
            type: 'line',
            data: {
                labels: [], // Will be populated with timestamps
                datasets: [{
                    label: 'Duration', // MODIFIED: Changed from 'Duration (seconds)'
                    data: [], // Will be populated with durations in seconds
                    borderColor: '#007bff',
                    backgroundColor: 'rgba(0, 123, 255, 0.1)',
                    borderWidth: 2,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                legend: {
                    display: true,
                    position: 'top'
                },
                // ADDED/MODIFIED: Tooltip configuration
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                let label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                if (context.parsed.y !== null) {
                                    // context.parsed.y will be the duration in seconds
                                    label += formatDurationForChart(context.parsed.y);
                                }
                                return label;
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        display: true,
                        title: {
                            display: true,
                            text: 'Time'
                        }
                    },
                    y: {
                        display: true,
                        title: {
                            display: true,
                            text: 'Duration' // MODIFIED: Changed from 'Duration (seconds)'
                        },
                        beginAtZero: true,
                        // ADDED: Y-axis ticks callback for formatting
                        ticks: {
                            callback: function(value, index, ticks) {
                                // 'value' here is the duration in seconds
                                return formatDurationForChart(value);
                            }
                        }
                    }
                }
            }
        });
    }

    console.log('Charts initialized');
}

/**
 * Attempts to parse a formatted date-time string (like "Jun 3, 2025, 10:15:18 AM")
 * into an ISO 8601 string, which can be reliably used with `new Date()`.
 * @param {string} formattedDateTime - The formatted date-time string.
 * @returns {string|null} ISO 8601 string or null if parsing fails.
 */
function parseFormattedDateTimeToISO(formattedDateTime) {
    if (!formattedDateTime || typeof formattedDateTime !== 'string' || formattedDateTime.toLowerCase() === 'not completed' || formattedDateTime.toLowerCase() === 'n/a') {
        return null;
    }
    try {
        // Common date formats like "Month Day, Year, HH:MM:SS AM/PM" are often parsable by new Date().
        const dateObj = new Date(formattedDateTime);
        if (isNaN(dateObj.getTime())) {
            // Fallback for specific known formats if direct parsing fails.
            console.warn(`[parseFormattedDateTimeToISO] Could not parse date directly: "${formattedDateTime}". Attempting manual parse for "Mon D, YYYY, HH:MM:SS AM/PM".`);
            
            const parts = formattedDateTime.match(/(\w{3})\s(\d{1,2}),\s(\d{4}),\s(\d{1,2}):(\d{2}):(\d{2})\s(AM|PM)/i);
            if (parts) {
                const months = {Jan:0,Feb:1,Mar:2,Apr:3,May:4,Jun:5,Jul:6,Aug:7,Sep:8,Oct:9,Nov:10,Dec:11};
                let hours = parseInt(parts[4], 10);
                const minutes = parseInt(parts[5], 10);
                const seconds = parseInt(parts[6], 10);
                const ampm = parts[7].toUpperCase();

                if (ampm === 'PM' && hours < 12) hours += 12;
                if (ampm === 'AM' && hours === 12) hours = 0; // Midnight case

                const year = parseInt(parts[3], 10);
                const month = months[parts[1]]; // Make sure parts[1] is a key in months
                const day = parseInt(parts[2], 10);
                
                if (month === undefined) {
                    console.warn(`[parseFormattedDateTimeToISO] Unknown month abbreviation: ${parts[1]}`);
                    return null;
                }

                // Use Date.UTC to avoid timezone issues during construction, then toISOString for standard format
                const parsedDate = new Date(Date.UTC(year, month, day, hours, minutes, seconds));
                if (!isNaN(parsedDate.getTime())) {
                     console.log(`[parseFormattedDateTimeToISO] Manual parse successful for "${formattedDateTime}": ${parsedDate.toISOString()}`);
                    return parsedDate.toISOString();
                } else {
                    console.warn(`[parseFormattedDateTimeToISO] Manual parse failed for "${formattedDateTime}" after regex match.`);
                    return null;
                }
            }
            console.warn(`[parseFormattedDateTimeToISO] Regex match failed for "${formattedDateTime}".`);
            return null;
        }
        return dateObj.toISOString();
    } catch (e) {
        console.warn(`[parseFormattedDateTimeToISO] Error parsing date string "${formattedDateTime}":`, e);
        return null;
    }
}

/**
 * Maps rows of data from DataTable (array of arrays, cell content)
 * back into an array of report objects suitable for [updateCharts](cci:1://file:///c:/Dev/smarttest/frontend/reports/reports.js:538:0-583:1).
 * @param {Array<Array<string>>} dataTableRows - Array of row data from DataTable.
 * @returns {Array<Object>} Array of report objects.
 */
function mapDataTableRowsToReportObjects(dataTableRows) {
    if (!dataTableRows || !Array.isArray(dataTableRows)) {
        console.warn('[mapDataTableRowsToReportObjects] Input is not a valid array.');
        return [];
    }
    console.log(`[mapDataTableRowsToReportObjects] Mapping ${dataTableRows.length} rows.`);

    // Column indices - these MUST match the actual table structure visible to the user.
    // Example: ID, Test Name, Test ID, Status, Started, Finished, Duration, Started By, Passed, Failed
    const COL_TSN_ID = 0;       // e.g., "15047"
    const COL_TEST_NAME = 1;    // e.g., "PE2.1 Sanity Test"
    const COL_TEST_ID = 2;      // e.g., "312" (the test case/suite ID)
    const COL_STATUS = 3;       // e.g., "Failed"
    const COL_STARTED = 4;      // e.g., "Jun 3, 2025, 10:15:18 AM"
    const COL_FINISHED = 5;     // e.g., "Jun 3, 2025, 10:17:02 AM"
    // Other columns like Duration (6), Started By (7), etc., are not directly needed by updateCharts' duration logic.

    const reportObjects = dataTableRows.map((rowArray, index) => {
        if (!Array.isArray(rowArray) || rowArray.length < 6) { // Ensure enough columns for essential data
            console.warn(`[mapDataTableRowsToReportObjects] Row ${index} is not a valid array or too short:`, rowArray);
            return null; // Skip invalid rows
        }

        const startTime = parseFormattedDateTimeToISO(rowArray[COL_STARTED]);
        const endTime = parseFormattedDateTimeToISO(rowArray[COL_FINISHED]);

        // Only include if essential time data is present for duration calculation
        if (!startTime || !endTime) {
            console.warn(`[mapDataTableRowsToReportObjects] Row ${index} skipped due to missing/unparsable start/end time. Start_Raw: "${rowArray[COL_STARTED]}", End_Raw: "${rowArray[COL_FINISHED]}"`);
            return null;
        }

        return {
            tsn_id: rowArray[COL_TSN_ID],
            test_name: rowArray[COL_TEST_NAME],
            test_id: rowArray[COL_TEST_ID],
            status: rowArray[COL_STATUS],
            start_time: startTime, // ISO String
            end_time: endTime,     // ISO String
        };
    }).filter(report => report !== null); // Remove any null entries from skipped/unparsable rows

    console.log(`[mapDataTableRowsToReportObjects] Mapped to ${reportObjects.length} valid report objects.`);
    return reportObjects;
}

// Function to update charts with new data

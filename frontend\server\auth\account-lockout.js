/**
 * Account Lockout Manager
 * Handles account lockout policies and failed login attempt tracking
 */

class AccountLockoutManager {
  constructor() {
    this.failedAttempts = new Map(); // uid -> attempts array
    this.lockedAccounts = new Map(); // uid -> lockout data
    this.ipAttempts = new Map(); // ip -> attempts array
    this.suspiciousIPs = new Set(); // IPs with suspicious activity
    
    // Configuration
    this.config = {
      // Account lockout settings
      maxFailedAttempts: 5,
      lockoutDuration: 15 * 60 * 1000, // 15 minutes
      progressiveLockout: true, // Increase lockout time with repeated failures
      
      // IP-based rate limiting
      maxIPAttempts: 20, // Per IP across all accounts
      ipLockoutDuration: 30 * 60 * 1000, // 30 minutes
      
      // Suspicious activity detection
      maxAttemptsPerMinute: 10,
      suspiciousThreshold: 50, // Failed attempts to mark IP as suspicious
      
      // Cleanup intervals
      cleanupInterval: 5 * 60 * 1000, // 5 minutes
      attemptRetention: 24 * 60 * 60 * 1000 // 24 hours
    };
    
    // Start cleanup timer
    this.cleanupTimer = setInterval(() => this.cleanup(), this.config.cleanupInterval);
    
    console.log('✅ Account Lockout Manager initialized');
  }

  /**
   * Record a failed login attempt
   * @param {string} uid - User ID
   * @param {string} ip - IP address
   * @returns {Object} Lockout status
   */
  recordFailedAttempt(uid, ip) {
    const now = Date.now();
    
    // Record attempt for user account
    if (!this.failedAttempts.has(uid)) {
      this.failedAttempts.set(uid, []);
    }
    this.failedAttempts.get(uid).push({ timestamp: now, ip });
    
    // Record attempt for IP
    if (!this.ipAttempts.has(ip)) {
      this.ipAttempts.set(ip, []);
    }
    this.ipAttempts.get(ip).push({ timestamp: now, uid });
    
    // Check for account lockout
    const accountLockout = this.checkAccountLockout(uid);
    const ipLockout = this.checkIPLockout(ip);
    
    // Check for suspicious activity
    this.checkSuspiciousActivity(ip);
    
    console.log(`❌ Failed login attempt: ${uid} from ${ip}`);
    
    return {
      accountLocked: accountLockout.locked,
      accountRetryAfter: accountLockout.retryAfter,
      ipLocked: ipLockout.locked,
      ipRetryAfter: ipLockout.retryAfter,
      suspicious: this.suspiciousIPs.has(ip)
    };
  }

  /**
   * Clear failed attempts for successful login
   * @param {string} uid - User ID
   */
  clearFailedAttempts(uid) {
    this.failedAttempts.delete(uid);
    
    // Remove from locked accounts if present
    if (this.lockedAccounts.has(uid)) {
      this.lockedAccounts.delete(uid);
      console.log(`✅ Account unlocked after successful login: ${uid}`);
    }
  }

  /**
   * Check if account should be locked
   * @param {string} uid - User ID
   * @returns {Object} Lockout status
   */
  checkAccountLockout(uid) {
    const attempts = this.failedAttempts.get(uid) || [];
    const now = Date.now();
    
    // Filter recent attempts (within lockout duration)
    const recentAttempts = attempts.filter(
      attempt => now - attempt.timestamp < this.config.lockoutDuration
    );
    
    if (recentAttempts.length >= this.config.maxFailedAttempts) {
      // Calculate lockout duration (progressive if enabled)
      let lockoutDuration = this.config.lockoutDuration;
      
      if (this.config.progressiveLockout) {
        const lockoutHistory = this.lockedAccounts.get(uid);
        if (lockoutHistory) {
          // Double the lockout time for repeated offenses
          lockoutDuration = Math.min(
            lockoutDuration * Math.pow(2, lockoutHistory.count),
            24 * 60 * 60 * 1000 // Max 24 hours
          );
        }
      }
      
      const lockoutEnd = now + lockoutDuration;
      const existingLockout = this.lockedAccounts.get(uid);
      
      // Update or create lockout record
      this.lockedAccounts.set(uid, {
        lockedAt: now,
        lockoutEnd: lockoutEnd,
        count: existingLockout ? existingLockout.count + 1 : 1,
        attempts: recentAttempts.length
      });
      
      console.log(`🔒 Account locked: ${uid} for ${Math.round(lockoutDuration / 1000)} seconds`);
      
      return {
        locked: true,
        retryAfter: Math.ceil(lockoutDuration / 1000)
      };
    }
    
    return { locked: false };
  }

  /**
   * Check if IP should be locked
   * @param {string} ip - IP address
   * @returns {Object} Lockout status
   */
  checkIPLockout(ip) {
    const attempts = this.ipAttempts.get(ip) || [];
    const now = Date.now();
    
    // Filter recent attempts (within IP lockout duration)
    const recentAttempts = attempts.filter(
      attempt => now - attempt.timestamp < this.config.ipLockoutDuration
    );
    
    if (recentAttempts.length >= this.config.maxIPAttempts) {
      console.log(`🔒 IP locked: ${ip} for ${this.config.ipLockoutDuration / 1000} seconds`);
      
      return {
        locked: true,
        retryAfter: Math.ceil(this.config.ipLockoutDuration / 1000)
      };
    }
    
    return { locked: false };
  }

  /**
   * Check for suspicious activity patterns
   * @param {string} ip - IP address
   */
  checkSuspiciousActivity(ip) {
    const attempts = this.ipAttempts.get(ip) || [];
    const now = Date.now();
    
    // Check attempts in the last minute
    const lastMinuteAttempts = attempts.filter(
      attempt => now - attempt.timestamp < 60 * 1000
    );
    
    // Check total failed attempts
    const totalAttempts = attempts.length;
    
    if (lastMinuteAttempts.length >= this.config.maxAttemptsPerMinute ||
        totalAttempts >= this.config.suspiciousThreshold) {
      
      if (!this.suspiciousIPs.has(ip)) {
        this.suspiciousIPs.add(ip);
        console.log(`🚨 Suspicious IP detected: ${ip}`);
      }
    }
  }

  /**
   * Check if account is currently locked
   * @param {string} uid - User ID
   * @returns {Object} Lock status
   */
  isAccountLocked(uid) {
    const lockout = this.lockedAccounts.get(uid);
    if (!lockout) {
      return { locked: false };
    }
    
    const now = Date.now();
    if (now >= lockout.lockoutEnd) {
      // Lockout has expired
      this.lockedAccounts.delete(uid);
      return { locked: false };
    }
    
    return {
      locked: true,
      retryAfter: Math.ceil((lockout.lockoutEnd - now) / 1000),
      lockedAt: lockout.lockedAt,
      attempts: lockout.attempts
    };
  }

  /**
   * Check if IP is currently locked
   * @param {string} ip - IP address
   * @returns {Object} Lock status
   */
  isIPLocked(ip) {
    const attempts = this.ipAttempts.get(ip) || [];
    const now = Date.now();
    
    const recentAttempts = attempts.filter(
      attempt => now - attempt.timestamp < this.config.ipLockoutDuration
    );
    
    if (recentAttempts.length >= this.config.maxIPAttempts) {
      const oldestRecentAttempt = Math.min(...recentAttempts.map(a => a.timestamp));
      const retryAfter = Math.ceil((oldestRecentAttempt + this.config.ipLockoutDuration - now) / 1000);
      
      return {
        locked: retryAfter > 0,
        retryAfter: Math.max(0, retryAfter)
      };
    }
    
    return { locked: false };
  }

  /**
   * Manually unlock an account (admin function)
   * @param {string} uid - User ID
   * @returns {boolean} Success status
   */
  unlockAccount(uid) {
    const wasLocked = this.lockedAccounts.has(uid);
    
    this.lockedAccounts.delete(uid);
    this.failedAttempts.delete(uid);
    
    if (wasLocked) {
      console.log(`🔓 Account manually unlocked: ${uid}`);
    }
    
    return wasLocked;
  }

  /**
   * Get lockout statistics
   * @returns {Object} Statistics
   */
  getStats() {
    const now = Date.now();
    
    return {
      lockedAccounts: this.lockedAccounts.size,
      accountsWithFailedAttempts: this.failedAttempts.size,
      suspiciousIPs: this.suspiciousIPs.size,
      totalIPsTracked: this.ipAttempts.size,
      config: { ...this.config }
    };
  }

  /**
   * Get detailed lockout information for an account
   * @param {string} uid - User ID
   * @returns {Object} Detailed lockout info
   */
  getAccountInfo(uid) {
    const lockout = this.lockedAccounts.get(uid);
    const attempts = this.failedAttempts.get(uid) || [];
    
    return {
      uid: uid,
      isLocked: !!lockout,
      lockoutInfo: lockout,
      failedAttempts: attempts.length,
      recentAttempts: attempts.filter(a => Date.now() - a.timestamp < 24 * 60 * 60 * 1000)
    };
  }

  /**
   * Cleanup expired data
   */
  cleanup() {
    const now = Date.now();
    let cleanedAccounts = 0;
    let cleanedAttempts = 0;
    
    // Clean expired lockouts
    for (const [uid, lockout] of this.lockedAccounts.entries()) {
      if (now >= lockout.lockoutEnd) {
        this.lockedAccounts.delete(uid);
        cleanedAccounts++;
      }
    }
    
    // Clean old failed attempts
    for (const [uid, attempts] of this.failedAttempts.entries()) {
      const validAttempts = attempts.filter(
        attempt => now - attempt.timestamp < this.config.attemptRetention
      );
      
      if (validAttempts.length === 0) {
        this.failedAttempts.delete(uid);
        cleanedAttempts++;
      } else if (validAttempts.length < attempts.length) {
        this.failedAttempts.set(uid, validAttempts);
      }
    }
    
    // Clean old IP attempts
    for (const [ip, attempts] of this.ipAttempts.entries()) {
      const validAttempts = attempts.filter(
        attempt => now - attempt.timestamp < this.config.attemptRetention
      );
      
      if (validAttempts.length === 0) {
        this.ipAttempts.delete(ip);
        this.suspiciousIPs.delete(ip); // Also remove from suspicious list
      } else if (validAttempts.length < attempts.length) {
        this.ipAttempts.set(ip, validAttempts);
      }
    }
    
    if (cleanedAccounts > 0 || cleanedAttempts > 0) {
      console.log(`🧹 Lockout cleanup: ${cleanedAccounts} expired lockouts, ${cleanedAttempts} old attempts`);
    }
  }

  /**
   * Destroy the lockout manager
   */
  destroy() {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }
    
    this.failedAttempts.clear();
    this.lockedAccounts.clear();
    this.ipAttempts.clear();
    this.suspiciousIPs.clear();
  }
}

// Export singleton instance
module.exports = new AccountLockoutManager();

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SmartTest API Explorer</title>
    <link rel="stylesheet" href="styles.css">
    <style>
        .api-explorer {
            padding: 20px;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .credentials-panel {
            background-color: #f5f5f5;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 4px;
        }
        
        .endpoint-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }
        
        .endpoint-card {
            background-color: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .endpoint-card:hover {
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border-color: #0078d4;
        }
        
        .endpoint-card h3 {
            margin-top: 0;
            color: #0078d4;
        }
        
        .endpoint-card .method {
            display: inline-block;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 12px;
            font-weight: bold;
            margin-right: 8px;
        }
        
        .endpoint-card .method.get {
            background-color: #61affe;
            color: white;
        }
        
        .endpoint-card .method.post {
            background-color: #49cc90;
            color: white;
        }
        
        .test-panel {
            background-color: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .parameters-form {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .parameters-form label {
            display: flex;
            align-items: center;
            font-weight: bold;
        }
        
        .response-container {
            background-color: #f5f5f5;
            border-radius: 4px;
            padding: 15px;
            margin-top: 20px;
            overflow: auto;
            max-height: 400px;
        }
        
        .response-container pre {
            margin: 0;
            white-space: pre-wrap;
        }
        
        .discovery-panel {
            background-color: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        button {
            background-color: #0078d4;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
        }
        
        button:hover {
            background-color: #106ebe;
        }
        
        .tabs {
            display: flex;
            border-bottom: 1px solid #ddd;
            margin-bottom: 20px;
        }
        
        .tab {
            padding: 10px 20px;
            cursor: pointer;
            border-bottom: 3px solid transparent;
        }
        
        .tab.active {
            border-bottom-color: #0078d4;
            font-weight: bold;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .generate-code-panel {
            margin-top: 20px;
            padding: 15px;
            background-color: #f5f5f5;
            border-radius: 4px;
        }
        
        .generate-code-panel textarea {
            width: 100%;
            height: 400px;
            font-family: monospace;
            margin-top: 15px;
            padding: 10px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>SmartTest API Explorer</h1>
        <p>Discover and test available API endpoints</p>
    </div>
    
    <div class="api-explorer">
        <div class="tabs">
            <div class="tab active" data-tab="endpoints">Test Endpoints</div>
            <div class="tab" data-tab="discovery">API Discovery</div>
            <div class="tab" data-tab="generate">Generate Code</div>
        </div>
        
        <div class="credentials-panel">
            <h2>API Credentials</h2>
            <div class="parameters-form">
                <label for="username">Username:</label>
                <input type="text" id="username" placeholder="Enter your username" value="<EMAIL>">
                
                <label for="password">Password:</label>
                <input type="password" id="password" placeholder="Enter your password" value="test">
            </div>
            
            <button id="save-credentials">Save Credentials</button>
        </div>
        
        <div class="tab-content active" id="endpoints-tab">
            <h2>Available Endpoints</h2>
            <div class="endpoint-list" id="endpoint-list">
                <!-- Endpoints will be populated dynamically -->
            </div>
            
            <div class="test-panel" id="test-panel">
                <h2 id="test-endpoint-name">Select an endpoint to test</h2>
                <div id="endpoint-description"></div>
                
                <div id="parameters-container">
                    <!-- Parameters will be populated dynamically -->
                </div>
                
                <button id="test-endpoint" style="display: none;">Test Endpoint</button>
                
                <div class="response-container" id="response-container" style="display: none;">
                    <h3>Response</h3>
                    <pre id="response-data"></pre>
                </div>
            </div>
        </div>
        
        <div class="tab-content" id="discovery-tab">
            <h2>API Discovery</h2>
            <div class="discovery-panel">
                <p>This tool will attempt to discover all available API endpoints by testing known endpoints and exploring potential new ones.</p>
                <button id="start-discovery">Start API Discovery</button>
                
                <div class="response-container" id="discovery-results" style="display: none;">
                    <h3>Discovery Results</h3>
                    <pre id="discovery-data"></pre>
                </div>
            </div>
        </div>
        
        <div class="tab-content" id="generate-tab">
            <h2>Generate API Service Code</h2>
            <div class="generate-code-panel">
                <p>Generate TypeScript/JavaScript code for the API service based on discovered endpoints.</p>
                <button id="generate-code">Generate Code</button>
                
                <textarea id="generated-code" readonly></textarea>
            </div>
        </div>
    </div>
    
    <script src="api-explorer.js"></script>
    <script>
        // Initialize API Explorer
        const apiExplorer = new ApiExplorer();
        
        // DOM Elements
        const endpointList = document.getElementById('endpoint-list');
        const testPanel = document.getElementById('test-panel');
        const testEndpointName = document.getElementById('test-endpoint-name');
        const endpointDescription = document.getElementById('endpoint-description');
        const parametersContainer = document.getElementById('parameters-container');
        const testEndpointButton = document.getElementById('test-endpoint');
        const responseContainer = document.getElementById('response-container');
        const responseData = document.getElementById('response-data');
        const usernameInput = document.getElementById('username');
        const passwordInput = document.getElementById('password');
        const saveCredentialsButton = document.getElementById('save-credentials');
        const startDiscoveryButton = document.getElementById('start-discovery');
        const discoveryResults = document.getElementById('discovery-results');
        const discoveryData = document.getElementById('discovery-data');
        const generateCodeButton = document.getElementById('generate-code');
        const generatedCode = document.getElementById('generated-code');
        const tabs = document.querySelectorAll('.tab');
        const tabContents = document.querySelectorAll('.tab-content');
        
        // Initialize the endpoint list
        function initializeEndpointList() {
            endpointList.innerHTML = '';
            
            apiExplorer.knownEndpoints.forEach(endpoint => {
                const card = document.createElement('div');
                card.className = 'endpoint-card';
                card.innerHTML = `
                    <span class="method ${endpoint.method.toLowerCase()}">${endpoint.method}</span>
                    <h3>${endpoint.name}</h3>
                    <p>${endpoint.description || 'No description available'}</p>
                `;
                
                card.addEventListener('click', () => {
                    displayEndpointTestPanel(endpoint);
                });
                
                endpointList.appendChild(card);
            });
        }
        
        // Display the test panel for a selected endpoint
        function displayEndpointTestPanel(endpoint) {
            testEndpointName.textContent = endpoint.name;
            endpointDescription.textContent = endpoint.description || 'No description available';
            
            // Clear parameters
            parametersContainer.innerHTML = '<h3>Parameters</h3><div class="parameters-form" id="parameters-form"></div>';
            const parametersForm = document.getElementById('parameters-form');
            
            // Add parameters
            endpoint.parameters.forEach(param => {
                const label = document.createElement('label');
                label.setAttribute('for', `param-${param.name}`);
                label.textContent = `${param.name}${param.required ? ' *' : ''}:`;
                
                const input = document.createElement('input');
                input.type = param.type === 'number' ? 'number' : 'text';
                input.id = `param-${param.name}`;
                input.name = param.name;
                input.placeholder = param.description || param.name;
                
                // Fill with default value if exists
                if (param.defaultValue !== undefined) {
                    input.value = param.defaultValue;
                }
                
                // Don't show credentials in the form if they're in the credentials panel
                if (param.name === 'uid' || param.name === 'password') {
                    return;
                }
                
                parametersForm.appendChild(label);
                parametersForm.appendChild(input);
            });
            
            // Show test button
            testEndpointButton.style.display = 'block';
            
            // Clear previous response
            responseContainer.style.display = 'none';
            
            // Save current endpoint for testing
            testEndpointButton.setAttribute('data-endpoint', JSON.stringify(endpoint));
        }
        
        // Test an endpoint
        async function testEndpoint(endpoint) {
            // Get parameters from form
            const params = {};
            endpoint.parameters.forEach(param => {
                if (param.name === 'uid') {
                    params[param.name] = usernameInput.value;
                } else if (param.name === 'password') {
                    params[param.name] = passwordInput.value;
                } else {
                    const input = document.getElementById(`param-${param.name}`);
                    if (input && input.value) {
                        params[param.name] = input.value;
                    }
                }
            });
            
            // Test the endpoint
            const result = await apiExplorer.testEndpoint(endpoint, params);
            
            // Display the result
            responseContainer.style.display = 'block';
            responseData.textContent = JSON.stringify(result, null, 2);
        }
        
        // Event Listeners
        saveCredentialsButton.addEventListener('click', () => {
            const username = usernameInput.value;
            const password = passwordInput.value;
            
            if (username && password) {
                apiExplorer.setCredentials(username, password);
                alert('Credentials saved successfully!');
            } else {
                alert('Please enter both username and password.');
            }
        });
        
        testEndpointButton.addEventListener('click', () => {
            const endpointJson = testEndpointButton.getAttribute('data-endpoint');
            if (endpointJson) {
                const endpoint = JSON.parse(endpointJson);
                testEndpoint(endpoint);
            }
        });
        
        startDiscoveryButton.addEventListener('click', async () => {
            if (!apiExplorer.credentials.uid || !apiExplorer.credentials.password) {
                alert('Please set your credentials first!');
                return;
            }
            
            startDiscoveryButton.disabled = true;
            startDiscoveryButton.textContent = 'Discovery in progress...';
            
            try {
                const results = await apiExplorer.exploreEndpoints();
                
                // Display the results
                discoveryResults.style.display = 'block';
                discoveryData.textContent = JSON.stringify(results, null, 2);
                
                // Update the endpoint list with discovered endpoints
                apiExplorer.knownEndpoints = apiExplorer.discoveredEndpoints.filter(e => e.verified);
                initializeEndpointList();
                
                startDiscoveryButton.disabled = false;
                startDiscoveryButton.textContent = 'Start API Discovery';
            } catch (error) {
                console.error('Error during API discovery:', error);
                alert('Error during API discovery. Check console for details.');
                
                startDiscoveryButton.disabled = false;
                startDiscoveryButton.textContent = 'Start API Discovery';
            }
        });
        
        generateCodeButton.addEventListener('click', () => {
            if (apiExplorer.discoveredEndpoints.length === 0) {
                alert('Please run API discovery first to generate code.');
                return;
            }
            
            const code = apiExplorer.generateApiServiceCode();
            generatedCode.value = code;
        });
        
        // Tab navigation
        tabs.forEach(tab => {
            tab.addEventListener('click', () => {
                // Remove active class from all tabs
                tabs.forEach(t => t.classList.remove('active'));
                tabContents.forEach(c => c.classList.remove('active'));
                
                // Add active class to clicked tab
                tab.classList.add('active');
                
                // Show corresponding content
                const tabName = tab.getAttribute('data-tab');
                document.getElementById(`${tabName}-tab`).classList.add('active');
            });
        });
        
        // Initialize the app
        initializeEndpointList();
    </script>
</body>
</html> 
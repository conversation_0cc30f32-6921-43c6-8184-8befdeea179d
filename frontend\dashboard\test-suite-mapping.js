// Frontend Test Suite Mapping Configuration
// Static mapping data to avoid API calls for test suite filtering

const TEST_SUITE_MAPPING = {
    // Microservice Level Test Suites
    'Microservice-2.1': {
        ts_id: 326,
        name: 'Test suite Microservice level – Prod version 2.1',
        level: 'Microservice',
        version: '2.1',
        description: 'Production version 2.1 microservice tests',
    },
    'Microservice-2.1 + Patch': {
        ts_id: 327,
        name: 'Test suite Microservice level 2.1 + Patch',
        level: 'Microservice',
        version: '2.1 + Patch',
        description: 'Version 2.1 with patches (e.g., 2.1_PR4212)',
    },
    'Microservice-3.0': {
        ts_id: 328,
        name: 'Test suite Microservice level 3.0',
        level: 'Microservice',
        version: '3.0',
        description: 'Version 3.0 microservice tests',
    },
    
    // Integrated Level Test Suites
    'Integrated-2.1': {
        ts_id: 331,
        name: 'Test suite Integrated level Prod version 2.1',
        level: 'Integrated',
        version: '2.1',
        description: 'Production version 2.1 integrated tests',
    },
    'Integrated-2.1 + Patch': {
        ts_id: 329,
        name: 'Test suite Integrated level 2.1 + Patch',
        level: 'Integrated',
        version: '2.1 + Patch',
        description: 'Version 2.1 with patches for integrated tests',
    },
    'Integrated-3.0': {
        ts_id: 330,
        name: 'Test suite Integrated level 3.0',
        level: 'Integrated',
        version: '3.0',
        description: 'Version 3.0 integrated tests',
    }
};

/**
 * Frontend Test Suite Service - No API calls, uses static mapping
 */
class FrontendTestSuiteService {
    /**
     * Get all test suites
     * @returns {Array} Array of all test suite objects
     */
    static getAllTestSuites() {
        return Object.values(TEST_SUITE_MAPPING);
    }

    /**
     * Get test suites based on filters
     * @param {Object} filters - Filter criteria
     * @param {string} filters.level - Integration level filter
     * @param {string} filters.version - Version filter
     * @returns {Array} Array of filtered test suite objects
     */
    static getTestSuites(filters = {}) {
        console.log('Frontend: Filtering test suites with filters:', filters);
        
        const allSuites = this.getAllTestSuites();
        
        // If no filters, return all suites
        if (!filters.level && !filters.version) {
            console.log(`Frontend: Returning all ${allSuites.length} test suites`);
            return allSuites;
        }
        
        // Apply filters
        const filteredSuites = allSuites.filter(suite => {
            const matchesLevel = !filters.level || filters.level === '' || filters.level === 'all' || suite.level === filters.level;
            const matchesVersion = !filters.version || filters.version === '' || filters.version === 'all' || suite.version === filters.version;
            
            return matchesLevel && matchesVersion;
        });
        
        console.log(`Frontend: Filtered ${allSuites.length} suites down to ${filteredSuites.length} matches`);
        return filteredSuites;
    }

    /**
     * Get filtered test suites (alias for getTestSuites for compatibility)
     * @param {string} level - Integration level filter
     * @param {string} version - Version filter
     * @returns {Array} Array of filtered test suite objects
     */
    static getFilteredTestSuites(level = '', version = '') {
        return this.getTestSuites({ level, version });
    }

    /**
     * Get test suite by ID
     * @param {number} ts_id - Test suite ID
     * @returns {Object|null} Test suite object or null if not found
     */
    static getTestSuiteById(ts_id) {
        const allSuites = this.getAllTestSuites();
        const suite = allSuites.find(suite => suite.ts_id === parseInt(ts_id));
        
        if (suite) {
            console.log(`Frontend: Found test suite ${ts_id}:`, suite.name);
        } else {
            console.warn(`Frontend: Test suite ${ts_id} not found`);
        }
        
        return suite || null;
    }

    /**
     * Get available filter options
     * @returns {Object} Object with available levels and versions
     */
    static getFilterOptions() {
        const allSuites = this.getAllTestSuites();
        
        const levels = [...new Set(allSuites.map(suite => suite.level))];
        const versions = [...new Set(allSuites.map(suite => suite.version))];
        
        const options = {
            levels: levels.sort(),
            versions: versions.sort()
        };
        
        console.log('Frontend: Available filter options:', options);
        return options;
    }
}

// Export for use in other modules
window.FrontendTestSuiteService = FrontendTestSuiteService;
window.TEST_SUITE_MAPPING = TEST_SUITE_MAPPING;

console.log('Frontend Test Suite Service loaded with', Object.keys(TEST_SUITE_MAPPING).length, 'test suites');

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Active Tests Fix</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button { padding: 8px 16px; margin: 5px; cursor: pointer; }
        pre { background-color: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; max-height: 300px; }
        .test-result { margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 4px; }
    </style>
</head>
<body>
    <h1>Test Active Tests Database Fix</h1>
    
    <div class="status info">
        <strong>Testing:</strong> The fix for "Unknown column 'ts.status'" error when fetching active tests
    </div>
    
    <div>
        <button onclick="testActiveTestsAPI()">Test Active Tests API</button>
        <button onclick="testRecentRunsAPI()">Test Recent Runs API</button>
        <button onclick="testBothAPIs()">Test Both APIs</button>
        <button onclick="clearResults()">Clear Results</button>
    </div>
    
    <h2>Test Results</h2>
    <div id="results"></div>
    
    <h2>API Responses</h2>
    <pre id="api-responses"></pre>
    
    <script>
        let testResults = [];
        let apiResponses = [];
        
        function addResult(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result status ${type}`;
            resultDiv.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            resultsDiv.appendChild(resultDiv);
            testResults.push({ time: new Date().toLocaleTimeString(), message, type });
        }
        
        function addAPIResponse(label, response) {
            apiResponses.push(`\n=== ${label} ===`);
            apiResponses.push(JSON.stringify(response, null, 2));
            document.getElementById('api-responses').textContent = apiResponses.join('\n');
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
            document.getElementById('api-responses').textContent = '';
            testResults = [];
            apiResponses = [];
        }
        
        async function testActiveTestsAPI() {
            addResult('Testing Active Tests API (with status filter)...', 'info');
            
            try {
                // This should trigger the fixed query that uses end_ts IS NULL instead of ts.status
                const response = await fetch('/local/recent-runs?limit=100&status=running,queued');
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                addAPIResponse('Active Tests API Response', data);
                
                if (data.error) {
                    addResult(`❌ API returned error: ${data.error}`, 'error');
                } else {
                    addResult(`✅ Active Tests API successful - returned ${data.length || 0} results`, 'success');
                    
                    // Check if any results have end_ts/end_time (they shouldn't for active tests)
                    const activeTests = data.filter(test => !test.end_ts && !test.end_time);
                    addResult(`📊 Found ${activeTests.length} truly active tests (no end time)`, 'info');
                }
                
            } catch (error) {
                addResult(`❌ Active Tests API failed: ${error.message}`, 'error');
                addAPIResponse('Active Tests API Error', { error: error.message });
            }
        }
        
        async function testRecentRunsAPI() {
            addResult('Testing Recent Runs API (with single_case filter)...', 'info');
            
            try {
                const response = await fetch('/local/recent-runs?limit=50&type=single_case');
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                addAPIResponse('Recent Runs API Response', data);
                
                if (data.error) {
                    addResult(`❌ API returned error: ${data.error}`, 'error');
                } else {
                    addResult(`✅ Recent Runs API successful - returned ${data.length || 0} results`, 'success');
                    
                    // Check if results are single test cases (have tc_id)
                    const singleCases = data.filter(test => test.tc_id && test.tc_id !== '');
                    addResult(`📊 Found ${singleCases.length} single test cases`, 'info');
                }
                
            } catch (error) {
                addResult(`❌ Recent Runs API failed: ${error.message}`, 'error');
                addAPIResponse('Recent Runs API Error', { error: error.message });
            }
        }
        
        async function testBothAPIs() {
            addResult('Running comprehensive test of both APIs...', 'info');
            
            await testActiveTestsAPI();
            await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second
            await testRecentRunsAPI();
            
            // Summary
            const errors = testResults.filter(r => r.type === 'error').length;
            const successes = testResults.filter(r => r.type === 'success').length;
            
            if (errors === 0) {
                addResult(`🎉 All tests passed! (${successes} successful operations)`, 'success');
            } else {
                addResult(`⚠️ ${errors} errors found out of ${testResults.length} operations`, 'error');
            }
        }
        
        // Auto-run a basic test when page loads
        document.addEventListener('DOMContentLoaded', () => {
            addResult('Page loaded. Ready to test the database fix.', 'info');
            addResult('The fix changes "ts.status IN (?)" to "ts.end_ts IS NULL" for active tests', 'info');
        });
    </script>
</body>
</html>

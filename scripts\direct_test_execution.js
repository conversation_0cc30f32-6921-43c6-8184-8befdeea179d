/**
 * Direct Test Execution Script
 * 
 * This script bypasses the API and initiates test execution directly through
 * database operations to ensure we can track tests initiated by the current user.
 */

const mysql = require('mysql2/promise');

// Process command line arguments
const args = process.argv.slice(2);
const environment = args[0] || 'qa01';
const testCaseId = args[1] || 1279;

// Configuration
const config = {
  // Environment settings
  environments: {
    qa01: {
      dbHost: 'mprts-qa01.lab.wagerworks.com',
      dbUser: 'rgs_rw',
      dbPassword: 'rgs_rw', 
      dbName: 'rgs_test',
      dbPort: 3306
    },
    qa02: {
      dbHost: 'mprts-qa02.lab.wagerworks.com',
      dbUser: 'rgs_rw',
      dbPassword: 'rgs_rw',
      dbName: 'rgs_test',
      dbPort: 3306
    },
    qa03: {
      dbHost: 'mprts-qa03.lab.wagerworks.com',
      dbUser: 'rgs_rw',
      dbPassword: 'rgs_rw',
      dbName: 'rgs_test',
      dbPort: 3306
    }
  },
  
  // User information
  user: {
    uid: '<EMAIL>'
  }
};

let connection;

async function main() {
  try {
    if (!config.environments[environment]) {
      throw new Error(`Unknown environment: ${environment}`);
    }
    
    // Connect to database
    const envConfig = config.environments[environment];
    console.log(`Connecting to ${envConfig.dbHost}...`);
    
    connection = await mysql.createConnection({
      host: envConfig.dbHost,
      user: envConfig.dbUser,
      password: envConfig.dbPassword,
      database: envConfig.dbName,
      port: envConfig.dbPort
    });
    
    console.log('Connected to database');
    
    // Get the next available tsn_id
    const [maxTsnIdRows] = await connection.query('SELECT MAX(tsn_id) as max_tsn_id FROM test_session');
    const nextTsnId = (maxTsnIdRows[0].max_tsn_id || 0) + 1;
    
    console.log(`Creating new test run with tsn_id: ${nextTsnId}`);
    
    // Start a transaction
    await connection.beginTransaction();
    
    // Insert record into test_session table
    const now = new Date().toISOString().slice(0, 19).replace('T', ' ');
    await connection.query(`
      INSERT INTO test_session 
      (tsn_id, uid, start_ts, tc_id, error) 
      VALUES (?, ?, ?, ?, ?)
    `, [nextTsnId, config.user.uid, now, testCaseId, 'In progress']);
    
    // Commit transaction
    await connection.commit();
    
    console.log(`Successfully created test session with ID: ${nextTsnId}`);
    console.log(`Test case ID: ${testCaseId}`);
    console.log(`User: ${config.user.uid}`);
    console.log('Start time:', now);
    
    // Query to verify the session was created
    const [sessionRows] = await connection.query(`
      SELECT * FROM test_session WHERE tsn_id = ?
    `, [nextTsnId]);
    
    if (sessionRows.length > 0) {
      console.log('\nVerification - Created session details:');
      console.log(sessionRows[0]);
    } else {
      console.log('WARNING: Could not verify session creation!');
    }
    
    // Check for active test runs by current user
    const [activeRunsRows] = await connection.query(`
      SELECT tsn_id, tc_id, uid, start_ts 
      FROM test_session 
      WHERE uid = ? AND end_ts IS NULL
      ORDER BY start_ts DESC
    `, [config.user.uid]);
    
    console.log(`\nFound ${activeRunsRows.length} active test runs for user ${config.user.uid}:`);
    activeRunsRows.forEach(row => {
      console.log(`- Test run ${row.tsn_id} for test case ${row.tc_id}, started at ${row.start_ts}`);
    });
    
    console.log('\nIMPORTANT: This method creates a database entry but may not actually trigger test execution.');
    console.log('Monitor the test status to see if the test engine picks up and executes this test.');
    console.log(`To monitor this test, use: node test_flow_verification_fixed.js ${environment} ${nextTsnId}`);
    
  } catch (error) {
    console.error('Error:', error);
    if (connection) {
      try {
        await connection.rollback();
      } catch (rollbackError) {
        console.error('Rollback error:', rollbackError);
      }
    }
  } finally {
    if (connection) {
      await connection.end();
      console.log('Database connection closed');
    }
  }
}

main().catch(console.error); 
#!/usr/bin/env node

/**
 * User Management CLI Tool
 * Provides command-line interface for managing allowed users
 */

const userManager = require('../auth/user-manager');
const ValidationUtils = require('../utils/validation');

// Command line arguments
const args = process.argv.slice(2);
const command = args[0];

/**
 * Display help information
 */
function showHelp() {
  console.log(`
SmartTest User Management CLI

Usage: node manage-users.js <command> [options]

Commands:
  list                          List all users
  add <email> <password> [role] Add a new user
  update <email> [options]      Update an existing user
  deactivate <email>            Deactivate a user
  activate <email>              Activate a user
  roles                         List available roles
  status                        Show system status
  help                          Show this help message

Options for 'update':
  --password <password>         New password
  --role <role>                 New role
  --name <name>                 New display name

Examples:
  node manage-users.js list
  node manage-users.<NAME_EMAIL> password123 tester
  node manage-users.<NAME_EMAIL> --role admin
  node manage-users.<NAME_EMAIL>
  node manage-users.js roles
`);
}

/**
 * List all users
 */
function listUsers() {
  try {
    const users = userManager.getAllUsers();
    
    if (users.length === 0) {
      console.log('No users found.');
      return;
    }

    console.log('\nUsers:');
    console.log('─'.repeat(80));
    console.log('Email'.padEnd(35) + 'Role'.padEnd(15) + 'Name'.padEnd(20) + 'Status');
    console.log('─'.repeat(80));
    
    users.forEach(user => {
      const status = user.active ? '✅ Active' : '❌ Inactive';
      console.log(
        user.uid.padEnd(35) + 
        user.role.padEnd(15) + 
        (user.name || '').padEnd(20) + 
        status
      );
    });
    
    console.log('─'.repeat(80));
    console.log(`Total: ${users.length} users`);
  } catch (error) {
    console.error('Error listing users:', error.message);
    process.exit(1);
  }
}

/**
 * Add a new user
 */
function addUser(email, password, role = 'viewer') {
  try {
    // Validate input
    const validation = ValidationUtils.validateCredentials({ uid: email, password });
    if (!validation.isValid) {
      console.error('Invalid input:', validation.errors.join(', '));
      process.exit(1);
    }

    // Validate role
    const roles = userManager.getRoles();
    if (!roles[role]) {
      console.error(`Invalid role '${role}'. Available roles: ${Object.keys(roles).join(', ')}`);
      process.exit(1);
    }

    // Add user
    const success = userManager.addUser({
      uid: validation.sanitized.uid,
      password: validation.sanitized.password,
      role: role,
      name: validation.sanitized.uid
    });

    if (success) {
      console.log(`✅ User '${validation.sanitized.uid}' added successfully with role '${role}'`);
    } else {
      console.error(`❌ Failed to add user (user may already exist)`);
      process.exit(1);
    }
  } catch (error) {
    console.error('Error adding user:', error.message);
    process.exit(1);
  }
}

/**
 * Update a user
 */
function updateUser(email, options) {
  try {
    // Validate email
    const emailValidation = ValidationUtils.validateEmail(email);
    if (!emailValidation.isValid) {
      console.error('Invalid email:', emailValidation.error);
      process.exit(1);
    }

    // Check if user exists
    const existingUser = userManager.getUser(emailValidation.sanitized);
    if (!existingUser) {
      console.error(`❌ User '${emailValidation.sanitized}' not found`);
      process.exit(1);
    }

    const updates = {};

    // Parse options
    for (let i = 0; i < options.length; i++) {
      const option = options[i];
      const value = options[i + 1];

      switch (option) {
        case '--password':
          if (!value) {
            console.error('Password value is required');
            process.exit(1);
          }
          const passwordValidation = ValidationUtils.validatePassword(value);
          if (!passwordValidation.isValid) {
            console.error('Invalid password:', passwordValidation.error);
            process.exit(1);
          }
          updates.password = passwordValidation.sanitized;
          i++; // Skip next argument
          break;

        case '--role':
          if (!value) {
            console.error('Role value is required');
            process.exit(1);
          }
          const roles = userManager.getRoles();
          if (!roles[value]) {
            console.error(`Invalid role '${value}'. Available roles: ${Object.keys(roles).join(', ')}`);
            process.exit(1);
          }
          updates.role = value;
          i++; // Skip next argument
          break;

        case '--name':
          if (!value) {
            console.error('Name value is required');
            process.exit(1);
          }
          const nameValidation = ValidationUtils.validateString(value, {
            maxLength: 100,
            name: 'name'
          });
          if (!nameValidation.isValid) {
            console.error('Invalid name:', nameValidation.error);
            process.exit(1);
          }
          updates.name = nameValidation.sanitized;
          i++; // Skip next argument
          break;

        default:
          console.error(`Unknown option: ${option}`);
          process.exit(1);
      }
    }

    if (Object.keys(updates).length === 0) {
      console.error('No updates specified. Use --password, --role, or --name options.');
      process.exit(1);
    }

    // Update user
    const success = userManager.updateUser(emailValidation.sanitized, updates);

    if (success) {
      console.log(`✅ User '${emailValidation.sanitized}' updated successfully`);
      console.log('Updated fields:', Object.keys(updates).join(', '));
    } else {
      console.error(`❌ Failed to update user`);
      process.exit(1);
    }
  } catch (error) {
    console.error('Error updating user:', error.message);
    process.exit(1);
  }
}

/**
 * Deactivate a user
 */
function deactivateUser(email) {
  try {
    const emailValidation = ValidationUtils.validateEmail(email);
    if (!emailValidation.isValid) {
      console.error('Invalid email:', emailValidation.error);
      process.exit(1);
    }

    const success = userManager.deactivateUser(emailValidation.sanitized);

    if (success) {
      console.log(`✅ User '${emailValidation.sanitized}' deactivated successfully`);
    } else {
      console.error(`❌ Failed to deactivate user (user may not exist)`);
      process.exit(1);
    }
  } catch (error) {
    console.error('Error deactivating user:', error.message);
    process.exit(1);
  }
}

/**
 * Activate a user
 */
function activateUser(email) {
  try {
    const emailValidation = ValidationUtils.validateEmail(email);
    if (!emailValidation.isValid) {
      console.error('Invalid email:', emailValidation.error);
      process.exit(1);
    }

    const success = userManager.updateUser(emailValidation.sanitized, { active: true });

    if (success) {
      console.log(`✅ User '${emailValidation.sanitized}' activated successfully`);
    } else {
      console.error(`❌ Failed to activate user (user may not exist)`);
      process.exit(1);
    }
  } catch (error) {
    console.error('Error activating user:', error.message);
    process.exit(1);
  }
}

/**
 * List available roles
 */
function listRoles() {
  try {
    const roles = userManager.getRoles();
    
    console.log('\nAvailable Roles:');
    console.log('─'.repeat(60));
    console.log('Role'.padEnd(15) + 'Description');
    console.log('─'.repeat(60));
    
    Object.entries(roles).forEach(([role, config]) => {
      console.log(role.padEnd(15) + config.description);
      console.log(''.padEnd(15) + `Permissions: ${config.permissions.join(', ')}`);
      console.log('');
    });
  } catch (error) {
    console.error('Error listing roles:', error.message);
    process.exit(1);
  }
}

/**
 * Show system status
 */
function showStatus() {
  try {
    const users = userManager.getAllUsers();
    const roles = userManager.getRoles();
    
    console.log('\nSystem Status:');
    console.log('─'.repeat(40));
    console.log(`Total Users: ${users.length}`);
    console.log(`Active Users: ${users.filter(u => u.active).length}`);
    console.log(`Inactive Users: ${users.filter(u => !u.active).length}`);
    
    console.log('\nUsers by Role:');
    Object.keys(roles).forEach(role => {
      const count = users.filter(u => u.role === role && u.active).length;
      console.log(`  ${role}: ${count}`);
    });
    
    console.log(`\nSession Timeout: ${userManager.getSessionTimeout()} seconds`);
  } catch (error) {
    console.error('Error showing status:', error.message);
    process.exit(1);
  }
}

// Main command processing
switch (command) {
  case 'list':
    listUsers();
    break;

  case 'add':
    if (args.length < 3) {
      console.error('Usage: node manage-users.js add <email> <password> [role]');
      process.exit(1);
    }
    addUser(args[1], args[2], args[3]);
    break;

  case 'update':
    if (args.length < 3) {
      console.error('Usage: node manage-users.js update <email> [options]');
      process.exit(1);
    }
    updateUser(args[1], args.slice(2));
    break;

  case 'deactivate':
    if (args.length < 2) {
      console.error('Usage: node manage-users.js deactivate <email>');
      process.exit(1);
    }
    deactivateUser(args[1]);
    break;

  case 'activate':
    if (args.length < 2) {
      console.error('Usage: node manage-users.js activate <email>');
      process.exit(1);
    }
    activateUser(args[1]);
    break;

  case 'roles':
    listRoles();
    break;

  case 'status':
    showStatus();
    break;

  case 'help':
  case '--help':
  case '-h':
    showHelp();
    break;

  default:
    console.error(`Unknown command: ${command}`);
    showHelp();
    process.exit(1);
}

/**
 * Register Mock Service Worker for SmartTest Frontend
 * 
 * This script handles registration of the mock service worker,
 * allowing it to intercept API requests and simulate backend responses.
 */

// Check if we're in a development environment where mocking is needed
const shouldUseMockApi = function() {
  // Detect environment - enable mocking in development or testing
  if (typeof process !== 'undefined' && process.env && process.env.NODE_ENV) {
    return process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'test';
  }
  
  // Default to enabling mock API if no environment detected (likely local testing)
  return true;
};

/**
 * Register the mock service worker
 * @returns {Promise<ServiceWorkerRegistration|null>} Registration if successful
 */
const registerMockServiceWorker = async function() {
  if (!shouldUseMockApi()) {
    console.log('Running in production mode - Mock API disabled');
    return null;
  }
  
  if (!('serviceWorker' in navigator)) {
    console.warn('Service workers are not supported in this browser');
    return null;
  }
  
  try {
    // Get base URL for the application to ensure correct path resolution
    const baseUrl = window.location.origin;
    
    // Construct an absolute path to the service worker
    const swUrl = new URL('/config/mock-service-worker.js', baseUrl).href;
    console.log(`Attempting to register mock service worker from: ${swUrl}`);
    
    const registration = await navigator.serviceWorker.register(swUrl, {
      scope: '/'
    });
    
    console.log('Mock service worker registered successfully:', registration);
    
    // Wait for the service worker to be ready
    await navigator.serviceWorker.ready;
    console.log('Mock service worker is active');
    
    // Send message to activate the mock API
    if (registration.active) {
      registration.active.postMessage({
        type: 'MOCK_ACTIVATE'
      });
    }
    
    return registration;
  } catch (error) {
    console.error('Failed to register mock service worker:', error);
    return null;
  }
};

/**
 * Initialize the mock API system
 */
const initMockApiSystem = async function() {
  try {
    console.log('Initializing mock API system...');
    
    // Ensure mock API is available
    if (!window.mockApi) {
      console.error('Mock API module not loaded. Make sure to include mock-api.js before this script.');
    }
    
    // Then attempt to register the service worker
    const swRegistration = await registerMockServiceWorker();
    
    if (swRegistration) {
      console.log('Mock API system fully initialized with service worker');
    } else {
      console.warn('Mock API initialized without service worker - using direct mocking');
      // Enable direct fetch mocking as fallback if service worker fails
      if (window.mockApi && typeof window.mockApi.enableDirectMocking === 'function') {
        window.mockApi.enableDirectMocking();
      }
    }
  } catch (error) {
    console.error('Error in mock API initialization:', error);
    // Enable alternate direct fetch mocking as a fallback
    if (window.mockApi && typeof window.mockApi.enableDirectMocking === 'function') {
      window.mockApi.enableDirectMocking();
    }
  }
};

// Start the registration process
if (typeof window !== 'undefined') {
  // Run when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initMockApiSystem);
  } else {
    initMockApiSystem();
  }
}

// Make functions available globally
window.registerMockServiceWorker = registerMockServiceWorker;
window.initMockApiSystem = initMockApiSystem;

/**
 * Database Manager Test Script
 *
 * This script tests the unified database manager that can automatically
 * select the best connection method based on the environment.
 *
 * Usage: node test-db-manager.js [environment_name]
 * Example: node test-db-manager.js qa01
 *
 * You can also force a specific connection method:
 * - node test-db-manager.js qa01 direct
 * - node test-db-manager.js qa01 tunnel
 */

// Import database manager
const dbManager = require('./database');

// Process command-line arguments
const args = process.argv.slice(2);
const environment = args[0] || 'qa01';
const connectionMethod = args[1] || 'auto';

// Enable debug mode
process.env.DB_DEBUG = 'true';

// Main test function
async function runTest() {
  try {
    console.log(`\n=== TESTING DATABASE MANAGER WITH ${environment.toUpperCase()} ENVIRONMENT ===\n`);
    console.log(`Connection method: ${connectionMethod === 'auto' ? 'AUTO' : connectionMethod.toUpperCase()}`);

    // Initialize database connection
    console.log('\n[Step 1] Initializing database connection...');

    const options = {};
    if (connectionMethod === 'direct') {
      options.forceDirect = true;
    } else if (connectionMethod === 'tunnel') {
      options.forceTunnel = true;
    }

    await dbManager.init(environment, options);

    // Get connection info
    console.log('\n[Step 2] Connection information:');
    const connectionInfo = dbManager.getConnectionInfo();
    console.log(JSON.stringify(connectionInfo, null, 2));

    // Test basic query
    console.log('\n[Step 3] Testing basic query...');
    const result = await dbManager.query('SELECT 1 AS test_value');
    console.log('Query result:', JSON.stringify(result));

    // Test version query
    console.log('\n[Step 4] Getting database version...');
    const versionResult = await dbManager.query('SELECT VERSION() AS version');
    console.log('Version result:', JSON.stringify(versionResult));

    // Test a more complex query
    console.log('\n[Step 5] Testing more complex query...');

    // Find test case IDs in current environment based on environment name
    const testCaseId = environment === 'qa01' ? 1279 : 3180;
    const testSuiteId = environment === 'qa01' ? 82 : 312;

    console.log(`Using test case ID: ${testCaseId}, test suite ID: ${testSuiteId}`);

    const testCaseQuery = `
      SELECT tc_id, COUNT(*) AS execution_count
      FROM test_result
      WHERE tc_id = ${testCaseId}
      GROUP BY tc_id
    `;
    const testCaseResult = await dbManager.query(testCaseQuery);
    console.log('Test case result:', JSON.stringify(testCaseResult));

    // Test transaction
    console.log('\n[Step 6] Testing transaction support...');
    try {
      const transactionResult = await dbManager.transaction(async (tx) => {
        // This is just a read-only transaction for testing
        const result = await tx.query(`
          SELECT COUNT(DISTINCT tc_id) AS test_case_count
          FROM test_case_group
          WHERE ts_id = ${testSuiteId}
        `);

        console.log('Transaction query result:', JSON.stringify(result));
        return result;
      });

      console.log('Transaction completed successfully');
    } catch (transactionError) {
      console.error('Transaction failed:', transactionError.message);
    }

    // Close connection
    console.log('\n[Step 7] Closing database connection...');
    await dbManager.close();

    console.log('\n=== TEST COMPLETED SUCCESSFULLY ===');
    return true;
  } catch (error) {
    console.error('\n❌ TEST FAILED:', error);

    // Try to close the connection even if test fails
    try {
      await dbManager.close();
    } catch (closeError) {
      // Ignore close errors during cleanup
    }

    return false;
  }
}

// Execute the test
runTest()
  .then(success => {
    if (success) {
      console.log('\nAll tests passed! Database manager is working properly.');
      process.exit(0);
    } else {
      console.error('\nTests failed. Please check the error messages above.');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('Unhandled error:', error);
    process.exit(1);
  });
# Dashboard Refactoring Plan: `api-integration.js`

This document outlines the detailed plan to refactor the monolithic `frontend/dashboard/api-integration.js` file into smaller, feature-focused modules. The goal is to improve code organization, maintainability, and readability without altering existing functionality.

## 1. New File Structure

The `frontend/dashboard/` directory will be reorganized with the following new modules:

- **`dashboard-config.js`**: Centralized configuration, state, and DOM element references.
- **`dashboard-api.js`**: Handles all communication with the backend API service.
- **`dashboard-ui.js`**: Manages general UI updates, notifications, and formatting utilities.
- **`dashboard-test-suites.js`**: Manages fetching, rendering, and interactions for test suites.
- **`dashboard-active-tests.js`**: Manages the "Active Tests" panel and status polling.
- **`dashboard-recent-runs.js`**: Manages the "Recent Runs" table.
- **`dashboard-details-modal.js`**: Manages the "View Details" modal.
- **`dashboard-main.js`**: The main orchestrator, replacing `api-integration.js`, to initialize and coordinate all modules.

---

## 2. Function and Property Migration Map

The `DashboardApiIntegration` class will be dismantled, and its methods and properties will be migrated to the new modules as follows:

### -> `dashboard-config.js`
This module will export a shared configuration object.

**State Variables:**
- `activeTests = new Map()`
- `recentRunsCache = []`
- `highestRecentTsnId = null`
- `testDetailsCache = {}`
- `_runningTestSuites = new Set()`
- `predefinedSuites = {}`
- `availableTestCases = []`

**Interval/Timeout IDs:**
- `statusPollingInterval = null`
- `recentRunsPollingInterval = null`

**Flags:**
- `_isUpdatingStatuses = false`

**Constants:**
- `MAX_API_CHECK_ATTEMPTS = 10`

**DOM Elements:**
- A comprehensive object containing all relevant DOM element references for the dashboard.

### -> `dashboard-api.js`
This module will abstract all backend communication.

**Functions:**
- `checkApiServiceAvailability()`
- `initializeApiService()`
- `runTestCase(tcId, params)`
- `runTestSuite(suiteId, suiteName)`
- `runMultipleTestSuites(suiteIds)`
- `getRecentRuns(sinceTsnId)`
- `getTestSuites()`
- `getFilterOptions()`
- `getFilteredTestSuites(filters)`
- `getTestDetails(tsnId)`
- `stopTestRun(tsnId)`
- `getAvailableTestCases()`

### -> `dashboard-ui.js`
This module will handle general UI updates and utilities.

**Functions:**
- `showNotification(message, title, type, duration)` (and its wrappers: `info`, `success`, `warning`, `error`)
- `incrementCounter(counterType, amount)`
- `getStatusColor(status)`
- `formatTime(dateString)`
- `formatUserEmail(email)`

### -> `dashboard-test-suites.js`
This module will manage the entire test suite selection feature.

**Functions:**
- `initializeTestSuites()` (Orchestrator for this module)
- `loadTestSuites()`
- `renderTestSuites(suites)`
- `setupTestSuiteSelectionHandlers()`
- `handleRunSuiteClick(event)`
- `handleRunFilteredSuitesClick()`
- `updateRunFilteredButtonState()`
- `loadFilterOptions()`
- `populateFilterDropdowns(options)`
- `handleFilterChange()`
- `loadAvailableTestCases()`
- `loadFreshTestCases()`
- `renderAvailableTestCasesOptimized(testCases)`

### -> `dashboard-active-tests.js`
This module will manage the active tests panel and polling.

**Functions:**
- `startRecentRunsPolling()`
- `pollRecentRuns()`
- `updateActiveTests(content)`
- `updateActiveTestsFromRecentRuns(newRuns)`
- `handleStopTestRunClick(event)`
- `ensureStringTsnId(tsnId)`
- `updateTestStatuses()` (The deprecated function, kept for reference)

### -> `dashboard-recent-runs.js`
This module will manage the recent runs history table.

**Functions:**
- `initializeRecentRuns()`
- `renderRecentTests(recentTests)`
- `refreshRecentRuns()`
- `updateRecentRunsTable(recentRuns)`
- `updateFromRecentRuns()`

### -> `dashboard-details-modal.js`
This module will manage the test details modal.

**Functions:**
- `viewTestDetails(tsnId)`
- `showTestDetails(tsnId, testData)`
- `createTestDetailsModal(tsnId, testData)`
- `createDetailsSection(title, data)`
- `createKeyValueTable(data)`
- `createTestCasesTable(testCases)`
- `createErrorCell(testCase)`

### -> `dashboard-main.js` (New Orchestrator)
This file will replace `api-integration.js` and `dashboard.js`'s initialization logic.

**Functions:**
- `initializeDashboard()`: The main entry point to be called on `DOMContentLoaded`.
- It will call the initialization functions from all other modules in the correct order.
- `loadDashboardData()`: The initial data fetch orchestrator.
- The `DashboardApiIntegration` class will be completely removed.

---

## 3. Execution Steps

1.  Create the new empty `.js` files in `frontend/dashboard/` as per the plan.
2.  Create `dashboard-config.js` and populate it with state variables, constants, and DOM element selectors.
3.  Systematically move functions from `api-integration.js` into their respective new modules, replacing internal references (`this.*`) with function calls and shared state from the config module.
4.  Update all new modules to import dependencies from each other and from the config file.
5.  Refactor `dashboard-main.js` to be the central orchestrator, setting up event listeners and calling the initialization functions for all other modules.
6.  Update `index.html` to remove the script tag for `api-integration.js` and add script tags for all the new modules, ensuring `dashboard-config.js` and `dashboard-api.js` are loaded first, and `dashboard-main.js` is loaded last.
7.  Delete the original `api-integration.js` file once all logic has been migrated and verified.

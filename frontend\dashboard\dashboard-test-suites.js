/**
 * dashboard-test-suites.js
 *
 * This module manages all functionality related to test suite selection and execution.
 * It handles fetching suite data, rendering the filterable card-based UI,
 * and processing all user interactions for selecting and running tests.
 */

import { config } from './dashboard-config.js';
import * as api from './dashboard-api.js';
import { notifications } from './dashboard-ui.js';

/**
 * Initializes all test suite functionalities.
 */
export function initializeTestSuites() {
    loadPredefinedTestSuites();
    setupFilterHandlers();
    loadFilterOptions();
    handleFilterChange(); // Initial load of filtered suites
    setupCustomRunHandler();
}

/**
 * Loads and renders the initial set of predefined test suites.
 */
async function loadPredefinedTestSuites() {
    try {
        const suites = await api.getTestSuites();
        console.log('TEST SUITES API RESPONSE:', suites);
        config.state.predefinedSuites = suites;
        console.log('PREDEFINED SUITES TO RENDER:', suites.predefined || []);
        renderPredefinedTestSuites(suites.predefined || []);
    } catch (error) {
        console.error('Error loading predefined test suites:', error);
        notifications.error('Failed to load predefined test suites.', 'Loading Error');
    }
}

/**
 * Renders the predefined test suite cards.
 * @param {Array} suites - An array of predefined suite objects.
 */
function renderPredefinedTestSuites(suites) {
    const container = config.elements.predefinedSuitesContainer;
    if (!container) return;
    
    // Check if there are static cards already present
    const staticCardsExist = container.querySelector('.ms-predefined-grid');
    
    // If there are no dynamic suites to render, preserve the static cards
    if (!suites || suites.length === 0) {
        console.log('No dynamic predefined suites to render, keeping static cards');
        return; // Just keep the static content
    }
    
    // If we have static cards and dynamic suites, append the dynamic ones after static content
    if (staticCardsExist) {
        console.log('Found static predefined suite cards, appending dynamic ones');
        
        // Create a container for dynamic cards
        let dynamicContainer = container.querySelector('.dynamic-predefined-suites');
        if (!dynamicContainer) {
            dynamicContainer = document.createElement('div');
            dynamicContainer.className = 'dynamic-predefined-suites';
            container.appendChild(dynamicContainer);
        } else {
            dynamicContainer.innerHTML = ''; // Clear previous dynamic content
        }
        
        // Add dynamic cards
        dynamicContainer.innerHTML = suites.map(suite => `
            <div class="ms-predefined-card">
                <h3 class="ms-font-l">${suite.name}</h3>
                <p>${suite.description}</p>
                <button class="ms-Button ms-Button--primary run-suite-btn" data-suite-id="${suite.id}" data-suite-name="${suite.name}">
                    <span class="ms-Button-label">Run Test</span>
                </button>
            </div>
        `).join('');
        
        // Attach event listeners to the newly created buttons
        dynamicContainer.querySelectorAll('.run-suite-btn').forEach(button => {
            button.addEventListener('click', handleRunSuiteClick);
        });
    } else {
        // No static cards exist, replace content with dynamic cards
        container.innerHTML = suites.map(suite => `
            <div class="ms-predefined-card">
                <h3 class="ms-font-l">${suite.name}</h3>
                <p>${suite.description}</p>
                <button class="ms-Button ms-Button--primary run-suite-btn" data-suite-id="${suite.id}" data-suite-name="${suite.name}">
                    <span class="ms-Button-label">Run Test</span>
                </button>
            </div>
        `).join('');
        
        // Attach event listeners to the newly created buttons
        container.querySelectorAll('.run-suite-btn').forEach(button => {
            button.addEventListener('click', handleRunSuiteClick);
        });
    }
}

/**
 * Handles the click event for running a single test suite.
 * @param {Event} event - The click event.
 */
async function handleRunSuiteClick(event) {
    const button = event.currentTarget;
    const suiteId = button.dataset.suiteId;
    const suiteName = button.dataset.suiteName;
    
    console.log('Run Suite Button clicked:', { suiteId, suiteName, button });
    
    // Disable the button to prevent double-clicking
    const originalText = button.innerHTML;
    button.disabled = true;
    button.innerHTML = '<span class="ms-Button-label">Starting...</span>';

    if (config.state.runningTestSuites.has(suiteId)) {
        console.log('Test suite already running, preventing duplicate run');
        notifications.warning(`Test suite "${suiteName}" is already running.`, 'Already Running');
        button.disabled = false;
        button.innerHTML = originalText;
        return;
    }

    try {
        console.log(`Starting test suite ID: ${suiteId}, Name: ${suiteName}`);
        notifications.info(`Starting test suite: "${suiteName}"`, 'Test Run Started');
        config.state.runningTestSuites.add(suiteId);
        
        const response = await api.runTestSuite(suiteId, suiteName);
        console.log('Test suite run API response:', response);
        
        // Show test session ID in notification
        if (response && response.tsn_id) {
            notifications.success(`Test suite "${suiteName}" started with session ID: ${response.tsn_id}`, 'Test Started');
        }
    } catch (error) {
        console.error(`Error running test suite ${suiteId}:`, error);
        notifications.error(`Failed to start test suite "${suiteName}".`, 'API Error');
        config.state.runningTestSuites.delete(suiteId);
    } finally {
        // Re-enable the button with original text
        button.disabled = false;
        button.innerHTML = originalText;
    }
}

/**
 * Sets up event listeners for the filter dropdowns and the 'Run Filtered' button.
 */
function setupFilterHandlers() {
    config.elements.suiteProjectFilter?.addEventListener('change', handleFilterChange);
    config.elements.suiteLevelFilter?.addEventListener('change', handleFilterChange);
    config.elements.suiteVersionFilter?.addEventListener('change', handleFilterChange);
    config.elements.runFilteredSuitesBtn?.addEventListener('click', handleRunFilteredSuitesClick);
}

/**
 * Loads and populates the filter dropdowns with options from the API.
 */
async function loadFilterOptions() {
    try {
        const options = await api.getFilterOptions();
        populateFilterDropdowns(options);
    } catch (error) {
        console.error('Error loading filter options:', error);
    }
}

/**
 * Populates the filter dropdowns with the provided options.
 * @param {object} options - An object containing arrays of options for each filter.
 */
function populateFilterDropdowns(options) {
    const { projects, levels, versions } = options;
    const projectFilter = config.elements.suiteProjectFilter;
    const levelFilter = config.elements.suiteLevelFilter;
    const versionFilter = config.elements.suiteVersionFilter;

    if (projectFilter && projects) {
        projectFilter.innerHTML = '<option value="">All Projects</option>' + projects.map(p => `<option value="${p.pj_id}">${p.pj_name}</option>`).join('');
    }
    if (levelFilter && levels) {
        levelFilter.innerHTML = '<option value="">All Levels</option>' + levels.map(l => `<option value="${l}">${l}</option>`).join('');
    }
    if (versionFilter && versions) {
        versionFilter.innerHTML = '<option value="">All Versions</option>' + versions.map(v => `<option value="${v}">${v}</option>`).join('');
    }
}

/**
 * Handles changes in the filter selections to load and display matching test suites.
 */
async function handleFilterChange() {
    const filters = {
        project: config.elements.suiteProjectFilter?.value,
        level: config.elements.suiteLevelFilter?.value,
        version: config.elements.suiteVersionFilter?.value,
    };

    try {
        const suites = await api.getFilteredTestSuites(filters);
        renderFilteredTestSuites(suites);
    } catch (error) {
        console.error('Error fetching filtered test suites:', error);
        config.elements.filteredSuitesContainer.innerHTML = '<p class="ms-font-m ms-fontColor-error">Failed to load test suites.</p>';
    }
}

/**
 * Renders the filtered test suite cards in the UI.
 * @param {Array} suites - An array of suite objects to render.
 */
function renderFilteredTestSuites(suites) {
    const container = config.elements.filteredSuitesContainer;
    if (!container) return;

    if (suites.length === 0) {
        container.innerHTML = '<p class="ms-font-m">No test suites match the selected criteria.</p>';
        return;
    }

    container.innerHTML = suites.map(suite => `
        <div class="ms-test-suite-card" data-suite-id="${suite.ts_id}">
            <div class="ms-card-header">
                <h4 class="ms-card-title">${suite.ts_name}</h4>
                <input type="checkbox" class="ms-card-checkbox" data-suite-id="${suite.ts_id}">
            </div>
            <div class="ms-card-body">
                <p><strong>Level:</strong> ${suite.level}</p>
                <p><strong>Version:</strong> ${suite.version}</p>
                <p><strong>ID:</strong> ${suite.ts_id}</p>
            </div>
            <div class="ms-card-footer">
                <a href="${suite.details_url}" target="_blank">View Details</a>
                <button class="ms-Button ms-Button--primary run-suite-btn" data-suite-id="${suite.ts_id}" data-suite-name="${suite.ts_name}">Run Suite</button>
            </div>
        </div>
    `).join('');

    // Add event listeners for selection and running individual suites
    container.querySelectorAll('.ms-test-suite-card').forEach(card => {
        card.addEventListener('click', (e) => {
            if (e.target.type !== 'checkbox' && e.target.tagName !== 'BUTTON' && e.target.tagName !== 'A') {
                const checkbox = card.querySelector('.ms-card-checkbox');
                checkbox.checked = !checkbox.checked;
                updateRunFilteredButtonState();
            }
        });
    });

    container.querySelectorAll('.run-suite-btn').forEach(button => {
        button.addEventListener('click', handleRunSuiteClick);
    });

    container.querySelectorAll('.ms-card-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', updateRunFilteredButtonState);
    });

    updateRunFilteredButtonState();
}

/**
 * Updates the state (enabled/disabled) of the 'Run Selected Suites' button.
 */
function updateRunFilteredButtonState() {
    const selectedSuites = config.elements.filteredSuitesContainer.querySelectorAll('.ms-card-checkbox:checked');
    const button = config.elements.runFilteredSuitesBtn;
    if (button) {
        button.disabled = selectedSuites.length === 0;
    }
}

/**
 * Handles the click event to run all selected filtered test suites.
 */
async function handleRunFilteredSuitesClick() {
    const selectedCheckboxes = config.elements.filteredSuitesContainer.querySelectorAll('.ms-card-checkbox:checked');
    const suiteIds = Array.from(selectedCheckboxes).map(cb => cb.dataset.suiteId);

    if (suiteIds.length === 0) {
        notifications.warning('No test suites selected.', 'Selection Required');
        return;
    }

    try {
        notifications.info(`Starting ${suiteIds.length} selected test suites.`, 'Bulk Run Started');
        await api.runMultipleTestSuites(suiteIds);
    } catch (error) {
        console.error('Error running multiple test suites:', error);
        notifications.error('Failed to start the selected test suites.', 'API Error');
    }
}

/**
 * Sets up the event handler for the custom test case run button.
 */
function setupCustomRunHandler() {
    config.elements.runCustomTcButton?.addEventListener('click', async () => {
        const tcIdInput = config.elements.customTcIdInput;
        const tcId = tcIdInput.value.trim();

        if (!tcId || !/^-?\d+$/.test(tcId)) {
            notifications.error('Please enter a valid numeric Test Case ID.', 'Invalid Input');
            return;
        }

        try {
            notifications.info(`Starting Test Case ID: ${tcId}`, 'Test Run Started');
            await api.runTestCase(parseInt(tcId, 10));
            tcIdInput.value = ''; // Clear input on success
        } catch (error) {
            console.error(`Error running test case ${tcId}:`, error);
            notifications.error(`Failed to start Test Case ID ${tcId}.`, 'API Error');
        }
    });
}
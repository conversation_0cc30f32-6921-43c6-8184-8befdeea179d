/**
 * Authentication Routes
 * Provides endpoints for login, logout, token refresh, and session management
 */

const express = require('express');
const router = express.Router();
const authService = require('../auth/auth-service');
const sessionManager = require('../auth/session-manager');
const { rateLimiters } = require('../middleware/security');
const ValidationUtils = require('../utils/validation');
const userManager = require('../auth/user-manager');

/**
 * Login endpoint
 * POST /auth/login
 */
router.post('/login', rateLimiters.auth, async (req, res) => {
  try {
    const { uid, password } = req.body;
    const clientIp = req.ip || req.connection.remoteAddress || 'unknown';
    const userAgent = req.headers['user-agent'] || 'unknown';

    // Authenticate user
    const authResult = await authService.authenticate(uid, password, clientIp);

    if (authResult.success) {
      // Set secure HTTP-only cookies for tokens
      const cookieOptions = {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production', // HTTPS only in production
        sameSite: 'strict',
        path: '/'
      };

      // Set access token cookie (short-lived)
      res.cookie('accessToken', authResult.accessToken, {
        ...cookieOptions,
        maxAge: authResult.expiresIn * 1000 // Convert to milliseconds
      });

      // Set refresh token cookie (long-lived)
      res.cookie('refreshToken', authResult.refreshToken, {
        ...cookieOptions,
        maxAge: 7 * 24 * 60 * 60 * 1000 // 7 days
      });

      // Set session ID cookie for client-side reference
      res.cookie('sessionId', authResult.sessionId, {
        ...cookieOptions,
        httpOnly: false // Allow client-side access for session management
      });

      // Return success response (without sensitive tokens in body)
      res.json({
        success: true,
        user: {
          uid: authResult.user.uid,
          role: authResult.user.role,
          name: authResult.user.name
        },
        permissions: authResult.permissions,
        expiresIn: authResult.expiresIn,
        tokenType: authResult.tokenType
      });

      console.log(`✅ Login successful: ${authResult.user.uid} from ${clientIp}`);
    } else {
      res.status(authResult.code === 'RATE_LIMITED' || authResult.code === 'ACCOUNT_LOCKED' ? 429 : 401).json({
        success: false,
        message: authResult.error,
        code: authResult.code,
        retryAfter: authResult.retryAfter
      });
    }
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error during login'
    });
  }
});

/**
 * Logout endpoint
 * POST /auth/logout
 */
router.post('/logout', async (req, res) => {
  try {
    const sessionId = req.cookies.sessionId || req.body.sessionId;
    const accessToken = req.cookies.accessToken || req.headers.authorization?.replace('Bearer ', '');

    if (sessionId) {
      await authService.logout(sessionId, accessToken);
    }

    // Clear all auth cookies
    const cookieOptions = {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      path: '/'
    };

    res.clearCookie('accessToken', cookieOptions);
    res.clearCookie('refreshToken', cookieOptions);
    res.clearCookie('sessionId', { ...cookieOptions, httpOnly: false });

    res.json({
      success: true,
      message: 'Logged out successfully'
    });

    console.log(`✅ Logout successful: ${sessionId}`);
  } catch (error) {
    console.error('Logout error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error during logout'
    });
  }
});

/**
 * Token refresh endpoint
 * POST /auth/refresh
 */
router.post('/refresh', async (req, res) => {
  try {
    const refreshToken = req.cookies.refreshToken || req.body.refreshToken;

    if (!refreshToken) {
      return res.status(401).json({
        success: false,
        message: 'Refresh token required',
        code: 'REFRESH_TOKEN_MISSING'
      });
    }

    const refreshResult = authService.refreshAccessToken(refreshToken);

    if (refreshResult.success) {
      // Set new access token cookie
      const cookieOptions = {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        path: '/',
        maxAge: refreshResult.expiresIn * 1000
      };

      res.cookie('accessToken', refreshResult.accessToken, cookieOptions);

      res.json({
        success: true,
        expiresIn: refreshResult.expiresIn,
        tokenType: refreshResult.tokenType
      });

      console.log(`✅ Token refreshed successfully`);
    } else {
      // Clear cookies if refresh failed
      const cookieOptions = {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        path: '/'
      };

      res.clearCookie('accessToken', cookieOptions);
      res.clearCookie('refreshToken', cookieOptions);
      res.clearCookie('sessionId', { ...cookieOptions, httpOnly: false });

      res.status(401).json({
        success: false,
        message: refreshResult.error,
        code: refreshResult.code
      });
    }
  } catch (error) {
    console.error('Token refresh error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error during token refresh'
    });
  }
});

/**
 * Session validation endpoint
 * GET /auth/validate
 */
router.get('/validate', (req, res) => {
  try {
    const accessToken = req.cookies.accessToken || req.headers.authorization?.replace('Bearer ', '');

    if (!accessToken) {
      return res.status(401).json({
        success: false,
        message: 'Access token required',
        code: 'ACCESS_TOKEN_MISSING'
      });
    }

    const validation = authService.validateAccessToken(accessToken);

    if (validation.valid) {
      res.json({
        success: true,
        user: {
          uid: validation.user.uid,
          role: validation.user.role,
          name: validation.user.name
        },
        permissions: validation.decoded.permissions,
        expiresAt: validation.decoded.exp * 1000 // Convert to milliseconds
      });
    } else {
      res.status(401).json({
        success: false,
        message: validation.error,
        code: validation.code
      });
    }
  } catch (error) {
    console.error('Session validation error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error during validation'
    });
  }
});

/**
 * Logout all sessions for a user
 * POST /auth/logout-all
 */
router.post('/logout-all', async (req, res) => {
  try {
    const { uid } = req.body;

    if (!uid) {
      return res.status(400).json({
        success: false,
        message: 'User ID required'
      });
    }

    // Get all sessions for the user
    const sessions = sessionManager.getUserSessions(uid);

    // Revoke all sessions
    let revokedCount = 0;
    for (const session of sessions) {
      const success = sessionManager.revokeSession(session.id);
      if (success) {
        revokedCount++;
      }
    }

    // Clear all auth cookies for current request
    const cookieOptions = {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      path: '/'
    };

    res.clearCookie('accessToken', cookieOptions);
    res.clearCookie('refreshToken', cookieOptions);
    res.clearCookie('sessionId', { ...cookieOptions, httpOnly: false });

    res.json({
      success: true,
      message: `Logged out from ${revokedCount} sessions`,
      revokedSessions: revokedCount
    });

    console.log(`✅ Logout all sessions: ${uid} (${revokedCount} sessions revoked)`);
  } catch (error) {
    console.error('Logout all sessions error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error during logout'
    });
  }
});

/**
 * Get user sessions endpoint (admin only)
 * GET /auth/sessions/:uid
 */
router.get('/sessions/:uid', async (req, res) => {
  try {
    // This would typically require admin authentication
    const { uid } = req.params;

    const sessions = sessionManager.getUserSessions(uid);

    res.json({
      success: true,
      sessions: sessions
    });
  } catch (error) {
    console.error('Get sessions error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

/**
 * Get session statistics endpoint (admin only)
 * GET /auth/stats
 */
router.get('/stats', (req, res) => {
  try {
    const stats = sessionManager.getStats();

    res.json({
      success: true,
      stats: stats
    });
  } catch (error) {
    console.error('Get stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

/**
 * Get external API password for current authenticated user
 * GET /auth/external-password
 */
router.get('/external-password', async (req, res) => {
  try {
    // Check if user is authenticated
    if (!req.user || !req.user.uid) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    const uid = req.user.uid;
    console.log(`Getting external API password for user: ${uid}`);

    // Get user details from user manager
    const user = await userManager.getUserByUid(uid);

    if (!user) {
      console.warn(`User not found in user manager: ${uid}`);
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // For external API compatibility, return the user's password
    // Note: This is the plaintext password stored in allowed-users.json for external API access
    let externalPassword = user.password;

    // If password is hashed (starts with $2b$), use default password
    if (externalPassword && externalPassword.startsWith('$2b$')) {
      console.log(`User ${uid} has hashed password, using default for external API`);
      externalPassword = 'test'; // Default password for external API
    }

    res.json({
      success: true,
      password: externalPassword || 'test' // Fallback to default
    });

  } catch (error) {
    console.error('Get external password error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

module.exports = router;

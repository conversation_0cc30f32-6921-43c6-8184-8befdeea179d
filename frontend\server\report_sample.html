

<html>
<head>
<title>Test Result Summary</title>
<script type="text/javascript" src="jquery-ui-1.11.0.custom/external/jquery/jquery.js"></script>
<script src="js/lib.js"></script>
<script src="js/menu.js"></script>
<link rel="stylesheet" type="text/css" href="css/table.css">
</head>
<body>
	<a href="home" class="clickable">Home</a> &nbsp;
<span id="case" class="clickable">Case#</span> &nbsp;
<span id="suite" class="clickable">Suite#</span> &nbsp;
<span id="project" class="clickable">Project#</span> &nbsp;
<a href="ReportList?tp_id=201&tp_id=202" class="clickable">Report</a>
<p/>
	Result Summary<p/>
	Test Session ID: 13732<br/>
	Session Owner: <EMAIL><br/>
	
	
	Test Case ID: <a href="CaseEditor?tc_id=3180" class="clickable">3180</a><br/>
	Start Time: 2025-04-15 13:12:04<br/>
	End Time: 2025-04-15 13:12:14<br/>
	Error: 1:3/3<p/>
	Report <ul><li><span style='color:red'>FAIL</span> Case: <a href='http://mprts-qa02.lab.wagerworks.com:5080/AutoRun/CaseEditor?tc_id=3180'>3180</a> &nbsp;check NO pending feeds
</li></ul>Case(s) passed: 0<br/>Case(s) failed: 1<br/>Debug Report: <a href='http://mprts-qa02.lab.wagerworks.com:5080/AutoRun/ReportDetails?tsn_id=13732'>http://mprts-qa02.lab.wagerworks.com:5080/AutoRun/ReportDetails?tsn_id=13732</a><br/><br/>Variables:<br/>user_id=<EMAIL><br/>username=<EMAIL><br/>environment=qa02<br/>shell_host=jps-qa10-app01<br/>file_path=/home/<USER>/<br/>operatorConfigs=operatorNameConfigs<br/>kafka_server=kafka-qa-a0.lab.wagerworks.com<br/>dataCenter=GU<br/>rgs_env=qa02<br/>old_version=0<br/>networkType1=multi-site<br/>networkType2=multi-site<br/>sign=-<br/>rate_src=local<br/>uid=<EMAIL><br/>envir=qa02<br/><br/>
ID: <a href="ProfileLoader" class="clickable"><EMAIL></a><p/>

</body>
</html>

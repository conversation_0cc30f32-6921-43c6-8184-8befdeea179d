# Function Registry for frontend/reports/reports.js

This document lists all functions found in `frontend/reports/reports.js`, along with their parameters and line numbers.

| Function Name                     | Parameters                               | Position (Start-End Line) |
|-----------------------------------|------------------------------------------|---------------------------|
| `createTableLoadingOverlay`       | (none)                                   | 71-123                    |
| `createProgressBar`               | (none)                                   | 125-163                   |
| `showLoadingIndicators`           | `isIncrementalRefresh = false`           | 165-233                   |
| `hideLoadingIndicators`           | (none)                                   | 235-267                   |
| `loadReportsData`                 | `options = {}`                           | 269-394                   |
| `refreshReports`                  | (none)                                   | 396-403                   |
| `getSelectedTimeRange`            | (none)                                   | 405-408                   |
| `getSelectedTimezone`             | (none)                                   | 410-413                   |
| `saveSelectedTimezone`            | `timezone`                               | 415-418                   |
| `initializeCharts`                | (none)                                   | 420-488                   |
| `updateCharts`                    | `reports`                                | 490-535                   |
| `formatUserEmail`                 | `email`                                  | 537-563                   |
| `updateRefreshStatus`             | (none)                                   | 565-596                   |
| `createUserCell`                  | `email, displayName`                     | 598-606                   |
| `formatDateTime`                  | `dateTimeString`                         | 608-665                   |
| `calculateDuration`               | `startTime, endTime`                     | 667-701                   |
| `refreshAllTimestamps`            | (none)                                   | 703-736                   |
| `setupEventListeners`             | (none)                                   | 738-855                   |
| `displayError`                    | `message`                                | 857-870                   |
| `initializeFilters`               | (none)                                   | 872-899                   |
| `getCredentials`                  | (none)                                   | 946-973                   |
| `loadReportsFromExternalApi`      | `credentials`                            | 975-1026                  |
| `loadReportsFromDatabaseApi`      | `credentials`                            | 1028-1120                 |
| `transformRecentRunsData`         | `recentRuns`                             | 1122-1133                 |
| `displayReports`                  | `reportsInput, isIncremental = false`    | 1138-1344                 |
| `displayEmptyMessage`             | (none)                                   | 1346-1352                 |
| `updateReportsTable`              | (none)                                   | 1354-1435                 |
| `createCell`                      | `content, className`                     | 1437-1450                 |
| `createDurationCell`              | `durationText, startTime, endTime`       | 1452-1483                 |
| `createUserCell`                  | `email, name`                            | 1485-1496                 |
| `createActionCell`                | `tsnId`                                  | 1498-1520                 |
| `getStatusClass`                  | `status`                                 | 1522-1545                 |
| `formatUserEmail`                 | `email`                                  | 1547-1583                 |
| `loadTestDetails`                 | `testId`                                 | 1585-1725                 |
| `loadTestDetailsFromExternalApi`  | `testId, credentials`                    | 1727-1779                 |
| `loadTestDetailsFromDatabaseApi`  | `testId, credentials`                    | 1781-1893                 |
| `displayTestDetails`              | (none)                                   | 1895-2025                 |
| `calculateDuration`               | `startTime, endTime`                     | 2027-2066                 |
| `updateTestCasesTable`            | `testCases`                              | 2068-2135                 |
| `updateCounters`                  | (none)                                   | 2137-2178                 |
| `exportReports`                   | (none)                                   | 2180-2227                 |
| `initializeDataTable`             | (none)                                   | 2229-2269                 |
| `setupCustomFilters`              | (none)                                   | 2271-2331                 |
| `populateUserFilter`              | (none)                                   | 2333-2415                 |
| `refreshAllTimestamps`            | (none)                                   | 2455-2522                 |
| `rerunFailedTests`                | `testId`                                 | 2524-2607                 |

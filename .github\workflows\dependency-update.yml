name: Automated Dependency Updates

# Temporarily disabled for test corrections - manual trigger only
on:
  workflow_dispatch:

jobs:
  update-dependencies:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        token: ${{ secrets.GITHUB_TOKEN }}

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'

    - name: Check for dependency updates
      run: |
        npm outdated --json > outdated.json || true
        
    - name: Update dependencies
      run: |
        npm update
        npm audit fix --force

    - name: Run tests after updates
      run: |
        npm install
        npm test
        npm run build

    - name: Create Pull Request
      uses: peter-evans/create-pull-request@v5
      with:
        token: ${{ secrets.GITHUB_TOKEN }}
        commit-message: 'chore: update dependencies'
        title: '🔧 Automated Dependency Updates'
        body: |
          ## 📦 Dependency Updates
          
          This PR contains automated dependency updates.
          
          ### Changes:
          - Updated npm dependencies to latest compatible versions
          - Ran security audit fixes
          - All tests passing ✅
          
          ### Testing:
          - [x] Unit tests pass
          - [x] Build succeeds
          - [x] Security audit clean
          
          **Auto-generated by GitHub Actions** 🤖
          
        branch: chore/dependency-updates
        delete-branch: true
        assignees: ${{ github.actor }}
        reviewers: ${{ github.actor }}

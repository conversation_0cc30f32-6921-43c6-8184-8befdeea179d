/**
 * Mock Service Worker for SmartTest Frontend
 * 
 * This service worker intercepts API requests and returns mock responses.
 * It is used for development and testing purposes only.
 */

// IMPORTANT: This file will be installed as a service worker and
// must execute in a ServiceWorkerGlobalScope

// Cache name for the service worker
const CACHE_NAME = 'smarttest-mock-api-cache-v1';

// Flag to track if mocking is active
let mockingActive = false;

// Add service worker event listeners
self.addEventListener('install', event => {
  console.log('[Mock SW] Service Worker installed');
  
  // Skip waiting to ensure the service worker activates immediately
  event.waitUntil(self.skipWaiting());
});

self.addEventListener('activate', event => {
  console.log('[Mock SW] Service Worker activated');
  
  // Claim all clients to ensure the service worker controls all pages
  event.waitUntil(self.clients.claim());
});

// Listen for messages from the page
self.addEventListener('message', event => {
  console.log('[Mock SW] Message received', event.data);
  
  if (event.data && event.data.type === 'MOCK_ACTIVATE') {
    mockingActive = true;
    console.log('[Mock SW] Mock API activated');
  } else if (event.data && event.data.type === 'MOCK_DEACTIVATE') {
    mockingActive = false;
    console.log('[Mock SW] Mock API deactivated');
  }
});

// Intercept fetch requests
self.addEventListener('fetch', event => {
  // Only intercept requests if the mocking is active
  if (!mockingActive) {
    return;
  }

  const url = new URL(event.request.url);

  // Skip chrome-extension URLs and external resources
  if (url.protocol === 'chrome-extension:' ||
      url.hostname === 'fonts.googleapis.com' ||
      url.hostname === 'fonts.gstatic.com' ||
      (!url.hostname.includes('localhost') && !url.hostname.includes('127.0.0.1'))) {
    // Let these requests pass through without interception
    return;
  }

  // Only intercept API requests
  if (url.pathname.includes('/AutoRun/') || url.pathname.includes('/api/')) {
    console.log(`[Mock SW] Intercepting request: ${url.pathname}`);

    // Handle the request with our mock API
    event.respondWith(handleApiRequest(event.request.clone()));
  }
});

/**
 * Handle API requests and generate mock responses
 * @param {Request} request - The fetch request
 * @returns {Promise<Response>} Mock API response
 */
async function handleApiRequest(request) {
  try {
    const url = new URL(request.url);
    const endpoint = url.pathname.split('/').pop();
    
    console.log(`[Mock SW] Processing ${request.method} request for: ${endpoint}`);
    
    // Extract request data based on the request method
    let requestData = {};
    
    // Get URL parameters
    url.searchParams.forEach((value, key) => {
      requestData[key] = value;
    });
    
    // Get body data if it's a POST request
    if (request.method === 'POST') {
      try {
        const contentType = request.headers.get('Content-Type') || '';
        
        if (contentType.includes('application/json')) {
          const text = await request.text();
          try {
            const json = JSON.parse(text);
            requestData = { ...requestData, ...json };
          } catch (e) {
            console.warn('[Mock SW] Failed to parse JSON body:', text);
          }
        } else if (contentType.includes('application/x-www-form-urlencoded')) {
          const formData = await request.formData();
          formData.forEach((value, key) => {
            requestData[key] = value;
          });
        } else {
          // Try to parse as JSON anyway
          try {
            const text = await request.text();
            if (text.startsWith('{') || text.startsWith('[')) {
              const json = JSON.parse(text);
              requestData = { ...requestData, ...json };
            }
          } catch (e) {
            console.warn('[Mock SW] Failed to parse request body');
          }
        }
      } catch (error) {
        console.error('[Mock SW] Error parsing request body:', error);
      }
    }
    
    // Generate mock response based on the endpoint
    let responseData;
    let status = 200;
    let headers = new Headers({
      'Content-Type': 'application/json'
    });
    
    // Send the request data to the main thread for processing
    // This allows us to use the mockApi functions in mock-api.js
    const client = await self.clients.get(event.clientId);
    
    if (client) {
      // Send data to client for processing
      client.postMessage({
        type: 'MOCK_REQUEST',
        endpoint,
        requestData
      });
    }
    
    // Process the request directly in the service worker
    if (endpoint === 'CaseRunner') {
      responseData = {
        success: true,
        tsn_id: Math.floor(Math.random() * 10000),
        message: 'Test case execution started'
      };
    } else if (endpoint === 'DynamicSuiteRunner' || 
              endpoint === 'DynamicTestSuite' || 
              endpoint === 'CreateDynamicSuite' || 
              endpoint === 'CreateTemporarySuite') {
      responseData = {
        success: true,
        tsn_id: Math.floor(Math.random() * 10000),
        suite_id: `dynamic-${Math.floor(Math.random() * 1000)}`,
        message: 'Dynamic test suite execution started'
      };
    } else if (endpoint === 'ReportSummary') {
      responseData = {
        success: true,
        summary: {
          total: 10,
          passed: 7,
          failed: 2,
          skipped: 1,
          duration: 120,
          start_time: new Date().toISOString(),
          end_time: new Date().toISOString()
        }
      };
    } else if (endpoint === 'TestStatus') {
      responseData = {
        success: true,
        status: 'completed',
        progress: 100,
        results: {
          passed: 7,
          failed: 2,
          skipped: 1,
          total: 10
        }
      };
    } else if (endpoint === 'ExecutionLog') {
      responseData = {
        success: true,
        logs: [
          { timestamp: new Date().toISOString(), level: 'INFO', message: 'Test execution started' },
          { timestamp: new Date().toISOString(), level: 'INFO', message: 'Running test case 1' },
          { timestamp: new Date().toISOString(), level: 'SUCCESS', message: 'Test case 1 passed' }
        ]
      };
    } else if (endpoint === 'StopTest') {
      responseData = {
        success: true,
        message: 'Test execution stopped'
      };
    } else {
      // Default response for unknown endpoints
      console.warn(`[Mock SW] Unknown endpoint: ${endpoint}`);
      responseData = {
        success: false,
        message: `Endpoint not implemented in mock service worker: ${endpoint}`
      };
      status = 404;
    }
    
    // Return the mock response
    return new Response(JSON.stringify(responseData), {
      status: status,
      headers: headers
    });
  } catch (error) {
    console.error('[Mock SW] Error handling request:', error);
    
    // Return an error response
    return new Response(JSON.stringify({
      success: false,
      error: true,
      message: `Error in mock service worker: ${error.message || 'Unknown error'}`
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
}

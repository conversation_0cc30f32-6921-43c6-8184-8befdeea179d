/**
 * Debug script to test API service functionality
 */

console.log('=== DEBUG API SCRIPT ===');

// Wait for page to load
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded - checking API service...');

    // Check if UnifiedApiService is available
    console.log('window.UnifiedApiService:', typeof window.UnifiedApiService);

    // Check if apiService is available
    console.log('window.apiService:', typeof window.apiService);

    if (window.apiService) {
        console.log('apiService methods:', Object.keys(window.apiService));
        console.log('apiService.searchTestCases:', typeof window.apiService.searchTestCases);
        console.log('apiService.moduleContext:', window.apiService.moduleContext);
    }

    // Check dropdown elements
    console.log('=== CHECKING DROPDOWN ELEMENTS ===');
    const projectSelect = document.getElementById('project-select');
    const shellHostSelect = document.getElementById('shell-host-select');

    console.log('project-select element:', projectSelect);
    console.log('shell-host-select element:', shellHostSelect);

    if (projectSelect) {
        const computedStyle = window.getComputedStyle(projectSelect);
        console.log('project-select style.display:', projectSelect.style.display);
        console.log('project-select computed display:', computedStyle.display);
        console.log('project-select computed visibility:', computedStyle.visibility);
        console.log('project-select computed opacity:', computedStyle.opacity);
        console.log('project-select computed height:', computedStyle.height);
        console.log('project-select parent:', projectSelect.parentElement);
        console.log('project-select parent computed style:', window.getComputedStyle(projectSelect.parentElement));

        // Try to make it visible
        projectSelect.style.display = 'block';
        projectSelect.style.visibility = 'visible';
        projectSelect.style.opacity = '1';
        console.log('Applied visibility styles to project-select');
    } else {
        console.error('project-select element not found!');
    }

    if (shellHostSelect) {
        const computedStyle = window.getComputedStyle(shellHostSelect);
        console.log('shell-host-select style.display:', shellHostSelect.style.display);
        console.log('shell-host-select computed display:', computedStyle.display);
        console.log('shell-host-select computed visibility:', computedStyle.visibility);
        console.log('shell-host-select computed opacity:', computedStyle.opacity);
        console.log('shell-host-select computed height:', computedStyle.height);
        console.log('shell-host-select parent:', shellHostSelect.parentElement);
        console.log('shell-host-select parent computed style:', window.getComputedStyle(shellHostSelect.parentElement));

        // Try to make it visible
        shellHostSelect.style.display = 'block';
        shellHostSelect.style.visibility = 'visible';
        shellHostSelect.style.opacity = '1';
        console.log('Applied visibility styles to shell-host-select');
    } else {
        console.error('shell-host-select element not found!');
    }

    // Check if the form is visible
    const testRunForm = document.getElementById('test-run-form');
    console.log('test-run-form element:', testRunForm);
    if (testRunForm) {
        console.log('test-run-form computed style:', window.getComputedStyle(testRunForm));
    }

    // List all select elements on the page
    const allSelects = document.querySelectorAll('select');
    console.log('All select elements on page:', allSelects);
    allSelects.forEach((select, index) => {
        console.log(`Select ${index}:`, select, 'ID:', select.id, 'Computed style:', window.getComputedStyle(select));
    });

    // Try to create a UnifiedApiService instance manually
    if (window.UnifiedApiService) {
        console.log('Creating test UnifiedApiService instance...');
        const testService = new window.UnifiedApiService();
        testService.moduleContext = 'config';
        console.log('Test service methods:', Object.keys(testService));
        console.log('Test service searchTestCases:', typeof testService.searchTestCases);

        // Try to call searchTestCases with different parameters
        if (typeof testService.searchTestCases === 'function') {
            console.log('=== TESTING API CALLS ===');

            // Test 1: Search with name
            console.log('🔍 Test 1: Searching with name="test"');
            testService.searchTestCases({ name: 'test' })
                .then(result => {
                    console.log('✅ searchTestCases(name="test") result:', result);
                    console.log('   - Type:', typeof result);
                    console.log('   - Is Array:', Array.isArray(result));
                    console.log('   - Length:', result ? result.length : 'null/undefined');
                })
                .catch(error => console.error('❌ searchTestCases(name="test") error:', error));

            // Test 2: Search with no parameters
            setTimeout(() => {
                console.log('🔍 Test 2: Searching with no parameters');
                testService.searchTestCases({})
                    .then(result => {
                        console.log('✅ searchTestCases({}) result:', result);
                        console.log('   - Type:', typeof result);
                        console.log('   - Is Array:', Array.isArray(result));
                        console.log('   - Length:', result ? result.length : 'null/undefined');
                    })
                    .catch(error => console.error('❌ searchTestCases({}) error:', error));
            }, 1000);

            // Test 3: Search with limit
            setTimeout(() => {
                console.log('🔍 Test 3: Searching with limit=10');
                testService.searchTestCases({ limit: 10 })
                    .then(result => {
                        console.log('✅ searchTestCases(limit=10) result:', result);
                        console.log('   - Type:', typeof result);
                        console.log('   - Is Array:', Array.isArray(result));
                        console.log('   - Length:', result ? result.length : 'null/undefined');
                    })
                    .catch(error => console.error('❌ searchTestCases(limit=10) error:', error));
            }, 2000);
        }
    }
});

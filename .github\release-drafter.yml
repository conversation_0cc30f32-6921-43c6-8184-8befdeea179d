name-template: 'SmartTest v$RESOLVED_VERSION'
tag-template: 'v$RESOLVED_VERSION'

categories:
  - title: '🚀 Features'
    labels:
      - 'feature'
      - 'enhancement'
  - title: '🐛 Bug Fixes'
    labels:
      - 'bug'
      - 'fix'
  - title: '🔒 Security'
    labels:
      - 'security'
  - title: '📚 Documentation'
    labels:
      - 'documentation'
      - 'docs'
  - title: '🏗️ Dependencies'
    labels:
      - 'dependencies'
      - 'chore'
  - title: '⚡ Performance'
    labels:
      - 'performance'
      - 'optimization'

change-template: '- $TITLE @$AUTHOR (#$NUMBER)'

change-title-escapes: '\<*_&'

version-resolver:
  major:
    labels:
      - 'major'
      - 'breaking'
  minor:
    labels:
      - 'minor'
      - 'feature'
      - 'enhancement'
  patch:
    labels:
      - 'patch'
      - 'bug'
      - 'fix'
      - 'security'
  default: patch

template: |
  ## What's Changed 🎉

  $CHANGES

  ## 📊 Stats
  - **Full Changelog**: $COMPARE_URL
  - **Contributors**: $CONTRIBUTORS

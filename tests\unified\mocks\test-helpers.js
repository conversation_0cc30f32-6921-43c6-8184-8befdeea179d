/**
 * Unified Test Helpers for SmartTest Application
 * 
 * This file provides common testing utilities and helpers for:
 * - Mock setup and teardown
 * - Test data generation
 * - Assertion helpers
 * - Service mocking
 */

const apiResponses = require('./api-responses');

/**
 * Mock Service Factory
 * Creates mocked versions of services for testing
 */
class MockServiceFactory {
  /**
   * Create a mock unified API service
   */
  static createUnifiedApiService() {
    return {
      get: jest.fn(),
      post: jest.fn(),
      put: jest.fn(),
      delete: jest.fn(),
      request: jest.fn(),
      
      // Specific method mocks
      getTestRuns: jest.fn().mockResolvedValue(apiResponses.success(apiResponses.database.testRuns)),
      getTestDetails: jest.fn().mockResolvedValue(apiResponses.success(apiResponses.externalApi.testDetails)),
      getTestSummary: jest.fn().mockResolvedValue(apiResponses.success(apiResponses.externalApi.testSummary)),
      
      // Configuration
      isConfigured: jest.fn().mockReturnValue(true),
      getConfig: jest.fn().mockReturnValue(apiResponses.internalApi.configResponse)
    };
  }

  /**
   * Create a mock external API service
   */
  static createExternalApiService() {
    return {
      authenticate: jest.fn().mockResolvedValue(apiResponses.success(apiResponses.externalApi.authResponse)),
      getRecentRuns: jest.fn().mockResolvedValue(apiResponses.success(apiResponses.externalApi.recentRuns)),
      getTestDetails: jest.fn().mockResolvedValue(apiResponses.success(apiResponses.externalApi.testDetails)),
      getSummary: jest.fn().mockResolvedValue(apiResponses.success(apiResponses.externalApi.testSummary)),
      
      // Connection status
      isConnected: jest.fn().mockReturnValue(true),
      testConnection: jest.fn().mockResolvedValue(true)
    };
  }

  /**
   * Create a mock database service
   */
  static createDatabaseService() {
    return {
      connect: jest.fn().mockResolvedValue(true),
      disconnect: jest.fn().mockResolvedValue(true),
      query: jest.fn(),
      execute: jest.fn(),
      
      // Specific queries
      getTestRuns: jest.fn().mockResolvedValue(apiResponses.database.testRuns),
      getTestCases: jest.fn().mockResolvedValue(apiResponses.database.testCases),
      getTestRunById: jest.fn().mockResolvedValue(apiResponses.database.testRuns[0]),
      
      // Connection status
      isConnected: jest.fn().mockReturnValue(true),
      testConnection: jest.fn().mockResolvedValue(apiResponses.database.connectionTest)
    };
  }
}

/**
 * Test Data Generators
 * Provides functions to generate test data with realistic values
 */
class TestDataGenerator {
  /**
   * Generate a test run with optional overrides
   */
  static testRun(overrides = {}) {
    return {
      id: Math.floor(Math.random() * 10000),
      session_id: `SESSION_${Math.floor(Math.random() * 1000)}`,
      test_name: 'Generated Test Run',
      environment: 'QA01',
      status: 'COMPLETED',
      start_time: new Date().toISOString(),
      end_time: new Date(Date.now() + 60000).toISOString(),
      total_tests: 10,
      passed_tests: 8,
      failed_tests: 2,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      ...overrides
    };
  }

  /**
   * Generate a test case with optional overrides
   */
  static testCase(overrides = {}) {
    return {
      id: Math.floor(Math.random() * 10000),
      test_run_id: 1001,
      case_name: 'Generated Test Case',
      status: 'PASSED',
      duration: Math.floor(Math.random() * 10000),
      error_message: null,
      stack_trace: null,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      ...overrides
    };
  }

  /**
   * Generate multiple test runs
   */
  static testRuns(count = 5, overrides = {}) {
    return Array.from({ length: count }, (_, index) => 
      this.testRun({ 
        id: 1000 + index,
        session_id: `SESSION_${1000 + index}`,
        ...overrides 
      })
    );
  }

  /**
   * Generate multiple test cases
   */
  static testCases(count = 10, testRunId = 1001, overrides = {}) {
    return Array.from({ length: count }, (_, index) => 
      this.testCase({ 
        id: 2000 + index,
        test_run_id: testRunId,
        case_name: `Test Case ${index + 1}`,
        ...overrides 
      })
    );
  }
}

/**
 * Assertion Helpers
 * Custom assertion functions for common test scenarios
 */
class AssertionHelpers {
  /**
   * Assert that a response has the expected structure
   */
  static assertApiResponse(response, expectedData = null) {
    expect(response).toHaveProperty('data');
    expect(response).toHaveProperty('status');
    expect(response.status).toBe(200);
    
    if (expectedData) {
      expect(response.data).toEqual(expectedData);
    }
  }

  /**
   * Assert that an error response has the expected structure
   */
  static assertErrorResponse(error, expectedStatus = 500) {
    expect(error).toHaveProperty('response');
    expect(error.response).toHaveProperty('status');
    expect(error.response.status).toBe(expectedStatus);
    expect(error.response).toHaveProperty('data');
    expect(error.response.data).toHaveProperty('error');
  }

  /**
   * Assert that a test run has valid structure
   */
  static assertTestRun(testRun) {
    expect(testRun).toHaveProperty('id');
    expect(testRun).toHaveProperty('session_id');
    expect(testRun).toHaveProperty('test_name');
    expect(testRun).toHaveProperty('environment');
    expect(testRun).toHaveProperty('status');
    expect(testRun).toHaveProperty('start_time');
    expect(testRun).toHaveProperty('total_tests');
    expect(testRun).toHaveProperty('passed_tests');
    expect(testRun).toHaveProperty('failed_tests');
    
    // Validate data types
    expect(typeof testRun.id).toBe('number');
    expect(typeof testRun.session_id).toBe('string');
    expect(typeof testRun.total_tests).toBe('number');
    expect(typeof testRun.passed_tests).toBe('number');
    expect(typeof testRun.failed_tests).toBe('number');
  }

  /**
   * Assert that a test case has valid structure
   */
  static assertTestCase(testCase) {
    expect(testCase).toHaveProperty('id');
    expect(testCase).toHaveProperty('test_run_id');
    expect(testCase).toHaveProperty('case_name');
    expect(testCase).toHaveProperty('status');
    expect(testCase).toHaveProperty('duration');
    
    // Validate data types
    expect(typeof testCase.id).toBe('number');
    expect(typeof testCase.test_run_id).toBe('number');
    expect(typeof testCase.case_name).toBe('string');
    expect(typeof testCase.status).toBe('string');
    expect(typeof testCase.duration).toBe('number');
  }
}

/**
 * Mock Setup Utilities
 * Functions to set up and tear down mocks for tests
 */
class MockSetup {
  /**
   * Set up all service mocks
   */
  static setupServiceMocks() {
    const mocks = {
      unifiedApiService: MockServiceFactory.createUnifiedApiService(),
      externalApiService: MockServiceFactory.createExternalApiService(),
      databaseService: MockServiceFactory.createDatabaseService()
    };

    // Store mocks globally for access in tests
    global.testMocks = mocks;
    
    return mocks;
  }

  /**
   * Reset all mocks
   */
  static resetMocks() {
    if (global.testMocks) {
      Object.values(global.testMocks).forEach(mock => {
        Object.values(mock).forEach(method => {
          if (jest.isMockFunction(method)) {
            method.mockReset();
          }
        });
      });
    }
  }

  /**
   * Clear all mocks
   */
  static clearMocks() {
    if (global.testMocks) {
      Object.values(global.testMocks).forEach(mock => {
        Object.values(mock).forEach(method => {
          if (jest.isMockFunction(method)) {
            method.mockClear();
          }
        });
      });
    }
  }
}

// Export all helpers
module.exports = {
  MockServiceFactory,
  TestDataGenerator,
  AssertionHelpers,
  MockSetup,
  
  // Quick access to common utilities
  createMocks: MockSetup.setupServiceMocks,
  resetMocks: MockSetup.resetMocks,
  clearMocks: MockSetup.clearMocks,
  
  // Data generators
  generateTestRun: TestDataGenerator.testRun,
  generateTestCase: TestDataGenerator.testCase,
  generateTestRuns: TestDataGenerator.testRuns,
  generateTestCases: TestDataGenerator.testCases,
  
  // Assertions
  assertApiResponse: AssertionHelpers.assertApiResponse,
  assertErrorResponse: AssertionHelpers.assertErrorResponse,
  assertTestRun: AssertionHelpers.assertTestRun,
  assertTestCase: AssertionHelpers.assertTestCase
};

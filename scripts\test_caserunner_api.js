const fs = require('fs');

async function testCaseRunnerApi() {
  console.log('Testing CaseRunner API...');
  
  // Dynamically import the required modules
  const { default: fetch } = await import('node-fetch');
  
  // Set the correct URL
  const apiUrl = 'http://mprts-qa02.lab.wagerworks.com:5080/AutoRun/CaseRunner';
  console.log(`API URL: ${apiUrl}`);
  
  // Create a URLSearchParams object for the form data
  const params = new URLSearchParams();
  params.append('tc_id', '3180');
  params.append('envir', 'qa02');
  params.append('shell_host', 'jps-qa10-app01');
  params.append('uid', '<EMAIL>');
  params.append('password', 'test');
  
  console.log('Request parameters:');
  for (const [key, value] of params.entries()) {
    console.log(`  ${key}: ${key === 'password' ? '******' : value}`);
  }
  
  try {
    // Make the POST request
    console.log('Sending request...');
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: params,
      redirect: 'follow', // Follow redirects automatically
    });
    
    // Log response details
    console.log(`\nResponse status: ${response.status} ${response.statusText}`);
    console.log('Response headers:');
    response.headers.forEach((value, name) => {
      console.log(`  ${name}: ${value}`);
    });
    
    // Get the response text
    const responseText = await response.text();
    
    // Log the first 500 characters of the response
    console.log('\nResponse preview (first 500 chars):');
    console.log(responseText.substring(0, 500));
    
    // Look for key phrases that indicate success or error
    if (responseText.includes('Test Case Auto Running')) {
      console.log('\n✅ TEST CASE IS RUNNING');
      
      // Extract the test session ID
      const tsnMatch = responseText.match(/Test session number is (\d+)/i);
      if (tsnMatch && tsnMatch[1]) {
        console.log(`✅ Test session ID: ${tsnMatch[1]}`);
      } else {
        console.log('❌ Could not find test session ID in the response');
      }
    } else if (responseText.includes('Login Page')) {
      console.log('\n❌ Authentication failed - received login page');
    } else if (responseText.includes('Error')) {
      console.log('\n❌ Error detected in response');
    } else {
      console.log('\n❓ Unknown response format');
    }
    
    // Save the full response to a file for analysis
    fs.writeFileSync('caserunner_response.html', responseText);
    console.log('\nFull response saved to caserunner_response.html');
    
  } catch (error) {
    console.error('Error making request:', error);
  }
}

// Run the test
testCaseRunnerApi();

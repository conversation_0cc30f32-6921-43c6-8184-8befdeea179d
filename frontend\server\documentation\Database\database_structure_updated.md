# Database Structure Documentation

## Overview

This document provides a comprehensive overview of the `rgs_test` database structure, which is the core data storage system for the test automation framework. The database is hosted on MySQL server `mprts-qa02.lab.wagerworks.com`.

## Database Schema

### Primary Tables

#### `test_project`
Stores top-level project information:
- `pj_id`: INT(8), Primary Key, Auto-increment - Unique project identifier
- `status`: CHAR(1) - Project status indicator
- `uid`: VARCHAR(50) - User ID of the project owner
- `comments`: VARCHAR(1024) - Project description or comments
- `tp_id`: INT(8) - Test plan ID reference
- `name`: VARCHAR(64) - Project name

#### `test_suite_group`
Manages test suites within projects:
- `pj_id`: INT(8) - Foreign key to test_project
- `ts_id`: INT(8) - Test suite identifier
- `seq_index`: INT(2) - Execution order within the project

#### `test_case_group`
Associates test cases with test suites:
- `ts_id`: INT(8), Indexed - Foreign key to test_suite_group
- `tc_id`: INT(8), Indexed - Foreign key to test_case
- `seq_index`: INT(2) - Execution order within the test suite

#### `test_case`
Stores individual test case definitions (schema from script references):
- `tc_id`: INT(8) - Primary key, test case identifier
- Additional fields store test case configuration and details

#### `test_result`
Records test execution results:
- `tc_id`: INT(8) - Test case ID
- `seq_index`: INT(8) - Sequence index
- `tsn_id`: INT(8), Indexed - Test suite run ID
- `outcome`: CHAR(1) - Result status ('P' for pass, 'F' for fail)
- `creation_time`: TIMESTAMP(3) with auto-update - Execution timestamp
- `cnt`: INT(10), Primary Key, Auto-increment - Counter linking to output

#### `output`
Contains detailed test output logs:
- `cnt`: INT(10), Indexed - Foreign key linking to test_result 
- `txt`: VARCHAR(64000) - The actual output text content with large capacity

#### `input`
Contains detailed test input data (eg. sql query input) 
cnt	int(10) - Foreign key linking to test_result
txt	varchar(64000)  The actual input text 

#### `test_session`
Contains test session execution report results 
tsn_id	int(8) - Test suite run ID
uid	varchar(50) - User ID of the project owner
start_ts	timestamp - execution  start time
end_ts	timestamp - execution end time
error	varchar(8192) number of failured (not always exact - bug) 
pj_id	int(8) progect ID (either null, or progect ID - when execution was started from a test project level)
ts_id	int(8) suite ID (either null, or suite ID - when execution was started from a test suite level)
tc_id	int(8) test case ID (either null, or test case ID - when execution was started from a test case level)
report	varchar(32000) test session execution report results (shown in the results page)

### Relationships

The database follows this relationship structure:
- Projects (`test_project`) contain multiple test suites (`test_suite_group`)
- Test suites contain multiple test cases (`test_case_group`)
- Test executions (`test_result`) are linked to specific test cases
- Test outputs (`output`) are linked to test results

## Data Model

```
test_project (1) --- (n) test_suite_group (1) --- (n) test_case_group (n) --- (1) test_case
                                                                                    |
                                                                                    |
test_result (1) --- (1) output                                                      |
      |                                                                             |
      +-----------------------------------------------------------------------------+
```

## Key Fields and Data Types

### Test Identification
- `pj_id`: INT(8), Project identifier with auto-increment
- `ts_id`: INT(8), Test suite identifier
- `tc_id`: INT(8), Test case identifier
- `tsn_id`: INT(8), Test suite run instance identifier

### Execution Data
- `creation_time`: TIMESTAMP(3), Automatically updated on modification
- `outcome`: CHAR(1), Test result status ('P'=Pass, 'F'=Fail)
- `cnt`: INT(10), Auto-increment counter linking results to output
- `seq_index`: INT(2) or INT(8), Ordering sequence depending on context

### Content Storage
- `txt`: VARCHAR(64000), Very large field for storing output content
- `comments`: VARCHAR(1024), Storing project descriptions
- `name`: VARCHAR(64), Storing project names

## Database Indexes

Indexes are used to optimize query performance:
- Primary keys on `pj_id` in `test_project` and `cnt` in `test_result`
- Multiple-column indexes (MUL) on relationship fields like `ts_id` and `tc_id` in `test_case_group`
- Index on `cnt` in `output` table for fast result lookups

## Query Examples

### 1. Get all test cases in a specific test suite
```sql
SELECT tc_id 
FROM test_case_group 
WHERE ts_id = [SUITE_ID]
ORDER BY seq_index;
```

### 2. Count test cases in a project
```sql
SELECT count(distinct tc_id) "test_case_count" 
FROM test_suite_group ts, test_project tp, test_case_group tg 
WHERE tp.pj_id = ts.pj_id 
  AND tg.ts_id = ts.ts_id 
  AND ts.pj_id = [PROJECT_ID];
```

### 3. Get test execution results
```sql
SELECT r.cnt, r.tsn_id, r.tc_id, r.outcome, o.txt 
FROM test_result r
LEFT JOIN output o ON r.cnt = o.cnt
WHERE r.tsn_id = [TEST_RUN_ID]
ORDER BY r.seq_index;
```

### 4. Get test execution statistics
```sql to get total amount of  pass/failed steps 
SELECT t.tsn_id, 
       SUM(CASE WHEN outcome='P' THEN 1 ELSE 0 END) passed_cases, 
       SUM(CASE WHEN outcome='F' THEN 1 ELSE 0 END) failed_cases, 
       TIMEDIFF(MAX(creation_time), MIN(creation_time)) duration
FROM test_result t
WHERE t.tsn_id = [TEST_RUN_ID]
GROUP BY t.tsn_id;
```

```sql to get total amount of pass/failed steps per test run grouped by test case
  SELECT t.tc_id, 
       SUM(CASE WHEN outcome='P' THEN 1 ELSE 0 END) passed_cases, 
       SUM(CASE WHEN outcome='F' THEN 1 ELSE 0 END) failed_cases, 
       TIMEDIFF(MAX(creation_time), MIN(creation_time)) duration
FROM test_result t
WHERE t.tsn_id = [TEST_RUN_ID]
GROUP BY t.tc_id;
```
  
```sql to get total amount of  pass/failed test cases per run
select   b.failed_cases failed, c.passed_cases passed, SEC_TO_TIME( SUM( TIME_TO_SEC( a.di ) ) ) execution_time from (
select tsn_id, min(creation_time), max(creation_time), TIMEDIFF(max(creation_time), min(creation_time)) di from rgs_test.test_result where tsn_id in ([TEST_RUN_ID]) group by tsn_id ) a, 
(select tsn_id, count(*) failed_cases from( SELECT distinct tsn_id, count(*) "failed_cases"  FROM rgs_test.test_result r, rgs_test.output i  where i.cnt=r.cnt  and r.tsn_id in ([TEST_RUN_ID]) and outcome = 'F' group by tc_id, tsn_id  order by creation_time asc) d ) b ,
(select tsn_id, count(*) passed_cases from( SELECT distinct tsn_id, count(*) "failed_cases"  FROM rgs_test.test_result r, rgs_test.output i  where i.cnt=r.cnt  and r.tsn_id in ([TEST_RUN_ID]) and outcome = 'P' group by  tc_id, tsn_id  order by creation_time asc) e ) c;
```



## Data Access and Security


Connection to the database requires:
1. SSH connection to `mprts-qa02.lab.wagerworks.com` with appropriate authentication
2. MySQL authentication with appropriate credentials

## Performance Considerations

- The database uses appropriate indexes for optimizing joins and lookups
- The `output` table can store very large content (up to 64,000 characters)
- Auto-increment fields provide efficient key generation
- Timestamp fields with millisecond precision allow for accurate timing measurements 
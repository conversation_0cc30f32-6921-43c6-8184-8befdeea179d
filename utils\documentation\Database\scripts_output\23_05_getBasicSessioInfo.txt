========== DATABASE CONNECTION TOOL ==========
1: Execute Count Query (Simple Test)
2: Get Latest Test Run Results
3: Get Latest Test Failures
4: Execute Custom Query
5: Change Credentials
6: Test SSH Key Authentication
Q: Quit
==============================================
Enter your choice: 4
Enter your custom SQL query: SELECT         ts.tsn_id,        ts.tc_id,        ts.ts_id,      
  ts.pj_id,        ts.uid,        ts.start_ts,        ts.end_ts,        ts.error,        ts.report      FROM test_session ts      WHERE ts.tsn_id = 14813
Save results to file? (y/n): n

==== Executing Query on qa2 (mprts-qa02.lab.wagerworks.com) ====
Query: SELECT         ts.tsn_id,        ts.tc_id,        ts.ts_id,        ts.pj_id,        ts.uid,        ts.start_ts,        ts.end_ts,        ts.error,        ts.report      FROM test_session ts      WHERE ts.tsn_id = 14813
VERBOSE: SSH command base: ssh -i "C:\Users\<USER>\.ssh\id_rsa_dbserver" -o 
HostKeyAlgorithms=+ssh-rsa
VERBOSE: Complete SSH command: ssh -i "C:\Users\<USER>\.ssh\id_rsa_dbserver" -o 
HostKeyAlgorithms=+ssh-rsa <EMAIL>
VERBOSE: SQL query saved to: C:\Users\<USER>\AppData\Local\Temp\temp_query.sql
Executing command...
Copying query file to server...
Warning: Using a password on the command line interface can be insecure.
Exit code: 0
Query executed successfully!
Result:
tsn_id  tc_id   ts_id   pj_id   uid     start_ts        end_ts  error   report
14813   NULL    213     NULL    <EMAIL>   2025-05-22 22:54:24     2025-05-22 23:26:00   3:642/385       <ul><li><span style='color:red'>FAIL</span> Suite: <a href='http://mprts-qa02.lab.wagerworks.com:6080/AutoRun/SuiteEditor?ts_id=213'>213</a> &nbsp;3214 temp suite to rerun mixed feeds
<ul><li><span style='color:red'>FAIL</span> Case: <a href='http://mprts-qa02.lab.wagerworks.com:6080/AutoRun/CaseEditor?tc_id=3075'>3075</a> &nbsp;1 F_O WINS  1 F_O WINS #2 currency base  
</li></ul></li></ul>Case(s) passed: 0<br/>Case(s) failed: 1<br/>Debug Report: <a href='http://mprts-qa02.lab.wagerworks.com:6080/AutoRun/ReportDetails?tsn_id=14813'>http://mprts-qa02.lab.wagerworks.com:6080/AutoRun/ReportDetails?tsn_id=14813</a><br/><br/>Variables:<br/>envir=qa05<br/>shell_host=jps-qa10-app01<br/>file_path=/home/<USER>/<br/>operatorConfigs=operatorNameConfigs<br/>operatorId=operatorName<br/>minus_days=0<br/>kafka_server=kafka-qa-a0.lab.wagerworks.com<br/>dataCenter=GU<br/>rgs_env=qa06<br/>env_config="envOperatorConfigs": [
\n\t\t{
\n "envId": ${id0},
\n "operatorNameConfigs": [{
\n\t"licenseeId": "${licenseeId1}",
\n\t"operatorName": ["${operatorId1}"]
\n\t}]
\n},
\n{
\n "envId": ${id1},
\n "operatorNameConfigs": [{
\n\t"licenseeId": "${licenseeId21}",
\n\t"operatorName": ["${operatorId21}"]
\n\t}]
\n}
\n\t],<br/>networkType1=multi-site<br/>networkType2=multi-site<br/>env_config_2530="envOperatorConfigs": [
\n\t\t{
\n "envId": ${id0},
\n "operatorNameConfigs": [{
\n\t"licenseeId": "${licenseeId11}",
\n\t"operatorName": ["${operatorId1_11}"]
\n\t}]
\n},
\n{
\n "envId": ${id1},
\n "operatorNameConfigs": [{
\n\t"licenseeId": "${licenseeId21}",
\n\t"operatorName": ["${operatorId2_21}",  "${operatorId21}"]
\n\t}]
\n}
\n\t],<br/>networkType3=multi-site<br/>networkType4=multi-site<br/>sign=-<br/>operator_start_point=500<br/>rate_src=local<br/>sleep_sec=60<br/>timeout=120<br/>data_center_type=central<br/>build_version=2.1<br/>feed_version=1<br/>uid=<EMAIL><br/>
Cleaning up remote file...

VERBOSE: Removed temporary query file: C:\Users\<USER>\AppData\Local\Temp\temp_query.sql
VERBOSE: Removed temporary SSH config file: 
C:\Users\<USER>\AppData\Local\Temp\temp_ssh_config_mprts-qa02.lab.wagerworks.com

Press Enter to continue:
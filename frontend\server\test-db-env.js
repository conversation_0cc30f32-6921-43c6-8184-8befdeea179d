/**
 * Database Environment Test Script
 *
 * This script tests SSH tunnel and database connection to any configured environment
 * Usage: node test-db-env.js [environment_name]
 * Example: node test-db-env.js qa01
 */

// Import modules
const fs = require('fs');
const path = require('path');
const dbEnv = require('./db-environments');

// Process command-line arguments
const args = process.argv.slice(2);
let environment = args[0] || 'qa01'; // Default to qa01 if not specified

// Validate environment
if (!Object.keys(dbEnv.environments).includes(environment)) {
  console.error(`Error: Unknown environment '${environment}'.`);
  console.error(`Available environments: ${Object.keys(dbEnv.environments).join(', ')}`);
  process.exit(1);
}

// Set environment variables
try {
  const config = dbEnv.setEnvironment(environment);

  // Print loaded SSH and DB variables for verification
  console.log(`Environment variables loaded for ${environment}:`);
  ['SSH_ENABLED', 'SSH_HOST', 'SSH_USER', 'SSH_PORT', 'SSH_KEY_PATH',
   'DB_HOST', 'DB_USER', 'DB_PASSWORD', 'DB_NAME', 'DB_PORT'].forEach(key => {
    // Mask password value in logs for security
    const value = key === 'DB_PASSWORD'
      ? (process.env[key] ? '********' : '(not set)')
      : (process.env[key] || '(not set)');
    console.log(`${key}: ${value}`);
  });
} catch (error) {
  console.error(`Failed to set environment: ${error.message}`);
  process.exit(1);
}

// Import the db module
const db = require('./database');

// Main function to run the test
async function runTest() {
  try {
    console.log(`\n--- STARTING ${environment.toUpperCase()} DATABASE CONNECTION TEST ---\n`);

    // Step 1: Initialize database connection
    console.log(`Step 1: Initializing database connection to ${environment.toUpperCase()}...`);
    await db.init();
    console.log('✅ Database connection initialized successfully!\n');

    // Step 2: Execute a simple query to test the connection
    console.log('Step 2: Testing basic connectivity...');
    console.log('Running test query: SELECT 1 AS test_value');
    const result = await db.query('SELECT 1 AS test_value');
    console.log('Query result:', JSON.stringify(result));
    console.log('✅ Basic connectivity test successful!\n');

    // Step 3: Get database version and information
    console.log('Step 3: Getting database information...');
    const infoQuery = `
      SELECT
        @@version as db_version,
        database() as db_name,
        current_user() as current_user,
        @@hostname as hostname,
        @@port as port
    `;
    const dbInfo = await db.query(infoQuery);
    console.log('Database information:', JSON.stringify(dbInfo));
    console.log('✅ Database information query successful!\n');

    // Step 4: Check for specific test cases and suites
    console.log('Step 4: Checking for specific test cases and suites...');

    // Different test IDs for different environments
    const testIds = {
      qa01: { testCaseId: 1279, testSuiteId: 82 },
      qa02: { testCaseId: 3180, testSuiteId: 312 },
      qa03: { testCaseId: 3180, testSuiteId: 312 } // Using same IDs as qa02 for now
    };

    const { testCaseId, testSuiteId } = testIds[environment];

    // Test case query
    console.log(`Checking test case ID ${testCaseId}...`);
    const testCaseQuery = `
      SELECT tc_id, COUNT(DISTINCT tsn_id) AS run_count
      FROM test_result
      WHERE tc_id = ${testCaseId}
      GROUP BY tc_id
    `;
    const testCaseInfo = await db.query(testCaseQuery);
    console.log('Test case info:', JSON.stringify(testCaseInfo));

    // Test suite query
    console.log(`Checking test suite ID ${testSuiteId}...`);
    const testSuiteQuery = `
      SELECT tg.ts_id, COUNT(DISTINCT tg.tc_id) AS test_case_count
      FROM test_case_group tg
      WHERE tg.ts_id = ${testSuiteId}
      GROUP BY tg.ts_id
    `;
    const testSuiteInfo = await db.query(testSuiteQuery);
    console.log('Test suite info:', JSON.stringify(testSuiteInfo));
    console.log('✅ Specific test queries successful!\n');

    console.log(`\n--- ${environment.toUpperCase()} TEST COMPLETED SUCCESSFULLY ---`);
    return true;
  } catch (error) {
    console.error(`\n❌ TEST FAILED:`, error);
    return false;
  } finally {
    // Always attempt to close the database connection
    try {
      console.log('\nClosing database connection...');
      await db.close();
      console.log('✅ Database connection closed successfully');
    } catch (closeError) {
      console.error('❌ Error closing database connection:', closeError);
    }
  }
}

// Run the test and exit with appropriate code
runTest()
  .then(success => {
    if (success) {
      console.log(`\nAll tests passed! SSH tunnel and database connection to ${environment.toUpperCase()} are working properly.`);
      process.exit(0);
    } else {
      console.error(`\nTests failed! Please check the error messages above.`);
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('\nUnexpected error in the test runner:', error);
    process.exit(1);
  });
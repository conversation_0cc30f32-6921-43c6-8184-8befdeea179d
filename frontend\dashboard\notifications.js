/**
 * Notifications system for SmartTest dashboard
 * Provides simple toast notifications
 */

// Create notification container if it doesn't exist
function ensureNotificationContainer() {
  let container = document.getElementById('notification-container');
  
  if (!container) {
    container = document.createElement('div');
    container.id = 'notification-container';
    container.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      z-index: 9999;
      max-width: 300px;
    `;
    document.body.appendChild(container);
  }
  
  return container;
}

/**
 * Show a notification toast
 * @param {string} title - Notification title
 * @param {string} message - Notification message
 * @param {string} type - Notification type (info, success, warning, error)
 * @param {number} duration - Duration in milliseconds (default: 5000)
 */
function showNotification(title, message, type = 'info', duration = 5000) {
  const container = ensureNotificationContainer();
  
  // Create notification element
  const notification = document.createElement('div');
  notification.className = `ms-MessageBar ms-MessageBar--${type}`;
  notification.style.cssText = `
    margin-bottom: 10px;
    animation: fadeIn 0.3s ease-in-out;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    opacity: 1;
    transition: opacity 0.3s ease-out;
  `;
  
  // Set notification content
  notification.innerHTML = `
    <div class="ms-MessageBar-content">
      <div class="ms-MessageBar-icon">
        <i class="ms-Icon ms-Icon--${getIconForType(type)}"></i>
      </div>
      <div class="ms-MessageBar-text">
        ${title ? `<strong>${title}</strong><br>` : ''}
        ${message}
      </div>
      <button class="ms-MessageBar-close" title="Dismiss">
        <i class="ms-Icon ms-Icon--Clear"></i>
      </button>
    </div>
  `;
  
  // Add to container
  container.appendChild(notification);
  
  // Add close button handler
  const closeButton = notification.querySelector('.ms-MessageBar-close');
  if (closeButton) {
    closeButton.addEventListener('click', () => {
      removeNotification(notification);
    });
  }
  
  // Auto-remove after duration
  if (duration > 0) {
    setTimeout(() => {
      removeNotification(notification);
    }, duration);
  }
  
  return notification;
}

/**
 * Remove a notification with animation
 * @param {HTMLElement} notification - The notification element to remove
 */
function removeNotification(notification) {
  // Fade out animation
  notification.style.opacity = '0';
  
  // Remove after animation completes
  setTimeout(() => {
    if (notification.parentNode) {
      notification.parentNode.removeChild(notification);
    }
  }, 300);
}

/**
 * Get icon name for notification type
 * @param {string} type - Notification type
 * @returns {string} - Icon name
 */
function getIconForType(type) {
  switch (type) {
    case 'success':
      return 'Completed';
    case 'error':
      return 'ErrorBadge';
    case 'warning':
      return 'Warning';
    case 'info':
    default:
      return 'Info';
  }
}

// Add CSS for animations
function addNotificationStyles() {
  const style = document.createElement('style');
  style.textContent = `
    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(-10px); }
      to { opacity: 1; transform: translateY(0); }
    }
  `;
  document.head.appendChild(style);
}

// Initialize on load
document.addEventListener('DOMContentLoaded', () => {
  addNotificationStyles();
});

// Export to window object for global access
window.showNotification = showNotification;

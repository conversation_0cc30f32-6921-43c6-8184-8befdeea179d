<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Filter Debug</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { margin: 10px 0; padding: 10px; border-radius: 4px; }
        .success { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        .warning { background-color: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .highlight { background-color: #ffeb3b; }
    </style>
</head>
<body>
    <h1>Database Filter Debug Tool</h1>
    <p>This tool helps debug the single_case filter logic by examining actual database records.</p>
    
    <button onclick="testDatabaseFilter()">Test Database Filter Logic</button>
    <button onclick="clearResults()">Clear Results</button>
    
    <div id="results"></div>

    <script>
        function addResult(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            resultDiv.innerHTML = message;
            resultsDiv.appendChild(resultDiv);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        async function testDatabaseFilter() {
            addResult('🔍 Starting database filter analysis...', 'info');
            
            try {
                // Test 1: Get all recent runs without any filter
                addResult('📊 Test 1: Getting all recent runs (no filter)', 'info');
                const allRuns = await fetch('/local/recent-runs?limit=10').then(r => r.json());
                
                if (allRuns.success && allRuns.data) {
                    addResult(`✅ Retrieved ${allRuns.data.length} total runs`, 'success');
                    
                    // Analyze the data structure
                    const analysis = analyzeTestSessions(allRuns.data);
                    displayAnalysis(analysis);
                    
                    // Display sample records
                    displaySampleRecords(allRuns.data.slice(0, 5));
                } else {
                    addResult(`❌ Failed to get all runs: ${JSON.stringify(allRuns)}`, 'error');
                }

                // Test 2: Get runs with single_case filter
                addResult('📊 Test 2: Getting runs with single_case filter', 'info');
                const filteredRuns = await fetch('/local/recent-runs?limit=10&type=single_case').then(r => r.json());
                
                if (filteredRuns.success && filteredRuns.data) {
                    addResult(`✅ Retrieved ${filteredRuns.data.length} filtered runs`, 'success');
                    
                    // Analyze the filtered data
                    const filteredAnalysis = analyzeTestSessions(filteredRuns.data);
                    displayAnalysis(filteredAnalysis, 'Filtered');
                    
                    // Display sample filtered records
                    displaySampleRecords(filteredRuns.data.slice(0, 5), 'Filtered');
                } else {
                    addResult(`❌ Failed to get filtered runs: ${JSON.stringify(filteredRuns)}`, 'error');
                }

                // Test 3: Compare the results
                if (allRuns.success && filteredRuns.success) {
                    compareResults(allRuns.data, filteredRuns.data);
                }

            } catch (error) {
                addResult(`❌ Error during testing: ${error.message}`, 'error');
                console.error('Database filter test error:', error);
            }
        }

        function analyzeTestSessions(sessions) {
            const analysis = {
                total: sessions.length,
                withTcId: 0,
                withTsId: 0,
                withPjId: 0,
                withBothTcAndTs: 0,
                withOnlyTc: 0,
                withOnlyTs: 0,
                types: {}
            };

            sessions.forEach(session => {
                if (session.tc_id && session.tc_id !== '' && session.tc_id !== null) {
                    analysis.withTcId++;
                }
                if (session.ts_id && session.ts_id !== '' && session.ts_id !== null) {
                    analysis.withTsId++;
                }
                if (session.pj_id && session.pj_id !== '' && session.pj_id !== null) {
                    analysis.withPjId++;
                }
                
                const hasTc = session.tc_id && session.tc_id !== '' && session.tc_id !== null;
                const hasTs = session.ts_id && session.ts_id !== '' && session.ts_id !== null;
                
                if (hasTc && hasTs) {
                    analysis.withBothTcAndTs++;
                } else if (hasTc && !hasTs) {
                    analysis.withOnlyTc++;
                } else if (!hasTc && hasTs) {
                    analysis.withOnlyTs++;
                }

                // Count by type
                const type = session.type || 'Unknown';
                analysis.types[type] = (analysis.types[type] || 0) + 1;
            });

            return analysis;
        }

        function displayAnalysis(analysis, prefix = '') {
            const title = prefix ? `${prefix} Data Analysis` : 'Data Analysis';
            addResult(`📈 ${title}:
                <table>
                    <tr><th>Metric</th><th>Count</th><th>Percentage</th></tr>
                    <tr><td>Total Records</td><td>${analysis.total}</td><td>100%</td></tr>
                    <tr><td>With tc_id</td><td>${analysis.withTcId}</td><td>${(analysis.withTcId/analysis.total*100).toFixed(1)}%</td></tr>
                    <tr><td>With ts_id</td><td>${analysis.withTsId}</td><td>${(analysis.withTsId/analysis.total*100).toFixed(1)}%</td></tr>
                    <tr><td>With pj_id</td><td>${analysis.withPjId}</td><td>${(analysis.withPjId/analysis.total*100).toFixed(1)}%</td></tr>
                    <tr class="highlight"><td>Both tc_id AND ts_id</td><td>${analysis.withBothTcAndTs}</td><td>${(analysis.withBothTcAndTs/analysis.total*100).toFixed(1)}%</td></tr>
                    <tr><td>Only tc_id (no ts_id)</td><td>${analysis.withOnlyTc}</td><td>${(analysis.withOnlyTc/analysis.total*100).toFixed(1)}%</td></tr>
                    <tr><td>Only ts_id (no tc_id)</td><td>${analysis.withOnlyTs}</td><td>${(analysis.withOnlyTs/analysis.total*100).toFixed(1)}%</td></tr>
                </table>
                
                <h4>By Type:</h4>
                <table>
                    <tr><th>Type</th><th>Count</th></tr>
                    ${Object.entries(analysis.types).map(([type, count]) => 
                        `<tr><td>${type}</td><td>${count}</td></tr>`
                    ).join('')}
                </table>`, 'info');
        }

        function displaySampleRecords(sessions, prefix = '') {
            const title = prefix ? `${prefix} Sample Records` : 'Sample Records';
            addResult(`📋 ${title}:
                <table>
                    <tr>
                        <th>TSN ID</th>
                        <th>tc_id</th>
                        <th>ts_id</th>
                        <th>pj_id</th>
                        <th>Type</th>
                        <th>Test Name</th>
                        <th>Start Time</th>
                    </tr>
                    ${sessions.map(session => `
                        <tr>
                            <td>${session.tsn_id || 'N/A'}</td>
                            <td>${session.tc_id || 'NULL'}</td>
                            <td>${session.ts_id || 'NULL'}</td>
                            <td>${session.pj_id || 'NULL'}</td>
                            <td>${session.type || 'Unknown'}</td>
                            <td>${session.test_name || 'N/A'}</td>
                            <td>${session.start_ts || session.start_time || 'N/A'}</td>
                        </tr>
                    `).join('')}
                </table>`, 'info');
        }

        function compareResults(allRuns, filteredRuns) {
            addResult('🔍 Comparison Analysis:', 'info');
            
            const excluded = allRuns.filter(run => 
                !filteredRuns.some(filtered => filtered.tsn_id === run.tsn_id)
            );
            
            addResult(`📊 Filter Results:
                <ul>
                    <li>Total runs: ${allRuns.length}</li>
                    <li>Filtered runs: ${filteredRuns.length}</li>
                    <li>Excluded runs: ${excluded.length}</li>
                </ul>`, 'info');

            if (excluded.length > 0) {
                addResult(`❌ Excluded Records (these were filtered out):
                    <table>
                        <tr>
                            <th>TSN ID</th>
                            <th>tc_id</th>
                            <th>ts_id</th>
                            <th>Type</th>
                            <th>Test Name</th>
                            <th>Reason Excluded</th>
                        </tr>
                        ${excluded.map(session => {
                            const hasTc = session.tc_id && session.tc_id !== '' && session.tc_id !== null;
                            const hasTs = session.ts_id && session.ts_id !== '' && session.ts_id !== null;
                            let reason = '';
                            if (!hasTc) reason = 'No tc_id';
                            else if (hasTs) reason = 'Has both tc_id and ts_id';
                            else reason = 'Unknown';
                            
                            return `
                                <tr>
                                    <td>${session.tsn_id || 'N/A'}</td>
                                    <td>${session.tc_id || 'NULL'}</td>
                                    <td>${session.ts_id || 'NULL'}</td>
                                    <td>${session.type || 'Unknown'}</td>
                                    <td>${session.test_name || 'N/A'}</td>
                                    <td class="highlight">${reason}</td>
                                </tr>
                            `;
                        }).join('')}
                    </table>`, 'warning');
            }
        }
    </script>
</body>
</html>

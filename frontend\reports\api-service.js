/**
 * Reports API Service - Unified Implementation
 *
 * Direct replacement of the original reports API service
 * using the unified service with reports context.
 */

// Load the unified API service directly (non-module approach to avoid path resolution issues)
const apiService = window.UnifiedApiService ? new window.UnifiedApiService() : null;

// Initialize if available
if (apiService) {
  console.log('Initializing Reports API Service with UnifiedApiService');
  apiService.moduleContext = 'reports';
  apiService.initializeConfiguration();

  // Make it globally available (preserving existing interface)
  window.apiService = apiService;
  
  console.log('Reports API Service (Unified) initialized successfully');
} else {
  console.error('UnifiedApiService not found! Make sure unified-api-service.js is loaded before this file.');
}

// Legacy support for module.exports
if (typeof module !== 'undefined' && typeof module.exports !== 'undefined') {
  module.exports = apiService;
}

# SmartTest API Documentation - Unified Architecture

This directory contains comprehensive documentation for the SmartTest unified API architecture, which consolidates functionality from dashboard, reports, and config modules into a single, cohesive system.

## Table of Contents

1. [Architecture Overview](./architecture.md) - **Updated for Unified Architecture**
2. [API Endpoints](./endpoints.md) - **Updated with Unified Endpoints**
3. [Authentication](./authentication.md)
4. [Database Schema](./database-schema.md)
5. [<PERSON><PERSON><PERSON>](./error-handling.md)
6. [Proxy Functionality](./proxy.md)
7. [Client-Side Services](./client-side-services.md) - **Updated for Unified Services**

## Unified Architecture Overview

The SmartTest API server now implements a **unified architecture** that provides:

- **Unified API Service**: Single service handling all module contexts (dashboard, reports, config)
- **Consistent Interface**: Standardized API patterns across all modules
- **Shared Database Layer**: Centralized database operations with real SSH connectivity
- **Module Context Awareness**: Automatic behavior adaptation based on calling module
- **Backward Compatibility**: Preserves existing functionality while providing enhanced capabilities

## API Organization

The unified API is organized into logical groups that serve all modules:

- **Local Endpoints** (`/local/*`): Database-backed operations for test data
  - Test Cases: `/local/test-cases`
  - Test Suites: `/local/test-suites`
  - Recent Runs: `/local/recent-runs`
  - Active Tests: `/local/active-tests`
  - Test Details: `/local/test-details/:tsn_id`

- **API Endpoints** (`/api/*`): External API integration and test execution
  - Test Execution: `/api/case-runner`
  - Test Status: `/api/test-status`
  - Test Reports: `/api/test-reports`
  - Stop Test: `/api/stop-test`

- **Module-Specific Behavior**: Same endpoints adapt behavior based on calling module context

All API endpoints require authentication using the credentials specified in the configuration.

## Migration to Unified Architecture

### What Changed
- **Consolidated Services**: Dashboard, reports, and config API services merged into unified service
- **Shared Database Layer**: Single database connection and query layer for all modules
- **Consistent Endpoints**: Standardized API patterns across all functionality
- **Module Context**: Services automatically adapt behavior based on calling module

### Backward Compatibility
- **Existing Endpoints**: All original endpoints preserved and functional
- **Client Code**: No changes required to existing client-side code
- **Response Formats**: Consistent with previous implementations

### New Features
- **Enhanced Error Handling**: Improved error responses with detailed context
- **Performance Monitoring**: Built-in performance tracking and logging
- **Database Integration**: Real database connectivity with comprehensive testing
- **Unified Testing**: Comprehensive test suite covering all modules

## API Base URL

The unified API is accessible at the following base URL:

```
http://localhost:9080
```

## Unified Response Format

All unified API responses follow a consistent format with enhanced context:

```json
{
  "success": true|false,
  "message": "Human-readable message",
  "data": { ... }, // Response data (if applicable)
  "moduleContext": "dashboard|reports|config",
  "requestId": "unique-request-identifier",
  "timestamp": "2023-01-01T00:00:00Z"
}
```

## Enhanced Error Responses

Error responses include detailed context and module information:

```json
{
  "success": false,
  "message": "Error message",
  "error": "Detailed error information",
  "moduleContext": "dashboard|reports|config",
  "requestId": "unique-request-identifier",
  "timestamp": "2023-01-01T00:00:00Z",
  "stack": "Error stack trace (in development mode)"
}
```

## Authentication

All API endpoints require authentication. See the [Authentication](./authentication.md) document for details.

## Testing

The unified architecture includes comprehensive testing:
- **Unit Tests**: Individual service testing
- **Integration Tests**: API-database flow testing
- **Database Tests**: Real database connectivity testing
- **Performance Tests**: Response time and optimization testing

Run tests with:
```bash
npm run test:unified           # All unified tests
npm run test:database         # Database integration tests
npm run test:unified:coverage # Coverage reporting
```

## Further Reading

For detailed information about specific aspects of the unified API, refer to the individual documentation files listed in the Table of Contents.

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Recent Runs Data Debug</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { margin: 10px 0; padding: 10px; border-radius: 4px; }
        .success { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        .warning { background-color: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .test-suite { background-color: #ffcdd2; }
        .test-case { background-color: #c8e6c9; }
        .mixed { background-color: #fff3e0; }
    </style>
</head>
<body>
    <h1>Recent Runs Data Debug</h1>
    <p>This tool analyzes what data is actually being displayed in the Recent Test Cases Runs table.</p>
    
    <button onclick="analyzeRecentRunsData()">Analyze Recent Runs Data</button>
    <button onclick="clearResults()">Clear Results</button>
    
    <div id="results"></div>

    <script>
        function addResult(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            resultDiv.innerHTML = message;
            resultsDiv.appendChild(resultDiv);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        async function analyzeRecentRunsData() {
            addResult('🔍 Analyzing Recent Runs data sources...', 'info');
            
            try {
                // Test different API endpoints
                await testAPIEndpoint('/local/recent-runs?limit=50&type=single_case', 'Filtered API (should be used by Recent Runs table)');
                await testAPIEndpoint('/local/recent-runs?limit=100', 'Unfiltered API (used by Active Tests polling)');
                await testAPIEndpoint('/local/recent-runs?limit=10', 'Default API');
                
                // Check what's actually in the Recent Runs table DOM
                await analyzeTableDOM();
                
                // Check global state
                await checkGlobalState();
                
            } catch (error) {
                addResult(`❌ Error during analysis: ${error.message}`, 'error');
                console.error('Analysis error:', error);
            }
        }

        async function testAPIEndpoint(endpoint, description) {
            addResult(`📡 Testing ${description}: ${endpoint}`, 'info');
            
            try {
                const response = await fetch(endpoint);
                const data = await response.json();
                
                if (data.success && data.data) {
                    const analysis = analyzeData(data.data);
                    displayEndpointAnalysis(description, analysis, data.data.slice(0, 3));
                } else {
                    addResult(`❌ ${description} failed: ${JSON.stringify(data)}`, 'error');
                }
            } catch (error) {
                addResult(`❌ ${description} error: ${error.message}`, 'error');
            }
        }

        function analyzeData(sessions) {
            const analysis = {
                total: sessions.length,
                testCases: 0,
                testSuites: 0,
                mixed: 0,
                standalone: 0,
                withinSuite: 0
            };

            sessions.forEach(session => {
                const hasTc = session.tc_id && session.tc_id !== '' && session.tc_id !== null;
                const hasTs = session.ts_id && session.ts_id !== '' && session.ts_id !== null;
                
                if (session.type === 'Test Case') {
                    analysis.testCases++;
                } else if (session.type === 'Test Suite') {
                    analysis.testSuites++;
                }
                
                if (hasTc && hasTs) {
                    analysis.mixed++;
                    analysis.withinSuite++;
                } else if (hasTc && !hasTs) {
                    analysis.standalone++;
                } else if (!hasTc && hasTs) {
                    // Pure test suite
                }
            });

            return analysis;
        }

        function displayEndpointAnalysis(description, analysis, samples) {
            const hasProblems = analysis.testSuites > 0 || analysis.withinSuite > 0;
            const resultType = hasProblems ? 'warning' : 'success';
            
            addResult(`📊 ${description} Results:
                <table>
                    <tr><th>Metric</th><th>Count</th><th>Status</th></tr>
                    <tr><td>Total Records</td><td>${analysis.total}</td><td>-</td></tr>
                    <tr class="${analysis.testSuites > 0 ? 'test-suite' : ''}"><td>Test Suites</td><td>${analysis.testSuites}</td><td>${analysis.testSuites > 0 ? '❌ Should be 0' : '✅ Good'}</td></tr>
                    <tr class="${analysis.testCases > 0 ? 'test-case' : ''}"><td>Test Cases</td><td>${analysis.testCases}</td><td>-</td></tr>
                    <tr class="${analysis.standalone > 0 ? 'test-case' : ''}"><td>Standalone Test Cases</td><td>${analysis.standalone}</td><td>${analysis.standalone > 0 ? '✅ Good' : '-'}</td></tr>
                    <tr class="${analysis.withinSuite > 0 ? 'mixed' : ''}"><td>Test Cases within Suites</td><td>${analysis.withinSuite}</td><td>${analysis.withinSuite > 0 ? '❌ Should be 0' : '✅ Good'}</td></tr>
                </table>
                
                <h4>Sample Records:</h4>
                <table>
                    <tr><th>TSN ID</th><th>tc_id</th><th>ts_id</th><th>Type</th><th>Test Name</th><th>Classification</th></tr>
                    ${samples.map(session => {
                        const hasTc = session.tc_id && session.tc_id !== '' && session.tc_id !== null;
                        const hasTs = session.ts_id && session.ts_id !== '' && session.ts_id !== null;
                        let classification = '';
                        let rowClass = '';
                        
                        if (!hasTc && hasTs) {
                            classification = 'Test Suite';
                            rowClass = 'test-suite';
                        } else if (hasTc && !hasTs) {
                            classification = 'Standalone Test Case';
                            rowClass = 'test-case';
                        } else if (hasTc && hasTs) {
                            classification = 'Test Case within Suite';
                            rowClass = 'mixed';
                        } else {
                            classification = 'Unknown';
                        }
                        
                        return `
                            <tr class="${rowClass}">
                                <td>${session.tsn_id || 'N/A'}</td>
                                <td>${session.tc_id || 'NULL'}</td>
                                <td>${session.ts_id || 'NULL'}</td>
                                <td>${session.type || 'Unknown'}</td>
                                <td>${session.test_name || 'N/A'}</td>
                                <td>${classification}</td>
                            </tr>
                        `;
                    }).join('')}
                </table>`, resultType);
        }

        async function analyzeTableDOM() {
            addResult('🔍 Analyzing Recent Runs table DOM...', 'info');
            
            // Check if we're on the config page
            const recentRunsTable = document.querySelector('#recent-runs-body');
            if (!recentRunsTable) {
                addResult('ℹ️ Recent runs table not found (not on config page)', 'info');
                return;
            }
            
            const rows = recentRunsTable.querySelectorAll('tr');
            addResult(`📋 Found ${rows.length} rows in Recent Runs table`, 'info');
            
            if (rows.length > 0) {
                const tableData = [];
                rows.forEach((row, index) => {
                    const cells = row.querySelectorAll('td');
                    if (cells.length >= 4) {
                        tableData.push({
                            index: index + 1,
                            tsn_id: cells[0]?.textContent?.trim(),
                            test_name: cells[1]?.textContent?.trim(),
                            status: cells[2]?.textContent?.trim(),
                            details: cells[3]?.textContent?.trim()
                        });
                    }
                });
                
                addResult(`📊 Recent Runs Table Content:
                    <table>
                        <tr><th>#</th><th>TSN ID</th><th>Test Name</th><th>Status</th><th>Details</th><th>Analysis</th></tr>
                        ${tableData.slice(0, 10).map(row => {
                            const isTestSuite = row.test_name?.includes('Test Suite') || row.test_name?.includes('cases)');
                            const rowClass = isTestSuite ? 'test-suite' : 'test-case';
                            const analysis = isTestSuite ? '❌ Test Suite detected!' : '✅ Looks like test case';
                            
                            return `
                                <tr class="${rowClass}">
                                    <td>${row.index}</td>
                                    <td>${row.tsn_id}</td>
                                    <td>${row.test_name}</td>
                                    <td>${row.status}</td>
                                    <td>${row.details}</td>
                                    <td>${analysis}</td>
                                </tr>
                            `;
                        }).join('')}
                    </table>`, 'info');
            }
        }

        async function checkGlobalState() {
            addResult('🔍 Checking global application state...', 'info');
            
            // Check if config.js global state is available
            if (typeof window !== 'undefined') {
                const checks = [];
                
                if (window.appState) {
                    checks.push(`✅ appState found`);
                    if (window.appState.recentRunsCache) {
                        const cache = window.appState.recentRunsCache;
                        checks.push(`📊 recentRunsCache: ${cache.length} items`);
                        
                        if (cache.length > 0) {
                            const analysis = analyzeData(cache);
                            checks.push(`📈 Cache analysis: ${analysis.testSuites} suites, ${analysis.testCases} cases, ${analysis.standalone} standalone`);
                        }
                    }
                } else {
                    checks.push(`❌ appState not found`);
                }
                
                if (window.pollingCoordinator) {
                    checks.push(`✅ pollingCoordinator found`);
                } else if (window.simplePollingCoordinator) {
                    checks.push(`✅ simplePollingCoordinator found`);
                } else {
                    checks.push(`❌ No polling coordinator found`);
                }
                
                addResult(`🔧 Global State Check:<ul>${checks.map(check => `<li>${check}</li>`).join('')}</ul>`, 'info');
            }
        }
    </script>
</body>
</html>

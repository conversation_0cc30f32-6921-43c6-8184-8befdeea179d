/**
 * Working Test Flow Script
 * 
 * This script implements a complete test flow using the exact format
 * required for successful API calls.
 */

const axios = require('axios');
const mysql = require('mysql2/promise');

// Process command line arguments
const args = process.argv.slice(2);
const username = args[0] || '<EMAIL>'; // Default to a working account
const testCaseId = args[1] || 3180;
const environment = args[2] || 'qa02';

// Configuration
const config = {
  // Environment settings
  environments: {
    qa01: {
      baseUrl: 'http://mprts-qa01.lab.wagerworks.com:9080/AutoRun/',
      dbHost: 'mprts-qa01.lab.wagerworks.com',
      dbUser: 'rgs_rw',
      dbPassword: 'rgs_rw',
      dbName: 'rgs_test',
      dbPort: 3306
    },
    qa02: {
      baseUrl: 'http://mprts-qa02.lab.wagerworks.com:5080/AutoRun/',
      dbHost: 'mprts-qa02.lab.wagerworks.com',
      dbUser: 'rgs_rw',
      dbPassword: 'rgs_rw',
      dbName: 'rgs_test',
      dbPort: 3306
    },
    qa03: {
      baseUrl: 'http://mprts-qa03.lab.wagerworks.com:5080/AutoRun/',
      dbHost: 'mprts-qa03.lab.wagerworks.com',
      dbUser: 'rgs_rw',
      dbPassword: 'rgs_rw',
      dbName: 'rgs_test',
      dbPort: 3306
    }
  }
};

// Database connection
let connection = null;

async function main() {
  try {
    console.log('Starting test flow verification...');
    
    // Validate environment
    if (!config.environments[environment]) {
      throw new Error(`Environment ${environment} not found. Available: ${Object.keys(config.environments).join(', ')}`);
    }
    
    const envConfig = config.environments[environment];
    console.log(`Using ${environment} environment`);
    console.log(`API Base URL: ${envConfig.baseUrl}`);
    
    // Connect to database
    console.log(`Connecting to database at ${envConfig.dbHost}...`);
    connection = await mysql.createConnection({
      host: envConfig.dbHost,
      user: envConfig.dbUser,
      password: envConfig.dbPassword,
      database: envConfig.dbName,
      port: envConfig.dbPort
    });
    console.log('Database connection established successfully');
    
    // Check for active tests
    console.log('Checking for active tests...');
    const activeTests = await getActiveTests();
    const currentUserTests = activeTests.filter(test => test.uid === username);
    
    console.log(`Found ${activeTests.length} active tests total`);
    console.log(`Found ${currentUserTests.length} active tests for user ${username}`);
    
    // Trigger test run
    console.log(`\nStarting test case ${testCaseId} with user ${username}...`);
    
    // Build the exact request data string - critical to keep the format exactly as in the curl command
    const requestData = `uid=${username}&password=test&tc_id=${testCaseId}&envir=${environment}&shell_host=jps-qa10-app01&file_path=/home/<USER>/&operatorConfigs=operatorNameConfigs&kafka_server=kafka-qa-a0.lab.wagerworks.com&dataCenter=GU&rgs_env=${environment}&old_version=0&&networkType1=multi-site&networkType2=multi-site&sign=-&rate_src=local`;
    
    // Make the API request
    console.log('Sending API request...');
    const response = await axios.post(
      `${envConfig.baseUrl}CaseRunner`,
      requestData,
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      }
    );
    
    console.log(`API response status: ${response.status}`);
    
    // Extract tsn_id from the response
    const tsnId = extractTsnId(response.data);
    
    if (!tsnId) {
      throw new Error('Failed to extract tsn_id from response');
    }
    
    console.log(`Test run started with tsn_id: ${tsnId}`);
    
    // Monitor test status until completion
    console.log(`\nMonitoring test status for tsn_id: ${tsnId}...`);
    
    let isComplete = false;
    let allPassed = false;
    const startTime = Date.now();
    const timeoutMs = 300000; // 5 minutes timeout
    
    while (!isComplete && (Date.now() - startTime < timeoutMs)) {
      const status = await checkTestStatus(tsnId);
      isComplete = status.isComplete;
      allPassed = status.allPassed;
      
      if (isComplete) {
        console.log('\nTest run completed!');
        break;
      }
      
      // Wait 5 seconds before checking again
      console.log('Waiting 5 seconds before next check...');
      await new Promise(resolve => setTimeout(resolve, 5000));
    }
    
    if (!isComplete) {
      console.log(`\nTest run timed out after ${timeoutMs / 60000} minutes`);
      console.log('The test might still be running, but the script will exit');
      console.log(`You can continue monitoring it with: node check_test_status.js ${environment} ${tsnId}`);
      return;
    }
    
    // Get test summary
    const summary = await getTestSummary(tsnId);
    
    // Get failure details if any
    if (summary.failed_cases > 0) {
      await getFailureDetails(tsnId);
    }
    
    // Check for active tests after completion
    console.log('\nChecking for active tests after completion...');
    const activeTestsAfter = await getActiveTests();
    console.log(`${activeTestsAfter.length} active tests remaining`);
    
    console.log('\nTest flow verification completed successfully');
    
  } catch (error) {
    console.error('Error:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      if (typeof error.response.data === 'string') {
        console.error('Response data (truncated):', error.response.data.substring(0, 200));
      } else {
        console.error('Response data:', error.response.data);
      }
    }
  } finally {
    if (connection) {
      await connection.end();
      console.log('Database connection closed');
    }
  }
}

// Extract tsn_id from API response
function extractTsnId(responseData) {
  if (typeof responseData !== 'string') {
    console.log('Response is not a string');
    return null;
  }
  
  // Try various patterns
  const patterns = [
    /ReportSummary\?tsn_id=(\d+)/,
    /CaseEditor\?tsn_id=(\d+)/,
    /tsn_id=(\d+)/,
    /tsn_id.*?(\d+)/
  ];
  
  for (const pattern of patterns) {
    const match = responseData.match(pattern);
    if (match && match[1]) {
      return match[1];
    }
  }
  
  console.log('Could not find tsn_id in response');
  return null;
}

// Get active tests
async function getActiveTests() {
  const rows = await connection.query(`
    SELECT tsn_id, tc_id, uid, start_ts, error
    FROM test_session
    WHERE end_ts IS NULL
    ORDER BY start_ts DESC
  `);
  
  return rows[0];
}

// Check test status
async function checkTestStatus(tsnId) {
  // Query to check session status
  const [sessionRows] = await connection.query(`
    SELECT end_ts FROM test_session WHERE tsn_id = ?
  `, [tsnId]);
  
  // If end_ts is not null, the test is complete
  const isComplete = sessionRows.length > 0 && sessionRows[0].end_ts !== null;
  
  // Query to check test results
  const [resultRows] = await connection.query(`
    SELECT r.tc_id, r.outcome, COUNT(*) as count
    FROM test_result r
    JOIN output i ON i.cnt = r.cnt
    WHERE r.tsn_id = ?
    GROUP BY r.tc_id, r.outcome
    ORDER BY r.tc_id
  `, [tsnId]);
  
  console.log(`\nCurrent test status for tsn_id ${tsnId}:`);
  
  if (resultRows.length === 0) {
    console.log('No test results found yet');
    return { isComplete, allPassed: false };
  }
  
  // Display current status
  const testCases = new Map();
  let allPassed = true;
  
  resultRows.forEach(row => {
    const { tc_id, outcome, count } = row;
    
    if (!testCases.has(tc_id)) {
      testCases.set(tc_id, { outcomes: {} });
    }
    
    testCases.get(tc_id).outcomes[outcome] = count;
    
    // If any outcome is not 'P' (Passed), the test is not all passed
    if (outcome !== 'P') {
      allPassed = false;
    }
  });
  
  // Print status summary
  for (const [tcId, data] of testCases.entries()) {
    const outcomes = Object.entries(data.outcomes)
      .map(([outcome, count]) => `${outcome}: ${count}`)
      .join(', ');
    
    console.log(`Test Case ${tcId}: ${outcomes}`);
  }
  
  console.log(`Status: ${isComplete ? 'Completed' : 'Running'}`);
  
  return { isComplete, allPassed };
}

// Get test summary
async function getTestSummary(tsnId) {
  const [rows] = await connection.query(`
    SELECT 
      tsn_id, 
      SUM(CASE WHEN outcome='P' THEN 1 ELSE 0 END) AS passed_cases,
      SUM(CASE WHEN outcome='F' THEN 1 ELSE 0 END) AS failed_cases,
      TIMEDIFF(MAX(creation_time), MIN(creation_time)) AS duration
    FROM test_result
    WHERE tsn_id = ?
    GROUP BY tsn_id
  `, [tsnId]);
  
  if (rows.length === 0) {
    console.log(`No summary available for tsn_id ${tsnId}`);
    return { passed_cases: 0, failed_cases: 0 };
  }
  
  console.log('\n====== TEST SUMMARY ======');
  console.log(`Test Run ID: ${rows[0].tsn_id}`);
  console.log(`Passed Cases: ${rows[0].passed_cases}`);
  console.log(`Failed Cases: ${rows[0].failed_cases}`);
  console.log(`Duration: ${rows[0].duration}`);
  console.log('==========================');
  
  return rows[0];
}

// Get failure details
async function getFailureDetails(tsnId) {
  const [rows] = await connection.query(`
    SELECT r.cnt, r.seq_index, r.tsn_id, r.tc_id, r.outcome, i.txt
    FROM test_result r
    JOIN output i ON i.cnt = r.cnt
    WHERE r.tsn_id = ?
    AND r.outcome = 'F'
  `, [tsnId]);
  
  if (rows.length === 0) {
    console.log('No failures detected');
    return;
  }
  
  console.log('\n====== FAILURE DETAILS ======');
  
  // Group failures by test case
  const failuresByTestCase = new Map();
  
  rows.forEach(row => {
    const { tc_id, cnt, seq_index, txt } = row;
    
    if (!failuresByTestCase.has(tc_id)) {
      failuresByTestCase.set(tc_id, []);
    }
    
    failuresByTestCase.get(tc_id).push({
      cnt,
      seq_index,
      output: txt
    });
  });
  
  // Print failures by test case
  for (const [tcId, failures] of failuresByTestCase.entries()) {
    console.log(`\nTest Case ${tcId} Failures:`);
    failures.forEach((failure, index) => {
      console.log(`  Failure #${index + 1}:`);
      console.log(`  - Sequence Index: ${failure.seq_index}`);
      console.log(`  - Output: ${failure.output.substring(0, 150)}${failure.output.length > 150 ? '...' : ''}`);
    });
  }
  
  console.log('=============================');
}

// Run the script
main().catch(console.error); 
# Test Initiator Tracking Feature Documentation

This document explains how the test initiator tracking feature is implemented in the test flow verification script to differentiate between tests run by the current user and tests run by other users.

## Overview

The test initiator tracking feature enhances the Active Tests panel by:

1. Tracking which user initiated each test
2. Visually distinguishing tests initiated by the current user vs. other users
3. Providing test ownership information for better coordination
4. Preventing test collisions by showing server utilization

## Implementation Details

### 1. Database Structure

The feature uses the existing database structure without requiring schema changes:

- `test_session` table contains user information:
  - `tsn_id`: Test suite run ID
  - `uid`: User ID who initiated the test
  - `start_ts`: Test start timestamp
  - `end_ts`: Test end timestamp (NULL for running tests)
  - `tc_id`: Test case ID (when executing a single test case)
  - `ts_id`: Test suite ID (when executing a test suite)

### 2. Test Initiation with User Information

When a test is started via the API, user information is included in the request:

```javascript
// User information configuration
user: {
  user_id: 'your_user_id',    // User ID
  username: 'your_username'   // Username
}

// Adding user info to API request
Object.entries(config.user).forEach(([key, value]) => {
  params.append(key, value);
});
```

The API handles storing this information in the `test_session` table.

### 3. Querying Active Tests with User Information

The script queries the `test_session` table to determine active tests and their owners:

```javascript
// Query to get all active tests with user information
const rows = await dbManager.query(`
  SELECT s.tsn_id, s.tc_id, s.uid as initiator_user, s.start_ts as creation_time
  FROM test_session s
  WHERE s.end_ts IS NULL  -- Tests that haven't finished yet
  ORDER BY s.start_ts DESC
`);
```

### 4. Checking Test Status with User Information

When checking a specific test's status, the script joins `test_result` with `test_session`:

```javascript
// Query to check test status with user information
const rows = await dbManager.query(`
  SELECT r.tsn_id, r.tc_id, r.outcome, s.uid as initiator_user, COUNT(*) as count
  FROM test_result r
  JOIN output i ON i.cnt = r.cnt
  LEFT JOIN test_session s ON r.tsn_id = s.tsn_id
  WHERE r.tsn_id = ?
  GROUP BY r.tc_id, r.outcome, r.tsn_id, s.uid
  ORDER BY r.creation_time ASC
`, [tsnId]);
```

### 5. Determining Test Completion

To determine if a test is complete, the script checks the `end_ts` field in the `test_session` table:

```javascript
// Check if test is complete
const statusRows = await dbManager.query(`
  SELECT end_ts 
  FROM test_session
  WHERE tsn_id = ?
`, [tsnId]);

// If end_ts is null, the test is still running
const isRunning = statusRows.length === 0 || statusRows[0].end_ts === null;
```

### 6. Visual Differentiation in Console Output

The script distinguishes between current user and other users' tests:

```javascript
// For test status display
const currentUser = data.initiator === config.user.username ? '(Current User)' : '';
console.log(`Test Case ${tcId}: ${outcomes} | Initiated by: ${data.initiator} ${currentUser}`);

// For active tests display
console.log('Current User Tests:');
// ... display current user's tests

console.log('\nOther Users\' Tests:');
// ... display other users' tests
```

## Testing the Feature

To test this feature:

1. Configure the script with your user information
2. Run the script to start a test
3. Observe how your tests are marked as "(Current User)"
4. Have another user run tests simultaneously
5. Run the script again to see both your tests and other users' tests displayed with proper differentiation

## UI Implementation Guidelines

For the web UI implementation of this feature:

1. Use different colors/icons for current user vs. other users' tests
2. Add tooltips showing the initiator's username and start time
3. Consider adding filtering options to show only current user's tests, only others' tests, or all tests
4. Implement a refresh mechanism to update the Active Tests panel regularly

## Importance in Test Environment

This feature is particularly important because:

1. The test framework doesn't support multi-threading for the same test case
2. Only one test can run at a time on the server
3. Users need to know if the server is already being used to avoid test collisions
4. Team coordination is improved by showing who is running which tests 
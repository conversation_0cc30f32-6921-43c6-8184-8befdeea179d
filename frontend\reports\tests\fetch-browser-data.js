/**
 * Browser-based Data Fetcher for External API
 * 
 * This script can be added to the reports page and executed in the browser console
 * to fetch real data from the external API and download it as files.
 */

// Function to save data as a file
function saveAsFile(filename, data, type = 'text/html') {
  // Create a blob from the data
  const blob = new Blob([data], { type });
  
  // Create a link element
  const link = document.createElement('a');
  link.href = URL.createObjectURL(blob);
  link.download = filename;
  
  // Append to body, click, and remove
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  
  console.log(`Downloaded ${filename}`);
}

// Fetch and save report summary
async function fetchReportSummary(tsnId, credentials) {
  console.log(`Fetching report summary for test session ${tsnId}...`);
  
  try {
    // Use the proxy endpoint for better CORS handling
    const url = `/api/external/ReportSummary?tsn_id=${tsnId}`;
    
    const response = await fetch(url, {
      method: 'GET',
      credentials: 'include',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    });
    
    if (!response.ok) {
      throw new Error(`Failed to fetch report summary: ${response.status} ${response.statusText}`);
    }
    
    // Get the response as text (HTML)
    const html = await response.text();
    
    // Save the HTML response
    saveAsFile(`real-summary-${tsnId}.html`, html);
    
    // Save response metadata
    saveAsFile(`real-summary-${tsnId}-metadata.json`, JSON.stringify({
      url,
      status: response.status,
      statusText: response.statusText,
      headers: Object.fromEntries([...response.headers.entries()]),
      contentLength: html.length
    }, null, 2), 'application/json');
    
    return html;
  } catch (error) {
    console.error('Error fetching report summary:', error);
    throw error;
  }
}

// Fetch and save report details
async function fetchReportDetails(tsnId, credentials) {
  console.log(`Fetching report details for test session ${tsnId}...`);
  
  try {
    // Use the proxy endpoint for better CORS handling
    const url = `/api/external/ReportDetails?tsn_id=${tsnId}`;
    
    const response = await fetch(url, {
      method: 'GET',
      credentials: 'include',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    });
    
    if (!response.ok) {
      throw new Error(`Failed to fetch report details: ${response.status} ${response.statusText}`);
    }
    
    // Get the response as text (HTML)
    const html = await response.text();
    
    // Save the HTML response
    saveAsFile(`real-details-${tsnId}.html`, html);
    
    // Save response metadata
    saveAsFile(`real-details-${tsnId}-metadata.json`, JSON.stringify({
      url,
      status: response.status,
      statusText: response.statusText,
      headers: Object.fromEntries([...response.headers.entries()]),
      contentLength: html.length
    }, null, 2), 'application/json');
    
    return html;
  } catch (error) {
    console.error('Error fetching report details:', error);
    throw error;
  }
}

// Login to the external API
async function loginToExternalApi(credentials) {
  console.log(`Logging in as ${credentials.uid}...`);
  
  try {
    // Use the proxy endpoint for better CORS handling
    const url = `/api/external/Login`;
    console.log(`Login URL: ${url}`);
    
    // Create form data
    const formData = new URLSearchParams();
    formData.append('uid', credentials.uid);
    formData.append('password', credentials.password);
    console.log(`Form data prepared: uid=${credentials.uid}, password=******`);
    
    console.log('Sending login request...');
    const response = await fetch(url, {
      method: 'POST',
      credentials: 'include',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      body: formData
    });
    
    console.log(`Login response status: ${response.status} ${response.statusText}`);
    console.log('Response headers:', Object.fromEntries([...response.headers.entries()]));
    
    // Save the response cookies
    const cookies = document.cookie;
    console.log('Current cookies:', cookies);
    
    // Check redirect or 200 status for success
    if (response.ok || response.status === 302) {
      console.log('Login successful');
      
      // Try to get the response text
      try {
        const text = await response.text();
        console.log('Login response body:', text.substring(0, 150) + '...');
      } catch (e) {
        console.log('Could not read response body:', e.message);
      }
      
      return true;
    } else {
      throw new Error(`Login failed with status ${response.status}`);
    }
  } catch (error) {
    console.error('Error logging in:', error);
    throw error;
  }
}

// Main function to fetch all data
async function fetchAllData(tsnId, credentials) {
  try {
    console.log(`Starting data fetch for test session ${tsnId}...`);
    
    // Login first
    await loginToExternalApi(credentials);
    
    // Fetch report summary
    const summaryHtml = await fetchReportSummary(tsnId, credentials);
    
    // Fetch report details
    const detailsHtml = await fetchReportDetails(tsnId, credentials);
    
    console.log('All data fetched and saved successfully');
    
    return {
      summary: summaryHtml,
      details: detailsHtml
    };
  } catch (error) {
    console.error('Error fetching data:', error);
    throw error;
  }
}

// Export the functions for use in the browser console
// Make sure the API object exists in the global scope
if (typeof window !== 'undefined') {
  // Initialize the API object if it doesn't exist
  window.api = window.api || {};
  
  // Explicitly assign all functions to the global API object
  window.api.fetchAllData = fetchAllData;
  window.api.fetchReportSummary = fetchReportSummary;
  window.api.fetchReportDetails = fetchReportDetails;
  window.api.loginToExternalApi = loginToExternalApi;
  
  console.log('API functions successfully exported to window.api:', {
    fetchAllData: typeof fetchAllData,
    fetchReportSummary: typeof fetchReportSummary,
    fetchReportDetails: typeof fetchReportDetails,
    loginToExternalApi: typeof loginToExternalApi
  });
}

// Usage instructions
console.log(`
External API Data Fetcher loaded!

To fetch data for a specific test session:

1. Login first (if needed):
   window.api.loginToExternalApi({
     uid: '<EMAIL>',
     password: 'your_password'
   })

2. Fetch all data:
   window.api.fetchExternalData('14683', {
     uid: '<EMAIL>',
     password: 'your_password'
   })

Or fetch individual parts:
   window.api.fetchReportSummary('14683')
   window.api.fetchReportDetails('14683')

The data will be downloaded as HTML and JSON files.
`);

/**
 * Proxy middleware to forward requests to MPTSR API
 */
const fetch = require('node-fetch');
const { MPTSR_BASE_URL } = require('../config/env-config');
const { getJsessionId } = require('../services/cookie-auth');

// List of endpoints that require cookie-based authentication (port 9080)
const cookieAuthEndpoints = [
  'Login',
  'ReportSummary',
  'ReportDetails',
  'RemoveSession'
];

// Proxy middleware to forward requests to MPTSR API
const proxyToMptsr = async (req, res) => {
  try {
    // Extract the path after /api/
    const apiPath = req.path.replace(/^\/api\//, '');
    // Remove any leading slash from the apiPath
    const cleanApiPath = apiPath.replace(/^\//, '');
    console.log(`[Proxy] Original API path: "${apiPath}", Cleaned: "${cleanApiPath}"`);

    // Special case for test-status endpoint - use ReportSummary instead
    let targetPath = cleanApiPath;
    if (cleanApiPath === 'test-status') {
      targetPath = 'ReportSummary';
      console.log(`[Proxy] Redirecting test-status request to ReportSummary endpoint`);
    }

    // Determine the endpoint name
    let endpointName = targetPath.split('/')[0];
    console.log(`[Proxy] Extracted endpoint: "${endpointName}"`);

    // Check if this endpoint needs cookie auth
    const requiresCookieAuth = cookieAuthEndpoints.includes(endpointName);

    // Determine the base URL based on whether cookie auth is required
    let baseUrl;

    if (requiresCookieAuth) {
      baseUrl = 'http://mprts-qa02.lab.wagerworks.com:9080/AutoRun';
      console.log(`[Proxy] Using cookie auth (port 9080) for endpoint: ${endpointName}`);
    } else {
      baseUrl = 'http://mprts-qa02.lab.wagerworks.com:9080/AutoRun';
      console.log(`[Proxy] Using standard auth (port 9080) for endpoint: ${endpointName}`);
    }

    const url = `${baseUrl}/${targetPath}`;
    console.log(`[Proxy] Forwarding ${req.method} request to: ${url}`);

    // Copy the query parameters
    const query = new URLSearchParams();
    for (const [key, value] of Object.entries(req.query)) {
      if (key !== 'password') { // Don't log the password
        query.append(key, value);
      } else {
        query.append(key, '********');
      }
    }

    // Log the proxy request
    console.log(`[Proxy] Forwarding ${req.method} request to: ${url}${query.toString() ? '?' + query.toString() : ''}`);

    // Prepare headers
    const headers = {
      'Content-Type': req.method === 'GET' ? 'application/json' : 'application/x-www-form-urlencoded'
    };

    // If cookie auth is required, get a JSESSIONID cookie
    if (requiresCookieAuth) {
      // Extract credentials from query or body
      const uid = req.query.uid || (req.body && req.body.uid);
      const password = req.query.password || (req.body && req.body.password);

      if (!uid || !password) {
        throw new Error('Missing credentials (uid/password) required for cookie authentication');
      }

      // Get a valid JSESSIONID cookie
      const jsessionId = await getJsessionId(uid, password);

      // Add the cookie to the headers
      headers['Cookie'] = `JSESSIONID=${jsessionId}`;
    }

    // Prepare the request body for POST requests
    let body;
    if (req.method !== 'GET') {
      if (requiresCookieAuth) {
        // For cookie auth endpoints, use form URL encoded
        const formData = new URLSearchParams();
        for (const [key, value] of Object.entries(req.body)) {
          if (key !== 'password') { // Don't include password in the form data
            formData.append(key, value);
          }
        }
        body = formData;
      } else {
        // For other endpoints, use JSON
        body = JSON.stringify(req.body);
      }
    }

    // Make the request to the external API
    const response = await fetch(url + (query.toString() ? '?' + query.toString() : ''), {
      method: req.method,
      headers,
      body
    });

    // Get the response data
    const data = await response.text();

    // Log the response status
    console.log(`[Proxy] Received response with status: ${response.status}`);

    // Forward any Set-Cookie headers from the external API to the client
    if (response.headers.has('set-cookie')) {
      const cookies = response.headers.raw()['set-cookie'];
      if (Array.isArray(cookies)) {
        // If there are multiple cookies, forward each one separately
        cookies.forEach(cookie => {
          // Remove Secure attribute from cookies when in development
          const modifiedCookie = cookie.replace(/; Secure/g, '');
          // Remove Domain attribute as it might cause issues with localhost
          const finalCookie = modifiedCookie.replace(/; Domain=[^;]+/g, '');
          res.append('Set-Cookie', finalCookie);
        });
        console.log(`[Proxy] Forwarding ${cookies.length} cookies to client`);
      } else {
        // Single cookie as string
        const modifiedCookies = cookies.replace(/; Secure/g, '');
        const finalCookies = modifiedCookies.replace(/; Domain=[^;]+/g, '');
        res.set('Set-Cookie', finalCookies);
        console.log('[Proxy] Forwarding cookie to client');
      }
    }

    // Forward the response status and data
    res.status(response.status).send(data);
  } catch (error) {
    console.error('[Proxy] Error forwarding request:', error);
    res.status(500).json({
      success: false,
      message: 'Error forwarding request to external API',
      error: error.message
    });
  }
};

module.exports = proxyToMptsr;

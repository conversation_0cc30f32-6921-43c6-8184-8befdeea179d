/**
 * Complete Test Workflow
 * 
 * This script provides a full end-to-end workflow for executing tests and monitoring their progress.
 * It combines the API test execution and database monitoring functionalities.
 * 
 * Usage:
 *   node workflow_test.js [environment] [testCaseId] [username] [monitorTimeoutMinutes]
 * 
 * Examples:
 *   node workflow_test.js qa02 3180 <EMAIL> 10
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');
const dbConnector = require('../utils/db-connector');

// Process command line arguments
const args = process.argv.slice(2);
const environment = args[0] || 'qa02';
const testCaseId = args[1] || '3180';
const username = args[2] || '<EMAIL>';
const monitorTimeoutMinutes = parseInt(args[3] || '10', 10);

// Main function
async function main() {
  console.log(`\n=== COMPLETE TEST WORKFLOW FOR ${environment.toUpperCase()} ===\n`);
  
  // Validate environment
  if (!dbConnector.environments[environment]) {
    console.error(`Error: Unknown environment '${environment}'`);
    console.error(`Available environments: ${Object.keys(dbConnector.environments).join(', ')}`);
    process.exit(1);
  }
  
  // Get environment configuration
  const config = dbConnector.environments[environment];
  
  // Base URL for API calls
  const baseUrl = config.BASE_URL;
  
  console.log('Test Configuration:');
  console.log('--------------------------');
  console.log(`Environment: ${environment}`);
  console.log(`API URL: ${baseUrl}`);
  console.log(`Test Case ID: ${testCaseId}`);
  console.log(`Username: ${username}`);
  console.log(`Monitor Timeout: ${monitorTimeoutMinutes} minutes`);
  console.log('--------------------------\n');
  
  try {
    // Step 1: Execute the test
    console.log('\n=== STEP 1: EXECUTING TEST ===');
    const tsnId = await executeTest(baseUrl, testCaseId, username, environment);
    
    if (!tsnId) {
      throw new Error('Failed to execute test - no tsn_id returned');
    }
    
    console.log(`Test execution started with tsn_id: ${tsnId}`);
    
    // Step 2: Monitor test execution
    console.log('\n=== STEP 2: MONITORING TEST EXECUTION ===');
    await monitorTestExecution(tsnId, environment, monitorTimeoutMinutes);
    
    // Step 3: Display final results
    console.log('\n=== STEP 3: DISPLAYING FINAL RESULTS ===');
    await displayTestResults(tsnId, environment);
    
    console.log('\n=== TEST WORKFLOW COMPLETED SUCCESSFULLY ===');
  } catch (error) {
    console.error(`\nWorkflow Error: ${error.message}`);
    process.exit(1);
  }
}

/**
 * Execute a test through the API
 * @param {string} baseUrl - Base API URL
 * @param {string} testCaseId - Test case ID
 * @param {string} username - Username
 * @param {string} environment - Environment name
 * @returns {Promise<string|null>} Test session ID (tsn_id) or null if failed
 */
async function executeTest(baseUrl, testCaseId, username, environment) {
  console.log('Executing test through API...');
  
  // Create request data with exact format (including double-ampersand)
  const requestData = `uid=${username}&password=test&tc_id=${testCaseId}&envir=${environment}&shell_host=jps-qa10-app01&file_path=/home/<USER>/&operatorConfigs=operatorNameConfigs&kafka_server=kafka-qa-a0.lab.wagerworks.com&dataCenter=GU&rgs_env=${environment}&old_version=0&&networkType1=multi-site&networkType2=multi-site&sign=-&rate_src=local`;
  
  try {
    // Make the API request
    console.log(`Sending request to ${baseUrl}CaseRunner...`);
    
    const response = await axios.post(
      `${baseUrl}CaseRunner`,
      requestData,
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      }
    );
    
    console.log(`API response status: ${response.status}`);
    
    // Extract tsn_id from the response
    const tsnId = extractTsnId(response.data);
    
    if (!tsnId) {
      console.error('Could not extract tsn_id from response');
      
      // Save the response data for debugging
      const responsePath = path.join(__dirname, 'api_response_error.html');
      fs.writeFileSync(responsePath, 
        typeof response.data === 'string' ? response.data : JSON.stringify(response.data, null, 2)
      );
      console.log(`Response saved to ${responsePath} for debugging`);
      
      return null;
    }
    
    return tsnId;
  } catch (error) {
    console.error(`API call error: ${error.message}`);
    if (error.response) {
      console.error(`Response status: ${error.response.status}`);
    }
    return null;
  }
}

/**
 * Extract tsn_id from API response
 * @param {string} responseData - API response data
 * @returns {string|null} Extracted tsn_id or null if not found
 */
function extractTsnId(responseData) {
  if (typeof responseData !== 'string') {
    console.log('Response is not a string');
    return null;
  }
  
  // Try various patterns to extract tsn_id
  const patterns = [
    /ReportSummary\?tsn_id=(\d+)/,
    /CaseEditor\?tsn_id=(\d+)/,
    /tsn_id=(\d+)/,
    /tsn_id.*?(\d+)/
  ];
  
  for (const pattern of patterns) {
    const match = responseData.match(pattern);
    if (match && match[1]) {
      return match[1];
    }
  }
  
  return null;
}

/**
 * Monitor test execution until completion or timeout
 * @param {string} tsnId - Test session ID
 * @param {string} environment - Environment name
 * @param {number} timeoutMinutes - Timeout in minutes
 * @returns {Promise<boolean>} True if test completed, false if timed out
 */
async function monitorTestExecution(tsnId, environment, timeoutMinutes) {
  console.log(`Monitoring test execution for tsn_id: ${tsnId}...`);
  
  try {
    // Connect to database
    await dbConnector.init(environment, { debug: false });
    
    let isComplete = false;
    const startTime = Date.now();
    const timeoutMs = timeoutMinutes * 60 * 1000;
    const intervalMs = 5000; // 5 seconds
    
    while (!isComplete && (Date.now() - startTime < timeoutMs)) {
      // Check if the test session has ended
      const sessionResult = await dbConnector.query(`
        SELECT end_ts FROM test_session WHERE tsn_id = ?
      `, [tsnId]);
      
      // Handle both result formats
      const sessionRows = Array.isArray(sessionResult[0]) ? sessionResult[0] : sessionResult;
      
      // If end_ts is not null, the test is complete
      isComplete = sessionRows.length > 0 && (sessionRows[0].end_ts || sessionRows[0].column1) !== null;
      
      // Display current status
      await displayCurrentStatus(tsnId);
      
      if (isComplete) {
        console.log('\n✅ Test execution completed!');
        break;
      }
      
      // Wait before checking again
      console.log(`Waiting ${intervalMs/1000} seconds before next check...`);
      await new Promise(resolve => setTimeout(resolve, intervalMs));
    }
    
    if (!isComplete) {
      console.log(`\n⚠️ Test run monitoring timed out after ${timeoutMinutes} minutes`);
      console.log('The test may still be running, but the script will exit monitoring');
      return false;
    }
    
    return true;
  } catch (error) {
    console.error(`Monitoring error: ${error.message}`);
    throw error;
  }
}

/**
 * Display current test status
 * @param {string} tsnId - Test session ID
 */
async function displayCurrentStatus(tsnId) {
  try {
    // Get latest test results
    const results = await dbConnector.query(`
      SELECT tc_id, outcome, COUNT(*) as count
      FROM test_result
      WHERE tsn_id = ?
      GROUP BY tc_id, outcome
      ORDER BY tc_id
    `, [tsnId]);
    
    console.log(`\nCurrent test status (${new Date().toISOString()}):`);
    console.log('----------------------------------------');
    
    // Handle both array formats: direct format or [rows, fields] format
    const resultRows = Array.isArray(results[0]) ? results[0] : results;
    
    if (resultRows.length === 0) {
      console.log('No test results found yet - execution may not have started');
      return;
    }
    
    // Group results by test case
    const testCases = {};
    
    resultRows.forEach(row => {
      const tcId = row.tc_id || row.column1;
      const outcome = row.outcome || row.column2;
      const count = row.count || row.column3 || 0;
      
      if (!testCases[tcId]) {
        testCases[tcId] = {};
      }
      
      testCases[tcId][outcome] = count;
    });
    
    // Display results
    for (const [tcId, outcomes] of Object.entries(testCases)) {
      const outcomeStr = Object.entries(outcomes)
        .map(([outcome, count]) => `${outcome}: ${count}`)
        .join(', ');
      
      console.log(`Test Case ${tcId}: ${outcomeStr}`);
    }
    
    // Get activity timestamp
    const activityResult = await dbConnector.query(`
      SELECT MAX(creation_time) as latest_activity
      FROM test_result
      WHERE tsn_id = ?
    `, [tsnId]);
    
    // Handle both result formats
    const activityRows = Array.isArray(activityResult[0]) ? activityResult[0] : activityResult;
    
    if (activityRows.length > 0 && activityRows[0].latest_activity) {
      const latestActivity = activityRows[0].latest_activity || activityRows[0].column1;
      console.log(`Latest activity: ${latestActivity}`);
    }
  } catch (error) {
    console.error(`Error getting test status: ${error.message}`);
  }
}

/**
 * Display final test results
 * @param {string} tsnId - Test session ID
 * @param {string} environment - Environment name
 */
async function displayTestResults(tsnId, environment) {
  try {
    // Ensure database is connected
    if (!dbConnector.getConnectionInfo().initialized) {
      await dbConnector.init(environment, { debug: false });
    }
    
    // Get test summary
    const summaryResult = await dbConnector.query(`
      SELECT 
        tsn_id, 
        SUM(CASE WHEN outcome='P' THEN 1 ELSE 0 END) AS passed_cases,
        SUM(CASE WHEN outcome='F' THEN 1 ELSE 0 END) AS failed_cases,
        TIMEDIFF(MAX(creation_time), MIN(creation_time)) AS duration
      FROM test_result
      WHERE tsn_id = ?
      GROUP BY tsn_id
    `, [tsnId]);
    
    // Handle both result formats
    const summaryRows = Array.isArray(summaryResult[0]) ? summaryResult[0] : summaryResult;
    
    // ------------------------------------------------------
    // Get user details from test_session
    const sessionResult = await dbConnector.query(`
      SELECT uid, start_ts, end_ts FROM test_session WHERE tsn_id = ?
    `, [tsnId]);
    
    // Handle both result formats
    const sessionRows = Array.isArray(sessionResult[0]) ? sessionResult[0] : sessionResult;
    
    if (summaryRows.length === 0) {
      console.log('\n╔═══════════════════════════════════════════════╗');
      console.log('║               TEST RESULTS                    ║');
      console.log('╚═══════════════════════════════════════════════╝');
      console.log('No test results available');
      return;
    }

    const summary = summaryRows[0];
    const session = sessionRows.length > 0 ? sessionRows[0] : {};
    
    const passedCases = summary.passed_cases || summary.column2 || 0;
    const failedCases = summary.failed_cases || summary.column3 || 0;
    const duration = summary.duration || summary.column4 || 'Unknown';
    const totalCases = parseInt(passedCases) + parseInt(failedCases);
    const successRate = totalCases > 0 ? Math.round((passedCases / totalCases) * 100) : 0;
    
    // Get all test steps for detailed reporting
    const detailedStepsResult = await dbConnector.query(`
      SELECT 
        r.tc_id, r.seq_index, r.outcome, r.cnt, 
        i.txt, r.creation_time
      FROM test_result r
      LEFT JOIN output i ON r.cnt = i.cnt
      WHERE r.tsn_id = ?
      ORDER BY r.tc_id, r.seq_index
    `, [tsnId]);
    
    // Handle both result formats
    const detailedSteps = Array.isArray(detailedStepsResult[0]) ? detailedStepsResult[0] : detailedStepsResult;
    
    // Format start and end times nicely
    const startTime = session.start_ts || session.column2 || 'Unknown';
    const endTime = session.end_ts || session.column3 || 'Unknown';
    const formattedStartTime = startTime !== 'Unknown' ? new Date(startTime).toLocaleString() : 'Unknown';
    const formattedEndTime = endTime !== 'Unknown' ? new Date(endTime).toLocaleString() : 'Unknown';
    
    // Group steps by test case for better organization
    const stepsByTestCase = {};
    detailedSteps.forEach(step => {
      const tcId = step.tc_id || step.column1;
      if (!stepsByTestCase[tcId]) {
        stepsByTestCase[tcId] = [];
      }
      stepsByTestCase[tcId].push(step);
    });
    
    // ╔═══════════════════════════════════════════════════════════════════════╗
    // ║                         TEST EXECUTION REPORT                         ║
    // ╚═══════════════════════════════════════════════════════════════════════╝
    
    console.log('\n╔═══════════════════════════════════════════════════════════════════════╗');
    console.log('║                         TEST EXECUTION REPORT                         ║');
    console.log('╚═══════════════════════════════════════════════════════════════════════╝');
    
    console.log('\n┌─── Test Session Information ───────────────────────────────────────┐');
    console.log(`│ Session ID:      ${tsnId.padEnd(53)} │`);
    console.log(`│ Initiated By:    ${(session.uid || session.column1 || 'Unknown').padEnd(53)} │`);
    console.log(`│ Started At:      ${formattedStartTime.padEnd(53)} │`);
    console.log(`│ Completed At:    ${formattedEndTime.padEnd(53)} │`);
    console.log(`│ Duration:        ${duration.padEnd(53)} │`);
    console.log('└───────────────────────────────────────────────────────────────────┘');
    
    console.log('\n┌─── Test Summary ─────────────────────────────────────────────────┐');
    console.log(`│ Total Test Cases: ${totalCases.toString().padEnd(51)} │`);
    console.log(`│ Passed:           ${passedCases.toString().padEnd(51)} │`);
    console.log(`│ Failed:           ${failedCases.toString().padEnd(51)} │`);
    console.log(`│ Success Rate:     ${successRate}%${' '.repeat(50 - successRate.toString().length)}│`);
    console.log('└───────────────────────────────────────────────────────────────────┘');
    
    // Detailed results by test case
    console.log('\n┌─── Detailed Test Results ───────────────────────────────────────┐');
    
    for (const [tcId, steps] of Object.entries(stepsByTestCase)) {
      // Count passed and failed steps within this test case
      const tcPassedSteps = steps.filter(s => (s.outcome || s.column3) === 'P').length;
      const tcFailedSteps = steps.filter(s => (s.outcome || s.column3) === 'F').length;
      const tcTotalSteps = steps.length;
      const tcSuccessRate = Math.round((tcPassedSteps / tcTotalSteps) * 100);
      
      // Test case header
      console.log(`│                                                                 │`);
      console.log(`│ Test Case: ${tcId.padEnd(54)} │`);
      console.log(`│ ${'-'.repeat(63)} │`);
      console.log(`│ Steps: ${tcTotalSteps.toString().padEnd(56)} │`);
      console.log(`│ Passed: ${tcPassedSteps.toString().padEnd(55)} │`);
      console.log(`│ Failed: ${tcFailedSteps.toString().padEnd(55)} │`);
      console.log(`│ Status: ${tcFailedSteps === 0 ? 'PASSED ✅' : 'FAILED ❌'}${' '.repeat(51 - (tcFailedSteps === 0 ? 'PASSED ✅'.length : 'FAILED ❌'.length))}│`);
      
      // Show detailed steps if it's not too many (or if there are failures)
      if (tcTotalSteps <= 20 || tcFailedSteps > 0) {
        console.log(`│ ${'-'.repeat(63)} │`);
        console.log(`│ Step Details:${' '.repeat(51)} │`);
        
        steps.forEach(step => {
          const stepNumber = step.seq_index || step.column2 || 'Unknown';
          const outcome = step.outcome || step.column3 || 'Unknown';
          const stepResult = outcome === 'P' ? 'PASS ✅' : outcome === 'F' ? 'FAIL ❌' : outcome;
          const description = step.txt || step.column5 || 'No description available';
          const shortDesc = description.length > 40 ? description.substring(0, 37) + '...' : description;
          
          console.log(`│   Step ${stepNumber.toString().padStart(2)}: ${stepResult} - ${shortDesc.padEnd(40)} │`);
        });
      } else {
        // Just show a summary for test cases with many steps
        console.log(`│ ${'-'.repeat(63)} │`);
        console.log(`│ Step Details: ${tcTotalSteps} steps executed (too many to display)${' '.repeat(19 - tcTotalSteps.toString().length)} │`);
      }
    }
    
    console.log('└───────────────────────────────────────────────────────────────────┘');
    
    // Final outcome
    console.log('\n┌─── Final Test Outcome ──────────────────────────────────────────┐');
    if (parseInt(failedCases) === 0) {
      console.log('│                                                                 │');
      console.log('│                      ALL TESTS PASSED ✅                       │');
      console.log('│                                                                 │');
    } else {
      console.log('│                                                                 │');
      console.log('│                      TESTS FAILED ❌                           │');
      console.log('│                                                                 │');
      
      // Show failure details
      console.log('│ Failure Details:                                                │');
      const failureResult = await dbConnector.query(`
        SELECT r.tc_id, r.outcome, r.seq_index, i.txt
        FROM test_result r
        JOIN output i ON r.cnt = i.cnt
        WHERE r.tsn_id = ? AND r.outcome = 'F'
        ORDER BY r.tc_id, r.seq_index
      `, [tsnId]);
      
      // Handle both result formats
      const failureRows = Array.isArray(failureResult[0]) ? failureResult[0] : failureResult;
      
      // Group failures by test case
      const failuresByTestCase = {};
      
      if (failureRows && failureRows.length > 0) {
        failureRows.forEach(row => {
          const tcId = row.tc_id || row.column1;
          const seqIndex = row.seq_index || row.column3;
          const txt = row.txt || row.column4;
          
          if (!failuresByTestCase[tcId]) {
            failuresByTestCase[tcId] = [];
          }
          
          failuresByTestCase[tcId].push({
            seq_index: seqIndex,
            output: txt
          });
        });
        
        // Print failures by test case
        for (const [tcId, failures] of Object.entries(failuresByTestCase)) {
          console.log(`│   Test Case ${tcId}:${' '.repeat(50 - tcId.toString().length)} │`);
          failures.forEach((failure, index) => {
            console.log(`│     Step ${failure.seq_index}: ${' '.repeat(51 - failure.seq_index.toString().length)} │`);
            // Truncate and format the output text to fit in the box
            const outputText = failure.output.substring(0, 55);
            console.log(`│       ${outputText}${' '.repeat(57 - outputText.length)} │`);
          });
        }
      } else {
        console.log('│   No failure details available                                │');
      }
    }
    console.log('└───────────────────────────────────────────────────────────────────┘');
    
    // Close database connection
    await dbConnector.close();
  } catch (error) {
    console.error(`Error displaying test results: ${error.message}`);
    
    // Ensure connection is closed
    try {
      await dbConnector.close();
    } catch (closeError) {
      // Ignore close errors
    }
  }
}

// Run the main function
main().catch(console.error);
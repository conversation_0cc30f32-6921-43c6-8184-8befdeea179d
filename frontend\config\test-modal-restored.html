<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Modal Restored</title>
    <link rel="stylesheet" href="css/test-details-modal.css">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-button { padding: 10px 20px; margin: 10px; cursor: pointer; background: #0078d4; color: white; border: none; border-radius: 4px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .success { background-color: #d4edda; color: #155724; }
        .info { background-color: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <h1>Test Details Modal - Restored Design</h1>
    
    <div class="status info">
        <strong>Status:</strong> Testing the restored original modal design and functionality
    </div>
    
    <div>
        <button class="test-button" onclick="testModal('17150')">Test Modal with TSN 17150</button>
        <button class="test-button" onclick="testModal('17151')">Test Modal with TSN 17151</button>
        <button class="test-button" onclick="testModalError()">Test Error State</button>
    </div>
    
    <h2>Features Restored:</h2>
    <ul>
        <li>✅ Original beautiful modal design with proper styling</li>
        <li>✅ Smooth animations and transitions</li>
        <li>✅ Proper grid layout for test details</li>
        <li>✅ Status badges with color coding</li>
        <li>✅ Responsive design for mobile devices</li>
        <li>✅ Accessibility features (keyboard navigation, ARIA labels)</li>
        <li>✅ Performance optimizations (caching) when available</li>
        <li>✅ Loading states with spinner</li>
        <li>✅ Error handling</li>
    </ul>
    
    <!-- Mock API Service -->
    <script>
        window.apiService = {
            getTestDetails: async function(tsnId) {
                console.log('Mock API: getTestDetails called for:', tsnId);
                
                // Simulate loading delay
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                if (tsnId === 'error') {
                    throw new Error('Test error for demonstration');
                }
                
                // Return mock test details
                return {
                    tsn_id: tsnId,
                    tc_id: '3180',
                    name: `Sample Test Case ${tsnId}`,
                    status: tsnId === '17150' ? 'passed' : 'running',
                    environment: 'qa02',
                    start_time: new Date(Date.now() - 300000).toISOString(), // 5 minutes ago
                    end_time: tsnId === '17150' ? new Date().toISOString() : null,
                    comments: 'This is a sample test case for demonstration purposes',
                    details_url: `https://example.com/test-details/${tsnId}`
                };
            }
        };
        
        // Mock simple request manager for performance optimization
        window.simpleRequestManager = {
            cache: new Map(),
            executeRequest: async function(key, requestFn, cacheType) {
                console.log('Using simple request manager for:', key);
                
                // Check cache
                const cached = this.cache.get(key);
                if (cached && Date.now() - cached.timestamp < 30000) { // 30 second TTL
                    console.log('Cache hit for:', key);
                    return cached.data;
                }
                
                // Execute request
                const result = await requestFn();
                
                // Cache result
                this.cache.set(key, {
                    data: result,
                    timestamp: Date.now()
                });
                
                console.log('Cached result for:', key);
                return result;
            }
        };
        
        function testModal(tsnId) {
            if (window.testDetailsModal) {
                window.testDetailsModal.show(tsnId);
            } else {
                alert('Test Details Modal not loaded yet. Please wait a moment and try again.');
            }
        }
        
        function testModalError() {
            if (window.testDetailsModal) {
                window.testDetailsModal.show('error');
            } else {
                alert('Test Details Modal not loaded yet. Please wait a moment and try again.');
            }
        }
        
        // Show status when modal is ready
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(() => {
                if (window.testDetailsModal) {
                    const statusDiv = document.createElement('div');
                    statusDiv.className = 'status success';
                    statusDiv.innerHTML = '<strong>✅ Success:</strong> Test Details Modal loaded and ready to use!';
                    document.body.insertBefore(statusDiv, document.querySelector('h2'));
                }
            }, 500);
        });
    </script>
    
    <!-- Load the restored modal -->
    <script src="js/test-details-modal.js"></script>
</body>
</html>

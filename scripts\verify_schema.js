/**
 * Database Schema Verification Tool
 * 
 * This script verifies the database schema structure for tables related to test execution.
 * It checks for required tables and columns that the application depends on.
 * 
 * Usage:
 *   node verify_schema.js [environment]
 * 
 * Examples:
 *   node verify_schema.js qa02
 */

const dbConnector = require('./db-connector');

// Process command line arguments
const args = process.argv.slice(2);
const environment = args[0] || 'qa02';

// Required tables and columns
const requiredSchema = {
  test_session: [
    'tsn_id',
    'uid',
    'start_ts',
    'end_ts',
    'error',
    'tc_id',
    'ts_id'
  ],
  test_result: [
    'tc_id',
    'seq_index',
    'tsn_id',
    'outcome',
    'creation_time',
    'cnt'
  ],
  output: [
    'cnt',
    'txt'
  ],
  test_case: [
    'tc_id',
    'status',
    'name'
  ],
  test_case_group: [
    'ts_id',
    'tc_id',
    'seq_index'
  ],
  test_suite_group: [
    'ts_id',
    'pj_id',
    'seq_index'
  ]
};

// Main function
async function main() {
  console.log(`\n=== DATABASE SCHEMA VERIFICATION FOR ${environment.toUpperCase()} ===\n`);
  
  try {
    // Connect to database
    console.log(`Connecting to ${environment} database...`);
    await dbConnector.init(environment, { debug: false });
    console.log('Connection established successfully');
    
    // Verify tables exist and have required columns
    console.log('\nVerifying database schema...');
    
    const results = {
      tables: {},
      missing: {
        tables: [],
        columns: {}
      },
      summary: {
        total_tables: Object.keys(requiredSchema).length,
        verified_tables: 0,
        missing_tables: 0,
        tables_with_missing_columns: 0
      }
    };
    
    // Check each required table
    for (const tableName of Object.keys(requiredSchema)) {
      await verifyTable(tableName, requiredSchema[tableName], results);
    }
    
    // Print summary
    console.log('\n=== SCHEMA VERIFICATION SUMMARY ===');
    console.log(`Total tables checked: ${results.summary.total_tables}`);
    console.log(`Verified tables: ${results.summary.verified_tables}`);
    console.log(`Missing tables: ${results.summary.missing_tables}`);
    console.log(`Tables with missing columns: ${results.summary.tables_with_missing_columns}`);
    
    if (results.missing.tables.length > 0) {
      console.log('\nMissing tables:');
      results.missing.tables.forEach(table => {
        console.log(`- ${table}`);
      });
    }
    
    if (Object.keys(results.missing.columns).length > 0) {
      console.log('\nMissing columns:');
      Object.entries(results.missing.columns).forEach(([table, columns]) => {
        console.log(`- ${table}: ${columns.join(', ')}`);
      });
    }
    
    if (results.summary.missing_tables === 0 && results.summary.tables_with_missing_columns === 0) {
      console.log('\n✅ Schema verification passed! All required tables and columns exist.');
    } else {
      console.log('\n⚠️ Schema verification failed! Some tables or columns are missing.');
    }
    
    // Close connection
    await dbConnector.close();
  } catch (error) {
    console.error(`\nError: ${error.message}`);
    
    // Close the database connection if it was opened
    try {
      await dbConnector.close();
    } catch (closeError) {
      // Ignore close errors
    }
    
    process.exit(1);
  }
}

/**
 * Verify a table exists and has required columns
 * @param {string} tableName - Table name to verify
 * @param {Array<string>} requiredColumns - Array of required column names
 * @param {Object} results - Results object to update
 */
async function verifyTable(tableName, requiredColumns, results) {
  console.log(`\nVerifying table: ${tableName}`);
  
  try {
    // Check if table exists
    const tableExists = await checkTableExists(tableName);
    
    if (!tableExists) {
      console.log(`❌ Table ${tableName} does not exist`);
      results.missing.tables.push(tableName);
      results.summary.missing_tables++;
      return;
    }
    
    // Get table structure
    const columns = await getTableColumns(tableName);
    console.log(`Table found with ${columns.length} columns`);
    
    // Initialize table result
    results.tables[tableName] = {
      exists: true,
      columns: columns,
      missing_columns: []
    };
    
    // Check for required columns
    const columnNames = columns.map(col => col.Field.toLowerCase());
    
    for (const requiredColumn of requiredColumns) {
      if (!columnNames.includes(requiredColumn.toLowerCase())) {
        console.log(`❌ Missing column: ${requiredColumn}`);
        results.tables[tableName].missing_columns.push(requiredColumn);
        
        // Add to missing columns in summary
        if (!results.missing.columns[tableName]) {
          results.missing.columns[tableName] = [];
        }
        results.missing.columns[tableName].push(requiredColumn);
      }
    }
    
    // Update summary
    if (results.tables[tableName].missing_columns.length > 0) {
      console.log(`⚠️ Table ${tableName} is missing ${results.tables[tableName].missing_columns.length} required columns`);
      results.summary.tables_with_missing_columns++;
    } else {
      console.log(`✅ Table ${tableName} has all required columns`);
      results.summary.verified_tables++;
    }
    
    // Display sample data
    await showTableSample(tableName);
  } catch (error) {
    console.error(`Error verifying table ${tableName}: ${error.message}`);
  }
}

/**
 * Check if a table exists
 * @param {string} tableName - Table name to check
 * @returns {Promise<boolean>} True if table exists
 */
async function checkTableExists(tableName) {
  try {
    const query = `
      SELECT COUNT(*) as count
      FROM information_schema.tables
      WHERE table_schema = DATABASE()
      AND table_name = ?
    `;
    
    const result = await dbConnector.query(query, [tableName]);
    
    // Handle both result formats from different connection methods
    const count = result[0].count || result[0].column1 || 0;
    return parseInt(count, 10) > 0;
  } catch (error) {
    console.error(`Error checking table existence: ${error.message}`);
    return false;
  }
}

/**
 * Get table columns
 * @param {string} tableName - Table name
 * @returns {Promise<Array>} Array of column information
 */
async function getTableColumns(tableName) {
  try {
    const result = await dbConnector.query(`DESCRIBE ${tableName}`);
    return result;
  } catch (error) {
    console.error(`Error getting table columns: ${error.message}`);
    return [];
  }
}

/**
 * Show sample data from a table
 * @param {string} tableName - Table name
 */
async function showTableSample(tableName) {
  try {
    const result = await dbConnector.query(`SELECT * FROM ${tableName} LIMIT 1`);
    
    if (result.length > 0) {
      console.log(`\nSample data from ${tableName}:`);
      console.log(JSON.stringify(result[0], null, 2));
    } else {
      console.log(`\nTable ${tableName} is empty`);
    }
  } catch (error) {
    console.error(`Error fetching sample data: ${error.message}`);
  }
}

// Run the main function
main().catch(console.error); 
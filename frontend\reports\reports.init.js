/**
 * Reports Initialization Module
 * Enhanced with unified authentication integration
 */

/**
 * Initialize unified authentication client
 */
function initUnifiedAuth() {
    if (window.unifiedAuthClient) {

        // Set up event listeners
        window.unifiedAuthClient.addEventListener('login', handleAuthLogin);
        window.unifiedAuthClient.addEventListener('logout', handleAuthLogout);
        window.unifiedAuthClient.addEventListener('sessionExpired', handleSessionExpired);

        console.log('✅ Reports: Connected to unified auth client');
        return true;
    }
    return false;
}

/**
 * Handle unified auth login event
 */
function handleAuthLogin(data) {
    console.log('✅ Reports: User authenticated via unified auth client');

    // Initialize external API service credentials
    initializeExternalApiCredentials();

    // Refresh reports data if needed
    if (typeof loadReportsData === 'function') {
        loadReportsData();
    }
}

/**
 * Handle unified auth logout event
 */
function handleAuthLogout() {
    console.log('✅ Reports: User logged out via unified auth client');
    // Clear any sensitive data
    clearReportsData();
}

/**
 * Handle session expiration
 */
function handleSessionExpired() {
    console.log('⏰ Reports: Session expired');
    // Show authentication required message
    displayError('Session expired. Please log in again.');
}

/**
 * Clear reports data on logout
 */
function clearReportsData() {
    const tableBody = document.querySelector('#reports-table tbody');
    if (tableBody) {
        tableBody.innerHTML = '<tr><td colspan="11" class="text-center">Please log in to view reports</td></tr>';
    }
}

/**
 * Initialize external API service credentials
 * This ensures the external API service has proper credentials for authentication
 */
async function initializeExternalApiCredentials() {
    console.log('🔧 Reports: Initializing external API service credentials...');

    // Get credentials from unified auth client
    let credentials = null;

    if (window.unifiedAuthClient && window.unifiedAuthClient.isAuthenticated && window.unifiedAuthClient.currentUser) {
        const user = window.unifiedAuthClient.currentUser;

        // For JWT users, we need to get their external API password
        // Since the external API requires a password, we'll use a default password for JWT users
        console.log('🔐 Reports: Using JWT user with default password for external API:', user.uid);
        credentials = { uid: user.uid, password: 'test' }; // Default password for external API compatibility
    }

    // Fallback to credential manager
    if (!credentials && window.credentialManager) {
        const creds = window.credentialManager.getCredentials();
        if (creds && creds.uid) {
            credentials = creds;
            console.log('🔐 Reports: Using credentials from credential manager:', creds.uid);
        }
    }

    // Fallback to session storage
    if (!credentials) {
        const sessionUid = sessionStorage.getItem('smarttest_uid');
        const sessionPwd = sessionStorage.getItem('smarttest_pwd');

        if (sessionUid) {
            credentials = { uid: sessionUid, password: sessionPwd || '' };
            console.log('🔐 Reports: Using credentials from session storage:', sessionUid);
        }
    }

    // Set credentials in external API services
    if (credentials) {
        setCredentialsInExternalApiServices(credentials);
    } else {
        console.warn('⚠️ Reports: No credentials found for external API service');
    }
}

/**
 * Set credentials in external API services with retry logic
 * @param {Object} credentials - Credentials object with uid and password
 */
function setCredentialsInExternalApiServices(credentials) {
    function setCredentialsWhenReady() {
        let success = false;

        // Set in external API service
        if (window.externalApiService) {
            window.externalApiService.credentials = credentials;
            console.log('✅ Reports: Set credentials in externalApiService:', credentials.uid);
            success = true;
        }

        // Set in enhanced external API service
        if (window.enhancedExternalApiService) {
            window.enhancedExternalApiService.credentials = credentials;
            console.log('✅ Reports: Set credentials in enhancedExternalApiService:', credentials.uid);
            success = true;
        }

        // Also ensure session storage has the credentials for fallback
        if (credentials.uid) {
            try {
                sessionStorage.setItem('smarttest_uid', credentials.uid);
                if (credentials.password) {
                    sessionStorage.setItem('smarttest_pwd', credentials.password);
                }
                console.log('✅ Reports: Updated session storage with credentials');
            } catch (error) {
                console.warn('⚠️ Reports: Could not update session storage:', error);
            }
        }

        return success;
    }

    // Try to set credentials immediately
    if (!setCredentialsWhenReady()) {
        // If external API services are not ready yet, wait for them
        console.log('🔄 Reports: External API services not ready yet, will set credentials when available');

        // Try periodically in case services load later
        const checkInterval = setInterval(() => {
            if (setCredentialsWhenReady()) {
                clearInterval(checkInterval);
            }
        }, 100);

        // Stop trying after 5 seconds
        setTimeout(() => clearInterval(checkInterval), 5000);
    }
}

// Try to initialize unified auth immediately, or wait for it to be available
if (!initUnifiedAuth()) {
    const checkInterval = setInterval(() => {
        if (initUnifiedAuth()) {
            clearInterval(checkInterval);
        }
    }, 100);

    setTimeout(() => clearInterval(checkInterval), 5000);
}

// Initialize external API credentials when the page loads
document.addEventListener('DOMContentLoaded', () => {
    // Wait a bit for all services to be loaded
    setTimeout(() => {
        initializeExternalApiCredentials();
    }, 500);
});

function displayError(message) {
    console.error(message);
    const tableBody = document.querySelector('#reports-table tbody');
    if (tableBody) {
        tableBody.innerHTML = `<tr><td colspan="11" class="text-center text-danger">${message}</td></tr>`;
    }

    const refreshStatus = document.getElementById('refresh-status');
    if (refreshStatus) {
        refreshStatus.textContent = `Error: ${message}`;
        refreshStatus.classList.add('text-danger');
    }
}

// Initialize filter controls
function initializeFilters() {
    console.log('Initializing filter controls...');

    // Reset filters button
    const resetFiltersBtn = document.getElementById('reset-filters');
    if (resetFiltersBtn) {
        resetFiltersBtn.addEventListener('click', function() {
            // Reset all filter inputs
            const userFilter = document.getElementById('filter-user');
            const statusFilter = document.getElementById('filter-status');
            const testIdFilter = document.getElementById('filter-test-id');

            if (userFilter) userFilter.value = '';
            if (statusFilter) statusFilter.value = '';
            if (testIdFilter) testIdFilter.value = '';

            // If DataTable is initialized, reset its filters too
            if (reportsDataTable) {
                reportsDataTable.search('').columns().search('').draw();
            }

            console.log('Filters have been reset');
        });
    }

    console.log('Filter controls initialized');
}

// Initialize application on document ready
/**
 * Initialize the reports page
 */
function initReportsPage() {
    console.log('Reports page initialization started');
    
    // Initialize the bulk rerun UI
    initializeBulkRerunUI();
}

document.addEventListener('DOMContentLoaded', function() {
    console.log('Reports page initialization started');

    // Initialize unified credential manager
    if (window.credentialManager) {
        console.log('✅ Unified credential manager available');
        // Load credentials from storage
        const loaded = window.credentialManager.loadCredentials();
        console.log('Credential manager load result:', loaded);
        const credentials = window.credentialManager.getCredentials();
        if (credentials && credentials.uid) {
            console.log('✅ Credentials loaded from unified manager:', credentials.uid);
        } else {
            console.log('⚠️ No credentials found in unified manager');
        }
    } else {
        console.warn('⚠️ Unified credential manager not available');
    }

    // DIAGNOSTIC: Check service availability on page load
    console.log('🔍 [INIT] Service availability check:');
    console.log('- window.externalApiService:', !!window.externalApiService, window.externalApiService);
    console.log('- window.enhancedExternalApiService:', !!window.enhancedExternalApiService, window.enhancedExternalApiService);
    console.log('- window.apiService:', !!window.apiService, window.apiService);
    console.log('- window.credentialManager:', !!window.credentialManager, window.credentialManager);

    // DIAGNOSTIC: Add cookie debugging function
    window.debugCookieAuth = function() {
        console.log('🍪 Cookie Authentication Debug (EXTERNAL API ONLY MODE):');
        console.log('- Document cookies:', document.cookie);
        console.log('- External API session ID:', window.externalApiService?.jsessionId);
        console.log('- Enhanced External API session ID:', window.enhancedExternalApiService?.jsessionId);
        console.log('- Session valid (external):', window.externalApiService?.isSessionValid());
        console.log('- Session valid (enhanced):', window.enhancedExternalApiService?.isSessionValid());
        console.log('🚫 Database fallback is DISABLED - External API must work');

        // Test a simple external API call
        if (window.enhancedExternalApiService || window.externalApiService) {
            const service = window.enhancedExternalApiService || window.externalApiService;
            const credentials = getCredentials();

            if (!credentials) {
                console.log('❌ No credentials available for external API test');
                return;
            }

            console.log('🧪 Testing external API connectivity (NO FALLBACK)...');
            fetch('/api/external/ReportSummary?tsn_id=14847', {
                credentials: 'include',
                headers: { 'Cache-Control': 'no-cache' }
            }).then(response => {
                console.log('🧪 Test API call result:', response.status, response.statusText);
                if (response.status === 401) {
                    console.log('❌ 401 Unauthorized - Cookie authentication is not working');
                    console.log('🚫 This will cause Details button to fail (no database fallback)');
                } else if (response.ok) {
                    console.log('✅ API call successful - Cookie authentication is working');
                    console.log('✅ Details button should work with external API');
                } else {
                    console.log('⚠️ API call returned:', response.status);
                    console.log('🚫 This will cause Details button to fail (no database fallback)');
                }
            }).catch(error => {
                console.error('❌ Test API call failed:', error);
                console.log('🚫 This will cause Details button to fail (no database fallback)');
            });
        } else {
            console.error('❌ No External API Service found');
            console.log('🚫 Details button will fail - External API service is required');
        }
    };

    console.log('💡 Use debugCookieAuth() in console to debug cookie authentication issues');
    console.log('🚫 EXTERNAL API ONLY MODE - Database fallback is disabled for test details');

    // Set up refresh button event listener
    const refreshBtn = document.getElementById('refresh-btn');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', refreshReports);
    }

    // Initialize timezone selector
    const timezoneSelector = document.getElementById('timezone-select');
    if (timezoneSelector) {
        const savedTimezone = localStorage.getItem('selected_timezone');
        if (savedTimezone) {
            timezoneSelector.value = savedTimezone;
        }
        timezoneSelector.addEventListener('change', function() {
            saveSelectedTimezone(this.value);
            refreshAllTimestamps();
        });
        console.log('Timezone selector initialized with timezone:', getSelectedTimezone());
    } else {
        console.warn('Timezone selector not found');
    }

    // Initialize filters (including time range event listeners setup by setupEventListeners)
    initializeFilters(); // This should be called before setupEventListeners if it also sets up parts of filters
    setupEventListeners(); // Sets up timeRangeDropdown listeners etc.

    // Initialize charts
    initializeCharts();

    // Initial refresh of timestamps to ensure they're formatted with the correct timezone
    // This should be called AFTER data is potentially loaded if it formats data in the table
    // refreshAllTimestamps();

    // Load initial data for the default time range (e.g., currentState.activeTimeRange which is '24h')
    // DataTable will be initialized after data is loaded and displayReports is called.
    loadReportsData({ forceFullReload: true, showLoading: true });

    // Call refreshAllTimestamps after the initial data load and table population might be better.
    // Or ensure formatDateTime is robust enough to be called on empty/loading table.
});

/**
 * Get credentials for API calls
 * @returns {Object} Credentials object with uid and password, or null if no credentials found
 */
function getCredentials() {
    // Try unified auth client first
    if (unifiedAuthClient && unifiedAuthClient.isAuthenticated) {
        const user = unifiedAuthClient.getCurrentUser();
        if (user) {
            // Get password from session storage for backward compatibility
            const password = sessionStorage.getItem('smarttest_pwd');
            if (password) {
                console.log('Using credentials from unified auth client');
                return { uid: user.uid, password: password };
            }
        }
    }

    // Try to get from window.apiService as fallback
    if (window.apiService && window.apiService.credentials && window.apiService.credentials.uid) {
        console.log('Using credentials from API service');
        return window.apiService.credentials;
    }

    // Try session storage as last fallback
    const sessionUid = sessionStorage.getItem('smarttest_uid');
    const sessionPwd = sessionStorage.getItem('smarttest_pwd');

    if (sessionUid && sessionPwd) {
        console.log('Using credentials from session storage');
        return { uid: sessionUid, password: sessionPwd };
    }

    // No credentials found - authentication required
    console.warn('No credentials found - authentication required');
    return null;
}

/**
 * Load reports data from the external API directly
 * @param {Object} credentials - User credentials
 */
async function loadReportsFromExternalApi(credentials) {
    try {
        // Use the unified external API service from shared services
        const externalApiService = window.enhancedExternalApiService;

        if (!externalApiService) {
            console.warn('Enhanced External API Service not available');
            currentState.reports = [];
            return;
        }

        // Get recent session IDs using the enhanced external API service
        const sessionIds = await externalApiService.getRecentSessionIds(credentials, config.maxReportsToShow);

        if (!sessionIds || sessionIds.length === 0) {
            console.warn('No session IDs found');
            currentState.reports = [];
            return;
        }

        console.log(`Found ${sessionIds.length} session IDs, fetching report data...`);

        // Get report data for each session ID using the enhanced external API service
        const reports = await externalApiService.getRecentTestRuns(
            sessionIds,
            credentials.uid,
            credentials.password,
            config.maxReportsToShow
        );

        // Transform the reports to match the expected format
        currentState.reports = reports.map(report => ({
            id: report.tsn_id,
            tsn_id: report.tsn_id, // Ensure tsn_id is available for view details
            test_id: report.test_id,
            test_name: report.test_name || '',
            type: report.type || 'Unknown',
            environment: report.environment || 'Unknown',
            status: report.status || 'Unknown',
            startTime: report.start_time,
            endTime: report.end_time,
            duration: report.duration || null,
            user: credentials.uid || 'System',
            trigger: 'Manual', // External API doesn't provide trigger info
            totalCases: report.total_cases || 0,
            passedCases: report.passed_cases || 0,
            failedCases: report.failed_cases || 0,
            skippedCases: 0, // External API doesn't provide skipped cases
            passRate: report.pass_rate || 0
        }));

        console.log(`Loaded ${currentState.reports.length} reports from external API`);
    } catch (error) {
        console.error('Error loading reports from external API:', error);
        throw error;
    }
}

/**
 * Load reports data from the database API
 * @param {Object} credentials - User credentials
 */
async function loadReportsFromDatabaseApi(credentials) {
    try {
        const uid = credentials.uid;
        const password = credentials.password;

        console.log(`🔍 Reports: Loading data for user: ${uid}`);

        // Get the highest session ID from localStorage for incremental updates
        const highestId = parseInt(localStorage.getItem('highestSessionId')) || 0;

        // Add parameters to the URL following naming conventions from parameter-mapping.md
        let url = `/local/recent-runs?uid=${encodeURIComponent(uid)}&password=${encodeURIComponent(password)}`;

        console.log(`🔍 Reports: Making request to ${url}`);
        document.getElementById('loading-indicator').style.display = 'block';

        // Make the request
        const response = await fetch(url);
        console.log(`🔍 Reports: Response status: ${response.status} ${response.statusText}`);

        if (!response.ok) {
            console.error(`API error status: ${response.status}`);
            document.getElementById('loading-indicator').style.display = 'none';
            return;
        }

        // Parse the response according to the standard format from frontend-api-database-integration.md
        const responseData = await response.json();

        // Log detailed diagnostic information
        console.log(`📊 Reports: Response structure:`, {
            success: responseData.success,
            messageType: responseData.message ? typeof responseData.message : 'undefined',
            dataType: responseData.data ? (Array.isArray(responseData.data) ? 'array' : typeof responseData.data) : 'undefined',
            dataLength: Array.isArray(responseData.data) ? responseData.data.length : 'N/A'
        });

        // Log a sample of the data received (first item only)
        if (responseData.data && responseData.data.length > 0) {
            console.log(`📊 Reports: Sample data received (first item):`, JSON.stringify(responseData.data[0], null, 2));
        }

        // Check if the response is in the correct format (success/data/message)
        if (responseData && responseData.success === true && Array.isArray(responseData.data)) {
            console.log(`🔍 Reports: Retrieved ${responseData.data.length} sessions`);

            // If we got new data, store the highest session ID for future incremental updates
            if (responseData.data.length > 0) {
                const newHighestId = Math.max(...responseData.data.map(session => parseInt(session.tsn_id)));
                console.log(`🔍 Reports: New highest ID: ${newHighestId}`);

                if (newHighestId > highestId) {
                    localStorage.setItem('highestSessionId', newHighestId);
                }

                // Display the data as an incremental update if we had a previous highest ID
                displayReports(responseData.data, highestId > 0);
            } else {
                console.log(`🔍 Reports: No new sessions found`);
            }
        } else {
            console.error('Invalid response format:', responseData);
            if (!document.querySelector('#reports-table tbody tr')) {
                // Only show "No reports found" if the table is empty
                displayEmptyMessage();
            }
        }

        // Update the UI
        document.getElementById('loading-indicator').style.display = 'none';
        // Format refresh time according to selected timezone
        const now = new Date();
        const selectedTimezone = getSelectedTimezone();
        const timeOptions = { hour: '2-digit', minute: '2-digit', second: '2-digit', hour12: true };

        // Add timeZone option if not local
        if (selectedTimezone !== 'local') {
            timeOptions.timeZone = selectedTimezone;
        }

        const refreshStatus = document.getElementById('refresh-status');
        refreshStatus.textContent = `Last updated: ${now.toLocaleTimeString('en-US', timeOptions)}`;
        // Store original time for timezone changes
        refreshStatus.dataset.originalTime = now.toISOString();
    } catch (error) {
        console.error('Error loading reports:', error);
        document.getElementById('loading-indicator').style.display = 'none';
        if (!document.querySelector('#reports-table tbody tr')) {
            displayEmptyMessage();
        }
    }
}

/**
 * Transform recent runs data from the database API to the frontend format
 * @param {Array} recentRuns - Recent runs data from the database API
 * @returns {Array} - Transformed data for the frontend
 */

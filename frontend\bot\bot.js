// Import required packages
const dotenv = require('dotenv');
const path = require('path');
const restify = require('restify');
const { BotFrameworkAdapter, CardFactory, MessageFactory } = require('botbuilder');
const axios = require('axios');

// Load environment variables
dotenv.config({ path: path.join(__dirname, '.env') });

// Create adapter
// This adapter handles bot connections to Teams, Facebook, etc.
const adapter = new BotFrameworkAdapter({
    appId: process.env.MicrosoftAppId,
    appPassword: process.env.MicrosoftAppPassword
});

// Error handling
adapter.onTurnError = async (context, error) => {
    console.error(`\n [onTurnError] unhandled error: ${error}`);
    await context.sendActivity('The bot encountered an error or bug.');
    await context.sendActivity('To continue to run this bot, please fix the bot source code.');
};

// Create HTTP server
const server = restify.createServer();
server.listen(process.env.port || process.env.PORT || 3978, () => {
    console.log(`\n${server.name} listening to ${server.url}`);
});

// Listen for incoming activities
server.post('/api/messages', (req, res) => {
    adapter.processActivity(req, res, async (context) => {
        if (context.activity.type === 'message') {
            await processMessage(context);
        } else {
            await context.sendActivity(`${context.activity.type} event detected.`);
        }
    });
});

// Process incoming messages
async function processMessage(context) {
    const text = context.activity.text.toLowerCase();
    
    // Check for predefined commands or use NLP
    if (text.includes('run smoke test')) {
        await runTest(context, 'smoke');
    } else if (text.includes('run regression test')) {
        await runTest(context, 'regression');
    } else if (text.includes('run heartbeat test')) {
        await runTest(context, 'heartbeat');
    } else if (text.includes('run feature test')) {
        await runTest(context, 'feature');
    } else if (text.includes('help')) {
        await showHelp(context);
    } else {
        // Use NLP to process natural language commands
        await processNaturalLanguage(context, text);
    }
}

// Handle running a specific test type
async function runTest(context, testType) {
    await context.sendActivity(`Starting ${testType} test execution...`);
    
    try {
        // Call n8n webhook for test execution
        const response = await axios.post(process.env.N8N_TEST_EXECUTION_WEBHOOK_URL, {
            testType: testType,
            environment: process.env.DEFAULT_ENVIRONMENT,
            userId: context.activity.from.id,
            timestamp: new Date().toISOString()
        });
        
        // Create and send card with test details
        const testCard = createTestExecutionCard(testType, response.data);
        await context.sendActivity({ attachments: [testCard] });
        
    } catch (error) {
        console.error(`Error executing ${testType} test:`, error);
        await context.sendActivity(`Sorry, there was an error executing the ${testType} test. Please try again later.`);
    }
}

// Process natural language commands using NLP
async function processNaturalLanguage(context, text) {
    await context.sendActivity('Processing your request...');
    
    try {
        // Call n8n webhook for NLP processing
        const response = await axios.post(process.env.N8N_NLP_WEBHOOK_URL, {
            text: text,
            userId: context.activity.from.id
        });
        
        if (response.data && response.data.understood) {
            // Execute the identified test
            await runTest(context, response.data.testType);
        } else {
            await context.sendActivity('I didn\'t understand that command. Try "run smoke test" or "help" for available commands.');
        }
        
    } catch (error) {
        console.error('Error processing natural language:', error);
        await context.sendActivity('Sorry, I couldn\'t process your request. Please try a different command or "help" for options.');
    }
}

// Show help message with available commands
async function showHelp(context) {
    const helpCard = CardFactory.heroCard(
        'Test Automation Commands',
        'Here are the commands you can use:',
        null,
        [
            {
                type: 'Action.Submit',
                title: 'Run Smoke Test',
                data: { command: 'run smoke test' }
            },
            {
                type: 'Action.Submit',
                title: 'Run Regression Test',
                data: { command: 'run regression test' }
            },
            {
                type: 'Action.Submit',
                title: 'Run Heartbeat Test',
                data: { command: 'run heartbeat test' }
            },
            {
                type: 'Action.Submit',
                title: 'Run Feature Test',
                data: { command: 'run feature test' }
            }
        ]
    );
    
    await context.sendActivity({ attachments: [helpCard] });
}

// Create a card for test execution details
function createTestExecutionCard(testType, testData) {
    return CardFactory.adaptiveCard({
        type: 'AdaptiveCard',
        body: [
            {
                type: 'TextBlock',
                size: 'Medium',
                weight: 'Bolder',
                text: `${testType.charAt(0).toUpperCase() + testType.slice(1)} Test Initiated`
            },
            {
                type: 'FactSet',
                facts: [
                    {
                        title: 'Test ID:',
                        value: testData.testId || 'Unknown'
                    },
                    {
                        title: 'Environment:',
                        value: testData.environment || process.env.DEFAULT_ENVIRONMENT
                    },
                    {
                        title: 'Status:',
                        value: 'Running'
                    },
                    {
                        title: 'Started:',
                        value: new Date().toLocaleString()
                    }
                ]
            }
        ],
        actions: [
            {
                type: 'Action.OpenUrl',
                title: 'View Results',
                url: `http://localhost:8080/dashboard?testId=${testData.testId}`
            }
        ],
        $schema: 'http://adaptivecards.io/schemas/adaptive-card.json',
        version: '1.2'
    });
} 
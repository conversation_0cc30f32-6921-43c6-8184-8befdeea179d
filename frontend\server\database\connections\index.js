/**
 * Connection Factory
 * Creates database connections with direct SSH as the primary method
 */
const DirectSshConnection = require('./direct-ssh');
const SshTunnelConnection = require('./ssh-tunnel');
const config = require('../config');

/**
 * Create a database connection
 * @param {string} environment - Environment name
 * @param {Object} options - Connection options
 * @returns {Object} - Database connection
 */
function createConnection(environment, options = {}) {
  const { forceTunnel } = options;
  const envConfig = config.getConfig(environment);
  
  // Always use direct SSH unless tunnel is explicitly forced
  if (forceTunnel) {
    console.log('Using SSH tunnel connection as requested');
    return new SshTunnelConnection(envConfig);
  } else {
    console.log('Using direct SSH connection (fixed implementation)');
    return new DirectSshConnection(envConfig);
  }
}

module.exports = {
  createConnection
};

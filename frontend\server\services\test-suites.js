// Test Suite Service
// Handles business logic for test suite operations

const { TestSuiteMapper } = require('../config/test-suite-mapping.js');
const db = require('../database');

class TestSuiteService {
    /**
     * Get filtered test suites based on level and version
     * @param {string} level - Integration level filter
     * @param {string} version - Version filter
     * @returns {Promise<Array>} Array of test suite objects
     */
    static async getFilteredTestSuites(level = '', version = '') {
        try {
            // Get mapped test suites based on filters
            const mappedSuites = TestSuiteMapper.getFilteredTestSuites(level, version);
            
            // Optionally enhance with database data
            const enhancedSuites = await this.enhanceWithDatabaseData(mappedSuites);
            
            return enhancedSuites;
        } catch (error) {
            console.error('Error in getFilteredTestSuites:', error);
            throw new Error(`Failed to get filtered test suites: ${error.message}`);
        }
    }
    
    /**
     * Get test suite by ID
     * @param {number} ts_id - Test suite ID
     * @returns {Promise<Object|null>} Test suite object or null
     */
    static async getTestSuiteById(ts_id) {
        try {
            // First check mapped suites
            const mappedSuite = TestSuiteMapper.getTestSuiteById(ts_id);
            if (mappedSuite) {
                return await this.enhanceWithDatabaseData([mappedSuite])[0];
            }
            
            // If not in mapping, query database directly
            return await this.queryDatabaseForTestSuite(ts_id);
        } catch (error) {
            console.error('Error in getTestSuiteById:', error);
            throw new Error(`Failed to get test suite by ID: ${error.message}`);
        }
    }
    
    /**
     * Search test suites by name pattern
     * @param {string} namePattern - Pattern to search for
     * @returns {Promise<Array>} Array of matching test suite objects
     */
    static async searchTestSuitesByName(namePattern) {
        try {
            // Search in mapped suites first
            const mappedResults = TestSuiteMapper.getTestSuitesByName(namePattern);
            
            // Also search in database for any additional suites
            const dbResults = await this.queryDatabaseForTestSuitesByName(namePattern);
            
            // Combine and deduplicate results
            const combined = [...mappedResults, ...dbResults];
            const unique = this.deduplicateByTsId(combined);
            
            return unique;
        } catch (error) {
            console.error('Error in searchTestSuitesByName:', error);
            throw new Error(`Failed to search test suites: ${error.message}`);
        }
    }
    
    /**
     * Get all available levels and versions for filter options
     * @returns {Object} Object containing arrays of levels and versions
     */
    static getFilterOptions() {
        return {
            levels: TestSuiteMapper.getAllLevels(),
            versions: TestSuiteMapper.getAllVersions()
        };
    }
    
    /**
     * Enhance mapped test suites with additional database data
     * @param {Array} mappedSuites - Array of mapped test suite objects
     * @returns {Promise<Array>} Enhanced test suite objects
     */
    static async enhanceWithDatabaseData(mappedSuites) {
        try {
            const enhancedSuites = [];

            for (const suite of mappedSuites) {
                try {
                    // Use the database module's getTestSuiteById function
                    const dbSuite = await db.getTestSuiteById(suite.ts_id);

                    if (dbSuite) {
                        // Merge mapped data with database data
                        enhancedSuites.push({
                            ...suite,
                            description: dbSuite.description || suite.description,
                            creation_time: dbSuite.creation_time,
                            last_modified: dbSuite.last_modified
                        });
                    } else {
                        // If not found in database, use mapped data as-is
                        enhancedSuites.push(suite);
                    }
                } catch (dbError) {
                    console.warn(`Failed to enhance suite ${suite.ts_id} with database data:`, dbError);
                    // Use mapped data as fallback
                    enhancedSuites.push(suite);
                }
            }

            return enhancedSuites;
        } catch (error) {
            console.warn('Failed to enhance with database data, using mapped data only:', error);
            return mappedSuites;
        }
    }
    
    /**
     * Query database for test suite by ID
     * @param {number} ts_id - Test suite ID
     * @returns {Promise<Object|null>} Test suite object or null
     */
    static async queryDatabaseForTestSuite(ts_id) {
        try {
            // Use the database module's getTestSuiteById function
            return await db.getTestSuiteById(ts_id);
        } catch (error) {
            console.error('Database query error:', error);
            return null;
        }
    }
    
    /**
     * Query database for test suites by name pattern
     * @param {string} namePattern - Pattern to search for
     * @returns {Promise<Array>} Array of test suite objects
     */
    static async queryDatabaseForTestSuitesByName(namePattern) {
        try {
            // Use the database module's getTestSuites function with name filter
            return await db.getTestSuites({ namePattern });
        } catch (error) {
            console.error('Database query error:', error);
            return [];
        }
    }
    
    /**
     * Remove duplicate test suites based on ts_id
     * @param {Array} suites - Array of test suite objects
     * @returns {Array} Deduplicated array
     */
    static deduplicateByTsId(suites) {
        const seen = new Set();
        return suites.filter(suite => {
            if (seen.has(suite.ts_id)) {
                return false;
            }
            seen.add(suite.ts_id);
            return true;
        });
    }
}

module.exports = TestSuiteService;

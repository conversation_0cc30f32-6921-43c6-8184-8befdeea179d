const mysql = require('mysql2/promise');

async function checkDatabaseSchema() {
  let connection;
  try {
    console.log('Connecting to database directly...');
    connection = await mysql.createConnection({
      host: 'mprts-qa02.lab.wagerworks.com',
      user: 'rgs_rw',
      password: 'rgs_rw',
      database: 'rgs_test'
    });
    
    console.log('Connected successfully');
    
    // Check test_session table structure
    const [testSessionColumns] = await connection.query('DESCRIBE test_session');
    console.log('\nTEST_SESSION TABLE STRUCTURE:');
    testSessionColumns.forEach(col => {
      console.log(`${col.Field} (${col.Type})${col.Null === 'YES' ? ' NULL' : ' NOT NULL'}${col.Key ? ' KEY: ' + col.Key : ''}`);
    });
    
    // Check if there are any active sessions
    const [activeSessions] = await connection.query(`
      SELECT * FROM test_session 
      WHERE end_ts IS NULL 
      LIMIT 5
    `);
    
    console.log('\nACTIVE SESSIONS SAMPLE:');
    if (activeSessions.length === 0) {
      console.log('No active sessions found');
    } else {
      console.log(JSON.stringify(activeSessions, null, 2));
    }
    
    // Check the format of the API response by querying related tables
    const [testCases] = await connection.query(`
      SELECT tc_id, name FROM test_case 
      WHERE tc_id = 3180
    `);
    
    console.log('\nTEST CASE 3180:');
    console.log(JSON.stringify(testCases, null, 2));
    
    const [testResults] = await connection.query(`
      SELECT tsn_id, tc_id, outcome, creation_time 
      FROM test_result 
      ORDER BY creation_time DESC 
      LIMIT 5
    `);
    
    console.log('\nRECENT TEST RESULTS:');
    console.log(JSON.stringify(testResults, null, 2));
    
  } catch (error) {
    console.error('Database connection error:', error);
  } finally {
    if (connection) {
      console.log('\nClosing connection');
      await connection.end();
    }
  }
}

checkDatabaseSchema().catch(console.error); 
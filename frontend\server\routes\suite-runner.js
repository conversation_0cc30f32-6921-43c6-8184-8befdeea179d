/**
 * Suite Runner Routes
 */
const express = require('express');
const router = express.Router();
const { runTest } = require('../services/case-runner');

// SuiteRunner API proxy endpoint
router.post('/suite-runner', async (req, res) => {
  try {
    const requestBody = { ...req.body };
    console.log(`[API /suite-runner] Received request with params:`, requestBody);

    if (!requestBody.ts_id) {
      return res.status(400).json({
        success: false,
        message: 'ts_id (test suite ID) is required.'
      });
    }

    // Add authenticated user credentials from JWT session
    if (req.user && req.user.uid) {
      requestBody.uid = req.user.uid;
      // For external API compatibility, we still need a password
      // Use a default password since the external API requires it
      requestBody.password = requestBody.password || 'test';
    }

    try {
      // Run the test using the case-runner service
      const result = await runTest(requestBody);

      return res.json({
        success: true,
        ...result
      });
    } catch (error) {
      console.error(`[API /suite-runner] Error running test suite:`, error);
      return res.status(500).json({
        success: false,
        message: `Failed to run test suite: ${error.message}`
      });
    }
  } catch (error) {
    console.error('Error in suite-runner endpoint:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
});

module.exports = router;

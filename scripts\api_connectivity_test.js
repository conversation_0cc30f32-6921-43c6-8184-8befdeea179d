/**
 * Simple connectivity test script to check API endpoints
 */

// We'll use dynamic imports for node-fetch (v3+)
async function main() {
  const fetch = (await import('node-fetch')).default;
  const { URLSearchParams } = await import('url');

  // Test credentials
  const TEST_UID = '<EMAIL>';
  const TEST_PASSWORD = 'test';

  // Test case ID
  const TEST_CASE_ID = '3180';

  // Endpoint information
  const endpoints = [
    {
      name: 'QA01',
      url: 'http://mprts-qa01.lab.wagerworks.com:5080/AutoRun/CaseRunner'
    },
    {
      name: 'QA02',
      url: 'http://mprts-qa02.lab.wagerworks.com:5080/AutoRun/CaseRunner'
    }
  ];

  // Complete parameter set based on working curl command
  const params = new URLSearchParams();
  params.append('uid', TEST_UID);
  params.append('password', TEST_PASSWORD);
  params.append('tc_id', TEST_CASE_ID);
  params.append('envir', 'qa02');
  params.append('shell_host', 'jps-qa10-app01');
  params.append('file_path', '/home/<USER>/');
  params.append('operatorConfigs', 'operatorNameConfigs');
  params.append('kafka_server', 'kafka-qa-a0.lab.wagerworks.com');
  params.append('dataCenter', 'GU');
  params.append('rgs_env', 'qa02');
  params.append('old_version', '0');
  params.append('networkType1', 'multi-site');
  params.append('networkType2', 'multi-site');
  params.append('sign', '-');
  params.append('rate_src', 'local');

  console.log('API Connectivity Test');
  console.log('====================');
  console.log(`Testing endpoints with test case ID: ${TEST_CASE_ID}`);
  console.log(`User: ${TEST_UID}`);
  console.log('');

  for (const endpoint of endpoints) {
    console.log(`Testing ${endpoint.name} endpoint: ${endpoint.url}`);
    try {
      // Make the request
      const response = await fetch(endpoint.url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        body: params,
        timeout: 10000  // 10 second timeout
      });

      // Check the response
      if (response.ok) {
        console.log(`✅ ${endpoint.name} is reachable and returned a successful response`);
        
        // Get first few characters of the response for diagnostic info
        const text = await response.text();
        const preview = text.substring(0, 100).replace(/\n/g, ' ').trim();
        console.log(`Response preview: ${preview}...`);
        
        // Check if it's a login page or actual response
        if (text.includes('Login Page')) {
          console.log(`⚠️ ${endpoint.name} returned a login page - authentication issue`);
        } else if (text.includes('Test Case Auto Running')) {
          console.log(`✅ ${endpoint.name} is running the test case successfully`);
          
          // Try to extract session ID
          const match = text.match(/Test session number is (\d+)/i);
          if (match && match[1]) {
            console.log(`📝 Test session ID: ${match[1]}`);
          }
        }
      } else {
        console.log(`❌ ${endpoint.name} returned status: ${response.status} ${response.statusText}`);
      }
    } catch (error) {
      console.log(`❌ Error connecting to ${endpoint.name}: ${error.message}`);
    }
    console.log(''); // Add a blank line between endpoint tests
  }
}

// Run the test
main().catch(error => {
  console.error('Test failed with error:', error);
});

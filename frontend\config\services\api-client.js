/**
 * SmartTest API Client
 *
 * This client provides a modern interface to interact with the SmartTest API server,
 * focusing on test session and input query management with enhanced error handling.
 */

// Note: envConfig is available globally via window.envConfig

class ApiClient {
  constructor() {
    // Get base URL from environment configuration
    this.baseUrl = window.envConfig.current.apiBaseUrl;
    this.token = null;
    this.username = null;
    
    // Initialize from localStorage if available
    this.loadAuthFromStorage();
    
    // Retry configuration for robustness
    this.MAX_RETRIES = 3;
    this.RETRY_DELAY_MS = 1000;
    this.RETRY_STATUS_CODES = [408, 429, 500, 502, 503, 504];
  }
  
  /**
   * Load authentication data from local storage
   */
  loadAuthFromStorage() {
    if (typeof localStorage !== 'undefined') {
      this.token = localStorage.getItem('smarttest_token');
      this.username = localStorage.getItem('smarttest_username');
    }
  }
  
  /**
   * Save authentication data to local storage
   */
  saveAuthToStorage() {
    if (typeof localStorage !== 'undefined') {
      localStorage.setItem('smarttest_token', this.token);
      localStorage.setItem('smarttest_username', this.username);
    }
  }
  
  /**
   * Clear authentication data from local storage
   */
  clearAuthFromStorage() {
    if (typeof localStorage !== 'undefined') {
      localStorage.removeItem('smarttest_token');
      localStorage.removeItem('smarttest_username');
    }
    this.token = null;
    this.username = null;
  }
  
  /**
   * Check if user is authenticated
   * @returns {boolean} - Authentication status
   */
  isAuthenticated() {
    return !!this.token;
  }
  
  /**
   * Authenticate user
   * @param {string} username - Username (email)
   * @param {string} password - User password
   * @returns {Promise<Object>} - Authentication result
   */
  async login(username, password) {
    try {
      const response = await this.postRequest('auth/login', { username, password }, false);
      this.token = response.token;
      this.username = response.username;
      this.saveAuthToStorage();
      return { 
        success: true, 
        username: this.username 
      };
    } catch (error) {
      console.error('Authentication failed:', error);
      return { 
        success: false, 
        error: error.message || 'Authentication failed'
      };
    }
  }
  
  /**
   * Log out user
   */
  logout() {
    this.clearAuthFromStorage();
  }
  
  /**
   * Create a new test session
   * @param {Object} params - Test session parameters
   * @param {string} params.test_type - Type of test (e.g., 'smoke', 'regression')
   * @param {string} params.environment - Test environment (e.g., 'dev', 'qa')
   * @param {string} params.description - Optional session description
   * @returns {Promise<Object>} - Created session info
   */
  async createTestSession(params) {
    return this.postRequest('sessions', params);
  }
  
  /**
   * Get all test sessions
   * @param {Object} options - Query options
   * @param {number} options.limit - Maximum number of sessions to retrieve
   * @param {number} options.offset - Offset for pagination
   * @param {string} options.status - Filter by status
   * @returns {Promise<Object>} - Test sessions
   */
  async getTestSessions(options = {}) {
    const queryParams = new URLSearchParams();
    if (options.limit) queryParams.append('limit', options.limit);
    if (options.offset) queryParams.append('offset', options.offset);
    if (options.status) queryParams.append('status', options.status);
    
    const query = queryParams.toString() ? `?${queryParams.toString()}` : '';
    return this.getRequest(`sessions${query}`);
  }
  
  /**
   * Get a specific test session
   * @param {string} sessionId - Test session ID
   * @returns {Promise<Object>} - Test session
   */
  async getTestSession(sessionId) {
    return this.getRequest(`sessions/${sessionId}`);
  }
  
  /**
   * Update a test session status
   * @param {string} sessionId - Test session ID
   * @param {Object} params - Update parameters
   * @param {string} params.status - New status
   * @param {number} params.progress - Optional progress percentage
   * @returns {Promise<Object>} - Update result
   */
  async updateTestSessionStatus(sessionId, params) {
    return this.patchRequest(`sessions/${sessionId}/status`, params);
  }
  
  /**
   * Delete a test session
   * @param {string} sessionId - Test session ID
   * @returns {Promise<Object>} - Delete result
   */
  async deleteTestSession(sessionId) {
    return this.deleteRequest(`sessions/${sessionId}`);
  }
  
  /**
   * Log an input query
   * @param {Object} params - Query parameters
   * @param {string} params.session_id - Test session ID
   * @param {string} params.query - Input query
   * @param {number} params.execution_time - Query execution time in ms
   * @param {string} params.status - Query status ('success', 'error', 'warning')
   * @param {string} params.result - Optional query result
   * @returns {Promise<Object>} - Log result
   */
  async logInputQuery(params) {
    return this.postRequest('queries', params);
  }
  
  /**
   * Get input queries for a session
   * @param {string} sessionId - Test session ID
   * @param {Object} options - Query options
   * @param {number} options.limit - Maximum number of queries to retrieve
   * @param {number} options.offset - Offset for pagination
   * @param {string} options.status - Filter by status
   * @returns {Promise<Object>} - Input queries
   */
  async getInputQueries(sessionId, options = {}) {
    const queryParams = new URLSearchParams();
    if (options.limit) queryParams.append('limit', options.limit);
    if (options.offset) queryParams.append('offset', options.offset);
    if (options.status) queryParams.append('status', options.status);
    
    const query = queryParams.toString() ? `?${queryParams.toString()}` : '';
    return this.getRequest(`sessions/${sessionId}/queries${query}`);
  }
  
  /**
   * Get a specific input query
   * @param {string} queryId - Query ID
   * @returns {Promise<Object>} - Input query
   */
  async getInputQuery(queryId) {
    return this.getRequest(`queries/${queryId}`);
  }
  
  /**
   * Get query execution stats for a session
   * @param {string} sessionId - Test session ID
   * @returns {Promise<Object>} - Query stats
   */
  async getQueryStats(sessionId) {
    return this.getRequest(`sessions/${sessionId}/query-stats`);
  }
  
  /**
   * Make a GET request
   * @param {string} endpoint - API endpoint
   * @returns {Promise<any>} - API response
   */
  async getRequest(endpoint) {
    return this.executeWithRetry(() => this.makeRequest(endpoint, {
      method: 'GET'
    }), `GET ${endpoint}`);
  }
  
  /**
   * Make a POST request
   * @param {string} endpoint - API endpoint
   * @param {Object} data - Request data
   * @param {boolean} requireAuth - Whether authentication is required
   * @returns {Promise<any>} - API response
   */
  async postRequest(endpoint, data, requireAuth = true) {
    return this.executeWithRetry(() => this.makeRequest(endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(data)
    }, requireAuth), `POST ${endpoint}`);
  }
  
  /**
   * Make a PATCH request
   * @param {string} endpoint - API endpoint
   * @param {Object} data - Request data
   * @returns {Promise<any>} - API response
   */
  async patchRequest(endpoint, data) {
    return this.executeWithRetry(() => this.makeRequest(endpoint, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(data)
    }), `PATCH ${endpoint}`);
  }
  
  /**
   * Make a DELETE request
   * @param {string} endpoint - API endpoint
   * @returns {Promise<any>} - API response
   */
  async deleteRequest(endpoint) {
    return this.executeWithRetry(() => this.makeRequest(endpoint, {
      method: 'DELETE'
    }), `DELETE ${endpoint}`);
  }
  
  /**
   * Execute a function with retry logic
   * @param {Function} fn - Function to execute
   * @param {string} operationName - Name of operation for logging
   * @param {number} attempt - Current attempt number
   * @returns {Promise<any>} - Function result
   */
  async executeWithRetry(fn, operationName, attempt = 1) {
    try {
      const startTime = Date.now();
      const result = await fn();
      const duration = Date.now() - startTime;
      
      console.log(`API call to ${operationName} succeeded in ${duration}ms (attempt ${attempt}/${this.MAX_RETRIES})`);
      return result;
    } catch (error) {
      console.error(`API call to ${operationName} failed (attempt ${attempt}/${this.MAX_RETRIES}):`, error);
      
      if (attempt < this.MAX_RETRIES && this.shouldRetry(error)) {
        const delay = this.RETRY_DELAY_MS * Math.pow(1.5, attempt - 1); // Exponential backoff
        console.log(`Retrying ${operationName} in ${delay}ms...`);
        
        await new Promise(resolve => setTimeout(resolve, delay));
        return this.executeWithRetry(fn, operationName, attempt + 1);
      }
      
      throw error;
    }
  }
  
  /**
   * Determine if a request should be retried based on the error
   * @param {Error} error - The error that occurred
   * @returns {boolean} - Whether the request should be retried
   */
  shouldRetry(error) {
    // Retry on network errors
    if (error.name === 'TypeError' || error.name === 'NetworkError') {
      return true;
    }
    
    // Retry on certain HTTP status codes
    if (error.status && this.RETRY_STATUS_CODES.includes(error.status)) {
      return true;
    }
    
    return false;
  }
  
  /**
   * Make an HTTP request
   * @param {string} endpoint - API endpoint
   * @param {Object} options - Fetch options
   * @param {boolean} requireAuth - Whether authentication is required
   * @returns {Promise<any>} - API response
   */
  async makeRequest(endpoint, options, requireAuth = true) {
    // Use environment configuration for base URL
    const url = `${envConfig.current.apiBaseUrl}/${endpoint}`;
    
    if (requireAuth) {
      if (!this.token) {
        throw new Error('Authentication required');
      }
      
      // Add authorization header
      if (!options.headers) {
        options.headers = {};
      }
      
      options.headers['Authorization'] = `Bearer ${this.token}`;
    }
    
    try {
      // Check if we should use mock API
      if (envConfig.useMock && window.mockApi) {
        console.log(`Using mock API for: ${url}`);
        return window.mockApi.processMockRequest(url, options);
      }
      
      // Use real API
      const response = await fetch(url, options);
      let data;
      
      // Check if response is JSON
      const contentType = response.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        data = await response.json();
      } else {
        // Handle non-JSON responses
        const text = await response.text();
        try {
          // Try to parse as JSON anyway in case the content-type is wrong
          data = JSON.parse(text);
        } catch (e) {
          // If it's not JSON, create a simple object with the text
          data = { text, success: response.ok };
        }
      }
      
      if (!response.ok) {
        const error = new Error(data.error || data.message || `HTTP error ${response.status}`);
        error.status = response.status;
        error.data = data;
        throw error;
      }
      
      return data;
    } catch (error) {
      console.error(`API error (${endpoint}):`, error);
      throw error;
    }
  }
}

// Create a singleton instance and make it globally available
const apiClient = new ApiClient();

// Make it globally available (no ES6 export needed for script context)
window.apiClient = apiClient;
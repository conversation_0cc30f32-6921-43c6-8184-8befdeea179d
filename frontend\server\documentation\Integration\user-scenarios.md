# User Scenarios

This document describes the key user scenarios in the SmartTest application and how they flow through the system layers.

## Table of Contents

1. [Running a Test Suite](#running-a-test-suite)
2. [Viewing Active Tests](#viewing-active-tests)
3. [Viewing Test Results](#viewing-test-results)
4. [Searching for Test Cases](#searching-for-test-cases)
5. [Viewing Test Suite Details](#viewing-test-suite-details)

## Running a Test Suite

### User Story

As a user, I want to run a predefined test suite so that I can execute multiple test cases at once.

### Flow Diagram

```
┌─────────────┐      ┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│    User     │      │   Frontend  │      │     API     │      │  External   │
│  Interface  │─────►│    Layer    │─────►│    Layer    │─────►│     API     │
└─────────────┘      └─────────────┘      └─────────────┘      └─────────────┘
                           │                    ▲                    │
                           │                    │                    │
                           ▼                    │                    ▼
                     ┌─────────────┐           │              ┌─────────────┐
                     │  Display    │◄──────────┴──────────────│  Test Run   │
                     │  Results    │                          │  Initiated  │
                     └─────────────┘                          └─────────────┘
```

### Steps

1. **User Action**: User selects a test suite and clicks "Run"
2. **Frontend Layer**: 
   - Collects test suite ID and parameters
   - Sends a POST request to `/api/run-suite` endpoint
   - Request body: `{ ts_id: "101", uid: "test_user", password: "password", environment: "qa02", shell_host: "jps-qa10-app01" }`
3. **API Layer**:
   - Validates the request parameters
   - Gets test suite information from the database
   - Initiates test suite execution through external API
   - Returns test session ID to the frontend
   - Response: `{ success: true, tsn_id: "12345", message: "Test suite 101 started successfully with 5 test cases" }`
4. **Frontend Layer**:
   - Stores the test session ID
   - Navigates to the Active Tests window
   - Begins polling for active tests

### Code Flow

#### Frontend Component

```javascript
/**
 * Run a test suite
 * @param {number} tsId - Test suite ID
 * @param {Object} params - Additional parameters
 * @returns {Promise<string>} - Test session ID
 */
async runTestSuite(tsId, params = {}) {
  try {
    // Build request body
    const requestBody = {
      ts_id: tsId,
      ...params,
      uid: this.credentials.uid,
      password: this.credentials.password
    };
    
    // Make POST request to run-suite endpoint
    const response = await this.postRequest(this.endpoints.runSuite, requestBody);
    
    if (response.success) {
      return response.tsn_id;
    } else {
      throw new Error(response.message || 'Failed to run test suite');
    }
  } catch (error) {
    console.error(`Error running test suite ${tsId}:`, error);
    throw error;
  }
}
```

#### API Route Handler

```javascript
// Run a test suite
router.post('/run-suite', validateCredentials, async (req, res) => {
  try {
    // Extract parameters
    const { ts_id, environment = 'qa02', shell_host = 'jps-qa10-app01' } = req.body;
    
    // Validate parameters
    if (!ts_id) {
      return res.status(400).json({
        success: false,
        message: 'Missing required parameter: ts_id'
      });
    }
    
    // Get test suite info to count test cases
    const testSuiteInfo = await db.getTestSuiteInfo(ts_id);
    const testCaseCount = testSuiteInfo.testCaseCount || 0;
    
    // Initiate test suite execution
    // This would typically call an external API
    
    // Generate a test session ID
    const tsnId = Math.floor(Math.random() * 100000);
    
    return res.json({
      success: true,
      tsn_id: tsnId,
      message: `Test suite ${ts_id} started successfully with ${testCaseCount} test cases`
    });
  } catch (error) {
    console.error('Error running test suite:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to run test suite',
      error: error.message
    });
  }
});
```

## Viewing Active Tests

### User Story

As a user, I want to see information about test suites running on the Active Tests window so that I can monitor their progress.

### Flow Diagram

```
┌─────────────┐      ┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│    User     │      │   Frontend  │      │     API     │      │  Database   │
│  Interface  │─────►│    Layer    │─────►│    Layer    │─────►│    Layer    │
└─────────────┘      └─────────────┘      └─────────────┘      └─────────────┘
                           │                    ▲                    │
                           │                    │                    │
                           ▼                    │                    ▼
                     ┌─────────────┐           │              ┌─────────────┐
                     │  Display    │◄──────────┴──────────────│  Database   │
                     │  Active     │                          │  Query      │
                     │  Tests      │                          │             │
                     └─────────────┘                          └─────────────┘
```

### Steps

1. **User Action**: User navigates to the Active Tests window
2. **Frontend Layer**:
   - Sends a GET request to `/local/active-tests` endpoint
   - Request parameters: None (user ID is extracted from authentication)
3. **API Layer**:
   - Validates the request
   - Calls the database layer to get active tests
   - Parameters: `{ userId: req.user.uid }`
4. **Database Layer**:
   - Executes a query to get active tests
   - SQL: `SELECT s.tsn_id, COALESCE(s.tc_id, 0) as tc_id, s.uid as initiator_user, s.start_ts as creation_time FROM test_session s WHERE s.end_ts IS NULL AND s.uid = ? ORDER BY s.start_ts DESC LIMIT ?`
   - Parameters: `[userId, limit]`
   - Returns active tests to the API layer
5. **API Layer**:
   - Formats the response and returns it to the frontend
   - Response: `{ success: true, data: [...], message: "Active tests retrieved successfully" }`
6. **Frontend Layer**:
   - Displays the active tests in the Active Tests window
   - Periodically polls for updates (e.g., every 5 seconds)

### Code Flow

#### Frontend Component

```javascript
/**
 * Get active tests
 * @returns {Promise<Array>} - Active tests
 */
async getActiveTests() {
  try {
    // Make GET request to active-tests endpoint
    const response = await this.getRequest(this.endpoints.activeTests);
    
    if (response.success) {
      return response.data || [];
    } else {
      throw new Error(response.message || 'Failed to get active tests');
    }
  } catch (error) {
    console.error('Error getting active tests:', error);
    throw error;
  }
}
```

#### API Route Handler

```javascript
// Get active tests
router.get('/active-tests', validateCredentials, async (req, res) => {
  try {
    console.log('GET /local/active-tests');
    
    // Use the database module to fetch active tests
    const activeTests = await db.getActiveTests({
      userId: req.user.uid
    });
    
    // Return as JSON with success flag
    return res.json({
      success: true,
      data: activeTests || [],
      message: 'Active tests retrieved successfully'
    });
  } catch (error) {
    console.error('Error retrieving active tests:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to retrieve active tests',
      error: error.message
    });
  }
});
```

#### Database Function

```javascript
/**
 * Get active test sessions
 * @param {Object} filters - Optional filters
 * @returns {Promise<Array>} - Active test sessions
 */
async function getActiveTests(filters = {}) {
  if (!isInitialized) {
    await init();
  }
  
  return await queries.testSessions.getActiveTests(connection, filters);
}
```

## Viewing Test Results

### User Story

As a user, I want to see test run results (overall and for each test case) in the Reports window when the suite run is finished.

### Flow Diagram

```
┌─────────────┐      ┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│    User     │      │   Frontend  │      │     API     │      │  Database   │
│  Interface  │─────►│    Layer    │─────►│    Layer    │─────►│    Layer    │
└─────────────┘      └─────────────┘      └─────────────┘      └─────────────┘
                           │                    ▲                    │
                           │                    │                    │
                           ▼                    │                    ▼
                     ┌─────────────┐           │              ┌─────────────┐
                     │  Display    │◄──────────┴──────────────│  Database   │
                     │  Test       │                          │  Query      │
                     │  Results    │                          │             │
                     └─────────────┘                          └─────────────┘
```

### Steps

1. **User Action**: User navigates to the Reports window and selects a test session
2. **Frontend Layer**:
   - Sends a GET request to `/local/test-results/:tsnId` endpoint
   - Request parameters: `tsnId` in URL path
3. **API Layer**:
   - Validates the request
   - Calls the database layer to get test results
   - Parameters: `tsnId`
4. **Database Layer**:
   - Executes a query to get test results
   - SQL: `SELECT r.tsn_id, r.tc_id, r.seq_index, r.outcome, r.creation_time, o.txt FROM test_result r JOIN output o ON r.cnt = o.cnt WHERE r.tsn_id = ? ORDER BY r.creation_time ASC`
   - Parameters: `[tsnId]`
   - Returns test results to the API layer
5. **API Layer**:
   - Formats the response and returns it to the frontend
   - Response: `{ success: true, data: {...}, message: "Test results retrieved successfully" }`
6. **Frontend Layer**:
   - Displays the test results in the Reports window
   - Shows overall summary and detailed results for each test case

### Code Flow

#### Frontend Component

```javascript
/**
 * Get test results
 * @param {string} tsnId - Test session ID
 * @returns {Promise<Object>} - Test results
 */
async getTestResults(tsnId) {
  try {
    // Make GET request to test-results endpoint
    const response = await this.getRequest(`${this.endpoints.testResults}/${tsnId}`);
    
    if (response.success) {
      return response.data || {};
    } else {
      throw new Error(response.message || 'Failed to get test results');
    }
  } catch (error) {
    console.error(`Error getting test results for session ${tsnId}:`, error);
    throw error;
  }
}
```

#### API Route Handler

```javascript
// Get test results
router.get('/test-results/:tsnId', validateCredentials, async (req, res) => {
  try {
    const { tsnId } = req.params;
    console.log(`GET /local/test-results/${tsnId}`);
    
    // Use the database module to fetch test results
    const testResults = await db.getTestResults(tsnId);
    
    // Return as JSON with success flag
    return res.json({
      success: true,
      data: testResults || {},
      message: 'Test results retrieved successfully'
    });
  } catch (error) {
    console.error('Error retrieving test results:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to retrieve test results',
      error: error.message
    });
  }
});
```

#### Database Function

```javascript
/**
 * Get test results for a specific test session
 * @param {string|number} tsnId - Test session ID
 * @returns {Promise<Object>} - Test results
 */
async function getTestResults(tsnId) {
  if (!isInitialized) {
    await init();
  }
  
  return await queries.testResults.getTestResults(connection, tsnId);
}
```

## Searching for Test Cases

### User Story

As a user, I want to search for test cases so that I can find specific test cases to run.

### Flow Diagram

```
┌─────────────┐      ┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│    User     │      │   Frontend  │      │     API     │      │  Database   │
│  Interface  │─────►│    Layer    │─────►│    Layer    │─────►│    Layer    │
└─────────────┘      └─────────────┘      └─────────────┘      └─────────────┘
                           │                    ▲                    │
                           │                    │                    │
                           ▼                    │                    ▼
                     ┌─────────────┐           │              ┌─────────────┐
                     │  Display    │◄──────────┴──────────────│  Database   │
                     │  Search     │                          │  Query      │
                     │  Results    │                          │             │
                     └─────────────┘                          └─────────────┘
```

### Steps

1. **User Action**: User enters search criteria and submits the search form
2. **Frontend Layer**:
   - Sends a GET request to `/local/test-cases` endpoint
   - Request parameters: `{ name: "login", status: "active", limit: 20 }`
3. **API Layer**:
   - Validates the request
   - Calls the database layer to search for test cases
   - Parameters: `{ name: req.query.name, status: req.query.status, limit: req.query.limit }`
4. **Database Layer**:
   - Executes a query to search for test cases
   - SQL: `SELECT tc_id, name, status, case_driver, tp_id, comments, tickets FROM test_case WHERE name LIKE ? AND status = ? ORDER BY tc_id DESC LIMIT ?`
   - Parameters: `[`%${name}%`, status, limit]`
   - Returns test cases to the API layer
5. **API Layer**:
   - Formats the response and returns it to the frontend
   - Response: `{ success: true, data: [...], message: "Test cases retrieved successfully" }`
6. **Frontend Layer**:
   - Displays the search results in the UI

## Viewing Test Suite Details

### User Story

As a user, I want to view test suite details so that I can see what test cases are included in a test suite.

### Flow Diagram

```
┌─────────────┐      ┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│    User     │      │   Frontend  │      │     API     │      │  Database   │
│  Interface  │─────►│    Layer    │─────►│    Layer    │─────►│    Layer    │
└─────────────┘      └─────────────┘      └─────────────┘      └─────────────┘
                           │                    ▲                    │
                           │                    │                    │
                           ▼                    │                    ▼
                     ┌─────────────┐           │              ┌─────────────┐
                     │  Display    │◄──────────┴──────────────│  Database   │
                     │  Test Suite │                          │  Query      │
                     │  Details    │                          │             │
                     └─────────────┘                          └─────────────┘
```

### Steps

1. **User Action**: User selects a test suite to view details
2. **Frontend Layer**:
   - Sends a GET request to `/local/test-suites/:tsId` endpoint
   - Request parameters: `tsId` in URL path
3. **API Layer**:
   - Validates the request
   - Calls the database layer to get test suite details
   - Parameters: `tsId`
4. **Database Layer**:
   - Executes a query to get test suite details
   - SQL: `SELECT tcg_id, ts_id, uid, status, pj_id, name, comments, tickets, tag FROM test_case_group WHERE ts_id = ?`
   - Parameters: `[tsId]`
   - Executes another query to get test cases in the suite
   - SQL: `SELECT tc_id FROM test_case_group WHERE ts_id = ?`
   - Parameters: `[tsId]`
   - Returns test suite details and test cases to the API layer
5. **API Layer**:
   - Formats the response and returns it to the frontend
   - Response: `{ success: true, data: {...}, message: "Test suite details retrieved successfully" }`
6. **Frontend Layer**:
   - Displays the test suite details and test cases in the UI

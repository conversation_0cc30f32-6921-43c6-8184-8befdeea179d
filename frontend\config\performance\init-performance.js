/**
 * Performance Optimization Initialization
 * Sets up all performance enhancements for the config module
 */

/**
 * Initialize all performance optimizations
 * Note: Performance modules are loaded via script tags and create global instances
 */
function initializePerformanceOptimizations() {
    console.log('🚀 Initializing performance optimizations...');
    
    // 1. Initialize Request Manager (already created globally)
    if (!window.requestManager) {
        window.requestManager = new RequestManager();
    }
    
    // 2. Initialize Polling Coordinator (already created globally)
    if (!window.pollingCoordinator) {
        window.pollingCoordinator = new PollingCoordinator();
    }
    
    // 3. Initialize Performance Monitor (already created globally)
    if (!window.performanceMonitor) {
        window.performanceMonitor = new PerformanceMonitor();
    }
    
    // 4. Load optimized styles
    loadOptimizedStyles();
    
    // 5. Set up performance event listeners
    setupPerformanceEventListeners();
    
    // 6. Configure API service for optimization
    configureApiServiceOptimizations();
    
    // 7. Set up cleanup on page unload
    setupCleanup();
    
    console.log('✅ Performance optimizations initialized successfully');
    
    // Show performance monitor in development
    if (isDevelopmentMode()) {
        setTimeout(() => {
            window.performanceMonitor.show();
            console.log('Performance monitor shown (Ctrl+Shift+P to toggle)');
        }, 2000);
    }
}

/**
 * Load optimized CSS styles
 */
function loadOptimizedStyles() {
    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = './performance/optimized-styles.css';
    document.head.appendChild(link);
    console.log('✅ Optimized styles loaded');
}

/**
 * Set up performance-related event listeners
 */
function setupPerformanceEventListeners() {
    // Track API request performance
    const originalFetch = window.fetch;
    window.fetch = async function(...args) {
        const startTime = Date.now();
        const url = args[0];
        
        try {
            const response = await originalFetch.apply(this, args);
            const endTime = Date.now();
            const duration = endTime - startTime;
            
            // Log performance event
            if (window.performanceMonitor) {
                window.performanceMonitor.logEvent('api_request', {
                    url: url,
                    duration: duration,
                    status: response.status,
                    cached: response.headers.get('x-cache') === 'HIT'
                });
            }
            
            return response;
        } catch (error) {
            const endTime = Date.now();
            const duration = endTime - startTime;
            
            if (window.performanceMonitor) {
                window.performanceMonitor.logEvent('api_request_error', {
                    url: url,
                    duration: duration,
                    error: error.message
                });
            }
            
            throw error;
        }
    };
    
    // Track DOM updates
    const observer = new MutationObserver((mutations) => {
        const significantMutations = mutations.filter(mutation => 
            mutation.type === 'childList' && mutation.addedNodes.length > 0
        );
        
        if (significantMutations.length > 0 && window.performanceMonitor) {
            window.performanceMonitor.logEvent('dom_update', {
                mutationCount: significantMutations.length,
                timestamp: Date.now()
            });
        }
    });
    
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
    
    console.log('✅ Performance event listeners set up');
}

/**
 * Configure API service for optimization
 */
function configureApiServiceOptimizations() {
    // Wait for API service to be available
    const checkApiService = () => {
        if (window.apiService) {
            // Wrap API service methods with request manager
            const originalGetRecentRuns = window.apiService.getRecentRuns;
            window.apiService.getRecentRuns = async function(options = {}) {
                const requestKey = `recentRuns_${JSON.stringify(options)}`;
                return await window.requestManager.executeRequest(
                    requestKey,
                    () => originalGetRecentRuns.call(this, options),
                    'recentRuns'
                );
            };
            
            const originalGetActiveTests = window.apiService.getActiveTests;
            window.apiService.getActiveTests = async function() {
                const requestKey = 'activeTests';
                return await window.requestManager.executeRequest(
                    requestKey,
                    () => originalGetActiveTests.call(this),
                    'activeTests'
                );
            };
            
            const originalGetTestDetails = window.apiService.getTestDetails;
            window.apiService.getTestDetails = async function(tsnId) {
                const requestKey = `testDetails_${tsnId}`;
                return await window.requestManager.executeRequest(
                    requestKey,
                    () => originalGetTestDetails.call(this, tsnId),
                    'testDetails'
                );
            };
            
            console.log('✅ API service optimizations configured');
        } else {
            // Retry after 100ms
            setTimeout(checkApiService, 100);
        }
    };
    
    checkApiService();
}

/**
 * Set up cleanup on page unload
 */
function setupCleanup() {
    window.addEventListener('beforeunload', () => {
        // Stop all polling
        if (window.pollingCoordinator) {
            window.pollingCoordinator.stopPolling();
        }
        
        // Hide performance monitor
        if (window.performanceMonitor) {
            window.performanceMonitor.hide();
        }
        
        console.log('✅ Performance optimizations cleaned up');
    });
}

/**
 * Check if running in development mode
 */
function isDevelopmentMode() {
    return window.location.hostname === 'localhost' || 
           window.location.hostname === '127.0.0.1' ||
           window.location.search.includes('debug=true');
}

/**
 * Get optimization status
 */
function getOptimizationStatus() {
    return {
        requestManager: !!window.requestManager,
        pollingCoordinator: !!window.pollingCoordinator,
        performanceMonitor: !!window.performanceMonitor,
        apiServiceOptimized: !!(window.apiService && window.apiService.getRecentRuns.toString().includes('requestManager')),
        stylesLoaded: !!document.querySelector('link[href*="optimized-styles.css"]')
    };
}

/**
 * Force refresh all cached data
 */
function forceRefreshAll() {
    console.log('🔄 Force refreshing all cached data...');
    
    if (window.requestManager) {
        window.requestManager.clearCache();
    }
    
    if (window.pollingCoordinator) {
        window.pollingCoordinator.forceRefresh('activeTests');
        window.pollingCoordinator.forceRefresh('recentRuns');
    }
    
    console.log('✅ All cached data refreshed');
}

/**
 * Get performance recommendations
 */
function getPerformanceRecommendations() {
    if (!window.performanceMonitor) {
        return ['Performance monitor not initialized'];
    }
    
    const summary = window.performanceMonitor.getSummary();
    return summary.recommendations;
}

/**
 * Export performance data
 */
function exportPerformanceData() {
    if (window.performanceMonitor) {
        window.performanceMonitor.exportData();
    } else {
        console.warn('Performance monitor not available');
    }
}

// Make functions available globally
window.initializePerformanceOptimizations = initializePerformanceOptimizations;
window.getOptimizationStatus = getOptimizationStatus;
window.forceRefreshAll = forceRefreshAll;
window.getPerformanceRecommendations = getPerformanceRecommendations;
window.exportPerformanceData = exportPerformanceData;

// Auto-initialize when script loads
document.addEventListener('DOMContentLoaded', () => {
    initializePerformanceOptimizations();
});

// Also initialize immediately if DOM is already loaded
if (document.readyState === 'loading') {
    // DOM is still loading, wait for DOMContentLoaded
} else {
    // DOM is already loaded
    initializePerformanceOptimizations();
}

/**
 * Secure Session Manager
 * Handles JWT token generation, validation, and session management
 */

const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const { AUTH_CONFIG } = require('../config/app-config');

class SessionManager {
  constructor() {
    this.activeSessions = new Map(); // In production, use Redis or database
    this.refreshTokens = new Map(); // Store refresh tokens
    this.blacklistedTokens = new Set(); // Blacklisted tokens
    
    // Session configuration
    this.accessTokenExpiry = 15 * 60; // 15 minutes
    this.refreshTokenExpiry = 7 * 24 * 60 * 60; // 7 days
    this.jwtSecret = AUTH_CONFIG.SESSION_SECRET || this.generateSecret();
    this.refreshSecret = this.jwtSecret + '_refresh';
    
    // Cleanup interval
    this.cleanupInterval = setInterval(() => this.cleanup(), 5 * 60 * 1000); // 5 minutes
    
    console.log('✅ Session Manager initialized');
  }

  /**
   * Generate a secure secret if none provided
   * @returns {string} Generated secret
   */
  generateSecret() {
    const secret = crypto.randomBytes(64).toString('hex');
    console.warn('⚠️ Using generated JWT secret. Set SESSION_SECRET environment variable for production.');
    return secret;
  }

  /**
   * Create a new session with JWT tokens
   * @param {Object} user - User object
   * @param {string} clientIp - Client IP address
   * @param {string} userAgent - User agent string
   * @returns {Object} Session with tokens
   */
  createSession(user, clientIp = 'unknown', userAgent = 'unknown') {
    const sessionId = this.generateSessionId();
    const now = Math.floor(Date.now() / 1000);
    
    // Create access token payload
    const accessTokenPayload = {
      sessionId: sessionId,
      uid: user.uid,
      role: user.role,
      permissions: this.getUserPermissions(user),
      iat: now,
      exp: now + this.accessTokenExpiry,
      iss: 'smarttest-auth',
      aud: 'smarttest-app'
    };

    // Create refresh token payload
    const refreshTokenPayload = {
      sessionId: sessionId,
      uid: user.uid,
      type: 'refresh',
      iat: now,
      exp: now + this.refreshTokenExpiry,
      iss: 'smarttest-auth',
      aud: 'smarttest-app'
    };

    // Generate tokens
    const accessToken = jwt.sign(accessTokenPayload, this.jwtSecret, { algorithm: 'HS256' });
    const refreshToken = jwt.sign(refreshTokenPayload, this.refreshSecret, { algorithm: 'HS256' });

    // Store session data
    const sessionData = {
      id: sessionId,
      user: user,
      createdAt: now * 1000,
      lastActivity: now * 1000,
      clientIp: clientIp,
      userAgent: userAgent,
      accessTokenExp: (now + this.accessTokenExpiry) * 1000,
      refreshTokenExp: (now + this.refreshTokenExpiry) * 1000
    };

    this.activeSessions.set(sessionId, sessionData);
    this.refreshTokens.set(refreshToken, sessionId);

    // Clear any existing CSRF tokens for previous sessions of this user
    // This helps prevent CSRF token mismatches after server restarts
    try {
      const { csrfProtection } = require('../middleware/security');
      // Note: We don't have the old session ID, but this is handled by the CSRF retry mechanism
      console.log(`🔄 New session created - CSRF tokens will be refreshed as needed`);
    } catch (error) {
      // Ignore if CSRF protection is not available
    }

    console.log(`✅ Session created: ${sessionId} for user: ${user.uid}`);

    return {
      sessionId: sessionId,
      accessToken: accessToken,
      refreshToken: refreshToken,
      expiresIn: this.accessTokenExpiry,
      refreshExpiresIn: this.refreshTokenExpiry,
      tokenType: 'Bearer'
    };
  }

  /**
   * Validate access token
   * @param {string} token - JWT access token
   * @returns {Object} Validation result
   */
  validateAccessToken(token) {
    try {
      // Check if token is blacklisted
      if (this.blacklistedTokens.has(token)) {
        return { valid: false, error: 'Token has been revoked', code: 'TOKEN_REVOKED' };
      }

      // Verify and decode token
      const decoded = jwt.verify(token, this.jwtSecret, { 
        algorithms: ['HS256'],
        issuer: 'smarttest-auth',
        audience: 'smarttest-app'
      });

      // Check if session exists
      const session = this.activeSessions.get(decoded.sessionId);
      if (!session) {
        return { valid: false, error: 'Session not found', code: 'SESSION_NOT_FOUND' };
      }

      // Update last activity
      session.lastActivity = Date.now();

      return {
        valid: true,
        decoded: decoded,
        session: session,
        user: session.user
      };

    } catch (error) {
      if (error.name === 'TokenExpiredError') {
        return { valid: false, error: 'Token expired', code: 'TOKEN_EXPIRED' };
      } else if (error.name === 'JsonWebTokenError') {
        return { valid: false, error: 'Invalid token', code: 'TOKEN_INVALID' };
      } else {
        console.error('Token validation error:', error);
        return { valid: false, error: 'Token validation failed', code: 'VALIDATION_ERROR' };
      }
    }
  }

  /**
   * Refresh access token using refresh token
   * @param {string} refreshToken - Refresh token
   * @returns {Object} New tokens or error
   */
  refreshAccessToken(refreshToken) {
    try {
      // Verify refresh token
      const decoded = jwt.verify(refreshToken, this.refreshSecret, {
        algorithms: ['HS256'],
        issuer: 'smarttest-auth',
        audience: 'smarttest-app'
      });

      // Check if refresh token is valid and mapped to a session
      const sessionId = this.refreshTokens.get(refreshToken);
      if (!sessionId || sessionId !== decoded.sessionId) {
        return { success: false, error: 'Invalid refresh token', code: 'REFRESH_INVALID' };
      }

      // Get session data
      const session = this.activeSessions.get(sessionId);
      if (!session) {
        this.refreshTokens.delete(refreshToken);
        return { success: false, error: 'Session not found', code: 'SESSION_NOT_FOUND' };
      }

      // Create new access token
      const now = Math.floor(Date.now() / 1000);
      const accessTokenPayload = {
        sessionId: sessionId,
        uid: session.user.uid,
        role: session.user.role,
        permissions: this.getUserPermissions(session.user),
        iat: now,
        exp: now + this.accessTokenExpiry,
        iss: 'smarttest-auth',
        aud: 'smarttest-app'
      };

      const newAccessToken = jwt.sign(accessTokenPayload, this.jwtSecret, { algorithm: 'HS256' });

      // Update session
      session.lastActivity = Date.now();
      session.accessTokenExp = (now + this.accessTokenExpiry) * 1000;

      console.log(`✅ Access token refreshed for session: ${sessionId}`);

      return {
        success: true,
        accessToken: newAccessToken,
        expiresIn: this.accessTokenExpiry,
        tokenType: 'Bearer'
      };

    } catch (error) {
      if (error.name === 'TokenExpiredError') {
        // Remove expired refresh token
        this.refreshTokens.delete(refreshToken);
        return { success: false, error: 'Refresh token expired', code: 'REFRESH_EXPIRED' };
      } else {
        console.error('Refresh token validation error:', error);
        return { success: false, error: 'Invalid refresh token', code: 'REFRESH_INVALID' };
      }
    }
  }

  /**
   * Revoke session and invalidate tokens
   * @param {string} sessionId - Session ID
   * @param {string} accessToken - Access token to blacklist
   * @returns {boolean} Success status
   */
  revokeSession(sessionId, accessToken = null) {
    try {
      // Remove session
      const session = this.activeSessions.get(sessionId);
      if (session) {
        this.activeSessions.delete(sessionId);
        console.log(`✅ Session revoked: ${sessionId}`);
      }

      // Remove refresh tokens for this session
      for (const [refreshToken, sessId] of this.refreshTokens.entries()) {
        if (sessId === sessionId) {
          this.refreshTokens.delete(refreshToken);
        }
      }

      // Blacklist access token if provided
      if (accessToken) {
        this.blacklistedTokens.add(accessToken);
      }

      return true;
    } catch (error) {
      console.error('Error revoking session:', error);
      return false;
    }
  }

  /**
   * Get user permissions (helper method)
   * @param {Object} user - User object
   * @returns {Array} Array of permissions
   */
  getUserPermissions(user) {
    // This would typically come from the user manager
    const rolePermissions = {
      admin: ['read', 'write', 'delete', 'manage_users', 'view_logs'],
      tester: ['read', 'write'],
      viewer: ['read']
    };

    return rolePermissions[user.role] || [];
  }

  /**
   * Generate secure session ID
   * @returns {string} Session ID
   */
  generateSessionId() {
    return crypto.randomBytes(32).toString('hex');
  }

  /**
   * Get session by ID
   * @param {string} sessionId - Session ID
   * @returns {Object|null} Session data or null
   */
  getSession(sessionId) {
    return this.activeSessions.get(sessionId) || null;
  }

  /**
   * Get all sessions for a user
   * @param {string} uid - User ID
   * @returns {Array} Array of sessions
   */
  getUserSessions(uid) {
    const sessions = [];
    for (const session of this.activeSessions.values()) {
      if (session.user.uid === uid) {
        sessions.push({
          id: session.id,
          createdAt: session.createdAt,
          lastActivity: session.lastActivity,
          clientIp: session.clientIp,
          userAgent: session.userAgent
        });
      }
    }
    return sessions;
  }

  /**
   * Cleanup expired sessions and tokens
   */
  cleanup() {
    const now = Date.now();
    let cleanedSessions = 0;
    let cleanedRefreshTokens = 0;
    let cleanedBlacklistedTokens = 0;

    // Clean expired sessions
    for (const [sessionId, session] of this.activeSessions.entries()) {
      if (now > session.refreshTokenExp) {
        this.activeSessions.delete(sessionId);
        cleanedSessions++;
      }
    }

    // Clean expired refresh tokens
    for (const [refreshToken, sessionId] of this.refreshTokens.entries()) {
      const session = this.activeSessions.get(sessionId);
      if (!session || now > session.refreshTokenExp) {
        this.refreshTokens.delete(refreshToken);
        cleanedRefreshTokens++;
      }
    }

    // Clean old blacklisted tokens (keep for 24 hours)
    const blacklistCutoff = now - (24 * 60 * 60 * 1000);
    for (const token of this.blacklistedTokens) {
      try {
        const decoded = jwt.decode(token);
        if (decoded && decoded.exp * 1000 < blacklistCutoff) {
          this.blacklistedTokens.delete(token);
          cleanedBlacklistedTokens++;
        }
      } catch (error) {
        // Invalid token, remove it
        this.blacklistedTokens.delete(token);
        cleanedBlacklistedTokens++;
      }
    }

    if (cleanedSessions > 0 || cleanedRefreshTokens > 0 || cleanedBlacklistedTokens > 0) {
      console.log(`🧹 Cleanup completed: ${cleanedSessions} sessions, ${cleanedRefreshTokens} refresh tokens, ${cleanedBlacklistedTokens} blacklisted tokens`);
    }
  }

  /**
   * Get session statistics
   * @returns {Object} Session statistics
   */
  getStats() {
    return {
      activeSessions: this.activeSessions.size,
      refreshTokens: this.refreshTokens.size,
      blacklistedTokens: this.blacklistedTokens.size,
      accessTokenExpiry: this.accessTokenExpiry,
      refreshTokenExpiry: this.refreshTokenExpiry
    };
  }

  /**
   * Destroy session manager and cleanup
   */
  destroy() {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    this.activeSessions.clear();
    this.refreshTokens.clear();
    this.blacklistedTokens.clear();
  }
}

// Export singleton instance
module.exports = new SessionManager();

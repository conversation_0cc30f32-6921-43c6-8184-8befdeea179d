/**
 * Mock implementation of reports.js
 */

// Global variables
window.elements = elements;
window.currentState = currentState;
window.config = config;

/**
 * Loads reports data from external API or database
 */
async function loadReportsData() {
  try {
    // Get session IDs
    const credentials = {
      uid: sessionStorage.getItem('smarttest_uid'),
      password: sessionStorage.getItem('smarttest_pwd')
    };
    
    const sessionIds = await window.sessionIdService.getRecentSessionIds(
      credentials,
      config.maxReportsToShow
    );
    
    // Get reports data
    let reports = [];
    if (config.useDirectExternalApi) {
      reports = await loadReportsFromExternalApi(sessionIds, credentials);
    } else {
      reports = await loadReportsFromDatabaseApi(sessionIds);
    }
    
    // Store reports in current state
    currentState.reports = reports;
    
    // Update UI
    updateReportsTable();
    updateCharts();
    
    return reports;
  } catch (error) {
    console.error('Error loading reports:', error);
    elements.reportsTable.innerHTML = `
      <tr>
        <td colspan="7" class="text-center text-danger">
          <strong>Error loading reports:</strong> ${error.message}
        </td>
      </tr>
    `;
    return [];
  }
}

/**
 * Loads reports data from external API
 * @param {string[]} sessionIds Array of session IDs
 * @param {Object} credentials User credentials
 * @returns {Promise<Object[]>} Array of reports
 */
async function loadReportsFromExternalApi(sessionIds, credentials) {
  return await window.externalApiService.getRecentTestRuns(
    sessionIds,
    credentials.uid,
    credentials.password,
    config.maxReportsToShow
  );
}

/**
 * Loads reports data from database API
 * @param {string[]} sessionIds Array of session IDs
 * @returns {Promise<Object[]>} Array of reports
 */
async function loadReportsFromDatabaseApi(sessionIds) {
  const response = await fetch(`${window.location.origin}${config.reportingEndpoint}?ids=${sessionIds.join(',')}`);
  
  if (!response.ok) {
    throw new Error(`API request failed with status ${response.status}`);
  }
  
  const data = await response.json();
  
  if (!data.success) {
    throw new Error(data.message || 'API returned an error');
  }
  
  return data.data || [];
}

/**
 * Loads test details for a specific test
 * @param {string} testId Test ID
 * @returns {Promise<Object>} Test details
 */
async function loadTestDetails(testId) {
  try {
    let testDetails = null;
    
    // Check if we already have the details
    const existingReport = currentState.reports.find(r => r.id === testId || r.tsn_id === testId);
    
    const credentials = {
      uid: sessionStorage.getItem('smarttest_uid'),
      password: sessionStorage.getItem('smarttest_pwd')
    };
    
    if (config.useDirectExternalApi) {
      testDetails = await loadTestDetailsFromExternalApi(testId, credentials);
    } else {
      testDetails = await loadTestDetailsFromDatabaseApi(testId);
    }
    
    // Store test details in current state
    currentState.currentTestDetails = testDetails;
    
    // Update UI
    elements.testDetailsSection.classList.remove('d-none');
    displayTestDetails();
    updateTestCasesTable(testDetails.test_cases || []);
    
    return testDetails;
  } catch (error) {
    console.error('Error loading test details:', error);
    elements.testDetailsTitle.textContent = 'Error Loading Test Details';
    elements.testDetailsInfo.innerHTML = `<div class="alert alert-danger">${error.message}</div>`;
    elements.testCasesTable.innerHTML = '';
    return null;
  }
}

/**
 * Loads test details from external API
 * @param {string} testId Test ID
 * @param {Object} credentials User credentials
 * @returns {Promise<Object>} Test details
 */
async function loadTestDetailsFromExternalApi(testId, credentials) {
  // Get report summary
  const summary = await window.externalApiService.getReportSummary(
    testId,
    credentials.uid,
    credentials.password
  );
  
  // Get report details
  const details = await window.externalApiService.getReportDetails(
    testId,
    1,
    credentials.uid,
    credentials.password
  );
  
  // Combine summary and details
  return {
    ...summary,
    test_cases: details.test_cases || []
  };
}

/**
 * Loads test details from database API
 * @param {string} testId Test ID
 * @returns {Promise<Object>} Test details
 */
async function loadTestDetailsFromDatabaseApi(testId) {
  const response = await fetch(`${window.location.origin}${config.testDetailsEndpoint}/${testId}`);
  
  if (!response.ok) {
    throw new Error(`API request failed with status ${response.status}`);
  }
  
  const data = await response.json();
  
  if (!data.success) {
    throw new Error(data.message || 'API returned an error');
  }
  
  return data.data || {};
}

/**
 * Updates the reports table with current data
 */
function updateReportsTable() {
  if (!currentState.reports || currentState.reports.length === 0) {
    elements.reportsTable.innerHTML = `
      <tr>
        <td colspan="7" class="text-center">
          No reports found
        </td>
      </tr>
    `;
    return;
  }
  
  elements.reportsTable.innerHTML = '';
  
  currentState.reports.forEach(report => {
    const row = document.createElement('tr');
    
    // Format date
    const startTime = formatDate(report.startTime || report.start_time);
    
    // Get status badge class
    const statusClass = getStatusBadgeClass(report.status);
    
    row.innerHTML = `
      <td>${report.tsn_id || report.id}</td>
      <td>${report.type || 'Test Case'}</td>
      <td>${report.environment || 'UNKNOWN'}</td>
      <td><span class="badge ${statusClass}">${report.status}</span></td>
      <td>${startTime}</td>
      <td>${report.duration || 'N/A'}</td>
      <td>
        <button class="btn btn-sm btn-primary view-details-btn" data-id="${report.tsn_id || report.id}">
          Details
        </button>
      </td>
    `;
    
    // Add event listener to details button
    const detailsBtn = row.querySelector('.view-details-btn');
    detailsBtn.addEventListener('click', () => {
      loadTestDetails(report.tsn_id || report.id);
    });
    
    elements.reportsTable.appendChild(row);
  });
}

/**
 * Displays test details in the UI
 */
function displayTestDetails() {
  const details = currentState.currentTestDetails;
  
  if (!details) {
    elements.testDetailsTitle.textContent = 'No Test Details Available';
    elements.testDetailsInfo.innerHTML = '';
    return;
  }
  
  // Update title
  elements.testDetailsTitle.textContent = `${details.type || 'Test Case'}: ${details.test_id || details.tsn_id || details.id}`;
  
  // Format dates
  const startTime = formatDate(details.startTime || details.start_time);
  const endTime = formatDate(details.endTime || details.end_time);
  
  // Get status badge class
  const statusClass = getStatusBadgeClass(details.status);
  
  // Update info
  elements.testDetailsInfo.innerHTML = `
    <div class="row">
      <div class="col-md-6">
        <p><strong>Environment:</strong> ${details.environment || 'UNKNOWN'}</p>
        <p><strong>Status:</strong> <span class="badge ${statusClass}">${details.status}</span></p>
        <p><strong>Duration:</strong> ${details.duration || 'N/A'}</p>
      </div>
      <div class="col-md-6">
        <p><strong>Start Time:</strong> ${startTime}</p>
        <p><strong>End Time:</strong> ${endTime}</p>
        <p><strong>User:</strong> ${details.user || 'N/A'}</p>
      </div>
    </div>
    <div class="row">
      <div class="col-md-12">
        <div class="progress">
          <div class="progress-bar bg-success" role="progressbar" style="width: ${details.pass_rate || 0}%">
            ${details.pass_rate || 0}% Passed
          </div>
          <div class="progress-bar bg-danger" role="progressbar" style="width: ${100 - (details.pass_rate || 0)}%">
            ${100 - (details.pass_rate || 0)}% Failed
          </div>
        </div>
      </div>
    </div>
  `;
}

/**
 * Updates the test cases table
 * @param {Object[]} testCases Array of test cases
 */
function updateTestCasesTable(testCases) {
  if (!testCases || testCases.length === 0) {
    elements.testCasesTable.innerHTML = `
      <tr>
        <td colspan="5" class="text-center">
          No test cases found
        </td>
      </tr>
    `;
    return;
  }
  
  elements.testCasesTable.innerHTML = '';
  
  testCases.forEach(testCase => {
    const row = document.createElement('tr');
    
    // Get status badge class
    const statusClass = getStatusBadgeClass(testCase.status);
    
    row.innerHTML = `
      <td>${testCase.tc_id}</td>
      <td>${testCase.seq_index}</td>
      <td><span class="badge ${statusClass}">${testCase.status}</span></td>
      <td>${testCase.description}</td>
      <td>
        ${testCase.error_message ? `<div class="alert alert-danger">${testCase.error_message}</div>` : ''}
      </td>
    `;
    
    elements.testCasesTable.appendChild(row);
  });
}

// Export functions for testing
if (typeof module !== 'undefined') {
  module.exports = {
    loadReportsData,
    loadReportsFromExternalApi,
    loadReportsFromDatabaseApi,
    loadTestDetails,
    loadTestDetailsFromExternalApi,
    loadTestDetailsFromDatabaseApi,
    updateReportsTable,
    displayTestDetails,
    updateTestCasesTable
  };
}

/**
 * API Testing Tool
 * 
 * This script tests the CaseRunner API endpoint with various parameter formats
 * to identify issues and demonstrate the correct approach for making API calls.
 * 
 * Usage:
 *   node api_test.js [environment] [testCaseId] [username]
 * 
 * Examples:
 *   node api_test.js qa02 3180 <EMAIL>
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');
const dbConnector = require('./db-connector');

// Process command line arguments
const args = process.argv.slice(2);
const environment = args[0] || 'qa02';
const testCaseId = args[1] || '3180';
const username = args[2] || '<EMAIL>';

// Main function
async function main() {
  console.log(`\n=== API TESTING TOOL FOR ${environment.toUpperCase()} ===\n`);
  
  // Validate environment
  if (!dbConnector.environments[environment]) {
    console.error(`Error: Unknown environment '${environment}'`);
    console.error(`Available environments: ${Object.keys(dbConnector.environments).join(', ')}`);
    process.exit(1);
  }
  
  // Get environment configuration
  const config = dbConnector.environments[environment];
  
  // Base URL for API calls
  const baseUrl = config.BASE_URL;
  
  console.log('Test Configuration:');
  console.log('--------------------------');
  console.log(`Environment: ${environment}`);
  console.log(`API URL: ${baseUrl}`);
  console.log(`Test Case ID: ${testCaseId}`);
  console.log(`Username: ${username}`);
  console.log('--------------------------\n');
  
  // Run tests with different parameter formats
  try {
    // Test 1: Using URLSearchParams with proper encoding (typically fails)
    await testUrlSearchParams(baseUrl, testCaseId, username, environment);
    
    // Test 2: Using exact string format with double-ampersand (correctly works)
    await testExactStringFormat(baseUrl, testCaseId, username, environment);
    
    // Test 3: Compare the differences between the two approaches
    await compareApproaches();
    
    console.log('\n=== RECOMMENDATIONS ===');
    console.log('1. Use the exact string format with double-ampersand (&&) for API calls');
    console.log('2. Do NOT use URLSearchParams or other encoding methods as they encode the double-ampersand');
    console.log('3. The API requires the exact format: "param1=value1&param2=value2&&param3=value3"');
    console.log('4. Include the user information (username) in the API call for proper test attribution');
  } catch (error) {
    console.error(`\nError: ${error.message}`);
    process.exit(1);
  }
}

/**
 * Test API call using URLSearchParams (encoded parameters)
 * @param {string} baseUrl - Base API URL
 * @param {string} testCaseId - Test case ID
 * @param {string} username - Username
 * @param {string} environment - Environment name
 */
async function testUrlSearchParams(baseUrl, testCaseId, username, environment) {
  console.log('TEST 1: Using URLSearchParams (encoded parameters)');
  console.log('------------------------------------------------------');
  
  // Create request parameters
  const params = new URLSearchParams();
  params.append('uid', username);
  params.append('password', 'test');
  params.append('tc_id', testCaseId);
  params.append('envir', environment);
  params.append('shell_host', 'jps-qa10-app01');
  params.append('file_path', '/home/<USER>/');
  params.append('operatorConfigs', 'operatorNameConfigs');
  params.append('kafka_server', 'kafka-qa-a0.lab.wagerworks.com');
  params.append('dataCenter', 'GU');
  params.append('rgs_env', environment);
  params.append('old_version', '0');
  // Try to add the double-ampersand
  params.append('', ''); // This adds a & but not && as needed
  params.append('networkType1', 'multi-site');
  params.append('networkType2', 'multi-site');
  params.append('sign', '-');
  params.append('rate_src', 'local');
  
  const encodedData = params.toString();
  console.log('Request data:');
  console.log(encodedData);
  
  try {
    // Make the API request
    const response = await axios.post(
      `${baseUrl}CaseRunner`,
      encodedData,
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      }
    );
    
    console.log(`Response status: ${response.status}`);
    
    // Extract tsn_id from the response
    const tsnId = extractTsnId(response.data);
    
    if (tsnId) {
      console.log(`SUCCESS: Test initiated with tsn_id: ${tsnId}`);
    } else {
      console.log('WARNING: Request succeeded but could not extract tsn_id');
      console.log('The test might not have been initiated correctly');
    }
  } catch (error) {
    console.error(`FAILED: API call error: ${error.message}`);
    if (error.response) {
      console.error(`Response status: ${error.response.status}`);
    }
  }
  
  console.log('------------------------------------------------------\n');
}

/**
 * Test API call using exact string format with double-ampersand
 * @param {string} baseUrl - Base API URL
 * @param {string} testCaseId - Test case ID
 * @param {string} username - Username
 * @param {string} environment - Environment name
 */
async function testExactStringFormat(baseUrl, testCaseId, username, environment) {
  console.log('TEST 2: Using exact string format with double-ampersand');
  console.log('------------------------------------------------------');
  
  // Create request data with exact format (including double-ampersand)
  const requestData = `uid=${username}&password=test&tc_id=${testCaseId}&envir=${environment}&shell_host=jps-qa10-app01&file_path=/home/<USER>/&operatorConfigs=operatorNameConfigs&kafka_server=kafka-qa-a0.lab.wagerworks.com&dataCenter=GU&rgs_env=${environment}&old_version=0&&networkType1=multi-site&networkType2=multi-site&sign=-&rate_src=local`;
  
  console.log('Request data:');
  console.log(requestData);
  
  try {
    // Make the API request
    const response = await axios.post(
      `${baseUrl}CaseRunner`,
      requestData,
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      }
    );
    
    console.log(`Response status: ${response.status}`);
    
    // Extract tsn_id from the response
    const tsnId = extractTsnId(response.data);
    
    if (tsnId) {
      console.log(`SUCCESS: Test initiated with tsn_id: ${tsnId}`);
      
      // Save the HTML response for analysis
      const responsePath = path.join(__dirname, 'api_response.html');
      fs.writeFileSync(responsePath, 
        typeof response.data === 'string' ? response.data : JSON.stringify(response.data, null, 2)
      );
      console.log(`Response HTML saved to ${responsePath}`);
    } else {
      console.log('WARNING: Request succeeded but could not extract tsn_id');
      console.log('The test might not have been initiated correctly');
    }
  } catch (error) {
    console.error(`FAILED: API call error: ${error.message}`);
    if (error.response) {
      console.error(`Response status: ${error.response.status}`);
    }
  }
  
  console.log('------------------------------------------------------\n');
}

/**
 * Compare the two approaches to identify differences
 */
async function compareApproaches() {
  console.log('TEST 3: Comparing the two approaches');
  console.log('------------------------------------------------------');
  
  // Original parameters from URLSearchParams
  const params = new URLSearchParams();
  params.append('uid', '<EMAIL>');
  params.append('password', 'test');
  params.append('tc_id', '3180');
  params.append('envir', 'qa02');
  params.append('old_version', '0');
  params.append('', ''); // Attempting to add double-ampersand
  params.append('networkType1', 'multi-site');
  
  const encodedData = params.toString();
  
  // Working parameters from exact string
  const workingData = "uid=<EMAIL>&password=test&tc_id=3180&envir=qa02&old_version=0&&networkType1=multi-site";
  
  console.log('Encoded parameters:');
  console.log(encodedData);
  console.log('\nExact string parameters:');
  console.log(workingData);
  
  // Find the key differences
  console.log('\nKey Differences:');
  
  // Check for double-ampersand in both strings
  const encodedHasDoubleAmp = encodedData.includes('&&');
  const workingHasDoubleAmp = workingData.includes('&&');
  
  console.log(`Double-ampersand in encoded data: ${encodedHasDoubleAmp ? 'YES' : 'NO'}`);
  console.log(`Double-ampersand in exact string: ${workingHasDoubleAmp ? 'YES' : 'NO'}`);
  
  // Highlight the specific difference
  if (!encodedHasDoubleAmp && workingHasDoubleAmp) {
    console.log('\nCRITICAL DIFFERENCE FOUND:');
    console.log('- URLSearchParams encodes "&&" as "&" (a single ampersand)');
    console.log('- The exact string format preserves "&&" (the double-ampersand)');
    console.log('- The API requires the exact "&&" to function correctly');
  }
  
  console.log('------------------------------------------------------\n');
}

/**
 * Extract tsn_id from API response
 * @param {string} responseData - API response data
 * @returns {string|null} Extracted tsn_id or null if not found
 */
function extractTsnId(responseData) {
  if (typeof responseData !== 'string') {
    console.log('Response is not a string');
    return null;
  }
  
  // Try various patterns to extract tsn_id
  const patterns = [
    /ReportSummary\?tsn_id=(\d+)/,
    /CaseEditor\?tsn_id=(\d+)/,
    /tsn_id=(\d+)/,
    /tsn_id.*?(\d+)/
  ];
  
  for (const pattern of patterns) {
    const match = responseData.match(pattern);
    if (match && match[1]) {
      return match[1];
    }
  }
  
  console.log('Could not find tsn_id in response data');
  
  // Show snippet of response for debugging
  if (typeof responseData === 'string') {
    const tsnIdPosition = responseData.indexOf('tsn_id');
    if (tsnIdPosition !== -1) {
      const start = Math.max(0, tsnIdPosition - 50);
      const end = Math.min(responseData.length, tsnIdPosition + 150);
      console.log('Content around tsn_id keyword:');
      console.log(responseData.substring(start, end));
    }
  }
  
  return null;
}

// Run the main function
main().catch(console.error); 
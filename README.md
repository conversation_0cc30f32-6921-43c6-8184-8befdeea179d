# SmartTest

SmartTest is a comprehensive testing solution for RGS game platforms, providing automated test case execution, reporting, and monitoring capabilities. It connects to an existing testing framework API and a MySQL database.

## Features

- Integration with the RGS Testing Framework
- Real-time test monitoring and status updates
- Customizable test suites for different game features
- Test execution metrics and reporting
- Historical test data analysis
- Custom test case creation and execution

## Getting Started

This guide will help you set up and run the SmartTest application from scratch using the command line.

### Prerequisites

1. Git installed on your computer
2. Node.js (version 14.x or higher)
3. SSH key authentication for secure database connections

### Setup Instructions

Follow these steps in order:

1. **Clone the Repository**
   
   Open Command Prompt or PowerShell and run:
   ```
   git clone -b feature/mvp-manual-testing https://github.com/yacov/smarttest.git
   cd smarttest
   ```
   
   This command clones the repository and specifically checks out the `feature/mvp-manual-testing` branch.

2. **Verify and Update the Code**
   
   First, check which branch you're currently on:
   ```
   git branch
   ```
   The current branch will be marked with an asterisk (*). 
   
   If you're not on the `feature/mvp-manual-testing` branch, switch to it:
   ```
   git checkout feature/mvp-manual-testing
   ```
   
   If you see an error like "error: pathspec 'feature/mvp-manual-testing' did not match any file(s) known to git", you need to fetch the remote branches first:
   ```
   git fetch
   git checkout feature/mvp-manual-testing
   ```

   Then, ensure you have the latest version:
   ```
   git pull
   ```

3. **Clean Up Existing Installation**
   
   Remove old package files:
   ```
   del package-lock.json
   rd /s /q node_modules
   ```

4. **Install Dependencies**
   
   Install all required packages:
   ```
   npm install
   ```

5. **Environment Setup**
   
   Copy the sample environment files:
   ```
   cd frontend/server
   copy .env.02.sample .env
   copy .env.01.sample .env.01
   copy .env.03.sample .env.03
   cd ../..
   ```
   
   Edit each environment file to add your credentials:
   - Open each file in Notepad:
     ```
     notepad frontend/server/.env
     ```
   - Update the following:
     - SSH_USER with your SSH username
     - SSH_KEY_PATH with the path to your SSH key (usually %USERPROFILE%\.ssh\id_rsa_dbserver)
     - API_USER and API_PASSWORD with your API credentials

6. **Build the Application**
   
   Compile and build the application:
   ```
   npm run build
   ```

7. **Start the Application**
   
   Start the server:
   ```
   npm start
   ```

8. **Access the Application**
   
   Open your web browser and go to:
   ```
   http://localhost:3000
   ```

## Troubleshooting

### Database Connection Issues
- Verify your environment files contain the correct credentials
- Check SSH key permissions (ensure your key is in the right location)
- Check that your SSH key has the correct permissions


### Common Problems and Solutions

1. **SSH Connection Failure**
   - Ensure your SSH key exists in the specified path
   - Check that the SSH server is accessible from your network
   - If you need to generate an SSH key, you can use:
     ```
     ssh-keygen -t rsa -b 4096 -f %USERPROFILE%\.ssh\id_rsa_dbserver
     ```

2. **Git Branch Issues**
   - If you can't switch to the feature branch, try:
     ```
     git fetch --all
     git checkout feature/mvp-manual-testing
     ```
   - If you have local changes preventing checkout:
     ```
     git stash
     git checkout feature/mvp-manual-testing
     ```

3. **Application Won't Start**
   - Check for error messages in the console
   - Verify that port 3000 is not in use by another application
   - Make sure all environment variables are set correctly
   - Try restarting with: `npm start`

4. **Test Execution Failures**
   - Verify API credentials and connectivity
   - Check test parameter configurations
   - Review the API logs for errors

## Project Structure

- `frontend/` - UI components and client-side logic
- `frontend/server/` - Node.js backend server
- `frontend/server/database/` - Database connection and queries
- `frontend/server/documentation/` - Project documentation and guides
- `utils/` - Utility scripts for setup and diagnostics

## Documentation

- [API Documentation](frontend/server/documentation/API/README.md)
- [Database Documentation](frontend/server/documentation/Database/README.md)
- [Integration Documentation](frontend/server/documentation/Integration/README.md)

# Active Tests Implementation Guide

## Overview
The Active Tests section displays real-time information about currently running or recently completed test executions. This document details the implementation, data flow, and recent fixes applied to resolve flickering and incorrect Stop button behavior.

## Core Components

### 1. Data Structures

#### `activeTests` Map
- **Location**: `api-integration.js`
- **Purpose**: Stores currently monitored test sessions
- **Key Format**: String TSN ID (Test Session Number ID)
- **Value Format**: Test object with status, progress, timing information
- **Issues Fixed**: Key format inconsistencies causing `[object Object]` errors

#### `recentRunsCache` Array  
- **Location**: `api-integration.js`
- **Purpose**: Caches recent test runs for quick access
- **Max Size**: 50 items
- **Update Frequency**: Every 10 seconds via `pollRecentRuns()`
- **Usage**: Supplements activeTests for comprehensive display

### 2. Key Functions

#### `renderActiveTests()`
**Location**: `api-integration.js` lines 1403-1661
**Purpose**: Main rendering function for Active Tests section
**Recent Fixes**:
- Implemented efficient DOM updates to prevent flickering
- Fixed status determination logic
- Corrected Stop button display logic

**Logic Flow**:
```javascript
1. Gather active tests from activeTests Map + recentRunsCache
2. Apply user filtering (all/mine/others)  
3. Group tests by TSN ID
4. Determine status and active state
5. Efficiently update DOM (reuse existing cards)
6. Show/hide Stop buttons based on active state
```

#### `updateTestStatuses()`
**Location**: `api-integration.js` lines 1140-1286
**Purpose**: Poll test status updates every 5 seconds
**Key Features**:
- Prevents concurrent updates with `_isUpdatingStatuses` flag
- Updates test progress, passed/failed counts
- Handles test completion notifications
- Manages activeTests Map lifecycle

#### `processTestData()`
**Purpose**: Normalizes test data from different sources
**Handles**: Inconsistent field names, status formats, timing data

## Status Logic Implementation

### Test State Determination
```javascript
// A test is completed if it has an endTime OR explicit completion status
const isCompleted = testInfo.endTime || 
                   ['passed', 'failed', 'completed', 'stopped'].includes(testInfo.status?.toLowerCase());

// A test is active (can be stopped) if it's running AND not completed  
const isActive = running > 0 && !isCompleted;
```

### Status Classes and Display
| Internal Status | Display Class | Display Text | Stop Button |
|----------------|---------------|--------------|-------------|
| `running > 0 && !completed` | `running` | "Running" | ✅ Yes |
| `completed && failed > 0` | `failed` | "Failed" | ❌ No |
| `completed && passed > 0` | `passed` | "Passed" | ❌ No |
| `completed && no results` | `completed` | "Completed" | ❌ No |
| `!running && !completed` | `pending` | "Pending" | ❌ No |

### User Filtering
- **All**: Shows all active tests
- **Mine**: Shows tests initiated by current user
- **Others**: Shows tests initiated by other users
- **User Identification**: Uses `uid`, `user_id`, or `initiator_user` fields

## DOM Update Strategy (Fixed)

### Previous Implementation (Problematic)
```javascript
// Caused flickering every 5 seconds
container.innerHTML = ''; 
// Recreate all cards from scratch
```

### Current Implementation (Optimized)
```javascript
// Efficient card reuse and selective updates
const existingCards = new Map();
const existingElements = container.querySelectorAll('.test-card[data-tsn-id]');

// Reuse existing cards, only update content
if (existingCard) {
  existingCard.innerHTML = card.innerHTML;
  existingCard.className = `test-card ${statusClass}`;
} else {
  container.appendChild(card);
}

// Remove cards for tests no longer active
existingCards.forEach((cardElement, tsnId) => {
  if (!renderedCards.has(tsnId)) {
    cardElement.remove();
  }
});
```

## API Integration

### Status Polling Endpoint
- **URL**: `/api/test-status`
- **Method**: GET
- **Parameters**: 
  - `tsn_id`: Array of test session IDs to check
  - `uid`, `password`: Credentials
- **Response**: Test status updates with progress information

### Recent Runs Endpoint  
- **URL**: `/local/recent-runs`
- **Method**: GET
- **Parameters**: `since_id` for incremental updates
- **Response**: New test runs since specified TSN ID

## Data Flow Diagram

```
User Initiates Test
        ↓
Test Added to activeTests Map
        ↓
updateTestStatuses() polls every 5s
        ↓
API call to /api/test-status
        ↓
Status updated in activeTests Map
        ↓
renderActiveTests() called
        ↓
Efficient DOM update (card reuse)
        ↓
User sees updated test status
        ↓
Test completion → Remove from activeTests
```

## Common Issues and Solutions

### 1. Flickering Active Tests
**Cause**: Full DOM clearing on every update
**Solution**: Implemented card reuse with selective updates
**Status**: ✅ Fixed

### 2. Incorrect Stop Button Display
**Cause**: Flawed `isActive` logic not considering endTime
**Solution**: Fixed logic to check both running state and completion status
**Status**: ✅ Fixed

### 3. Status Inconsistencies  
**Cause**: Multiple status vocabularies and mixed casing
**Solution**: Normalized status checking with toLowerCase()
**Status**: ✅ Fixed

### 4. Object Keys in activeTests Map
**Cause**: Non-string keys causing `[object Object]` in API calls
**Solution**: Ensured consistent string key format
**Status**: ✅ Fixed

## Testing Scenarios

### Test Case 1: Running Test
- **Expected**: Shows "Running" status, Stop button visible
- **Card Color**: Blue/running class
- **Polling**: Active every 5 seconds

### Test Case 2: Completed Test (Passed)
- **Expected**: Shows "Passed" status, no Stop button
- **Card Color**: Green/passed class  
- **Lifecycle**: Removed after 10-second grace period

### Test Case 3: Completed Test (Failed)
- **Expected**: Shows "Failed" status, no Stop button
- **Card Color**: Red/failed class
- **Additional**: May show failure details

### Test Case 4: Pending Test
- **Expected**: Shows "Pending" status, no Stop button
- **Card Color**: Gray/pending class
- **Behavior**: Waiting to start execution

## Performance Considerations

### Current State
- DOM updates every 5 seconds regardless of changes
- Full status polling for all active tests
- Individual API calls per test session

### Optimization Opportunities
1. **Change Detection**: Only update DOM when status actually changes
2. **Batch Status Updates**: Single API call for multiple test sessions
3. **Progressive Loading**: Load test details on demand
4. **WebSocket Integration**: Real-time updates without polling

## Configuration Options

### Polling Intervals
```javascript
// Current hard-coded values
const STATUS_POLLING_INTERVAL = 5000;  // 5 seconds
const RECENT_RUNS_POLLING_INTERVAL = 10000;  // 10 seconds
const DASHBOARD_REFRESH_INTERVAL = 30000;  // 30 seconds
```

### Grace Period
```javascript
const COMPLETED_TEST_GRACE_PERIOD = 10000;  // 10 seconds
// Tests remain visible for 10 seconds after completion
```

### Cache Limits
```javascript
const MAX_RECENT_RUNS_CACHE = 50;  // Maximum cached recent runs
```

---
*Document created: 2025-07-02*
*Related to: Active Tests section implementation and flickering bug fix*

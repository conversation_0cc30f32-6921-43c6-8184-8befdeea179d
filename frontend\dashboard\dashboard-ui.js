/**
 * dashboard-ui.js
 *
 * This module handles general UI updates and utility functions that are not
 * tied to a specific feature. This includes managing notifications, updating
 * statistic counters, and formatting data for display.
 */

import { config } from './dashboard-config.js';

/**
 * Displays a notification on the screen.
 * This is a wrapper around the global notification function.
 * @param {string} message - The main message of the notification.
 * @param {string} title - The title of the notification.
 * @param {string} type - The type of notification ('info', 'success', 'warning', 'error').
 * @param {number} duration - How long the notification should be visible (in ms).
 */
function showNotification(message, title, type = 'info', duration = 5000) {
    if (window.showNotification) {
        window.showNotification(title, message, type, duration);
    } else {
        console.log(`${type.toUpperCase()}: ${title} - ${message}`);
    }
}

export const notifications = {
    info: (message, title, duration) => showNotification(message, title, 'info', duration),
    success: (message, title, duration) => showNotification(message, title, 'success', duration),
    warning: (message, title, duration) => showNotification(message, title, 'warning', duration),
    error: (message, title, duration) => showNotification(message, title, 'error', duration),
};

/**
 * Increments a dashboard counter by the specified amount.
 * @param {string} counterType - The key for the counter element in config.elements.
 * @param {number} amount - Amount to increment by (default: 1).
 */
export function incrementCounter(counterType, amount = 1) {
    const counterElement = config.elements[counterType];
    if (counterElement) {
        const currentValue = parseInt(counterElement.textContent, 10) || 0;
        counterElement.textContent = currentValue + amount;
    } else {
        console.warn(`Counter element for '${counterType}' not found.`);
    }
}

/**
 * Gets a color class based on the test status.
 * @param {string} status - The test status (e.g., 'passed', 'failed', 'running').
 * @returns {string} - A CSS color class.
 */
export function getStatusColor(status) {
    if (!status) return 'text-muted';
    const lowerStatus = status.toLowerCase();
    if (lowerStatus.includes('pass')) return 'text-success';
    if (lowerStatus.includes('fail')) return 'text-danger';
    if (lowerStatus.includes('run')) return 'text-primary';
    if (lowerStatus.includes('stop')) return 'text-warning';
    return 'text-muted';
}

/**
 * Formats a date string into a more readable local time format.
 * @param {string} dateString - The date string to format.
 * @returns {string} - The formatted date and time, or 'N/A'.
 */
export function formatTime(dateString) {
    if (!dateString || dateString.includes('0000-00-00')) {
        return 'N/A';
    }
    try {
        return new Date(dateString).toLocaleString();
    } catch (e) {
        console.error('Error formatting date:', e);
        return 'Invalid Date';
    }
}

/**
 * Shows the main loading overlay.
 * @param {string} [message='Loading...'] - The message to display.
 */
export function showLoading(message = 'Loading...') {
    const { loadingIndicator } = config.elements;
    if (loadingIndicator) {
        const loadingMessage = loadingIndicator.querySelector('.ms-loading-message');
        if (loadingMessage) {
            loadingMessage.textContent = message;
        }
        loadingIndicator.style.display = 'flex';
    }
}

/**
 * Hides the main loading overlay.
 */
export function hideLoading() {
    const { loadingIndicator } = config.elements;
    if (loadingIndicator) {
        loadingIndicator.style.display = 'none';
    }
}

/**
 * Formatter functions for consistently displaying data.
 */

/**
 * Formats a user's email into a more readable display name.
 * @param {string} email - The user's email address.
 * @returns {string} - The formatted user name (e.g., 'John D.').
 */
export function formatUserEmail(email) {
    if (!email) return 'Unknown';
    // Example: convert '<EMAIL>' to 'John D.'
    const [localPart] = email.split('@');
    const parts = localPart.split('.');
    if (parts.length > 1) {
        const firstName = parts[0].charAt(0).toUpperCase() + parts[0].slice(1);
        const lastNameInitial = parts[1].charAt(0).toUpperCase();
        return `${firstName} ${lastNameInitial}.`;
    }
    return localPart;
}
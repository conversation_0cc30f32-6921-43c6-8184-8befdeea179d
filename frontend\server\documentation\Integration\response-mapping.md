# Response Mapping

This document provides a detailed mapping of response structures between the database, API, and frontend layers of the SmartTest application.

## Table of Contents

1. [Database to API Response Mapping](#database-to-api-response-mapping)
2. [API to Frontend Response Mapping](#api-to-frontend-response-mapping)
3. [Response Structure Examples](#response-structure-examples)
4. [Error Response Mapping](#error-response-mapping)
5. [Response Transformation](#response-transformation)

## Database to API Response Mapping

### Test Cases

#### Database Response

```javascript
[
  {
    tc_id: 3180,
    uid: "test_user",
    status: "active",
    case_driver: "selenium",
    tp_id: 101,
    comments: "Test case comments",
    tickets: "JIRA-123",
    name: "Login Test Case"
  },
  // ...
]
```

#### API Response

```javascript
{
  success: true,
  data: [
    {
      tc_id: 3180,
      uid: "test_user",
      status: "active",
      case_driver: "selenium",
      tp_id: 101,
      comments: "Test case comments",
      tickets: "JIRA-123",
      name: "Login Test Case"
    },
    // ...
  ],
  message: "Test cases retrieved successfully"
}
```

### Test Suites

#### Database Response

```javascript
[
  {
    ts_id: 101,
    tcg_id: 201,
    uid: "test_user",
    status: "active",
    pj_id: 301,
    name: "Regression Test Suite",
    comments: "Test suite comments",
    tickets: "JIRA-456",
    tag: "regression"
  },
  // ...
]
```

#### API Response

```javascript
{
  success: true,
  data: [
    {
      ts_id: 101,
      tcg_id: 201,
      uid: "test_user",
      status: "active",
      pj_id: 301,
      name: "Regression Test Suite",
      comments: "Test suite comments",
      tickets: "JIRA-456",
      tag: "regression"
    },
    // ...
  ],
  message: "Test suites retrieved successfully"
}
```

### Test Suite Info

#### Database Response

```javascript
{
  ts_id: 101,
  tcg_id: 201,
  uid: "test_user",
  status: "active",
  pj_id: 301,
  name: "Regression Test Suite",
  comments: "Test suite comments",
  tickets: "JIRA-456",
  tag: "regression",
  testCaseCount: 5,
  testCases: [
    { tc_id: 3180 },
    { tc_id: 3181 },
    { tc_id: 3182 },
    { tc_id: 3183 },
    { tc_id: 3184 }
  ]
}
```

#### API Response

```javascript
{
  success: true,
  data: {
    ts_id: 101,
    tcg_id: 201,
    uid: "test_user",
    status: "active",
    pj_id: 301,
    name: "Regression Test Suite",
    comments: "Test suite comments",
    tickets: "JIRA-456",
    tag: "regression",
    testCaseCount: 5,
    testCases: [
      { tc_id: 3180 },
      { tc_id: 3181 },
      { tc_id: 3182 },
      { tc_id: 3183 },
      { tc_id: 3184 }
    ]
  },
  message: "Test suite info retrieved successfully"
}
```

### Active Tests

#### Database Response

```javascript
[
  {
    tsn_id: "12345",
    tc_id: "3180",
    initiator_user: "test_user",
    creation_time: "2023-01-01T12:00:00Z",
    is_current_user: true
  },
  // ...
]
```

#### API Response

```javascript
{
  success: true,
  data: [
    {
      tsn_id: "12345",
      tc_id: "3180",
      initiator_user: "test_user",
      creation_time: "2023-01-01T12:00:00Z",
      is_current_user: true
    },
    // ...
  ],
  message: "Active tests retrieved successfully"
}
```

### Recent Runs

#### Database Response

```javascript
[
  {
    tsn_id: "12345",
    start_time: "2023-01-01T12:00:00Z",
    end_time: "2023-01-01T12:05:00Z",
    type: "TestSuite",
    envir: "qa02",
    tc_id: null,
    ts_id: "101",
    pj_id: "301"
  },
  // ...
]
```

#### API Response

```javascript
{
  success: true,
  data: [
    {
      tsn_id: "12345",
      start_time: "2023-01-01T12:00:00Z",
      end_time: "2023-01-01T12:05:00Z",
      type: "TestSuite",
      envir: "qa02",
      tc_id: null,
      ts_id: "101",
      pj_id: "301"
    },
    // ...
  ],
  message: "Recent runs retrieved successfully"
}
```

### Test Results

#### Database Response

```javascript
{
  tsn_id: "12345",
  total_results: 15,
  pass_count: 12,
  fail_count: 3,
  start_time: "2023-01-01T12:00:00Z",
  end_time: "2023-01-01T12:05:00Z",
  duration: "5:00",
  results: [
    {
      tc_id: "3180",
      total_steps: 5,
      passed_steps: 4,
      failed_steps: 1,
      steps: [
        {
          seq_index: 1,
          outcome: "P",
          creation_time: "2023-01-01T12:00:01Z",
          output: "Step 1 output"
        },
        {
          seq_index: 2,
          outcome: "P",
          creation_time: "2023-01-01T12:00:02Z",
          output: "Step 2 output"
        },
        {
          seq_index: 3,
          outcome: "P",
          creation_time: "2023-01-01T12:00:03Z",
          output: "Step 3 output"
        },
        {
          seq_index: 4,
          outcome: "P",
          creation_time: "2023-01-01T12:00:04Z",
          output: "Step 4 output"
        },
        {
          seq_index: 5,
          outcome: "F",
          creation_time: "2023-01-01T12:00:05Z",
          output: "Step 5 output - Failed"
        }
      ]
    },
    // ...
  ]
}
```

#### API Response

```javascript
{
  success: true,
  data: {
    tsn_id: "12345",
    total_results: 15,
    pass_count: 12,
    fail_count: 3,
    start_time: "2023-01-01T12:00:00Z",
    end_time: "2023-01-01T12:05:00Z",
    duration: "5:00",
    results: [
      {
        tc_id: "3180",
        total_steps: 5,
        passed_steps: 4,
        failed_steps: 1,
        steps: [
          {
            seq_index: 1,
            outcome: "P",
            creation_time: "2023-01-01T12:00:01Z",
            output: "Step 1 output"
          },
          {
            seq_index: 2,
            outcome: "P",
            creation_time: "2023-01-01T12:00:02Z",
            output: "Step 2 output"
          },
          {
            seq_index: 3,
            outcome: "P",
            creation_time: "2023-01-01T12:00:03Z",
            output: "Step 3 output"
          },
          {
            seq_index: 4,
            outcome: "P",
            creation_time: "2023-01-01T12:00:04Z",
            output: "Step 4 output"
          },
          {
            seq_index: 5,
            outcome: "F",
            creation_time: "2023-01-01T12:00:05Z",
            output: "Step 5 output - Failed"
          }
        ]
      },
      // ...
    ]
  },
  message: "Test results retrieved successfully"
}
```

### Test Result Summary

#### Database Response

```javascript
{
  tsn_id: "12345",
  total_results: 15,
  pass_count: 12,
  fail_count: 3,
  start_time: "2023-01-01T12:00:00Z",
  end_time: "2023-01-01T12:05:00Z",
  duration: "5:00"
}
```

#### API Response

```javascript
{
  success: true,
  data: {
    tsn_id: "12345",
    total_results: 15,
    pass_count: 12,
    fail_count: 3,
    start_time: "2023-01-01T12:00:00Z",
    end_time: "2023-01-01T12:05:00Z",
    duration: "5:00"
  },
  message: "Test result summary retrieved successfully"
}
```

## API to Frontend Response Mapping

The frontend typically extracts the `data` field from the API response and uses it directly:

```javascript
/**
 * Get test results
 * @param {string} tsnId - Test session ID
 * @returns {Promise<Object>} - Test results
 */
async getTestResults(tsnId) {
  try {
    // Make GET request to test-results endpoint
    const response = await this.getRequest(`${this.endpoints.testResults}/${tsnId}`);
    
    if (response.success) {
      return response.data || {};
    } else {
      throw new Error(response.message || 'Failed to get test results');
    }
  } catch (error) {
    console.error(`Error getting test results for session ${tsnId}:`, error);
    throw error;
  }
}
```

The frontend components then use the data to render the UI:

```javascript
// Example of using test results in a React component
function TestResultsComponent({ tsnId }) {
  const [testResults, setTestResults] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  useEffect(() => {
    async function fetchTestResults() {
      try {
        const results = await apiService.getTestResults(tsnId);
        setTestResults(results);
        setLoading(false);
      } catch (error) {
        setError(error.message);
        setLoading(false);
      }
    }
    
    fetchTestResults();
  }, [tsnId]);
  
  if (loading) {
    return <div>Loading...</div>;
  }
  
  if (error) {
    return <div>Error: {error}</div>;
  }
  
  return (
    <div>
      <h2>Test Results</h2>
      <div>
        <p>Session ID: {testResults.tsn_id}</p>
        <p>Total Results: {testResults.total_results}</p>
        <p>Pass Count: {testResults.pass_count}</p>
        <p>Fail Count: {testResults.fail_count}</p>
        <p>Duration: {testResults.duration}</p>
      </div>
      <h3>Test Case Results</h3>
      <ul>
        {testResults.results.map(result => (
          <li key={result.tc_id}>
            <p>Test Case ID: {result.tc_id}</p>
            <p>Total Steps: {result.total_steps}</p>
            <p>Passed Steps: {result.passed_steps}</p>
            <p>Failed Steps: {result.failed_steps}</p>
            <h4>Steps</h4>
            <ul>
              {result.steps.map(step => (
                <li key={step.seq_index}>
                  <p>Sequence: {step.seq_index}</p>
                  <p>Outcome: {step.outcome === 'P' ? 'Pass' : 'Fail'}</p>
                  <p>Time: {new Date(step.creation_time).toLocaleString()}</p>
                  <pre>{step.output}</pre>
                </li>
              ))}
            </ul>
          </li>
        ))}
      </ul>
    </div>
  );
}
```

## Response Structure Examples

### Run Test Suite

#### API Request

```
POST /api/run-suite
Content-Type: application/json

{
  "ts_id": 101,
  "uid": "test_user",
  "password": "password",
  "environment": "qa02",
  "shell_host": "jps-qa10-app01"
}
```

#### API Response

```javascript
{
  "success": true,
  "tsn_id": "12345",
  "message": "Test suite 101 started successfully with 5 test cases"
}
```

### Get Active Tests

#### API Request

```
GET /local/active-tests
```

#### API Response

```javascript
{
  "success": true,
  "data": [
    {
      "tsn_id": "12345",
      "tc_id": "3180",
      "initiator_user": "test_user",
      "creation_time": "2023-01-01T12:00:00Z",
      "is_current_user": true
    },
    {
      "tsn_id": "12346",
      "tc_id": "3181",
      "initiator_user": "other_user",
      "creation_time": "2023-01-01T12:01:00Z",
      "is_current_user": false
    }
  ],
  "message": "Active tests retrieved successfully"
}
```

### Get Test Results

#### API Request

```
GET /local/test-results/12345
```

#### API Response

```javascript
{
  "success": true,
  "data": {
    "tsn_id": "12345",
    "total_results": 15,
    "pass_count": 12,
    "fail_count": 3,
    "start_time": "2023-01-01T12:00:00Z",
    "end_time": "2023-01-01T12:05:00Z",
    "duration": "5:00",
    "results": [
      {
        "tc_id": "3180",
        "total_steps": 5,
        "passed_steps": 4,
        "failed_steps": 1,
        "steps": [
          {
            "seq_index": 1,
            "outcome": "P",
            "creation_time": "2023-01-01T12:00:01Z",
            "output": "Step 1 output"
          },
          {
            "seq_index": 2,
            "outcome": "P",
            "creation_time": "2023-01-01T12:00:02Z",
            "output": "Step 2 output"
          },
          {
            "seq_index": 3,
            "outcome": "P",
            "creation_time": "2023-01-01T12:00:03Z",
            "output": "Step 3 output"
          },
          {
            "seq_index": 4,
            "outcome": "P",
            "creation_time": "2023-01-01T12:00:04Z",
            "output": "Step 4 output"
          },
          {
            "seq_index": 5,
            "outcome": "F",
            "creation_time": "2023-01-01T12:00:05Z",
            "output": "Step 5 output - Failed"
          }
        ]
      }
    ]
  },
  "message": "Test results retrieved successfully"
}
```

## Error Response Mapping

### Database Error

```javascript
throw new Error(`Error getting test results for session ID ${tsnId}: ${error.message}`);
```

### API Error Response

```javascript
{
  "success": false,
  "message": "Failed to retrieve test results",
  "error": "Error getting test results for session ID 12345: Database connection error"
}
```

### Frontend Error Handling

```javascript
try {
  const results = await apiService.getTestResults(tsnId);
  // Process results
} catch (error) {
  console.error(`Error getting test results: ${error.message}`);
  // Display error message to user
}
```

## Response Transformation

### Database to API Transformation

The database layer includes formatter utilities to transform raw database results into a consistent format:

```javascript
// utils/result-formatter.js

/**
 * Format test results
 * @param {Array} rows - Raw database rows
 * @returns {Object} - Formatted test results
 */
function formatTestResults(rows) {
  if (!rows || rows.length === 0) {
    return {
      tsn_id: null,
      total_results: 0,
      pass_count: 0,
      fail_count: 0,
      start_time: null,
      end_time: null,
      duration: '0:00',
      results: []
    };
  }
  
  // Group results by test case
  const resultsByTestCase = {};
  
  rows.forEach(row => {
    const tc_id = row.tc_id;
    
    if (!resultsByTestCase[tc_id]) {
      resultsByTestCase[tc_id] = {
        tc_id,
        total_steps: 0,
        passed_steps: 0,
        failed_steps: 0,
        steps: []
      };
    }
    
    resultsByTestCase[tc_id].total_steps++;
    
    if (row.outcome === 'P') {
      resultsByTestCase[tc_id].passed_steps++;
    } else if (row.outcome === 'F') {
      resultsByTestCase[tc_id].failed_steps++;
    }
    
    resultsByTestCase[tc_id].steps.push({
      seq_index: row.seq_index,
      outcome: row.outcome,
      creation_time: row.creation_time,
      output: row.txt
    });
  });
  
  // Calculate summary
  const tsn_id = rows[0].tsn_id;
  const total_results = rows.length;
  const pass_count = rows.filter(row => row.outcome === 'P').length;
  const fail_count = rows.filter(row => row.outcome === 'F').length;
  const start_time = new Date(Math.min(...rows.map(row => new Date(row.creation_time))));
  const end_time = new Date(Math.max(...rows.map(row => new Date(row.creation_time))));
  
  // Calculate duration
  const durationMs = end_time - start_time;
  const minutes = Math.floor(durationMs / 60000);
  const seconds = Math.floor((durationMs % 60000) / 1000);
  const duration = `${minutes}:${seconds.toString().padStart(2, '0')}`;
  
  return {
    tsn_id,
    total_results,
    pass_count,
    fail_count,
    start_time,
    end_time,
    duration,
    results: Object.values(resultsByTestCase)
  };
}
```

### API to Frontend Transformation

The API layer wraps the database response in a standard format:

```javascript
// API route handler
router.get('/test-results/:tsnId', validateCredentials, async (req, res) => {
  try {
    const { tsnId } = req.params;
    console.log(`GET /local/test-results/${tsnId}`);
    
    // Use the database module to fetch test results
    const testResults = await db.getTestResults(tsnId);
    
    // Return as JSON with success flag
    return res.json({
      success: true,
      data: testResults || {},
      message: 'Test results retrieved successfully'
    });
  } catch (error) {
    console.error('Error retrieving test results:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to retrieve test results',
      error: error.message
    });
  }
});
```

The frontend then extracts the `data` field from the API response:

```javascript
// Frontend API service
async getTestResults(tsnId) {
  try {
    // Make GET request to test-results endpoint
    const response = await this.getRequest(`${this.endpoints.testResults}/${tsnId}`);
    
    if (response.success) {
      return response.data || {};
    } else {
      throw new Error(response.message || 'Failed to get test results');
    }
  } catch (error) {
    console.error(`Error getting test results for session ${tsnId}:`, error);
    throw error;
  }
}
```

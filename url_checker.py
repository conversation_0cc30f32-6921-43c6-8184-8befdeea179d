import requests
import json
import os
import zipfile
import shutil
from pathlib import Path

URL = "https://app.codeguide.dev/api/urls/53c0750f-586c-4689-baaa-5418be490352?download=true"

def check_url_response():
    print(f"Checking URL: {URL}")
    try:
        # Make request with stream=True to avoid loading entire content into memory
        response = requests.get(URL, stream=True)
        response.raise_for_status()
        
        # Check content type to determine if it's JSON or blob
        content_type = response.headers.get('Content-Type', '')
        
        if 'application/json' in content_type:
            # Parse JSON response
            json_data = response.json()
            print("Received JSON response:")
            print(json.dumps(json_data, indent=2))
            
            # Check if URL is expired based on message
            if 'expired' in json.dumps(json_data).lower():
                print("URL is expired.")
                print("Please follow the instructions in the JSON response.")
                return False
            
            return json_data
        else:
            # Assume it's a blob/file
            print(f"Received blob response with content type: {content_type}")
            return response
    except requests.exceptions.RequestException as e:
        print(f"Error accessing URL: {e}")
        return None

def download_and_extract(response):
    # Create downloads directory if it doesn't exist
    os.makedirs('downloads', exist_ok=True)
    
    # Download the file
    file_path = 'downloads/documentation.zip'
    with open(file_path, 'wb') as f:
        for chunk in response.iter_content(chunk_size=8192):
            f.write(chunk)
    print(f"Downloaded file to {file_path}")
    
    # Create documentation directory if it doesn't exist
    os.makedirs('documentation', exist_ok=True)
    
    # Extract the zip file
    with zipfile.ZipFile(file_path, 'r') as zip_ref:
        zip_ref.extractall('documentation')
    print("Extracted files to documentation folder")
    
    # Move .mdc files to .cursor/rules/
    move_mdc_files()
    
    # Check for implementation plan
    check_implementation_plan()

def move_mdc_files():
    # Create .cursor/rules/ directory if it doesn't exist
    rules_dir = Path('.cursor/rules/')
    rules_dir.mkdir(parents=True, exist_ok=True)
    
    # Find all .mdc files in the documentation folder and move them
    for mdc_file in Path('documentation').glob('**/*.mdc'):
        target_path = rules_dir / mdc_file.name
        shutil.copy2(mdc_file, target_path)
        print(f"Moved {mdc_file} to {target_path}")

def check_implementation_plan():
    impl_plan_path = Path('documentation/implementation_plan.md')
    if impl_plan_path.exists():
        print("Found implementation_plan.md. Please follow instructions in this file.")
        with open(impl_plan_path, 'r') as f:
            print("\nImplementation Plan Preview:")
            print("-" * 40)
            preview = f.read(500)  # Show first 500 chars as preview
            print(preview + ("..." if len(preview) >= 500 else ""))
            print("-" * 40)
    else:
        print("No implementation_plan.md found. Please proceed with implementation based on other documents.")
        # List other documents for reference
        print("\nAvailable documentation:")
        for doc_file in Path('documentation').glob('**/*.*'):
            if doc_file.is_file():
                print(f"- {doc_file}")

def main():
    # Step 1: Check the URL response
    response = check_url_response()
    
    # Step 2: Handle based on response type
    if response is False:
        # URL is expired, process stopped
        return
    elif response is None:
        # Error occurred
        print("Failed to process the URL. Please check your connection and try again.")
        return
    elif isinstance(response, requests.Response):
        # Got a blob response, download and extract
        download_and_extract(response)
    else:
        # Got a JSON response (not expired)
        print("Received JSON data but it doesn't indicate expiry. Please review the content.")

if __name__ == "__main__":
    main() 
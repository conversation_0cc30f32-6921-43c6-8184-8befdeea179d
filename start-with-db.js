/**
 * Start script with database connection enabled
 */
const { spawn } = require('child_process');
const path = require('path');

// Set up environment variables for the child process
const env = { ...process.env };
env.DB_DISABLED = 'false'; // Explicitly enable database connections

console.log('Starting server with database connections ENABLED...');

// Determine which server script to run
const serverScript = path.resolve(__dirname, 'proxy-server.js');

// Spawn the server process with the modified environment
const serverProcess = spawn('node', [serverScript], {
  env,
  stdio: 'inherit' // Forward stdio to parent process
});

// Handle process events
serverProcess.on('error', (err) => {
  console.error('Failed to start server process:', err);
  process.exit(1);
});

serverProcess.on('exit', (code, signal) => {
  if (code !== 0) {
    console.log(`Server process exited with code ${code} and signal ${signal}`);
    process.exit(code);
  }
});

// Handle parent process termination
process.on('SIGINT', () => {
  console.log('Terminating server...');
  serverProcess.kill('SIGINT');
});

process.on('SIGTERM', () => {
  console.log('Terminating server...');
  serverProcess.kill('SIGTERM');
});

/**
 * Unified Mock API Responses for SmartTest Application
 * 
 * This file consolidates all mock API responses from:
 * - External API service responses
 * - Database query results
 * - Internal API endpoints
 * 
 * All mock data uses real response formats from actual APIs
 */

// External API Mock Responses
const externalApiResponses = {
  // Recent test runs from external API
  recentRuns: [
    {
      "tsn_id": "13782",
      "test_id": "3180",
      "type": "Test Case",
      "environment": "QA02",
      "status": "Unknown",
      "start_time": "2023-01-01 12:00:00",
      "end_time": "2023-01-01 12:05:00",
      "duration": "5:00",
      "total_cases": 10,
      "passed_cases": 10,
      "failed_cases": 0,
      "pass_rate": 100
    },
    {
      "tsn_id": "13783",
      "test_id": "3181",
      "type": "Test Suite",
      "environment": "QA01",
      "status": "Passed",
      "start_time": "2023-01-01 13:00:00",
      "end_time": "2023-01-01 13:15:00",
      "duration": "15:00",
      "total_cases": 25,
      "passed_cases": 23,
      "failed_cases": 2,
      "pass_rate": 92
    }
  ],

  // Test case details from external API
  testDetails: {
    "test_id": "3180",
    "test_name": "User Authentication Flow",
    "description": "Tests user login and authentication process",
    "environment": "QA02",
    "status": "Passed",
    "start_time": "2023-01-01 12:00:00",
    "end_time": "2023-01-01 12:05:00",
    "duration": "5:00",
    "steps": [
      {
        "step_id": 1,
        "description": "Navigate to login page",
        "status": "Passed",
        "duration": "1:00"
      },
      {
        "step_id": 2,
        "description": "Enter credentials",
        "status": "Passed",
        "duration": "2:00"
      },
      {
        "step_id": 3,
        "description": "Verify dashboard access",
        "status": "Passed",
        "duration": "2:00"
      }
    ]
  },

  // Test summary from external API
  testSummary: {
    "total_tests": 150,
    "passed_tests": 142,
    "failed_tests": 8,
    "pass_rate": 94.67,
    "last_updated": "2023-01-01 15:30:00",
    "environments": {
      "QA01": {
        "total": 75,
        "passed": 71,
        "failed": 4,
        "pass_rate": 94.67
      },
      "QA02": {
        "total": 75,
        "passed": 71,
        "failed": 4,
        "pass_rate": 94.67
      }
    }
  },

  // Authentication response
  authResponse: {
    "success": true,
    "token": "mock-jwt-token-12345",
    "expires_in": 3600,
    "user": {
      "id": "user123",
      "username": "testuser",
      "role": "tester"
    }
  },

  // Error responses
  errorResponse: {
    "success": false,
    "error": {
      "code": "API_ERROR",
      "message": "Internal server error",
      "details": "Connection timeout"
    },
    "timestamp": "2023-01-01 15:30:00"
  }
};

// Database Mock Responses
const databaseResponses = {
  // Test runs from database
  testRuns: [
    {
      "id": 1001,
      "session_id": "SESSION_001",
      "test_name": "Regression Suite",
      "environment": "QA01",
      "status": "COMPLETED",
      "start_time": "2023-01-01T12:00:00Z",
      "end_time": "2023-01-01T12:30:00Z",
      "total_tests": 50,
      "passed_tests": 48,
      "failed_tests": 2,
      "created_at": "2023-01-01T12:00:00Z",
      "updated_at": "2023-01-01T12:30:00Z"
    },
    {
      "id": 1002,
      "session_id": "SESSION_002",
      "test_name": "Smoke Tests",
      "environment": "QA02",
      "status": "RUNNING",
      "start_time": "2023-01-01T13:00:00Z",
      "end_time": null,
      "total_tests": 20,
      "passed_tests": 15,
      "failed_tests": 0,
      "created_at": "2023-01-01T13:00:00Z",
      "updated_at": "2023-01-01T13:15:00Z"
    }
  ],

  // Test cases from database
  testCases: [
    {
      "id": 2001,
      "test_run_id": 1001,
      "case_name": "Login Test",
      "status": "PASSED",
      "duration": 5000,
      "error_message": null,
      "stack_trace": null,
      "created_at": "2023-01-01T12:05:00Z",
      "updated_at": "2023-01-01T12:10:00Z"
    },
    {
      "id": 2002,
      "test_run_id": 1001,
      "case_name": "Navigation Test",
      "status": "FAILED",
      "duration": 8000,
      "error_message": "Element not found",
      "stack_trace": "Error: Element not found\n    at test.js:45:12",
      "created_at": "2023-01-01T12:10:00Z",
      "updated_at": "2023-01-01T12:18:00Z"
    }
  ],

  // Database connection test
  connectionTest: {
    "connected": true,
    "database": "smarttest_qa",
    "host": "qa-db-server",
    "port": 3306,
    "timestamp": "2023-01-01T15:30:00Z"
  }
};

// Internal API Mock Responses
const internalApiResponses = {
  // Unified API service responses
  unifiedApiSuccess: {
    "success": true,
    "data": [],
    "message": "Operation completed successfully",
    "timestamp": "2023-01-01T15:30:00Z",
    "version": "1.0.0"
  },

  // Configuration responses
  configResponse: {
    "environments": ["QA01", "QA02", "STAGING"],
    "apiEndpoints": {
      "external": "https://api.external.com",
      "database": "mysql://localhost:3306/smarttest"
    },
    "features": {
      "realTimeUpdates": true,
      "advancedFiltering": true,
      "exportData": true
    }
  },

  // Health check response
  healthCheck: {
    "status": "healthy",
    "services": {
      "database": "connected",
      "externalApi": "connected",
      "cache": "connected"
    },
    "uptime": 86400,
    "timestamp": "2023-01-01T15:30:00Z"
  }
};

// HTTP Response Wrappers
const createHttpResponse = (data, status = 200, headers = {}) => ({
  data,
  status,
  statusText: status === 200 ? 'OK' : 'Error',
  headers: {
    'content-type': 'application/json',
    ...headers
  },
  config: {}
});

const createErrorResponse = (message, status = 500) => ({
  response: {
    data: {
      error: message,
      timestamp: new Date().toISOString()
    },
    status,
    statusText: 'Error'
  }
});

// Export all mock responses
module.exports = {
  externalApi: externalApiResponses,
  database: databaseResponses,
  internalApi: internalApiResponses,
  
  // Helper functions
  createHttpResponse,
  createErrorResponse,
  
  // Quick access to common responses
  success: (data) => createHttpResponse(data),
  error: (message, status) => createErrorResponse(message, status),
  
  // Response generators
  generateTestRun: (overrides = {}) => ({
    ...databaseResponses.testRuns[0],
    ...overrides,
    id: Math.floor(Math.random() * 10000),
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }),
  
  generateTestCase: (overrides = {}) => ({
    ...databaseResponses.testCases[0],
    ...overrides,
    id: Math.floor(Math.random() * 10000),
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  })
};

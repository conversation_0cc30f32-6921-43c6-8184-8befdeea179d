/**
 * dashboard-details-modal.js
 *
 * This module manages the test details modal, which displays comprehensive
 * information about a single test run.
 */

import { config } from './dashboard-config.js';
import * as api from './dashboard-api.js';
import { notifications } from './dashboard-ui.js';
import { formatters } from './formatters.js';

/**
 * Fetches test details and displays them in the modal.
 * @param {number} tsnId - The Test Suite Number ID of the run to display.
 */
export async function viewTestDetails(tsnId) {
    const modal = config.elements.detailsModal;
    const modalContent = config.elements.detailsModalContent;
    if (!modal || !modalContent) return;

    // Show loading state
    modalContent.innerHTML = '<div class="ms-Spinner ms-Spinner--large"></div>';
    modal.classList.add('is-visible');

    try {
        const details = await api.getTestDetails(tsnId);
        renderModalContent(details);
    } catch (error) {
        console.error(`Error fetching details for TSN ID ${tsnId}:`, error);
        notifications.error('Failed to load test details.', 'API Error');
        modalContent.innerHTML = '<p class="ms-fontColor-error">Could not load details for this test run.</p>';
    }
}

/**
 * Renders the fetched test details into the modal's content area.
 * @param {object} details - The detailed test run data from the API.
 */
function renderModalContent(details) {
    const modalContent = config.elements.detailsModalContent;
    if (!details) {
        modalContent.innerHTML = '<p>No details available for this test run.</p>';
        return;
    }

    const {
        suite_name,
        status,
        user,
        start_time,
        end_time,
        passed_cases,
        failed_cases,
        total_cases,
        test_cases = []
    } = details;

    const duration = formatters.formatDuration(start_time, end_time);

    modalContent.innerHTML = `
        <div class="ms-modal-header">
            <h2>${suite_name || 'Test Details'}</h2>
            <button class="ms-modal-close-btn">&times;</button>
        </div>
        <div class="ms-modal-body">
            <div class="ms-grid">
                <div class="ms-grid-row">
                    <div class="ms-grid-col ms-sm6"><strong>Status:</strong> <span class="${formatters.getStatusClass(status)}">${status}</span></div>
                    <div class="ms-grid-col ms-sm6"><strong>User:</strong> ${formatters.formatUserEmail(user)}</div>
                </div>
                <div class="ms-grid-row">
                    <div class="ms-grid-col ms-sm6"><strong>Started:</strong> ${formatters.formatDateTime(start_time)}</div>
                    <div class="ms-grid-col ms-sm6"><strong>Ended:</strong> ${formatters.formatDateTime(end_time)}</div>
                </div>
                <div class="ms-grid-row">
                    <div class="ms-grid-col ms-sm6"><strong>Duration:</strong> ${duration}</div>
                    <div class="ms-grid-col ms-sm6"><strong>Result:</strong> ${passed_cases} Passed, ${failed_cases} Failed (${total_cases} Total)</div>
                </div>
            </div>
            <hr>
            <h3>Test Cases</h3>
            <div class="ms-table-container">
                <table class="ms-Table">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Status</th>
                            <th>Duration (s)</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${test_cases.map(createTestCaseRow).join('')}
                    </tbody>
                </table>
            </div>
        </div>
    `;

    // Add event listener to the close button
    modalContent.querySelector('.ms-modal-close-btn').addEventListener('click', closeModal);
}

/**
 * Creates the HTML for a single test case row in the details table.
 * @param {object} testCase - The test case data.
 * @returns {string} The HTML string for the table row.
 */
function createTestCaseRow(testCase) {
    const { case_name, status, duration_seconds } = testCase;
    return `
        <tr>
            <td>${case_name}</td>
            <td><span class="${formatters.getStatusClass(status)}">${status}</span></td>
            <td>${duration_seconds ?? 'N/A'}</td>
        </tr>
    `;
}

/**
 * Initializes the modal by setting up the close handler on the overlay.
 */
export function initializeDetailsModal() {
    const modal = config.elements.detailsModal;
    modal?.addEventListener('click', (event) => {
        // Close modal if the overlay (background) is clicked
        if (event.target === modal) {
            closeModal();
        }
    });
}

/**
 * Closes the details modal.
 */
function closeModal() {
    config.elements.detailsModal?.classList.remove('is-visible');
}

/**
 * Test Details Routes
 */
const express = require('express');
const router = express.Router();
const db = require('../database');
const { getTestStepsWithFailureContext } = require('../database/queries/test-sessions');

/**
 * Get test details by tsn_id from the database
 * This is the standardized central route for retrieving test details
 */
router.get('/test-details/:tsn_id', async (req, res) => {
  console.log('GET /local/test-details/:tsn_id');
  try {
    const { tsn_id } = req.params;
    
    if (!tsn_id) {
      return res.status(400).json({
        success: false,
        message: 'Missing required parameter: tsn_id'
      });
    }
    
    console.log(`Fetching test details for tsn_id: ${tsn_id}`);
    
    // Check if database is disabled
    if (process.env.DB_DISABLED === 'true') {
      console.log('Database is disabled, returning empty test details');
      return res.json({
        success: false,
        message: 'Test details not available - database disabled'
      });
    }
    
    try {
      // Get test details from the database using getTestSessionDetails
      // This is the comprehensive function that retrieves all necessary data
      const test = await db.getTestSessionDetails(tsn_id);
      
      if (!test) {
        return res.status(404).json({
          success: false,
          message: `Test details not found for tsn_id: ${tsn_id}`
        });
      }
      
      console.log(`Retrieved test details for tsn_id: ${tsn_id}`);
      console.log(`Test case count: ${test.test_cases ? test.test_cases.length : 0}`);
      
      if (test.test_cases && test.test_cases.length > 0) {
        console.log(`First test case: ${JSON.stringify(test.test_cases[0], null, 2)}`);
      } else {
        console.log('No test cases found in database result');
      }
      
      // Return the data in a consistent format that matches both response patterns
      // Include both 'test' and 'data' properties for backward compatibility
      return res.json({
        success: true,
        test, // For the original format expected by some frontend code
        data: test, // For compatibility with the format from the removed /test-cases/test-details route
        message: 'Test details retrieved successfully'
      });
    } catch (dbError) {
      console.error('Database error:', dbError);
      // Provide a more helpful error message
      throw new Error(`Database access failed: ${dbError.message}`);
    }
  } catch (error) {
    console.error('Error in /local/test-details route:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to retrieve test details',
      error: error.message
    });
  }
});

/**
 * Get test steps with failure context
 * Returns the last 10 steps before a failure and the failed steps
 */
router.get('/test-steps/:tsn_id', async (req, res) => {
  console.log('GET /local/test-steps/:tsn_id');
  try {
    const { tsn_id } = req.params;
    
    if (!tsn_id) {
      return res.status(400).json({
        success: false,
        message: 'Missing required parameter: tsn_id'
      });
    }
    
    console.log(`Fetching test steps with failure context for tsn_id: ${tsn_id}`);
    
    // Check if database is disabled
    if (process.env.DB_DISABLED === 'true') {
      console.log('Database is disabled, cannot fetch test steps');
      return res.json({
        success: false,
        message: 'Test steps not available - database disabled'
      });
    }
    
    try {
      // Get the database connection
      const connection = await db.getConnection();
      
      // Get test steps with failure context
      const steps = await getTestStepsWithFailureContext(connection, tsn_id);
      
      // Release the connection back to the pool
      connection.release();
      
      console.log(`Retrieved ${steps ? steps.length : 0} test steps for tsn_id: ${tsn_id}`);
      
      return res.json({
        success: true,
        data: steps || [],
        message: 'Test steps retrieved successfully'
      });
      
    } catch (dbError) {
      console.error('Database error:', dbError);
      throw new Error(`Database access failed: ${dbError.message}`);
    }
    
  } catch (error) {
    console.error('Error in /local/test-steps route:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to retrieve test steps',
      error: error.message
    });
  }
});

module.exports = router;

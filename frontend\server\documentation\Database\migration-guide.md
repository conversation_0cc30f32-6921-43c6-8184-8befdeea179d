# Migration Guide

## Overview

This guide provides instructions for migrating from the old database layer to the new refactored database layer. The migration process is designed to be as smooth as possible, with minimal changes required to existing code.

## Migration Steps

### Step 1: Update Imports

Replace imports of the old database modules with the new one:

```javascript
// Old
const db = require('../db-manager');
// or
const db = require('../db');
// or
const db = require('../db-direct');

// New
const db = require('../database');
```

### Step 2: Update Initialization

Update the initialization code:

```javascript
// Old
await db.init(environment);
// or
await db.init({ forceDirect: true });
// or
await db.init({ forceTunnel: true });

// New
await db.init(environment);
// or
await db.init(environment, { forceDirect: true });
// or
await db.init(environment, { forceTunnel: true });
```

### Step 3: Update Query Calls

Update query calls to use the new API:

```javascript
// Old
const testCases = await db.getTestCases({ suite_id: 123 });

// New
const testCases = await db.getTestCases({ suiteId: 123 });
```

### Step 4: Update Error Handling

Update error handling to use the new error handling approach:

```javascript
// Old
try {
  const testCases = await db.getTestCases({ suite_id: 123 });
  console.log(testCases);
} catch (error) {
  console.error('Error getting test cases:', error);
}

// New
try {
  const testCases = await db.getTestCases({ suiteId: 123 });
  console.log(testCases);
} catch (error) {
  console.error('Error getting test cases:', error);
}
```

### Step 5: Update Connection Management

Update connection management to use the new approach:

```javascript
// Old
await db.close();

// New
await db.close();
```

### Step 6: Update Custom Queries

If you have custom queries that use the old database layer's `query` method, update them to use the new query builder:

```javascript
// Old
const sql = `SELECT * FROM test_case WHERE status = ? LIMIT ?`;
const params = ['active', 10];
const results = await db.query(sql, params);

// New
const queryBuilder = new db.QueryBuilder();
queryBuilder.select('test_case');
queryBuilder.where('status', '=', 'active');
queryBuilder.limit(10);

const { sql, params } = queryBuilder.build();
const results = await db.query(sql, params);
```

Alternatively, you can continue to use the old approach with the new database layer:

```javascript
// New (using old approach)
const sql = `SELECT * FROM test_case WHERE status = ? LIMIT ?`;
const params = ['active', 10];
const results = await db.query(sql, params);
```

### Step 7: Update Connection Information

If you use the old database layer's `getConnectionInfo` method, update it to use the new approach:

```javascript
// Old
const info = db.getConnectionInfo();
console.log(`Connected to ${info.environment} using ${info.connectionMethod} method`);

// New
const info = db.getConnectionInfo();
console.log(`Connected to ${info.environment} environment`);
```

## API Mapping

The following table maps the old API to the new API:

| Old API | New API | Notes |
|---------|---------|-------|
| `db.init(environment)` | `db.init(environment)` | Same signature |
| `db.init({ forceDirect: true })` | `db.init(null, { forceDirect: true })` | Options moved to second parameter |
| `db.init({ forceTunnel: true })` | `db.init(null, { forceTunnel: true })` | Options moved to second parameter |
| `db.query(sql, params)` | `db.query(sql, params)` | Same signature |
| `db.getTestCases(filters)` | `db.getTestCases(filters)` | Parameter names changed (see below) |
| `db.getTestSuites(filters)` | `db.getTestSuites(filters)` | Parameter names changed and structure updated (see below) |
| `db.getActiveTests(filters)` | `db.getActiveTests(filters)` | Parameter names changed (see below) |
| `db.getTestResults(tsnId)` | `db.getTestResults(tsnId)` | Same signature |
| `db.close()` | `db.close()` | Same signature |
| `db.getConnectionInfo()` | `db.getConnectionInfo()` | Return value structure changed |
| `db.setEnvironment(environment)` | `db.setEnvironment(environment)` | Same signature |
| `db.detectEnvironment()` | `db.detectEnvironment()` | Same signature |

## Parameter Mapping

The following table maps the old parameter names to the new parameter names:

| Old Parameter | New Parameter | Notes |
|---------------|---------------|-------|
| `suite_id` | `suiteId` | Camel case instead of snake case |
| `project_id` | Removed | The `projectId` parameter has been removed from `getTestSuites` as it's not applicable to the `test_suite` table |
| `user_id` | `userId` | Camel case instead of snake case |
| `tsn_id` | `tsnId` | Camel case instead of snake case |
| `tc_id` | `tcId` | Camel case instead of snake case |
| `ts_id` | `tsId` | Camel case instead of snake case |

## Return Value Mapping

The following table maps the old return value structures to the new return value structures:

| Method | Old Return Value | New Return Value | Notes |
|--------|------------------|------------------|-------|
| `getTestCases` | Array of test cases | Array of test cases | Same structure |
| `getTestSuites` | Array of test suites | Array of test suites | Structure changed to match `test_suite` table (removed `tcg_id`, `pj_id`, `tickets`, `tag`; added `tp_id`) |
| `getActiveTests` | Array of active tests | Array of active tests | Same structure |
| `getTestResults` | Test results object | Test results object | Same structure |
| `getConnectionInfo` | Connection info object | Connection info object | Structure changed (see below) |

### Connection Info Object

The structure of the connection info object returned by `getConnectionInfo` has changed:

```javascript
// Old
{
  initialized: true,
  environment: 'qa02',
  connectionMethod: 'direct'
}

// New
{
  initialized: true,
  environment: 'qa02',
  config: {
    // Environment configuration
    PORT: 3000,
    BASE_URL: 'http://mprts-qa02.lab.wagerworks.com:5080/AutoRun/',
    DB_HOST: 'mprts-qa02.lab.wagerworks.com',
    // ...
  }
}
```

## Examples

### Example 1: Basic Usage

```javascript
// Old
const db = require('../db-manager');

async function main() {
  try {
    await db.init('qa02');

    const testCases = await db.getTestCases({ suite_id: 123 });
    console.log(testCases);

    await db.close();
  } catch (error) {
    console.error('Error:', error);
  }
}

// New
const db = require('../database');

async function main() {
  try {
    await db.init('qa02');

    const testCases = await db.getTestCases({ suiteId: 123 });
    console.log(testCases);

    await db.close();
  } catch (error) {
    console.error('Error:', error);
  }
}
```

### Example 2: Custom Queries

```javascript
// Old
const db = require('../db-manager');

async function main() {
  try {
    await db.init('qa02');

    const sql = `
      SELECT tc.tc_id, tc.name, tc.status
      FROM test_case tc
      JOIN test_case_group tcg ON tc.tc_id = tcg.tc_id
      WHERE tcg.ts_id = ?
      ORDER BY tc.tc_id DESC
      LIMIT ?
    `;
    const params = [123, 10];

    const results = await db.query(sql, params);
    console.log(results);

    await db.close();
  } catch (error) {
    console.error('Error:', error);
  }
}

// New (using query builder)
const db = require('../database');

async function main() {
  try {
    await db.init('qa02');

    const queryBuilder = new db.QueryBuilder();
    queryBuilder.select('test_case tc', ['tc.tc_id', 'tc.name', 'tc.status']);
    queryBuilder.join('test_case_group tcg', 'tc.tc_id = tcg.tc_id');
    queryBuilder.where('tcg.ts_id', '=', 123);
    queryBuilder.orderBy('tc.tc_id', 'DESC');
    queryBuilder.limit(10);

    const { sql, params } = queryBuilder.build();
    const results = await db.query(sql, params);
    console.log(results);

    await db.close();
  } catch (error) {
    console.error('Error:', error);
  }
}

// New (using old approach)
const db = require('../database');

async function main() {
  try {
    await db.init('qa02');

    const sql = `
      SELECT tc.tc_id, tc.name, tc.status
      FROM test_case tc
      JOIN test_case_group tcg ON tc.tc_id = tcg.tc_id
      WHERE tcg.ts_id = ?
      ORDER BY tc.tc_id DESC
      LIMIT ?
    `;
    const params = [123, 10];

    const results = await db.query(sql, params);
    console.log(results);

    await db.close();
  } catch (error) {
    console.error('Error:', error);
  }
}
```

### Example 3: Connection Options

```javascript
// Old
const db = require('../db-manager');

async function main() {
  try {
    // Force direct connection
    await db.init({ forceDirect: true });

    // Force tunnel connection
    await db.init({ forceTunnel: true });

    // Get connection info
    const info = db.getConnectionInfo();
    console.log(`Connected to ${info.environment} using ${info.connectionMethod} method`);

    await db.close();
  } catch (error) {
    console.error('Error:', error);
  }
}

// New
const db = require('../database');

async function main() {
  try {
    // Force direct connection
    await db.init(null, { forceDirect: true });

    // Force tunnel connection
    await db.init(null, { forceTunnel: true });

    // Get connection info
    const info = db.getConnectionInfo();
    console.log(`Connected to ${info.environment} environment`);

    await db.close();
  } catch (error) {
    console.error('Error:', error);
  }
}
```

## Database Structure Changes

### Test Suite Structure

The implementation has been updated to correctly use the `test_suite` table instead of `test_case_group` for test suite metadata:

```javascript
// Old (incorrect) implementation
queryBuilder.select('test_case_group tcg', [
  'tcg.tcg_id',
  'tcg.ts_id',
  'tcg.uid',
  'tcg.status',
  'tcg.pj_id',
  'tcg.name',
  'tcg.comments',
  'tcg.tickets',
  'tcg.tag'
]);

// New (correct) implementation
queryBuilder.select('test_suite ts', [
  'ts.ts_id',
  'ts.uid',
  'ts.status',
  'ts.tp_id',
  'ts.comments',
  'ts.name'
]);
```

The `test_suite` table has the following verified columns:
- `ts_id`: Test Suite ID
- `status`: Status flag (e.g., 'A')
- `uid`: User ID associated with the suite
- `comments`: Comment string
- `tp_id`: Related parameter/plan ID
- `name`: Test Suite Name

The `test_case_group` table is used only for mapping test cases to test suites:
- `ts_id`: Test Suite ID
- `tc_id`: Test Case ID
- `seq_index`: Sequence of the test case within the suite

## Troubleshooting

### Common Issues

1. **Module Not Found**: If you get a "Module not found" error, make sure you've updated all imports to use the new database module.
2. **Parameter Names**: If you get unexpected results, check that you're using the new parameter names (camelCase instead of snake_case).
3. **Connection Options**: If you're having trouble with connection options, make sure you're passing them as the second parameter to `init`.
4. **Return Value Structure**: If you're having trouble with return values, check that you're using the new return value structure.

### Debugging

To enable debug logging, set the `DB_DEBUG` environment variable to `true`:

```
DB_DEBUG=true
```

This will log detailed information about the database operations, which can be helpful for troubleshooting.

## Conclusion

Migrating from the old database layer to the new refactored database layer should be a straightforward process. The new database layer maintains compatibility with the old API while providing new features and improvements. If you encounter any issues during migration, refer to the troubleshooting section or consult the detailed documentation for the new database layer.

/**
 * Tests for database module with SSH tunneling
 */

// Import modules
const path = require('path');
const fs = require('fs');

// Mock dependencies
jest.mock('ssh2', () => {
  return {
    Client: jest.fn().mockImplementation(() => ({
      on: jest.fn().mockImplementation(function(event, callback) {
        if (event === 'ready') {
          // Store the ready callback to call it later
          this.readyCallback = callback;
          return this;
        }
        if (event === 'error') {
          this.errorCallback = callback;
          return this;
        }
        if (event === 'close') {
          this.closeCallback = callback;
          return this;
        }
        // Support for additional events as in real implementation
        if (event === 'end') {
          this.endCallback = callback;
          return this;
        }
        if (event === 'timeout') {
          this.timeoutCallback = callback;
          return this;
        }
        return this;
      }),
      connect: jest.fn().mockImplementation(function(config) {
        // Validate SSH config similar to real implementation
        if (!config.host || !config.username) {
          if (this.errorCallback) {
            setTimeout(() => this.errorCallback(new Error('Invalid SSH configuration')), 10);
            return this;
          }
        }

        // Support for connection timeout simulation
        if (config.readyTimeout && config.host === 'timeout.example.com') {
          if (this.timeoutCallback) {
            setTimeout(() => this.timeoutCallback(), config.readyTimeout);
          }
          return this;
        }

        // Call the ready callback to simulate successful connection after a realistic delay
        if (this.readyCallback) setTimeout(() => this.readyCallback(), 50);
        return this;
      }),
      forwardOut: jest.fn().mockImplementation(function(srcAddr, srcPort, dstAddr, dstPort, callback) {
        // More realistic port forwarding behavior - verify parameters
        if (!dstAddr || !dstPort) {
          callback(new Error('Invalid destination address or port'));
          return this;
        }

        // Simulate successful port forwarding
        callback(null, {
          localPort: srcPort || 3307,
          on: jest.fn(),
          pipe: jest.fn().mockReturnThis(),
          end: jest.fn()
        });
        return this;
      }),
      end: jest.fn().mockImplementation(function() {
        // Simulate connection end with proper callback
        if (this.closeCallback) {
          setTimeout(() => this.closeCallback(), 10);
        }
        return this;
      }),
      triggerError: function(error) {
        if (this.errorCallback) this.errorCallback(error);
      },
      triggerClose: function() {
        if (this.closeCallback) this.closeCallback();
      }
    }))
  };
});

jest.mock('mysql2/promise', () => {
  return {
    createPool: jest.fn().mockImplementation((config) => {
      // Validate MySQL config similar to real implementation
      if (!config.host || config.port === undefined) {
        throw new Error('Invalid MySQL configuration');
      }

      return {
        execute: jest.fn().mockImplementation(async (sql, params = []) => {
          // Simulate database behavior more accurately
          if (sql.includes('SELECT 1 AS connection_test')) {
            return [[{ connection_test: 1 }], []];
          } else if (sql.includes('INSERT INTO')) {
            return [[{ insertId: 1, affectedRows: 1 }], []];
          } else if (sql.includes('UPDATE')) {
            return [[{ affectedRows: 1 }], []];
          } else if (sql.includes('SELECT * FROM test')) {
            const result = params.length > 0
              ? [{ id: params[0], name: 'Test' }]
              : [{ id: 1, name: 'Test' }];
            return [result, []];
          }
          return [[], []];
        }),
        end: jest.fn().mockResolvedValue(true)
      };
    })
  };
});

jest.mock('fs', () => ({
  ...jest.requireActual('fs'),
  readFileSync: jest.fn().mockImplementation((filePath) => {
    // More realistic key handling based on path
    if (filePath.includes('id_rsa')) {
      return '-----BEGIN RSA PRIVATE KEY-----\nMOCK_RSA_KEY_CONTENT\n-----END RSA PRIVATE KEY-----';
    } else if (filePath.includes('id_ed25519')) {
      return '-----BEGIN OPENSSH PRIVATE KEY-----\nMOCK_ED25519_KEY_CONTENT\n-----END OPENSSH PRIVATE KEY-----';
    }
    return 'mock-private-key-content';
  }),
  existsSync: jest.fn().mockImplementation((filePath) => {
    // Handle common SSH key paths
    if (filePath.includes('id_rsa') ||
        filePath.includes('id_ed25519') ||
        filePath.includes('.pem')) {
      return true;
    }
    return false;
  })
}));

// Reset environment between tests
const originalEnv = process.env;

describe('Database Module', () => {
  let db;
  let ssh2Client;
  let mysqlClient;

  beforeEach(() => {
    // Reset modules
    jest.resetModules();

    // Prepare clean environment for each test with realistic defaults
    process.env = {
      ...originalEnv,
      DB_HOST: 'mprts-qa02.lab.wagerworks.com', // Actual DB host from docs
      DB_USER: 'testuser',
      DB_PASSWORD: 'testpass',
      DB_NAME: 'rgs_test', // Actual DB name from docs
      DB_PORT: '3306',
      SSH_ENABLED: 'true',
      SSH_HOST: 'mprts-qa02.lab.wagerworks.com', // Same as DB host per docs
      SSH_USER: 'sshuser',
      SSH_PORT: '22',
      SSH_KEY_PATH: '~/.ssh/id_rsa', // Realistic path
      SSH_LOCAL_HOST: '127.0.0.1',
      SSH_LOCAL_PORT: '3307'
    };

    // Import the module under test
    db = require('../database');

    // Get mock constructors
    ssh2Client = require('ssh2').Client;
    mysqlClient = require('mysql2/promise');
  });

  afterEach(() => {
    // Restore environment
    process.env = originalEnv;

    // Clear all mocks
    jest.clearAllMocks();
  });

  test('should establish SSH tunnel when SSH_ENABLED=true', async () => {
    // Call to setup SSH tunnel
    await db.init();

    // Verify SSH Client was constructed
    expect(ssh2Client).toHaveBeenCalledTimes(1);

    // Verify connect was called with correct config
    const connectMock = ssh2Client.mock.results[0].value.connect;
    expect(connectMock).toHaveBeenCalledTimes(1);
    expect(connectMock.mock.calls[0][0]).toMatchObject({
      host: 'mprts-qa02.lab.wagerworks.com',
      port: 22,
      username: 'sshuser',
      readyTimeout: 30000,
      // Verify algorithm options are included as in the real implementation
      algorithms: expect.objectContaining({
        serverHostKey: expect.arrayContaining(['ssh-rsa']),
        kex: expect.arrayContaining(['diffie-hellman-group14-sha1']),
        cipher: expect.arrayContaining(['aes128-ctr']),
        hmac: expect.arrayContaining(['hmac-sha1'])
      })
    });

    // Verify forwardOut was called with correct parameters
    const sshInstance = ssh2Client.mock.results[0].value;
    expect(sshInstance.forwardOut).toHaveBeenCalledWith(
      '127.0.0.1',  // SSH_LOCAL_HOST
      3307,         // SSH_LOCAL_PORT
      'mprts-qa02.lab.wagerworks.com',  // DB_HOST
      3306,         // DB_PORT
      expect.any(Function)
    );

    // Verify MySQL connection pool was created with tunnel settings
    expect(mysqlClient.createPool).toHaveBeenCalledWith({
      host: '127.0.0.1',
      port: 3307,
      user: 'testuser',
      password: 'testpass',
      database: 'rgs_test',
      waitForConnections: true,
      connectionLimit: 10,
      queueLimit: 0
    });
  });

  test('should connect directly to DB when SSH_ENABLED=false', async () => {
    // Change environment to disable SSH
    process.env.SSH_ENABLED = 'false';

    // Reset mocks completely
    jest.clearAllMocks();

    // Now mysql.createPool should be called when we run init
    await db.init();

    // SSH Client should not be constructed
    expect(ssh2Client).not.toHaveBeenCalled();

    // MySQL connection pool should have been called with direct connection parameters
    expect(mysqlClient.createPool).toHaveBeenCalledWith({
      host: 'mprts-qa02.lab.wagerworks.com',
      port: 3306,
      user: 'testuser',
      password: 'testpass',
      database: 'rgs_test',
      waitForConnections: true,
      connectionLimit: 10,
      queueLimit: 0
    });
  });

  test('should handle SSH connection errors', async () => {
    // Need to reset modules to set up our special mock
    jest.resetModules();

    // Create a custom error for testing
    const mockError = new Error('Mock SSH connection error');

    // Get a fresh SSH client mock
    ssh2Client = require('ssh2').Client;

    // Instead of re-mocking SSH2, manipulate the existing mock
    const sshClientInstance = {
      on: jest.fn().mockImplementation(function(event, callback) {
        if (event === 'error') {
          // Store the error callback to call later
          setTimeout(() => callback(mockError), 10);
        }
        return this;
      }),
      connect: jest.fn(),
      end: jest.fn()
    };

    // Override the SSH client constructor
    ssh2Client.mockReset();
    ssh2Client.mockReturnValue(sshClientInstance);

    // Re-import the module to get a fresh instance
    db = require('../db');

    // Call init and expect it to reject
    await expect(db.init()).rejects.toThrow('Mock SSH connection error');
  });

  test('should handle scenario where database is on same server as SSH', async () => {
    // Reset environment to simulate DB on same server as SSH
    process.env.SSH_HOST = 'remote-server.example.com';
    process.env.DB_HOST = 'localhost';

    // Reset modules
    jest.resetModules();
    db = require('../db');
    ssh2Client = require('ssh2').Client;
    mysqlClient = require('mysql2/promise');

    // Initialize DB
    await db.init();

    // Verify SSH connection was made to remote server
    const connectMock = ssh2Client.mock.results[0].value.connect;
    expect(connectMock.mock.calls[0][0]).toMatchObject({
      host: 'remote-server.example.com',
    });

    // Verify tunnel forwards to localhost on the remote server
    const sshInstance = ssh2Client.mock.results[0].value;
    expect(sshInstance.forwardOut).toHaveBeenCalledWith(
      '127.0.0.1',  // SSH_LOCAL_HOST
      3307,         // SSH_LOCAL_PORT
      'localhost',  // DB_HOST should be localhost
      3306,         // DB_PORT
      expect.any(Function)
    );
  });

  test('should handle scenario where database is on different server than SSH', async () => {
    // Reset environment to simulate DB on different server than SSH
    process.env.SSH_HOST = 'jump-server.example.com';
    process.env.DB_HOST = 'db-internal.example.com';

    // Reset modules
    jest.resetModules();
    db = require('../database');
    ssh2Client = require('ssh2').Client;
    mysqlClient = require('mysql2/promise');

    // Initialize DB
    await db.init();

    // Verify SSH connection was made to jump server
    const connectMock = ssh2Client.mock.results[0].value.connect;
    expect(connectMock.mock.calls[0][0]).toMatchObject({
      host: 'jump-server.example.com',
    });

    // Verify tunnel forwards to internal DB server
    const sshInstance = ssh2Client.mock.results[0].value;
    expect(sshInstance.forwardOut).toHaveBeenCalledWith(
      '127.0.0.1',              // SSH_LOCAL_HOST
      3307,                     // SSH_LOCAL_PORT
      'db-internal.example.com', // DB_HOST should be internal server
      3306,                     // DB_PORT
      expect.any(Function)
    );
  });

  test('should use DB_HOST for SSH_HOST if SSH_HOST not specified', async () => {
    // Reset environment to simulate SSH_HOST not specified
    delete process.env.SSH_HOST;
    process.env.DB_HOST = 'database.example.com';

    // Reset modules
    jest.resetModules();
    db = require('../db');
    ssh2Client = require('ssh2').Client;

    // Initialize DB
    await db.init();

    // Verify SSH connection was made using DB_HOST
    const connectMock = ssh2Client.mock.results[0].value.connect;
    expect(connectMock.mock.calls[0][0]).toMatchObject({
      host: 'database.example.com', // Should use DB_HOST
    });
  });

  test('should handle SSH connection timeout', async () => {
    // Set timeout server
    process.env.SSH_HOST = 'timeout.example.com';

    // Reset modules
    jest.resetModules();
    db = require('../database');
    ssh2Client = require('ssh2').Client;

    // Mock the connection timeout
    const sshInstance = ssh2Client.mock.results[0].value;

    // Call init and expect it to eventually reject
    const initPromise = db.init();

    // Manually trigger timeout error
    if (sshInstance.timeoutCallback) {
      sshInstance.errorCallback(new Error('Connection timed out'));
    }

    await expect(initPromise).rejects.toThrow('Connection timed out');
  });

  test('should execute SQL queries through the database', async () => {
    // Reset modules for a fresh start
    jest.resetModules();

    // Get fresh mocks
    ssh2Client = require('ssh2').Client;
    mysqlClient = require('mysql2/promise');

    // Create a properly functioning SSH mock
    const sshClientInstance = {
      on: jest.fn().mockImplementation(function(event, callback) {
        if (event === 'ready') {
          // Store the ready callback to call immediately
          setTimeout(() => callback(), 10);
        }
        return this;
      }),
      connect: jest.fn(),
      forwardOut: jest.fn().mockImplementation((srcAddr, srcPort, dstAddr, dstPort, callback) => {
        callback(null, { localPort: 3307 });
      }),
      end: jest.fn()
    };

    // Create the expected row format
    const mockRows = [{ id: 1, name: 'Test' }];

    // Mock pool execute response with our expected format
    const mockPool = {
      execute: jest.fn().mockImplementation(async (sql) => {
        if (sql.includes('SELECT 1 AS connection_test')) {
          return [[{ connection_test: 1 }]];
        }
        return [mockRows, []];  // Return expected format - array of rows in first element
      }),
      end: jest.fn().mockResolvedValue(true)
    };

    // Set up the mocks
    ssh2Client.mockReturnValue(sshClientInstance);
    mysqlClient.createPool.mockReturnValue(mockPool);

    // Re-import the module
    db = require('../database');

    // Setup successful connection
    await db.init();

    // Execute query
    const result = await db.query('SELECT * FROM test');

    // Verify execute was called
    expect(mockPool.execute).toHaveBeenCalledWith('SELECT * FROM test', []);

    // Verify result is correct
    expect(result).toEqual(mockRows);
  });

  test('should handle query parameters correctly', async () => {
    // We can reuse the mocks from the previous test but need to make them accessible
    // So we won't completely reset modules

    // Clear previous mocks
    jest.clearAllMocks();

    // Store reference to the mock pool created in the previous test
    const mockPool = mysqlClient.createPool();

    // Update the mock for this test case
    mockPool.execute.mockImplementation(async (sql, params) => {
      if (sql.includes('SELECT 1 AS connection_test')) {
        return [[{ connection_test: 1 }]];
      }
      return [[{ id: params[0], name: 'Test' }], []];
    });

    // Execute query with parameters
    const result = await db.query('SELECT * FROM test WHERE id = ?', [1]);

    // Verify execute was called with parameters
    expect(mockPool.execute).toHaveBeenCalledWith('SELECT * FROM test WHERE id = ?', [1]);

    // Verify result is correct
    expect(result).toEqual([{ id: 1, name: 'Test' }]);
  });

  test('should close connections when closing the database', async () => {
    // Reset modules for a fresh start
    jest.resetModules();

    // Get fresh mocks
    ssh2Client = require('ssh2').Client;
    mysqlClient = require('mysql2/promise');

    // Create a mock pool with proper verification response
    const mockPool = {
      execute: jest.fn().mockImplementation(async (sql) => {
        if (sql.includes('SELECT 1 AS connection_test')) {
          return [[{ connection_test: 1 }]];
        }
        return [[{ test: 'data' }], []];
      }),
      end: jest.fn().mockResolvedValue(true)
    };

    // Create a mock SSH client with properly working callbacks
    const mockSSHClient = {
      on: jest.fn().mockImplementation(function(event, callback) {
        if (event === 'ready') {
          setTimeout(() => callback(), 10);
        }
        if (event === 'close') {
          this.closeCallback = callback;
        }
        return this;
      }),
      connect: jest.fn(),
      end: jest.fn().mockImplementation(function() {
        // Simulate calling the close callback after a short delay
        if (this.closeCallback) {
          setTimeout(() => this.closeCallback(), 10);
        }
        return this;
      }),
      forwardOut: jest.fn().mockImplementation((srcAddr, srcPort, dstAddr, dstPort, callback) => {
        callback(null, { localPort: 3307 });
      })
    };

    // Set up the mocks
    ssh2Client.mockReturnValue(mockSSHClient);
    mysqlClient.createPool.mockReturnValue(mockPool);

    // Re-import the module
    db = require('../database');

    // Setup successful connection
    await db.init();

    // Close database with shorter timeout
    const closePromise = db.close();

    // Wait for close to complete
    await closePromise;

    // Verify that DB pool was ended
    expect(mockPool.end).toHaveBeenCalled();

    // Verify that SSH connection was closed
    expect(mockSSHClient.end).toHaveBeenCalled();
  }, 10000); // Give this test a longer timeout

  test('should handle cross-platform path resolution properly', () => {
    // Test all path types mentioned in the SSH implementation guide

    // Test absolute path
    const unixAbsPath = '/home/<USER>/.ssh/id_rsa';
    expect(db.resolvePath(unixAbsPath)).toBe(unixAbsPath);

    // Test Windows path
    const winPath = 'C:\\Users\\<USER>\\.ssh\\id_rsa';
    expect(db.resolvePath(winPath)).toBe(winPath);

    // Home directory expansion testing
    // Save original env
    const originalHome = process.env.HOME;
    const originalUserProfile = process.env.USERPROFILE;

    // Test Unix home directory
    process.env.HOME = '/home/<USER>';
    delete process.env.USERPROFILE;
    expect(db.resolvePath('~/.ssh/id_rsa')).toBe('/home/<USER>/.ssh/id_rsa');

    // Test Windows home directory
    delete process.env.HOME;
    process.env.USERPROFILE = 'C:\\Users\\<USER>\\Users\\testuser', '.ssh/id_rsa'));

    // Restore original environment
    process.env.HOME = originalHome;
    process.env.USERPROFILE = originalUserProfile;

    // Test relative path
    const relPath = '.ssh/id_rsa';
    const cwd = process.cwd();
    expect(db.resolvePath(relPath)).toBe(path.resolve(cwd, relPath));
  });

  test('should handle network errors and reconnection', async () => {
    // Initialize DB first
    await db.init();

    // Get reference to the mock pool
    const mockPool = mysqlClient.createPool.mock.results[0].value;

    // Make the execute method throw a connection error once
    let hasErrored = false;
    const originalExecute = mockPool.execute;
    mockPool.execute = jest.fn().mockImplementation(async (sql, params) => {
      if (!hasErrored && sql !== 'SELECT 1 AS connection_test') {
        hasErrored = true;
        const error = new Error('Connection lost');
        error.code = 'PROTOCOL_CONNECTION_LOST';
        throw error;
      }
      return originalExecute(sql, params);
    });

    // Execute a query that should trigger reconnection
    const result = await db.query('SELECT * FROM test');

    // Verify execute was called more than once (due to reconnection)
    expect(mockPool.execute).toHaveBeenCalledTimes(3); // Initial + reconnection test + retry

    // Verify the query executed successfully after reconnection
    expect(result).toEqual([{ id: 1, name: 'Test' }]);
  });

  test('getRecentSessionIds should return recent sessions from test_session table', async () => {
    // This test is no longer applicable as db-direct has been removed
    console.log('Skipping getRecentSessionIds test - db-direct has been removed');
  });
});
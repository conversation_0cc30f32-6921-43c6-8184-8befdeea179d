Now, I see that we have other bunch or errors in console - not related with login, but affecting page style: 
Dispatched apiservice-ready event
service-worker.js:64 Refused to connect to 'https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap' because it violates the following Content Security Policy directive: "connect-src 'self' wss:".

(anonymous) @ service-worker.js:64
Promise.then
(anonymous) @ service-worker.js:62Understand this error
service-worker.js:64 Fetch API cannot load https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap. Refused to connect because it violates the document's Content Security Policy.
(anonymous) @ service-worker.js:64
Promise.then
(anonymous) @ service-worker.js:62Understand this error
The FetchEvent for "https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" resulted in a network error response: the promise was rejected.
Promise.then
(anonymous) @ service-worker.js:60Understand this warning
service-worker.js:64 Uncaught (in promise) TypeError: Failed to fetch. Refused to connect because it violates the document's Content Security Policy.
    at service-worker.js:64:28
(anonymous) @ service-worker.js:64
Promise.then
(anonymous) @ service-worker.js:62Understand this error
about:client:2  GET https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap net::ERR_FAILEDUnderstand this error
content.js:1  GET http://localhost:3000/assets/index.5d1d86ea.js net::ERR_ABORTED 404 (Not Found)
(anonymous) @ content.js:1
l @ content.js:1
(anonymous) @ content.js:1Understand this error
content.js:1  GET http://localhost:3000/assets/index.b9d36532.js net::ERR_ABORTED 404 (Not Found)
(anonymous) @ content.js:1
l @ content.js:1
(anonymous) @ content.js:1Understand this error
7Fetch failed loading: GET "<URL>".
service-worker.js:71 Uncaught (in promise) TypeError: Failed to execute 'put' on 'Cache': Request scheme 'chrome-extension' is unsupported
    at service-worker.js:71:25
(anonymous) @ service-worker.js:71
Promise.then
(anonymous) @ service-worker.js:70
Promise.then
(anonymous) @ service-worker.js:65
Promise.then
(anonymous) @ service-worker.js:62Understand this error
content.js:1  GET http://localhost:3000/assets/_commonjsHelpers.b8add541.js net::ERR_ABORTED 404 (Not Found)
(anonymous) @ content.js:1
l @ content.js:1
(anonymous) @ content.js:1Understand this error
service-worker.js:64 Fetch finished loading: GET "chrome-extension://ajiejmhbejpdgkkigpddefnjmgcbkenk/ckeditor-inject.js".
(anonymous) @ service-worker.js:64
Promise.then
(anonymous) @ service-worker.js:62
content.js:1  GET http://localhost:3000/assets/redux-toolkit.esm.71406f46.js net::ERR_ABORTED 404 (Not Found)
(anonymous) @ content.js:1
l @ content.js:1
(anonymous) @ content.js:1Understand this error
content.js:1  GET http://localhost:3000/assets/index.ccc8e88d.js net::ERR_ABORTED 404 (Not Found)
(anonymous) @ content.js:1
l @ content.js:1
(anonymous) @ content.js:1Understand this error
content.js:1  GET http://localhost:3000/assets/index.d1783429.js net::ERR_ABORTED 404 (Not Found)
(anonymous) @ content.js:1
l @ content.js:1
(anonymous) @ content.js:1Understand this error
content.js:1  GET http://localhost:3000/assets/index.63256c84.js net::ERR_ABORTED 404 (Not Found)
0Refused to apply inline style because it violates the following Content Security Policy directive: "style-src 'self' <URL>". Either the 'unsafe-inline' keyword, a hash ('sha256-ARI6pVIGJd+j3a2Jrdm4HwTn30COHG3Vk6Fu/wUOAS8='), or a nonce ('nonce-...') is required to enable inline execution. Note that hashes do not apply to event handlers, style attributes and javascript: navigations unless the 'unsafe-hashes' keyword is present.
Understand this error
400Refused to apply inline style because it violates the following Content Security Policy directive: "style-src 'self' <URL>". Either the 'unsafe-inline' keyword, a hash ('sha256-VSqx9/ybHcYFbYYjdpqLhtM/AOAO/43O8QUi8T4/SLU='), or a nonce ('nonce-...') is required to enable inline execution. Note that hashes do not apply to event handlers, style attributes and javascript: navigations unless the 'unsafe-hashes' keyword is present.
Understand this error
400Refused to apply inline style because it violates the following Content Security Policy directive: "style-src 'self' <URL>". Either the 'unsafe-inline' keyword, a hash ('sha256-kItswOO0yyG9nsMdWc48GRfdXx/4OtFJKdp7vQwYiO0='), or a nonce ('nonce-...') is required to enable inline execution. Note that hashes do not apply to event handlers, style attributes and javascript: navigations unless the 'unsafe-hashes' keyword is present.
Understand this error
400Refused to apply inline style because it violates the following Content Security Policy directive: "style-src 'self' <URL>". Either the 'unsafe-inline' keyword, a hash ('sha256-/kXZODfqoc2myS1eI6wr0HH8lUt+vRhW8H/oL+YJcMg='), or a nonce ('nonce-...') is required to enable inline execution. Note that hashes do not apply to event handlers, style attributes and javascript: navigations unless the 'unsafe-hashes' keyword is present.
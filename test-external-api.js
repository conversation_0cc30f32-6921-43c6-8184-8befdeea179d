/**
 * Test script to check external API connectivity
 */

async function testExternalApiConnectivity() {
  const fetch = (await import('node-fetch')).default;
  const baseUrl = 'http://mprts-qa02.lab.wagerworks.com:9080/AutoRun';
  
  console.log('Testing external API connectivity...');
  console.log(`Base URL: ${baseUrl}`);
  console.log('');
  
  // Test 1: HEAD request (what the proxy uses)
  console.log('Test 1: HEAD request to base URL');
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000);
    
    const headResponse = await fetch(baseUrl, {
      method: 'HEAD',
      signal: controller.signal
    });
    
    clearTimeout(timeoutId);
    console.log(`✅ HEAD request successful: ${headResponse.status} ${headResponse.statusText}`);
  } catch (error) {
    console.log(`❌ HEAD request failed: ${error.message}`);
  }
  
  console.log('');
  
  // Test 2: GET request to base URL
  console.log('Test 2: GET request to base URL');
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000);
    
    const getResponse = await fetch(baseUrl, {
      method: 'GET',
      signal: controller.signal
    });
    
    clearTimeout(timeoutId);
    console.log(`✅ GET request successful: ${getResponse.status} ${getResponse.statusText}`);
    
    const responseText = await getResponse.text();
    console.log(`Response length: ${responseText.length} characters`);
    console.log(`Response preview: ${responseText.substring(0, 100)}...`);
  } catch (error) {
    console.log(`❌ GET request failed: ${error.message}`);
  }
  
  console.log('');
  
  // Test 3: GET request to Login page
  console.log('Test 3: GET request to Login page');
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000);
    
    const loginResponse = await fetch(`${baseUrl}/Login`, {
      method: 'GET',
      signal: controller.signal
    });
    
    clearTimeout(timeoutId);
    console.log(`✅ Login page request successful: ${loginResponse.status} ${loginResponse.statusText}`);
    
    const loginText = await loginResponse.text();
    console.log(`Login page length: ${loginText.length} characters`);
    console.log(`Contains "Login Page": ${loginText.includes('Login Page')}`);
  } catch (error) {
    console.log(`❌ Login page request failed: ${error.message}`);
  }
  
  console.log('');
  
  // Test 4: POST request to Login (with test credentials)
  console.log('Test 4: POST request to Login with test credentials');
  try {
    const formData = new URLSearchParams();
    formData.append('uid', '<EMAIL>');
    formData.append('password', 'test');
    
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000);
    
    const loginPostResponse = await fetch(`${baseUrl}/Login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
        'Origin': 'http://mprts-qa02.lab.wagerworks.com:9080',
        'Referer': `${baseUrl}/Login`
      },
      body: formData,
      redirect: 'manual',
      signal: controller.signal
    });
    
    clearTimeout(timeoutId);
    console.log(`✅ Login POST successful: ${loginPostResponse.status} ${loginPostResponse.statusText}`);
    
    // Check for Set-Cookie headers
    const setCookieHeaders = loginPostResponse.headers.get('set-cookie');
    if (setCookieHeaders) {
      console.log(`Set-Cookie headers found: ${setCookieHeaders}`);
      const jsessionMatch = setCookieHeaders.match(/JSESSIONID=([^;]+)/);
      if (jsessionMatch) {
        console.log(`✅ JSESSIONID found: ${jsessionMatch[1]}`);
      } else {
        console.log(`❌ No JSESSIONID in Set-Cookie headers`);
      }
    } else {
      console.log(`❌ No Set-Cookie headers found`);
    }
    
    const loginPostText = await loginPostResponse.text();
    console.log(`Login response length: ${loginPostText.length} characters`);
    console.log(`Response preview: ${loginPostText.substring(0, 200)}...`);
  } catch (error) {
    console.log(`❌ Login POST failed: ${error.message}`);
  }
}

// Run the test
testExternalApiConnectivity().catch(console.error);

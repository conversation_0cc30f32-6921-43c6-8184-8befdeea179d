/**
 * <PERSON>ie Authentication Service
 * Handles authentication with the external API using cookies
 */
const fetch = require('node-fetch');
const { parse } = require('cookie');

// Cache for JSESSIONID cookies
const cookieCache = new Map();

/**
 * Login to the external API and get a JSESSIONID cookie
 * @param {string} uid - User ID
 * @param {string} password - Password
 * @returns {Promise<string>} - JSESSIONID cookie
 */
async function login(uid, password) {
  // Build the login URL
  const loginUrl = 'http://mprts-qa02.lab.wagerworks.com:9080/AutoRun/Login';
  
  // Prepare the form data
  const formData = new URLSearchParams();
  formData.append('uid', uid);
  formData.append('password', password);
  
  // Make the login request
  const response = await fetch(loginUrl, {
    method: 'POST',
    body: formData,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
      'Origin': 'http://mprts-qa02.lab.wagerworks.com:9080',
      'Referer': 'http://mprts-qa02.lab.wagerworks.com:9080/AutoRun/Login'
    },
    redirect: 'manual' // Don't follow redirects to capture cookies
  });
  
  // Check if the response is OK
  if (!response.ok && response.status !== 302) {
    throw new Error(`Login failed: ${response.status} ${response.statusText}`);
  }
  
  // Get the Set-Cookie header
  const setCookieHeader = response.headers.get('set-cookie');
  if (!setCookieHeader) {
    throw new Error('No Set-Cookie header in login response');
  }
  
  // Parse the cookie
  const cookies = setCookieHeader.split(',').map(cookie => cookie.trim());
  let jsessionId = null;
  
  for (const cookie of cookies) {
    const parsedCookie = parse(cookie);
    if (parsedCookie.JSESSIONID) {
      jsessionId = parsedCookie.JSESSIONID;
      break;
    }
  }
  
  if (!jsessionId) {
    throw new Error('No JSESSIONID cookie in login response');
  }
  
  // Cache the cookie
  const expiryTime = Date.now() + 30 * 60 * 1000; // 30 minutes
  cookieCache.set(uid, {
    jsessionId,
    expires: expiryTime
  });

  console.log(`[CookieAuth] 💾 Cached new JSESSIONID for ${uid}: ${jsessionId.substring(0, 8)}...`);
  console.log(`[CookieAuth] 💾 Cache expires at: ${new Date(expiryTime).toISOString()}`);
  console.log(`[CookieAuth] 💾 Cache expires in: ${Math.round((expiryTime - Date.now()) / 1000)}s`);

  return jsessionId;
}

/**
 * Get a valid JSESSIONID cookie for a user
 * @param {string} uid - User ID
 * @param {string} password - Password
 * @returns {Promise<string>} - JSESSIONID cookie
 */
async function getJsessionId(uid, password) {
  console.log(`[CookieAuth] 🔑 getJsessionId called for user: ${uid}`);

  // Check if we have a valid cached cookie
  const cachedCookie = cookieCache.get(uid);
  const now = Date.now();

  if (cachedCookie) {
    const timeToExpiry = cachedCookie.expires - now;
    const isValid = timeToExpiry > 0;
    console.log(`[CookieAuth] 🔑 Found cached session, expires in: ${Math.round(timeToExpiry / 1000)}s`);

    if (isValid) {
      console.log(`[CookieAuth] 🔑 ✅ Using cached JSESSIONID: ${cachedCookie.jsessionId.substring(0, 8)}...`);
      return cachedCookie.jsessionId;
    } else {
      console.log(`[CookieAuth] 🔑 ❌ Cached session expired, will login fresh`);
      cookieCache.delete(uid);
    }
  } else {
    console.log(`[CookieAuth] 🔑 No cached session found, will login fresh`);
  }

  // Otherwise, login and get a new cookie
  console.log(`[CookieAuth] 🔑 Performing fresh login for ${uid}`);
  return login(uid, password);
}

/**
 * Get a fresh JSESSIONID cookie for a user (bypasses cache)
 * Use this for critical operations like stopping tests where session validity is crucial
 * @param {string} uid - User ID
 * @param {string} password - Password
 * @returns {Promise<string>} - Fresh JSESSIONID cookie
 */
async function getFreshJsessionId(uid, password) {
  console.log(`[CookieAuth] 🔄 getFreshJsessionId called for user: ${uid} (BYPASSING CACHE)`);

  // Always perform fresh login, ignore cache
  console.log(`[CookieAuth] 🔄 Forcing fresh login for critical operation`);
  return login(uid, password);
}

/**
 * Get a cached JSESSIONID cookie for a user (without login)
 * @param {string} uid - User ID
 * @returns {string|null} - JSESSIONID cookie or null if not cached/expired
 */
function getCachedJsessionId(uid) {
  const cachedCookie = cookieCache.get(uid);
  const now = Date.now();

  console.log(`[CookieAuth] 🔍 Checking cached session for user: ${uid}`);
  console.log(`[CookieAuth] 🔍 Cache entry exists: ${!!cachedCookie}`);

  if (cachedCookie) {
    const timeToExpiry = cachedCookie.expires - now;
    const isValid = timeToExpiry > 0;
    console.log(`[CookieAuth] 🔍 Cache entry expires in: ${Math.round(timeToExpiry / 1000)}s`);
    console.log(`[CookieAuth] 🔍 Cache entry valid: ${isValid}`);
    console.log(`[CookieAuth] 🔍 Cached JSESSIONID: ${cachedCookie.jsessionId?.substring(0, 8)}...`);

    if (isValid) {
      console.log(`[CookieAuth] ✅ Using cached JSESSIONID for ${uid}`);
      return cachedCookie.jsessionId;
    } else {
      console.log(`[CookieAuth] ❌ Cached JSESSIONID expired for ${uid}, removing from cache`);
      cookieCache.delete(uid);
    }
  } else {
    console.log(`[CookieAuth] ❌ No cached session found for ${uid}`);
  }

  return null;
}

/**
 * Get cache status for debugging
 * @returns {Object} Cache status information
 */
function getCacheStatus() {
  const now = Date.now();
  const status = {
    totalEntries: cookieCache.size,
    entries: []
  };

  for (const [uid, entry] of cookieCache.entries()) {
    const timeToExpiry = entry.expires - now;
    status.entries.push({
      uid,
      jsessionId: entry.jsessionId.substring(0, 8) + '...',
      expiresIn: Math.round(timeToExpiry / 1000),
      isValid: timeToExpiry > 0,
      expiresAt: new Date(entry.expires).toISOString()
    });
  }

  return status;
}

/**
 * Manually update the cache with a new JSESSIONID (for external login integration)
 * @param {string} uid - User ID
 * @param {string} jsessionId - JSESSIONID to cache
 */
function updateCache(uid, jsessionId) {
  const expiryTime = Date.now() + 30 * 60 * 1000; // 30 minutes
  cookieCache.set(uid, {
    jsessionId,
    expires: expiryTime
  });

  console.log(`[CookieAuth] 🔧 Manually updated cache for ${uid}: ${jsessionId.substring(0, 8)}...`);
  console.log(`[CookieAuth] 🔧 Cache expires at: ${new Date(expiryTime).toISOString()}`);
  console.log(`[CookieAuth] 🔧 Cache expires in: ${Math.round((expiryTime - Date.now()) / 1000)}s`);
}

module.exports = {
  getJsessionId,
  getFreshJsessionId,
  getCachedJsessionId,
  getCacheStatus,
  updateCache
};

/**
 * Base API Service
 * 
 * This is the foundational API service class that provides common functionality
 * for all API interactions in the SmartTest application.
 */

import { apiConfig } from './api-config.js';

export class BaseApiService {
  constructor() {
    this.config = apiConfig;
    this.credentials = { uid: '', password: '' };
    this.loadCredentials();
  }

  /**
   * Set API credentials (using shared credential manager)
   * @param {string} username - Username
   * @param {string} password - Password
   */
  setCredentials(username, password) {
    // Use shared credential manager for consistent storage logic
    if (window.credentialManager) {
      const result = window.credentialManager.setCredentials(username, password);
      this.credentials = window.credentialManager.getCredentials();
      return result;
    } else {
      // Fallback to legacy logic if credential manager not available
      console.warn('Shared credential manager not available, using fallback logic');
      this.credentials = { uid: username, password: password };

      try {
        sessionStorage.setItem('smarttest_uid', username);
        sessionStorage.setItem('smarttest_pwd', password);
        console.log('API credentials set for user:', username, '(fallback)');
      } catch (error) {
        console.warn('Could not save credentials to session storage:', error);
      }

      return true;
    }
  }

  /**
   * Load credentials from storage (using shared credential manager)
   * @returns {boolean} Whether credentials were successfully loaded
   */
  loadCredentials() {
    // Use shared credential manager for consistent loading logic
    if (window.credentialManager) {
      const result = window.credentialManager.loadCredentials();
      this.credentials = window.credentialManager.getCredentials();
      return result;
    } else {
      // Fallback to legacy logic if credential manager not available
      console.warn('Shared credential manager not available, using fallback logic');

      try {
        const uid = sessionStorage.getItem('smarttest_uid');
        const password = sessionStorage.getItem('smarttest_pwd');

        if (uid && password) {
          this.credentials = { uid, password };
          console.log(`Credentials loaded for user: ${uid} (fallback)`);
          return true;
        }

        console.log('No valid credentials found, user needs to log in (fallback)');
        return false;
      } catch (error) {
        console.error('Error loading credentials (fallback):', error);
        this.credentials = { uid: '', password: '' };
        return false;
      }
    }
  }

  /**
   * Clear stored credentials (using shared credential manager)
   */
  clearCredentials() {
    if (window.credentialManager) {
      window.credentialManager.clearCredentials();
      this.credentials = window.credentialManager.getCredentials();
    } else {
      // Fallback to legacy logic
      this.credentials = { uid: '', password: '' };
      try {
        sessionStorage.removeItem('smarttest_uid');
        sessionStorage.removeItem('smarttest_pwd');
      } catch (error) {
        console.warn('Could not clear credentials from session storage:', error);
      }
    }
  }

  /**
   * Get authentication parameters
   * @returns {Object} Authentication parameters
   */
  getAuthParams() {
    return {
      uid: this.credentials.uid,
      password: this.credentials.password
    };
  }

  /**
   * Make a GET request with retry logic
   * @param {string} endpoint - Endpoint key or path
   * @param {Object} params - Query parameters
   * @param {Object} options - Request options
   * @returns {Promise<any>} Response data
   */
  async getRequest(endpoint, params = {}, options = {}) {
    const url = this.config.getUrl(endpoint);
    const requestConfig = { ...this.config.getRequestConfig(), ...options };
    
    return this.makeRequestWithRetry('GET', url, params, requestConfig);
  }

  /**
   * Make a POST request with retry logic
   * @param {string} endpoint - Endpoint key or path
   * @param {Object} params - Request body parameters
   * @param {Object} options - Request options
   * @returns {Promise<any>} Response data
   */
  async postRequest(endpoint, params = {}, options = {}) {
    const url = this.config.getUrl(endpoint);
    const requestConfig = { ...this.config.getRequestConfig(), ...options };
    
    return this.makeRequestWithRetry('POST', url, params, requestConfig);
  }

  /**
   * Make a request with retry logic
   * @param {string} method - HTTP method
   * @param {string} url - Request URL
   * @param {Object} params - Request parameters
   * @param {Object} config - Request configuration
   * @returns {Promise<any>} Response data
   */
  async makeRequestWithRetry(method, url, params, config) {
    let lastError;
    
    for (let attempt = 0; attempt <= config.retries; attempt++) {
      try {
        return await this.makeRequest(method, url, params, config);
      } catch (error) {
        lastError = error;
        
        if (attempt < config.retries && this.isRetryableError(error)) {
          console.warn(`Request failed (attempt ${attempt + 1}/${config.retries + 1}), retrying...`, error);
          await this.delay(config.retryDelay * Math.pow(2, attempt)); // Exponential backoff
        } else {
          break;
        }
      }
    }
    
    throw lastError;
  }

  /**
   * Make a single HTTP request
   * @param {string} method - HTTP method
   * @param {string} url - Request URL
   * @param {Object} params - Request parameters
   * @param {Object} config - Request configuration
   * @returns {Promise<any>} Response data
   */
  async makeRequest(method, url, params, config) {
    console.log(`Making ${method} request to: ${url}`);

    // Add authentication parameters
    const allParams = {
      ...params,
      ...this.getAuthParams()
    };

    // Build request options
    const requestOptions = {
      method,
      headers: {
        'Content-Type': 'application/json'
      },
      signal: AbortSignal.timeout(config.timeout)
    };

    // Handle different HTTP methods
    if (method === 'GET') {
      // Add query parameters to URL
      const queryString = new URLSearchParams(allParams).toString();
      url = `${url}?${queryString}`;
    } else {
      // Add body for POST/PUT/PATCH requests
      requestOptions.body = JSON.stringify(allParams);
    }

    // Make the request
    const response = await fetch(url, requestOptions);

    // Handle non-200 responses
    if (!response.ok) {
      const errorText = await response.text();
      console.error(`API error (${response.status}): ${errorText}`);
      throw new ApiError(`Request failed with status ${response.status}`, response.status, errorText);
    }

    // Parse and return response
    return await response.json();
  }

  /**
   * Check if an error is retryable
   * @param {Error} error - Error to check
   * @returns {boolean} Whether the error is retryable
   */
  isRetryableError(error) {
    // Retry on network errors and 5xx server errors
    return error.name === 'TypeError' || 
           error.name === 'AbortError' ||
           (error instanceof ApiError && error.status >= 500);
  }

  /**
   * Delay execution for a specified time
   * @param {number} ms - Milliseconds to delay
   * @returns {Promise<void>}
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

/**
 * Custom API Error class
 */
export class ApiError extends Error {
  constructor(message, status, response) {
    super(message);
    this.name = 'ApiError';
    this.status = status;
    this.response = response;
  }
}

// Export for CommonJS compatibility
if (typeof module !== 'undefined' && typeof module.exports !== 'undefined') {
  module.exports = { BaseApiService, ApiError };
}

// Reports Page JavaScript

// Configuration
const config = {
    // Hybrid approach: Use both database API and direct external API
    reportingEndpoint: '/local/recent-runs', // Database API endpoint for recent runs
    testDetailsEndpoint: '/local/test-details', // Database API endpoint for test details
    refreshInterval: 30000, // 30 seconds - polling interval for external API
    externalApiBaseUrl: '/api', // Use our proxy server instead of direct connection
    maxReportsToShow: 25, // Maximum number of reports to show in the table by default
    autoRefresh: false // Disable auto-refresh by default
};

// Constants
const DEFAULT_LIMIT = 25; // Default number of records to fetch

// DOM Elements
const elements = {
    reportsTable: document.getElementById('reports-table'),
    refreshBtn: document.getElementById('refresh-btn'),
    exportBtn: document.getElementById('export-btn'),
    timeRangeDropdown: document.getElementById('timeRangeDropdown'),
    dropdownItems: document.querySelectorAll('.dropdown-item'),
    environmentDisplay: document.getElementById('environment-display'),
    testDetailsSection: document.getElementById('test-details-section'),
    testDetailsTitle: document.getElementById('test-details-title'),
    closeDetailsBtn: document.getElementById('close-details-btn'),
    // Test details elements
    detailTestId: document.getElementById('detail-test-id'),
    detailTestType: document.getElementById('detail-test-type'),
    detailEnvironment: document.getElementById('detail-environment'), // Corrected ID
    detailStartTime: document.getElementById('detail-start-time'),
    detailStatus: document.getElementById('detail-status'),
    detailDuration: document.getElementById('detail-duration'),
    detailUser: document.getElementById('detail-user'),
    detailTrigger: document.getElementById('detail-trigger'),
    detailTotalCases: document.getElementById('detail-total-cases'),
    detailPassedCases: document.getElementById('detail-passed-cases'),
    detailFailedCases: document.getElementById('detail-failed-cases'),
    detailSkippedCases: document.getElementById('detail-skipped-cases'),
    testCasesTable: document.getElementById('test-cases-table'),
    // Chart elements
    successRateChart: document.getElementById('success-rate-chart'),
    durationTrendChart: document.getElementById('duration-trend-chart'),
    // Rerun failed tests button
    rerunFailedBtn: document.getElementById('rerun-failed-btn'),
    loadingIndicator: document.getElementById('loading-indicator'),
    refreshStatus: document.getElementById('refresh-status'),
    resetButton: document.getElementById('reset-button'),
    // New loading indicator elements
    tableLoadingOverlay: document.getElementById('table-loading-overlay') || createTableLoadingOverlay(),
    progressBar: document.getElementById('loading-progress-bar') || createProgressBar()
};

// Charts
let successRateChart;
let durationTrendChart;

// Current state
let currentState = {
    activeTimeRange: '24h', // Default active time range (e.g., '24h', '7d', { type: 'custom', start: '...', end: '...' })
    customStartDate: null,    // For custom date range picker
    customEndDate: null,      // For custom date range picker
    reports: [],              // Holds ALL reports for the activeTimeRange
    totalRecordsForActiveTimeRange: 0, // Total records in DB for activeTimeRange
    lastSeenTsnId: null,      // Highest tsn_id from the last fetch, for incremental updates
    currentTestDetails: null, // Stores details of the currently viewed test
    // highestSessionId: localStorage.getItem('highestSessionId') || 0 // Review if needed, replaced by lastSeenTsnId for this flow
    bulkRerunUIInitialized: false // Flag to track if bulk rerun UI has been initialized
};

// Create table loading overlay element
function createTableLoadingOverlay() {
    // First check if it already exists
    let overlay = document.getElementById('table-loading-overlay');
    if (overlay) {
        return overlay;
    }

    overlay = document.createElement('div');
    overlay.id = 'table-loading-overlay';
    overlay.className = 'position-absolute w-100 h-100';
    overlay.style.top = '0';
    overlay.style.left = '0';
    overlay.style.backgroundColor = 'rgba(255, 255, 255, 0.8)';
    overlay.style.zIndex = '1000';
    overlay.style.display = 'flex';
    overlay.style.justifyContent = 'center';
    overlay.style.alignItems = 'center';
    overlay.style.transition = 'opacity 0.3s ease-in-out';

    const spinnerHTML = `
        <div class="text-center">
            <div class="spinner-border text-primary" role="status" style="width: 3rem; height: 3rem;">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2 loading-text fw-bold">Loading data...</p>
        </div>
    `;
    overlay.innerHTML = spinnerHTML;

    // Find the table container and add the overlay
    const tableContainer = document.querySelector('.table-responsive');
    if (tableContainer) {
        tableContainer.style.position = 'relative';
        tableContainer.appendChild(overlay);
        console.log('Added table loading overlay to table-responsive container');
    } else {
        // Fallback to adding it near the table itself
        const table = document.getElementById('reports-table');
        if (table && table.parentNode) {
            table.parentNode.style.position = 'relative';
            table.parentNode.appendChild(overlay);
            console.log('Added table loading overlay to table parent');
        } else {
            console.warn('Could not find a suitable container for the loading overlay');
        }
    }

    // Initially hide it (but it exists in the DOM now)
    overlay.style.display = 'none';

    return overlay;
}

// Helper function to escape HTML special characters
function escapeHtml(unsafe) {
    if (unsafe === null || unsafe === undefined) return '';
    return String(unsafe) // Ensure it's a string
         .replace(/&/g, "&amp;")
         .replace(/</g, "&lt;")
         .replace(/>/g, "&gt;")
         .replace(/"/g, "&quot;")
         .replace(/'/g, "&#039;");
}

function generateEnhancedReportHtml(detailedStepTableHtmlString, originalTestCases, tsnIdFromFile) {
    const parser = new DOMParser();
    const detailedStepDoc = parser.parseFromString(detailedStepTableHtmlString, 'text/html');
    const newReportParts = [];

    // Extract all steps from the detailedStepTableHtml into a more usable format
    const allSteps = [];
    // Assuming the table in ReportDetails?tsn_id=... has id="table"
    // If not, you'll need to adjust this selector, e.g., 'table tbody tr' or a more specific class
    const tableRows = detailedStepDoc.querySelectorAll('table#table tbody tr'); 
    tableRows.forEach(row => {
        const cells = row.querySelectorAll('td');
        if (cells.length >= 6) { // TC_ID, Seq, Status, Description, Input, Output/Error
            allSteps.push({
                tcId: cells[0].querySelector('a')?.textContent.trim(),
                stepSeq: cells[1].querySelector('a')?.textContent.trim(),
                stepStatus: cells[2].querySelector('a')?.textContent.trim(), // 'P' or 'F'
                stepDescription: cells[3].querySelector('a')?.textContent.trim() || cells[3].textContent.trim(),
                stepInput: cells[4].querySelector('a')?.textContent.trim() || cells[4].textContent.trim(), // Column 5 is input
                stepErrorDetails: cells[5].querySelector('a')?.textContent.trim() || cells[5].textContent.trim() // Column 6 is output/error
            });
        }
    });

    newReportParts.push('<div class="external-report">'); // Main wrapper
    newReportParts.push(`
        <div class="report-header mb-3">
             <h5>Test steps for Session ${tsnIdFromFile || (originalTestCases.length > 0 ? originalTestCases[0].tsn_id : 'N/A')}</h5>
        </div>
        <div class="report-content">
            <table class="table table-sm table-hover">
                <thead class="table-light">
                    <tr>
                        <th style="width: 15%;">Test Case ID</th>
                        <th style="width: 35%;">Description</th>
                        <th style="width: 15%;">Status</th>
                        <th style="width: 35%;">Details</th>
                    </tr>
                </thead>
                <tbody>
    `);

    for (const tc of originalTestCases) {
        // Ensure tc.tc_id is treated as a string for comparison with step.tcId (which is parsed as a string)
        const currentTcIdString = String(tc.tc_id);
        
        // Use description from originalTestCases, clean it up. Fallback if not present.
        const tcDescription = tc.description ? tc.description.replace(/&nbsp;/g, ' ').trim() : `Test Case ${currentTcIdString}`;
        const overallStatus = tc.status; // "Passed", "Failed", "Skipped" from original API

        const relevantStepsForThisTc = allSteps.filter(step => step.tcId === currentTcIdString);
        const failedStepDetailsForThisTc = relevantStepsForThisTc.filter(step => step.stepStatus === 'F');

        let statusBadgeClass = 'bg-secondary';
        let statusText = overallStatus; // Default to what original API gave
        let rowClass = '';

        if (overallStatus === 'Success' || overallStatus === 'Passed') {
            statusBadgeClass = 'bg-success';
            statusText = 'Success';
            rowClass = 'table-light'; // Or keep default, or table-success if you want full row color
        } else if (overallStatus === 'Failed') {
            statusBadgeClass = 'bg-danger';
            rowClass = 'table-light'; // Or table-danger
        } else if (overallStatus === 'Skipped') {
            statusBadgeClass = 'bg-warning text-dark';
            rowClass = 'table-light'; // Or table-warning
        }
        
        newReportParts.push(`<tr class="${rowClass}">`);
        newReportParts.push(`<td>${currentTcIdString}</td>`);
        newReportParts.push(`<td>${escapeHtml(tcDescription)}</td>`);
        newReportParts.push(`<td><span class="badge ${statusBadgeClass}">${escapeHtml(statusText)}</span></td>`);

        if (overallStatus === 'Failed' && failedStepDetailsForThisTc.length > 0) {
            const accordionId = `failures-${currentTcIdString}-${Math.random().toString(36).substring(2, 9)}`; // Unique ID
            newReportParts.push('<td>');
            newReportParts.push(`
                <div class="accordion" id="accordion-${accordionId}">
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="heading-${accordionId}">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse-${accordionId}" aria-expanded="false" aria-controls="collapse-${accordionId}">
                                Show Failed Steps (${failedStepDetailsForThisTc.length})
                            </button>
                        </h2>
                        <div id="collapse-${accordionId}" class="accordion-collapse collapse" aria-labelledby="heading-${accordionId}" data-bs-parent="#accordion-${accordionId}">
                            <div class="accordion-body">
            `);
            failedStepDetailsForThisTc.forEach(fs => {
                newReportParts.push(`
                    <div class="failed-step mb-2 p-2 border-start border-danger border-3 bg-light">
                        <div><strong>Step ${escapeHtml(fs.stepSeq)}:</strong> ${escapeHtml(fs.stepDescription)}</div>
                        <div class="mt-1 small"><strong>Input:</strong> <pre class="mb-0 bg-white p-1">${escapeHtml(fs.stepInput)}</pre></div>
                        <div class="mt-1 small text-danger"><strong>Error:</strong> <pre class="mb-0 bg-white p-1">${escapeHtml(fs.stepErrorDetails)}</pre></div>
                    </div>
                `);
            });
            newReportParts.push('</div></div></div></div></td>'); // Close accordion item, accordion, td
        } else if (overallStatus === 'Success' || overallStatus === 'Passed') {
            newReportParts.push('<td><span class="text-muted fst-italic">All steps passed</span></td>');
        } else if (overallStatus === 'Skipped') {
             newReportParts.push('<td><span class="text-muted fst-italic">Test skipped</span></td>');
        } else { 
            newReportParts.push('<td><span class="text-muted fst-italic">Details not available or no failed steps found in breakdown.</span></td>');
        }
        newReportParts.push('</tr>');
    }

    newReportParts.push('</tbody></table></div></div>'); // Close report-content, external-report
    return newReportParts.join('');
}

// Create progress bar element
function createProgressBar() {
    // First check if it already exists
    let progressBar = document.getElementById('loading-progress-bar');
    if (progressBar) {
        return progressBar;
    }

    progressBar = document.createElement('div');
    progressBar.id = 'loading-progress-bar';
    progressBar.style.height = '4px';
    progressBar.style.width = '0%';
    progressBar.style.backgroundColor = '#007bff';
    progressBar.style.position = 'absolute';
    progressBar.style.top = '0';
    progressBar.style.left = '0';
    progressBar.style.zIndex = '1001';
    progressBar.style.transition = 'width 0.3s ease-in-out';

    // Find the table container and add the progress bar
    const tableContainer = document.querySelector('.table-responsive');
    if (tableContainer) {
        tableContainer.style.position = 'relative';
        tableContainer.appendChild(progressBar);
        console.log('Added progress bar to table-responsive container');
    } else {
        // Fallback to adding it to the card that contains the table
        const tableCard = document.querySelector('.card');
        if (tableCard) {
            tableCard.style.position = 'relative';
            tableCard.appendChild(progressBar);
            console.log('Added progress bar to card container');
        } else {
            console.warn('Could not find a suitable container for the progress bar');
        }
    }

    return progressBar;
}

// Show loading indicators

# ======================================================
# SSH KEY AUTHENTICATION DIAGNOSTICS
# ======================================================
# This script helps diagnose SSH key authentication issues
# by running a series of checks and providing recommendations

# Set up parameters
param (
    [string]$KeyPath = "$env:USERPROFILE\.ssh\id_rsa_dbserver",
    [string]$Server = "mprts-qa02.lab.wagerworks.com",
    [string]$Username = "volfkoi"
)

# Enable verbose output for easier debugging
$VerbosePreference = "Continue"
$ErrorActionPreference = "Continue"

Write-Host "==================================================" -ForegroundColor Cyan
Write-Host "SSH KEY AUTHENTICATION DIAGNOSTIC TOOL" -ForegroundColor Cyan
Write-Host "==================================================" -ForegroundColor Cyan
Write-Host "Running diagnostics for the following configuration:"
Write-Host "Server:   $Server" -ForegroundColor White
Write-Host "Username: $Username" -ForegroundColor White
Write-Host "Key path: $KeyPath" -ForegroundColor White
Write-Host "==================================================" -ForegroundColor Cyan

# STEP 1: Check if SSH key exists
Write-Host "`n[STEP 1] Checking if SSH private key exists..." -ForegroundColor Green
if (Test-Path $KeyPath) {
    $keyInfo = Get-Item $KeyPath
    Write-Host "[OK] SSH private key found at $KeyPath" -ForegroundColor Green
    Write-Host "  File size: $($keyInfo.Length) bytes" -ForegroundColor White
    Write-Host "  Last modified: $($keyInfo.LastWriteTime)" -ForegroundColor White
    
    # Check file permissions
    $acl = Get-Acl $KeyPath
    Write-Host "`n  File permissions:" -ForegroundColor Cyan
    $acl.Access | Format-Table IdentityReference, FileSystemRights, AccessControlType -AutoSize
    
    if ($acl.Access.Where({$_.IdentityReference -like "*Everyone*" -or $_.IdentityReference -like "*Authenticated Users*" -and $_.FileSystemRights -match "FullControl|Write"})) {
        Write-Host "[ERROR] WARNING: Your key has overly permissive access rights!" -ForegroundColor Red
        Write-Host "  SSH keys should not be accessible by other users." -ForegroundColor Red
    } else {
        Write-Host "[OK] Key file permissions look reasonable" -ForegroundColor Green
    }
} else {
    Write-Host "[ERROR] SSH private key NOT found at $KeyPath" -ForegroundColor Red
    Write-Host "  You need to generate an SSH key pair using:" -ForegroundColor Yellow
    Write-Host "  ssh-keygen -t rsa -b 4096 -f $KeyPath" -ForegroundColor Yellow
    exit
}

# STEP 2: Check if public key exists
Write-Host "`n[STEP 2] Checking for public key file..." -ForegroundColor Green
$publicKeyPath = "$KeyPath.pub"
if (Test-Path $publicKeyPath) {
    Write-Host "[OK] Public key found at $publicKeyPath" -ForegroundColor Green
    
    # Check the format of the public key
    $pubKey = Get-Content $publicKeyPath -Raw
    if ($pubKey -match '^ssh-\w+ \S+ .*$') {
        Write-Host "[OK] Public key format appears to be valid" -ForegroundColor Green
        Write-Host "  Public key (first 50 chars): $($pubKey.Substring(0, [Math]::Min(50, $pubKey.Length)))..." -ForegroundColor DarkGray
    } else {
        Write-Host "[ERROR] Public key format may not be valid" -ForegroundColor Red
        Write-Host "  Expected format: 'ssh-rsa AAAAB3Nz... username@host'" -ForegroundColor Yellow
    }
} else {
    Write-Host "[ERROR] Public key NOT found at $publicKeyPath" -ForegroundColor Red
    Write-Host "  You can generate a public key from your private key using:" -ForegroundColor Yellow
    Write-Host "  ssh-keygen -y -f $KeyPath > ${KeyPath}.pub" -ForegroundColor Yellow
}

# STEP 3: Check SSH configuration
Write-Host "`n[STEP 3] Checking SSH client configuration..." -ForegroundColor Green
$sshConfigPath = "$env:USERPROFILE\.ssh\config"
if (Test-Path $sshConfigPath) {
    Write-Host "[OK] SSH config file found at $sshConfigPath" -ForegroundColor Green
    $sshConfig = Get-Content $sshConfigPath -Raw
    
    # Check for server-specific configuration
    if ($sshConfig -match "Host\s+$([regex]::Escape($Server))") {
        Write-Host "[OK] Found server-specific configuration for $Server" -ForegroundColor Green
        
        if ($sshConfig -match "HostKeyAlgorithms\s+\+ssh-rsa") {
            Write-Host "[OK] HostKeyAlgorithms configuration for legacy ssh-rsa found" -ForegroundColor Green
        } else {
            Write-Host "[WARNING] Missing HostKeyAlgorithms configuration for legacy ssh-rsa" -ForegroundColor Yellow
            Write-Host "  Consider adding: 'HostKeyAlgorithms +ssh-rsa'" -ForegroundColor Yellow
        }
        
        if ($sshConfig -match "PubkeyAcceptedAlgorithms\s+\+ssh-rsa") {
            Write-Host "[OK] PubkeyAcceptedAlgorithms configuration for legacy ssh-rsa found" -ForegroundColor Green
        } else {
            Write-Host "[WARNING] Missing PubkeyAcceptedAlgorithms configuration for legacy ssh-rsa" -ForegroundColor Yellow
            Write-Host "  Consider adding: 'PubkeyAcceptedAlgorithms +ssh-rsa'" -ForegroundColor Yellow
        }
    } else {
        Write-Host "[WARNING] No server-specific configuration for $Server" -ForegroundColor Yellow
        Write-Host "  Consider adding this to your SSH config:" -ForegroundColor Yellow
        Write-Host "  Host $Server" -ForegroundColor Yellow
        Write-Host "      HostKeyAlgorithms +ssh-rsa" -ForegroundColor Yellow
        Write-Host "      PubkeyAcceptedAlgorithms +ssh-rsa" -ForegroundColor Yellow
    }
} else {
    Write-Host "[WARNING] No SSH config file found at $sshConfigPath" -ForegroundColor Yellow
    Write-Host "  Consider creating one with:" -ForegroundColor Yellow
    Write-Host "  Host $Server" -ForegroundColor Yellow
    Write-Host "      HostKeyAlgorithms +ssh-rsa" -ForegroundColor Yellow
    Write-Host "      PubkeyAcceptedAlgorithms +ssh-rsa" -ForegroundColor Yellow
}

# STEP 4: Test SSH connection with verbose output
Write-Host "`n[STEP 4] Testing SSH connection with verbose logging..." -ForegroundColor Green
$sshTestCmd = "ssh -v -i `"$KeyPath`" -o HostKeyAlgorithms=+ssh-rsa -o PubkeyAcceptedAlgorithms=+ssh-rsa ${Username}@${Server} `"echo SUCCESS:CONNECTION_ESTABLISHED`""
Write-Host "Running: $sshTestCmd" -ForegroundColor DarkGray

Write-Host "`nOutput from SSH connection attempt:" -ForegroundColor Cyan
try {
    $output = Invoke-Expression $sshTestCmd 2>&1
    $output | ForEach-Object { Write-Host $_ }
    
    # Check for successful authentication
    if ($output -contains "SUCCESS:CONNECTION_ESTABLISHED") {
        Write-Host "`n[OK] SSH connection successful with key authentication!" -ForegroundColor Green
    } else {
        Write-Host "`n[ERROR] SSH connection test could not confirm successful key authentication" -ForegroundColor Red
        
        # Analyze output for specific issues
        if ($output -match "Permission denied \(publickey,password\)") {
            Write-Host "  Issue: Permission denied - the server rejected your public key" -ForegroundColor Red
            Write-Host "  Make sure your public key is properly installed in ~/.ssh/authorized_keys on the server" -ForegroundColor Yellow
        }
        
        if ($output -match "No more authentication methods to try") {
            Write-Host "  Issue: Authentication methods exhausted" -ForegroundColor Red
            Write-Host "  Server might not be configured to accept your key type or the key is not in authorized_keys" -ForegroundColor Yellow
        }
        
        if ($output -match "debug1: Offering public key") {
            Write-Host "  Your key was offered to the server, but wasn't accepted" -ForegroundColor Yellow
        }
    }
} catch {
    Write-Host "Error during SSH test: $_" -ForegroundColor Red
}

# STEP 5: Create server key installation script
Write-Host "`n[STEP 5] Creating server key installation script..." -ForegroundColor Green

# Create a temporary file for server-side commands
$serverCommandsFile = Join-Path $env:TEMP "ssh_key_setup.sh"
$pubKeyContent = Get-Content "$KeyPath.pub" -ErrorAction SilentlyContinue

@"
#!/bin/bash
# ======================================================
# SERVER-SIDE SSH KEY INSTALLATION SCRIPT
# ======================================================
# Run this script on the server ($Server) to set up SSH key authentication
# You may need to log in with password authentication first

# Create .ssh directory with proper permissions
mkdir -p ~/.ssh
chmod 700 ~/.ssh

# Create/update authorized_keys file
touch ~/.ssh/authorized_keys
chmod 600 ~/.ssh/authorized_keys

# Add your public key to authorized_keys if not already present
grep -q "$pubKeyContent" ~/.ssh/authorized_keys || echo "$pubKeyContent" >> ~/.ssh/authorized_keys

echo "Key installation complete!"
echo "Testing key permissions..."
ls -la ~/.ssh
echo "Contents of authorized_keys:"
cat ~/.ssh/authorized_keys

# Optional: Check server SSH configuration
if [ -f /etc/ssh/sshd_config ]; then
  echo "Checking sshd_config settings:"
  grep -E "PubkeyAuthentication|PasswordAuthentication|PubkeyAcceptedKeyTypes" /etc/ssh/sshd_config
else
  echo "Can't access /etc/ssh/sshd_config (requires sudo)"
fi
"@ | Out-File -FilePath $serverCommandsFile -Encoding ascii

Write-Host "[CREATED] Server key installation script generated at: $serverCommandsFile" -ForegroundColor Green

# Create alternative manual instructions file
$manualInstructionsFile = Join-Path $env:TEMP "manual_key_setup.txt"
@"
# ======================================================
# MANUAL SSH KEY SETUP INSTRUCTIONS
# ======================================================

To set up SSH key authentication on the server, follow these steps:

1. Log into the server using password authentication:
   ssh $Username@$Server

2. Create the .ssh directory if it doesn't exist:
   mkdir -p ~/.ssh
   chmod 700 ~/.ssh

3. Edit or create the authorized_keys file:
   nano ~/.ssh/authorized_keys

4. Paste this public key (all on one line):
$pubKeyContent

5. Save the file and set correct permissions:
   chmod 600 ~/.ssh/authorized_keys

6. Test the connection from your local machine:
   ssh -i "$KeyPath" $Username@$Server

# ======================================================
# ALTERNATIVE METHOD: COPY KEY FROM LOCAL MACHINE
# ======================================================

Instead of manually copying, you can use ssh-copy-id (from a Linux/Mac system)
or run this PowerShell command:

Get-Content "$KeyPath.pub" | ssh $Username@$Server "mkdir -p ~/.ssh && cat >> ~/.ssh/authorized_keys && chmod 700 ~/.ssh && chmod 600 ~/.ssh/authorized_keys"

# ======================================================
# TROUBLESHOOTING SERVER CONFIGURATION
# ======================================================

If key authentication still fails, check server settings:

1. Server SSH configuration may need adjustment:
   sudo nano /etc/ssh/sshd_config

2. Ensure these settings are set:
   PubkeyAuthentication yes
   PubkeyAcceptedKeyTypes +ssh-rsa

3. Restart SSH service if changes were made:
   sudo systemctl restart sshd
   # or on older systems:
   sudo service sshd restart
"@ | Out-File -FilePath $manualInstructionsFile -Encoding ascii

Write-Host "[CREATED] Manual instructions generated at: $manualInstructionsFile" -ForegroundColor Green

# STEP 6: Add a direct key copy command
Write-Host "`n[STEP 6] Key installation commands:" -ForegroundColor Green
Write-Host "The following command can copy your key directly to the server:" -ForegroundColor Cyan
$keyInstallCmd = "Get-Content `"$KeyPath.pub`" | ssh $Username@$Server `"mkdir -p ~/.ssh && cat >> ~/.ssh/authorized_keys && chmod 700 ~/.ssh && chmod 600 ~/.ssh/authorized_keys`""
Write-Host $keyInstallCmd -ForegroundColor Yellow
Write-Host "(Note: You'll be prompted for your password)" -ForegroundColor DarkGray

Write-Host "`nServer key installation options:" -ForegroundColor Green
Write-Host "1. Run script manually on server: $serverCommandsFile" -ForegroundColor White
Write-Host "2. Follow manual instructions: $manualInstructionsFile" -ForegroundColor White
Write-Host "3. Use direct key copy command above" -ForegroundColor White

# Ask if the user wants to try the direct key installation now
$tryNow = Read-Host "`nWould you like to try installing the key now? (y/n)"
if ($tryNow -eq "y") {
    Write-Host "Attempting to install your public key on the server..." -ForegroundColor Green
    Invoke-Expression $keyInstallCmd
    Write-Host "Key installation attempted. Let's test the connection now..." -ForegroundColor Green
    
    # Test connection with key again
    $testAfterInstall = "ssh -v -i `"$KeyPath`" -o HostKeyAlgorithms=+ssh-rsa -o PubkeyAcceptedAlgorithms=+ssh-rsa -o PreferredAuthentications=publickey ${Username}@${Server} `"echo KEY_AUTH_SUCCESS`""
    Write-Host "Testing with command: $testAfterInstall" -ForegroundColor DarkGray
    $testResult = Invoke-Expression $testAfterInstall 2>&1
    
    if ($testResult -contains "KEY_AUTH_SUCCESS") {
        Write-Host "`n[SUCCESS] SSH key authentication is now working!" -ForegroundColor Green
    } else {
        Write-Host "`n[WARNING] SSH key authentication still not working." -ForegroundColor Yellow
        Write-Host "Review the manual instructions file for additional troubleshooting steps." -ForegroundColor Yellow
    }
}

Write-Host "`n==================================================" -ForegroundColor Cyan
Write-Host "SSH DIAGNOSTICS COMPLETE" -ForegroundColor Cyan
Write-Host "==================================================" -ForegroundColor Cyan 
/**
 * Comprehensive CSRF Fix Test
 * Tests the CSRF token handling after session changes
 */

const fetch = require('node-fetch');

const BASE_URL = 'http://localhost:3000';

async function testCSRFHandling() {
  console.log('🧪 Testing CSRF handling after session changes...\n');

  try {
    // Step 1: Login to get a session
    console.log('1️⃣ Logging in...');
    const loginResponse = await fetch(`${BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        uid: '<EMAIL>',
        password: 'test'
      })
    });

    if (!loginResponse.ok) {
      throw new Error(`Login failed: ${loginResponse.status}`);
    }

    // Extract cookies from login response
    const cookies = loginResponse.headers.get('set-cookie');
    console.log('✅ Login successful');

    // Step 2: Get CSRF token
    console.log('\n2️⃣ Getting CSRF token...');
    const csrfResponse = await fetch(`${BASE_URL}/csrf-token`, {
      method: 'GET',
      headers: {
        'Cookie': cookies
      }
    });

    if (!csrfResponse.ok) {
      throw new Error(`CSRF token request failed: ${csrfResponse.status}`);
    }

    const csrfData = await csrfResponse.json();
    const csrfToken = csrfData.csrfToken;
    console.log(`✅ CSRF token obtained: ${csrfToken.substring(0, 8)}...`);

    // Step 3: Simulate server restart by waiting a moment
    console.log('\n3️⃣ Simulating session change (server restart scenario)...');
    console.log('   (In real scenario, server would restart and sessions would be lost)');
    
    // Step 4: Try to use the old CSRF token (should fail initially but retry should work)
    console.log('\n4️⃣ Testing API call with potentially stale CSRF token...');
    const testResponse = await fetch(`${BASE_URL}/api/case-runner`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': csrfToken,
        'Cookie': cookies
      },
      body: JSON.stringify({
        tc_id: '3180',
        envir: 'qa02',
        shell_host: 'jps-qa10-app01',
        uid: '<EMAIL>',
        password: ''
      })
    });

    console.log(`📊 API Response Status: ${testResponse.status}`);
    
    if (testResponse.ok) {
      console.log('✅ API call successful - CSRF handling working properly!');
    } else {
      const errorData = await testResponse.json();
      console.log(`❌ API call failed: ${errorData.message}`);
      console.log(`🔍 Error code: ${errorData.code}`);
      
      if (errorData.details) {
        console.log(`🔍 Details: ${JSON.stringify(errorData.details, null, 2)}`);
      }
    }

    console.log('\n🎯 Test Summary:');
    console.log('- The enhanced CSRF system should now provide better error messages');
    console.log('- Session mismatches are detected and reported with specific error codes');
    console.log('- Frontend retry mechanisms should handle token refresh automatically');
    console.log('- Check the server logs for detailed CSRF validation information');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testCSRFHandling();

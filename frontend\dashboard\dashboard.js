// Dashboard JavaScript

// Make the notification function globally available
window.showNotification = function(title, message, type = 'info', duration = 5000) {
  console.log(`UI NOTIFICATION to be displayed [${type}]: ${title} - ${message}`);

  const container = document.getElementById('notification-container');
  if (!container) {
    console.error('Notification container #notification-container not found.');
    return;
  }

  const notificationId = 'notification-' + Date.now();
  const messageBar = document.createElement('div');
  messageBar.id = notificationId;
  messageBar.classList.add('ms-MessageBar');

  let iconClass = 'ms-Icon--Info';
  let barClass = ''; // Corresponds to ms-MessageBar--info, ms-MessageBar--success etc.

  switch (type.toLowerCase()) {
    case 'success':
      barClass = 'ms-MessageBar--success';
      iconClass = 'ms-Icon--Completed';
      break;
    case 'error':
      barClass = 'ms-MessageBar--error';
      iconClass = 'ms-Icon--ErrorBadge';
      break;
    case 'warning':
      barClass = 'ms-MessageBar--warning';
      iconClass = 'ms-Icon--Info'; // Or ms-Icon--Warning
      break;
    case 'info':
    default:
      barClass = 'ms-MessageBar--info'; // Default or 'info'
      iconClass = 'ms-Icon--Info';
      break;
  }
  if (barClass) {
    messageBar.classList.add(barClass);
  }

  messageBar.innerHTML = `
    <div class="ms-MessageBar-content">
      <div class="ms-MessageBar-icon">
        <i class="ms-Icon ${iconClass}"></i>
      </div>
      <div class="ms-MessageBar-text">
        <span class="ms-fontWeight-semibold">${title}</span><br>
        ${message}
      </div>
    </div>
    <div class="ms-MessageBar-actions">
        <button class="ms-MessageBar-dismiss ms-Button ms-Button--icon" onclick="document.getElementById('${notificationId}').remove()">
            <i class="ms-Icon ms-Icon--Clear"></i>
        </button>
    </div>
  `;

  // Prepend to show newest on top
  if (container.firstChild) {
    container.insertBefore(messageBar, container.firstChild);
  } else {
    container.appendChild(messageBar);
  }

  // Auto-dismiss
  setTimeout(() => {
    const el = document.getElementById(notificationId);
    if (el) {
      // Add a fade-out effect before removing
      el.style.transition = 'opacity 0.5s ease-out';
      el.style.opacity = '0';
      setTimeout(() => el.remove(), 500); // Remove after fade
    }
  }, duration);
};

// Function to show login modal
function showLoginModal() {
  const loginModal = document.getElementById('login-modal');
  const loginButton = document.getElementById('login-button'); // Ensure this is the header button
  const logoutButton = document.getElementById('logout-button');
  const userDisplay = document.getElementById('user-display');
  
  if (loginModal) {
    loginModal.style.display = 'block';
    loginModal.classList.add('active');
    
    // Focus on the username field
    const usernameField = document.getElementById('username');
    if (usernameField) {
      usernameField.focus();
    }
  }
  
  // When showing login modal, ensure header reflects a pre-login or modal-active state
  if (loginButton) {
    loginButton.style.display = 'none'; // Hide header login button as modal is the login prompt
  }
  if (logoutButton) {
    logoutButton.style.display = 'none';
  }
  if (userDisplay) {
    userDisplay.textContent = 'Not logged in';
  }
}

// Function to handle successful login
function handleSuccessfulLogin(username) {
  const loginButton = document.getElementById('login-button');
  const logoutButton = document.getElementById('logout-button');
  const userDisplay = document.getElementById('user-display');
  const loginModal = document.getElementById('login-modal');
  
  // Update button visibility
  if (loginButton) {
    loginButton.style.display = 'none';
  }
  if (logoutButton) {
    logoutButton.style.display = 'inline-block';
  }
  if (userDisplay) {
    userDisplay.textContent = `Logged in as: ${username}`;
  }
  if (loginModal) {
    loginModal.style.display = 'none';
    loginModal.classList.remove('active');
  }
}

// Function to handle logout
function handleLogout() {
  if (window.apiService && typeof window.apiService.clearCredentials === 'function') {
    window.apiService.clearCredentials();
  }
  
  // Update UI to logged-out state
  const loginButton = document.getElementById('login-button');
  const logoutButton = document.getElementById('logout-button');
  const userDisplay = document.getElementById('user-display');
  
  if (loginButton) {
    loginButton.style.display = 'inline-block';
  }
  if (logoutButton) {
    logoutButton.style.display = 'none';
  }
  if (userDisplay) {
    userDisplay.textContent = 'Not logged in';
  }
  
  // Clear dashboard data from localStorage on logout
  localStorage.removeItem(config.storageKey);
  console.log('Dashboard data cleared from localStorage');
  
  // Show login modal
  showLoginModal();
}

// Configuration
const config = {
    n8nReportingEndpoint: 'http://localhost:5678/webhook/test-results',
    refreshInterval: 30000, // 30 seconds
    storageKey: 'smarttest_dashboard_data' // Storage key for dashboard data
};

// DOM Elements
const elements = {
    totalTests: document.getElementById('total-tests'),
    successfulTests: document.getElementById('successful-tests'),
    failedTests: document.getElementById('failed-tests'),
    runningTests: document.getElementById('running-tests'),
    recentTestsTable: document.getElementById('recent-tests-table'),
    refreshBtn: document.getElementById('refresh-btn'),
    environmentDisplay: document.getElementById('environment-display'),
    testResultsChart: document.getElementById('test-results-chart'),
    testDurationChart: document.getElementById('test-duration-chart'),
    predefinedSuitesContainer: document.getElementById('predefined-suites-container'),
    customSuiteBuilder: document.getElementById('available-testcases'),
    loginButton: document.getElementById('login-button'), // Corrected ID to match HTML
    suiteProjectFilter: document.getElementById('suite-project-filter'),
    suiteLevelFilter: document.getElementById('suite-level-filter'),
    suiteVersionFilter: document.getElementById('suite-version-filter'),
    refreshSuitesBtn: document.getElementById('refresh-suites'),
    runSelectedSuiteBtn: document.getElementById('run-selected-suite'),
    availableTestSuites: document.getElementById('available-testsuites'),
    activeTestsFilterButtons: document.querySelectorAll('.active-tests-filter-btn')
};

// Charts
let resultsChart;
let durationChart;

// Data
let testData = {
    recent: [],
    summary: {
        total: 0,
        successful: 0,
        failed: 0,
        running: 0
    }
};

// Process test data from API response
function processTestData(data) {
    // Update test summary
    testData.summary = data.summary || {
        total: 0,
        successful: 0,
        failed: 0,
        running: 0
    };
    
    // Update recent tests
    testData.recent = data.recent || [];
    
    // Update environment display
    if (data.environment) {
        elements.environmentDisplay.textContent = `Environment: ${data.environment}`;
    }
    
    // Save to localStorage
    saveDashboardData();
    
    // Update dashboard UI
    updateDashboard();
}

// Save dashboard data to localStorage
function saveDashboardData() {
    try {
        const dataToSave = {
            summary: testData.summary,
            recent: testData.recent.slice(0, 10), // Limit recent data for storage efficiency
            timestamp: new Date().toISOString()
        };
        
        localStorage.setItem(config.storageKey, JSON.stringify(dataToSave));
        console.log('Dashboard data saved to localStorage');
    } catch (error) {
        console.error('Error saving to localStorage:', error);
    }
}

// Load dashboard data from localStorage
function loadDashboardData() {
    try {
        const savedData = localStorage.getItem(config.storageKey);
        if (savedData) {
            const data = JSON.parse(savedData);
            
            // Check if data is fresh (less than 24 hours old)
            const timestamp = new Date(data.timestamp);
            const now = new Date();
            const isDataFresh = (now - timestamp) < (24 * 60 * 60 * 1000); // 24 hours
            
            if (isDataFresh) {
                console.log('Loading dashboard data from localStorage');
                
                if (data.summary) {
                    testData.summary = data.summary;
                }
                
                if (data.recent && data.recent.length > 0) {
                    testData.recent = data.recent;
                }
                
                return true;
            } else {
                console.log('Stored dashboard data is outdated, not using it');
                return false;
            }
        }
    } catch (error) {
        console.error('Error loading from localStorage:', error);
    }
    return false;
}

// New function to centralize UI updates based on login state
function updateLoginUI() {
    if (window.apiService && window.apiService.credentials && window.apiService.credentials.uid && window.apiService.credentials.uid !== '') {
        handleSuccessfulLogin(window.apiService.credentials.uid);
    } else {
        // This implies user is not logged in or credentials not fully loaded/cleared
        // If loginModal is already active (e.g. user clicked login), let it be
        // Otherwise, ensure header reflects a default logged-out state
        const loginModal = document.getElementById('login-modal');
        if (loginModal && loginModal.style.display === 'block' && loginModal.classList.contains('active')) {
            // Modal is active, showLoginModal() should have set button states correctly for modal interaction
            // Specifically, header login button is hidden when modal is up
        } else {
            const loginButton = document.getElementById('login-button');
            const logoutButton = document.getElementById('logout-button');
            const userDisplay = document.getElementById('user-display');
            
            if (loginButton) loginButton.style.display = 'inline-block';
            if (logoutButton) logoutButton.style.display = 'none';
            if (userDisplay) userDisplay.textContent = 'Not logged in';
        }
    }
}

// New function to encapsulate API integration initialization and subsequent actions.
// This function is now intended to be called ONLY by the 'apiservice-ready' event handler.
async function initializeApiIntegrationAndData() {
    console.log('Event apiservice-ready received. Starting main dashboard initialization.');
    // apiService should be guaranteed by the 'apiservice-ready' event.
    if (!window.apiService) {
        console.error('CRITICAL: initializeApiIntegrationAndData called but apiService is not available.');
        updateLoginUI(); // Update UI to reflect potential error state
        return;
    }

    if (typeof DashboardApiIntegration === 'undefined') {
        console.error('DashboardApiIntegration class not found. Cannot initialize.');
        updateLoginUI(); // Update UI to reflect potential error state
        return;
    }

    // Create instance if it doesn't exist
    if (!window.dashboardApiIntegration) {
        console.log('Creating new DashboardApiIntegration instance as it does not exist.');
        window.dashboardApiIntegration = new DashboardApiIntegration();
    }

    console.log('Attempting to initialize DashboardApiIntegration...');
    try {
        const success = await window.dashboardApiIntegration.initialize(); // This is an async function
        
        if (success) {
            console.info('DashboardApiIntegration initialized successfully. Setting up UI components.');
            
            // Set up main dashboard UI if elements exist
            if (elements.refreshBtn) {
                elements.refreshBtn.addEventListener('click', refreshDashboard);
                console.log('Main dashboard UI elements initialized');
            } else {
                console.warn('Some main dashboard elements not found, but continuing initialization');
            }
            
            // Setup active test filters if they exist
            if (elements.activeTestsFilterButtons) {
                elements.activeTestsFilterButtons.forEach(button => {
                    button.addEventListener('click', function() {
                        const filterValue = this.dataset.filter;
                        elements.activeTestsFilterButtons.forEach(btn => btn.classList.remove('active'));
                        this.classList.add('active');
                        // TODO: Implement filtering logic based on filterValue
                    });
                });
            }
            
            setupTestSuiteSelectionHandlers();
            updateDashboard(); // Initial render with loaded data

            // Listen for authentication state changes
            document.addEventListener('auth-state-changed', function(event) {
                if (event.detail.loggedIn) {
                    console.log('User logged in, refreshing dashboard data');
                    // Refresh test suites after login
                    fetchFilteredTestSuites();
                    // Refresh other dashboard data
                    updateDashboard();
                }
            });

            // Automatic refreshing disabled as requested
            // setInterval(refreshDashboard, config.refreshInterval);

        } else {
            console.warn('DashboardApiIntegration.initialize() reported failure. Dashboard may be incomplete.');
        }
    } catch (error) {
        console.error('Error occurred during call to DashboardApiIntegration.initialize():', error);
    } finally {
        // Always update UI and handle URL params after an initialization attempt.
        updateLoginUI();
        handleUrlParameters();

        // Ensure loading spinner is hidden even if initialization fails
        setTimeout(() => {
            const loadingIndicator = document.getElementById('loading-indicator');
            if (loadingIndicator) {
                loadingIndicator.style.display = 'none';
            }
        }, 2000);
    }
}

// Initialize dashboard - This function now only sets up the event listener.
function initDashboard() {
    console.log("Dashboard script loaded. Setting up 'apiservice-ready' listener.");
    document.addEventListener('apiservice-ready', initializeApiIntegrationAndData);
}

// Update dashboard with current data
function updateDashboard() {
    try {
        // Update summary statistics with null checks
        if (elements.totalTests) elements.totalTests.textContent = testData.summary.total;
        if (elements.successfulTests) elements.successfulTests.textContent = testData.summary.successful;
        if (elements.failedTests) elements.failedTests.textContent = testData.summary.failed;
        if (elements.runningTests) elements.runningTests.textContent = testData.summary.running;
        
        // Update recent tests table (now a stubbed function that safely handles missing elements)
        updateRecentTestsTable();
        
        // Update charts only if both charts and DOM elements exist
        if (typeof resultsChart !== 'undefined' && typeof durationChart !== 'undefined' && 
            typeof updateCharts === 'function') {
            // The chart initialization is now guarded to prevent errors
            updateCharts();
        }
        
        // Save current data to localStorage
        saveDashboardData();
    } catch (error) {
        console.warn('Error updating dashboard:', error);
    }
}

// Handle URL parameters for specific actions
function handleUrlParameters() {
    // Get URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const testId = urlParams.get('testId');
    const rerunFailed = urlParams.get('rerunFailed');
    
    // If specific test ID is provided, focus on that test
    if (testId) {
        fetchSpecificTestData(testId);
    }
    
    // If rerunFailed parameter is provided, rerun failed tests from that test run
    if (rerunFailed && window.dashboardApiIntegration) {
        window.dashboardApiIntegration.rerunFailedTests(rerunFailed)
            .then(newTestId => {
                if (newTestId) {
                    // Update URL without reloading the page
                    const newUrl = new URL(window.location.href);
                    newUrl.searchParams.delete('rerunFailed');
                    newUrl.searchParams.set('testId', newTestId);
                    window.history.pushState({}, '', newUrl);
                    
                    // Focus on the new test
                    fetchSpecificTestData(newTestId);
                }
            })
            .catch(error => {
                console.error('Error handling rerunFailed parameter:', error);
                showNotification('Error', `Failed to rerun tests: ${error.message}`, 'error');
            });
    }
}

// Refresh dashboard data
function refreshDashboard() {
    // If API integration is available, use it
    if (window.dashboardApiIntegration && window.dashboardApiIntegration.loadDashboardData) {
        // Show loading indicator
        showLoading('Refreshing dashboard data...');
        
        // Refresh data through API integration
        window.dashboardApiIntegration.loadDashboardData().then(() => {
            hideLoading();
        }).catch(error => {
            console.error('Error refreshing dashboard data:', error);
            hideLoading();
        });
    } else {
        // Otherwise refresh from n8n endpoint (or use mock data)
        fetchDashboardData();
    }
}

// Show loading indicator
function showLoading(message = 'Loading...') {
    const loadingIndicator = document.getElementById('loading-indicator');
    if (loadingIndicator) {
        const loadingMessage = loadingIndicator.querySelector('.ms-loading-message');
        if (loadingMessage) {
            loadingMessage.textContent = message;
        }
        loadingIndicator.style.display = 'flex';
    }
}

// Hide loading indicator
function hideLoading() {
    const loadingIndicator = document.getElementById('loading-indicator');
    if (loadingIndicator) {
        loadingIndicator.style.display = 'none';
    }
}

// Initialize charts - DEPRECATED (Test Results Overview section has been removed from UI)
function initCharts() {
    console.debug('initCharts called but chart elements have been removed from UI - skipping chart initialization');
    
    // Check if chart elements or elements object exists before attempting to initialize
    if (!elements || !elements.testResultsChart || !elements.testDurationChart) {
        console.log('Chart elements not found - Test Results Overview section has been removed from UI');
        return;
    }
    
    // If we somehow get here with elements defined, the original initialization would happen below
    // But this should never happen since the elements have been removed from the UI
}

// Fetch dashboard data from n8n
async function fetchDashboardData() {
    try {
        const response = await fetch(config.n8nReportingEndpoint);
        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }
        
        const data = await response.json();
        processTestData(data);
        updateDashboard();
    } catch (error) {
        console.error('Error fetching dashboard data:', error);
        // Optionally show an error message on the dashboard
    }
}

// Fetch data for a specific test
async function fetchSpecificTestData(testId) {
    try {
        const response = await fetch(`${config.n8nReportingEndpoint}?testId=${testId}`);
        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }
        
        const data = await response.json();
        // Process and display the specific test data
        if (data.test) {
            // Add to recent tests if not already there
            if (!testData.recent.some(test => test.id === data.test.id)) {
                testData.recent.unshift(data.test);
                // Keep only the most recent 10 tests
                testData.recent = testData.recent.slice(0, 10);
            }
            updateDashboard();
            
            // Scroll to the test in the table
            highlightTest(testId);
        }
    } catch (error) {
        console.error('Error fetching specific test data:', error);
    }
}

// Update the recent tests table - DEPRECATED (Recent Test Executions section has been removed from UI)
function updateRecentTestsTable() {
    // This function is now a stub since the Recent Test Executions section has been removed from the dashboard UI
    console.debug('updateRecentTestsTable called but is deprecated - Recent Test Executions UI has been removed');
    
    // No operation needed as the elements no longer exist in the DOM
    // Keeping the function to maintain API compatibility with other parts of the code
}

// Update the charts with current data
function updateCharts() {
    // Update results chart
    resultsChart.data.datasets[0].data = [
        testData.summary.successful,
        testData.summary.failed,
        testData.summary.running
    ];
    resultsChart.update();
    
    // Process data for duration chart
    const testTypes = {};
    testData.recent.forEach(test => {
        if (test.duration && test.type) {
            if (!testTypes[test.type]) {
                testTypes[test.type] = [];
            }
            testTypes[test.type].push(test.duration);
        }
    });
    
    // Calculate average duration by type
    const labels = [];
    const durations = [];
    
    Object.entries(testTypes).forEach(([type, durationList]) => {
        const avgDuration = durationList.reduce((sum, duration) => sum + duration, 0) / durationList.length;
        labels.push(type);
        durations.push(avgDuration.toFixed(2));
    });
    
    // Update duration chart
    durationChart.data.labels = labels;
    durationChart.data.datasets[0].data = durations;
    durationChart.update();
}

// Highlight a specific test in the table
function highlightTest(testId) {
    const testRow = document.getElementById(`test-${testId}`);
    if (testRow) {
        // Scroll to the row
        testRow.scrollIntoView({ behavior: 'smooth', block: 'center' });
        
        // Highlight the row
        testRow.classList.add('ms-row-highlight');
        
        // Remove highlight after a few seconds
        setTimeout(() => {
            testRow.classList.remove('ms-row-highlight');
        }, 3000);
    }
}

// New function to set up test suite selection handlers
function setupTestSuiteSelectionHandlers() {
    // Check if the elements exist (they might not in some parts of the app)
    if (!elements.suiteProjectFilter || !elements.suiteLevelFilter || 
        !elements.suiteVersionFilter || !elements.refreshSuitesBtn || 
        !elements.runSelectedSuiteBtn) {
        console.warn('Test suite selection elements not found in DOM');
        return;
    }

    // Add change event listeners to filters
    elements.suiteProjectFilter.addEventListener('change', fetchFilteredTestSuites);
    elements.suiteLevelFilter.addEventListener('change', fetchFilteredTestSuites);
    elements.suiteVersionFilter.addEventListener('change', fetchFilteredTestSuites);
    elements.refreshSuitesBtn.addEventListener('click', fetchFilteredTestSuites);
    
    // Initial fetch of test suites
    fetchFilteredTestSuites();
    
    console.log('Test suite selection handlers initialized');
}

// Fetch test suites filtered by selected criteria
async function fetchFilteredTestSuites() {
    try {
        if (!elements.availableTestSuites) {
            console.error('Available test suites container not found');
            return;
        }
        
        // Check if user is authenticated before making API calls
        if (window.unifiedAuthClient && !window.unifiedAuthClient.isAuthenticated) {
            console.log('User not authenticated, skipping test suites fetch');
            elements.availableTestSuites.innerHTML =
                '<div class="ms-empty-message">Please log in to view test suites</div>';
            return;
        }

        // Show loading state
        elements.availableTestSuites.innerHTML = '<div class="ms-empty-message">Loading test suites...</div>';

        // Check if API service is available
        if (!window.apiService) {
            console.error('API service not available');
            elements.availableTestSuites.innerHTML = '<div class="ms-empty-message">Error: API service not available</div>';
            return;
        }
        
        // Get filter values
        const filters = {
            project: elements.suiteProjectFilter.value,
            level: elements.suiteLevelFilter.value,
            version: elements.suiteVersionFilter.value
        };
        
        console.log('Fetching test suites with filters:', filters);
        
        // Fetch filtered test suites from API
        const testSuites = await window.apiService.getTestSuites(filters);
        
        // Update UI with results
        renderTestSuites(testSuites);
    } catch (error) {
        console.error('Error fetching test suites:', error);
        elements.availableTestSuites.innerHTML = 
            `<div class="ms-empty-message">Error loading test suites: ${error.message}</div>`;
    }
}

// Render test suites in the selection list
function renderTestSuites(testSuites) {
    if (!elements.availableTestSuites) {
        console.error('Available test suites container not found');
        return;
    }
    
    // Clear existing content
    elements.availableTestSuites.innerHTML = '';
    
    // Check if we have any test suites
    if (!testSuites || testSuites.length === 0) {
        elements.availableTestSuites.innerHTML = '<div class="ms-empty-message">No test suites found matching the selected criteria</div>';
        return;
    }
    
    // Create a container for the test suite cards
    const cardsContainer = document.createElement('div');
    cardsContainer.className = 'test-suite-cards-container';
    
    // Track selected suites
    const selectedSuites = new Set();
    
    // Create cards for each test suite
    testSuites.forEach(suite => {
        const cardElement = document.createElement('div');
        cardElement.className = 'test-suite-card';
        cardElement.dataset.suiteId = suite.ts_id || suite.id;
        cardElement.dataset.suiteName = suite.name;
        
        // Build the card HTML structure
        cardElement.innerHTML = `
            <div class="card-header">
                <div class="card-title">${suite.name}</div>
                <div class="card-select-indicator">
                    <i class="fas fa-check-circle"></i>
                </div>
            </div>
            <div class="card-body">
                <div class="card-details">
                    <div class="detail-item">
                        <span class="detail-label">Level:</span>
                        <span class="detail-value">${suite.level || 'N/A'}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Version:</span>
                        <span class="detail-value">${suite.version || 'N/A'}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">ID:</span>
                        <span class="detail-value">${suite.ts_id || suite.id || 'N/A'}</span>
                    </div>
                </div>
                ${suite.description ? `<div class="card-description">${suite.description}</div>` : ''}
                ${suite.url ? `<div class="card-url"><a href="${suite.url}" target="_blank" rel="noopener">${suite.url}</a></div>` : ''}
            </div>
            <div class="card-actions">
                <button class="btn btn-sm btn-outline-primary run-single-suite" data-suite-id="${suite.ts_id || suite.id}">
                    <i class="fas fa-play"></i> Run Suite
                </button>
            </div>
        `;
        
        // Add click event for card selection
        cardElement.addEventListener('click', function(e) {
            // Don't trigger card selection if clicking on buttons or links
            if (e.target.closest('.card-actions') || e.target.closest('a')) {
                return;
            }
            
            const suiteId = this.dataset.suiteId;
            
            // Toggle selection
            if (selectedSuites.has(suiteId)) {
                selectedSuites.delete(suiteId);
                this.classList.remove('selected');
            } else {
                selectedSuites.add(suiteId);
                this.classList.add('selected');
            }
            
            // Update the run selected suites button
            updateRunSelectedButton(selectedSuites, testSuites);
        });
        
        // Add event listener for individual run button
        const runButton = cardElement.querySelector('.run-single-suite');
        runButton.addEventListener('click', async function(e) {
            e.stopPropagation(); // Prevent card selection
            
            const suiteId = this.dataset.suiteId;
            const suiteName = cardElement.dataset.suiteName;
            
            await runTestSuite(suiteId, suiteName);
        });
        
        cardsContainer.appendChild(cardElement);
    });
    
    elements.availableTestSuites.appendChild(cardsContainer);
    
    // Initialize the run selected button state
    updateRunSelectedButton(selectedSuites, testSuites);
    
    // Add event listener for the run selected suites button (only once)
    if (!elements.runSelectedSuiteBtn.hasAttribute('data-listener-added')) {
        elements.runSelectedSuiteBtn.setAttribute('data-listener-added', 'true');
        elements.runSelectedSuiteBtn.addEventListener('click', async function() {
            if (this.disabled) return;
            
            const selectedSuiteIds = Array.from(selectedSuites);
            if (selectedSuiteIds.length === 0) {
                showNotification('Error', 'No test suites selected', 'error');
                return;
            }
            
            // Run multiple test suites
            await runMultipleTestSuites(selectedSuiteIds, testSuites);
        });
    }
}

// Helper function to update the run selected button state
function updateRunSelectedButton(selectedSuites, testSuites) {
    const button = elements.runSelectedSuiteBtn;
    const count = selectedSuites.size;
    
    button.disabled = count === 0;
    
    if (count === 0) {
        button.textContent = 'Run Selected Suites';
        button.title = 'Select test suites to run';
    } else if (count === 1) {
        const selectedId = Array.from(selectedSuites)[0];
        const suite = testSuites.find(s => (s.ts_id || s.id) == selectedId);
        button.textContent = `Run Selected Suite (${suite?.name || selectedId})`;
        button.title = `Run ${suite?.name || selectedId}`;
    } else {
        button.textContent = `Run Selected Suites (${count})`;
        button.title = `Run ${count} selected test suites`;
    }
}

// Helper function to run a single test suite
async function runTestSuite(suiteId, suiteName) {
    if (!suiteId) {
        showNotification('Error', 'Invalid test suite ID', 'error');
        return;
    }
    
    try {
        showLoading(`Running test suite ${suiteName}...`);
        
        // Run test suite via API service
        const result = await window.apiService.runTestSuite(suiteId);
        
        hideLoading();
        
        if (result && result.success) {
            showNotification('Success', `Started test suite ${suiteName}`, 'success');
            
            // Refresh dashboard after short delay to show the new run
            setTimeout(refreshDashboard, 2000);
        } else {
            showNotification('Error', `Failed to run test suite ${suiteName}`, 'error');
        }
    } catch (error) {
        hideLoading();
        showNotification('Error', `Failed to run test suite: ${error.message}`, 'error');
    }
}

// Helper function to run multiple test suites
async function runMultipleTestSuites(selectedSuiteIds, testSuites) {
    if (!selectedSuiteIds || selectedSuiteIds.length === 0) {
        showNotification('Error', 'No test suites selected', 'error');
        return;
    }
    
    try {
        showLoading(`Running ${selectedSuiteIds.length} test suites...`);
        
        const results = [];
        let successCount = 0;
        let failureCount = 0;
        
        // Run test suites sequentially to avoid overwhelming the system
        for (const suiteId of selectedSuiteIds) {
            const suite = testSuites.find(s => (s.ts_id || s.id) == suiteId);
            const suiteName = suite?.name || suiteId;
            
            try {
                const result = await window.apiService.runTestSuite(suiteId);
                
                if (result && result.success) {
                    results.push({ suiteId, suiteName, success: true, message: result.message });
                    successCount++;
                } else {
                    results.push({ suiteId, suiteName, success: false, message: 'Failed to start' });
                    failureCount++;
                }
                
                // Small delay between runs
                await new Promise(resolve => setTimeout(resolve, 500));
            } catch (error) {
                results.push({ suiteId, suiteName, success: false, message: error.message });
                failureCount++;
            }
        }
        
        hideLoading();
        
        // Show summary notification
        if (failureCount === 0) {
            showNotification('Success', `Successfully started all ${successCount} test suites`, 'success');
        } else if (successCount === 0) {
            showNotification('Error', `Failed to start all ${failureCount} test suites`, 'error');
        } else {
            showNotification('Warning', `Started ${successCount} test suites, ${failureCount} failed`, 'warning');
        }
        
        // Log detailed results
        console.log('Test suite run results:', results);
        
        // Refresh dashboard after short delay to show the new runs
        setTimeout(refreshDashboard, 3000);
        
    } catch (error) {
        hideLoading();
        showNotification('Error', `Failed to run test suites: ${error.message}`, 'error');
    }
}
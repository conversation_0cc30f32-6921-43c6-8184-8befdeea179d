# SmartTest Testing Suite

This directory contains a comprehensive collection of test scripts for the SmartTest application, focused on testing database connections, API interactions, and complete test workflows.

## Directory Structure

The tests are organized into the following directories:

```
tests/
├── api-tests/       # API testing scripts
├── db-tests/        # Database connection and schema tests
├── utils/           # Shared utilities like database connector
└── workflow-tests/  # End-to-end workflow tests
```

## Setting Up

1. Install dependencies:

```bash
cd tests
npm install
```

## Available Tests

### Database Tests

These scripts verify database connections and schemas.

* **verify_connection.js** - Tests SSH and database connections to different environments
  ```
  node db-tests/verify_connection.js [environment] [test_type]
  ```
  Example: `node db-tests/verify_connection.js qa02 all`

* **verify_schema.js** - Verifies the database schema structure required by the application
  ```
  node db-tests/verify_schema.js [environment]
  ```
  Example: `node db-tests/verify_schema.js qa02`

### API Tests

These scripts test the API endpoints and demonstrate how to correctly format API requests.

* **api_test.js** - Tests different approaches to calling the API and identifies correct formatting
  ```
  node api-tests/api_test.js [environment] [testCaseId] [username]
  ```
  Example: `node api-tests/api_test.js qa02 3180 <EMAIL>`

### Workflow Tests

These scripts provide end-to-end testing of the complete test execution workflow.

* **workflow_test.js** - Executes a test through the API and monitors its progress to completion
  ```
  node workflow-tests/workflow_test.js [environment] [testCaseId] [username] [timeoutMinutes]
  ```
  Example: `node workflow-tests/workflow_test.js qa02 3180 <EMAIL> 10`

* **monitor_test.js** - Monitors an already running test execution
  ```
  node workflow-tests/monitor_test.js [tsnId] [environment] [timeoutMinutes]
  ```
  Example: `node workflow-tests/monitor_test.js 12345 qa02 10`

## Environment Configuration

The testing scripts support multiple test environments (QA01, QA02, QA03). The environment configuration is managed by the shared `db-connector.js` utility, which handles:

- SSH connections
- Database queries
- Connection error handling and recovery
- Environment-specific settings

## Common Troubleshooting

### SSH Connection Issues

1. Ensure your SSH key is properly set up (default path: `~/.ssh/id_rsa_dbserver`)
2. Verify network connectivity to the target server
3. Check that you have the correct permissions for the SSH key

### API Call Failures

1. The most common issue is the double-ampersand (&&) formatting in the API request.
   Make sure to use the exact string format: `param1=value1&param2=value2&&param3=value3`.
2. Don't use URLSearchParams or other encoding methods that might encode the double-ampersand.
3. Verify you're using the correct environment URL.

### Database Query Errors

1. Verify table and column names match the required schema
2. Check that you have sufficient permissions for the database user
3. Look for database connection timeouts or dropped connections

## Best Practices

1. Always start by verifying your connection with `verify_connection.js`
2. Use the complete workflow test (`workflow_test.js`) when you need to test the entire process
3. Use `monitor_test.js` if you need to monitor a test that was initiated outside of the testing suite
4. Check the test database schema before making assumptions about column names 
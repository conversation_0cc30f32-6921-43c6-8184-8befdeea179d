{"name": "smarttest-tests", "version": "1.0.0", "description": "Test suite for SmartTest application", "main": "index.js", "scripts": {"test": "jest", "test:api": "node api-tests/api_test.js", "test:connection": "node db-tests/verify_connection.js", "test:schema": "node db-tests/verify_schema.js", "test:workflow": "node workflow-tests/workflow_test.js", "test:monitor": "node workflow-tests/monitor_test.js", "test:qa01": "node workflow-tests/workflow_test.js qa01", "test:qa02": "node workflow-tests/workflow_test.js qa02", "test:qa03": "node workflow-tests/workflow_test.js qa03"}, "author": "", "license": "ISC", "dependencies": {"axios": "^0.24.0", "mysql2": "^2.3.3", "ssh2": "^1.10.0"}, "devDependencies": {"jest": "^29.7.0"}}
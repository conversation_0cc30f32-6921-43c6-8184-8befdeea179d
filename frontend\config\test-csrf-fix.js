/**
 * Test script to verify CSRF fix for config page
 * This script simulates the test runner request to verify CSRF token handling
 */

// Test CSRF token handling
async function testCSRFTokenHandling() {
  console.log('🧪 Testing CSRF Token Handling for Config Page');
  console.log('='.repeat(50));

  try {
    // Step 1: Get CSRF token
    console.log('1️⃣ Getting CSRF token...');
    const tokenResponse = await fetch('/csrf-token', {
      method: 'GET',
      credentials: 'include'
    });

    if (!tokenResponse.ok) {
      throw new Error(`Failed to get CSRF token: ${tokenResponse.status}`);
    }

    const tokenData = await tokenResponse.json();
    console.log(`✅ CSRF token received: ${tokenData.csrfToken.substring(0, 8)}...`);

    // Step 2: Test case runner with CSRF token
    console.log('\n2️⃣ Testing case runner with CSRF token...');
    const testParams = {
      tc_id: '2805',
      envir: 'qa02',
      shell_host: 'jps-qa10-app01',
      uid: '<EMAIL>',
      password: 'test'
    };

    const runResponse = await fetch('/api/case-runner', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': tokenData.csrfToken
      },
      credentials: 'include',
      body: JSON.stringify(testParams)
    });

    console.log(`📊 Response status: ${runResponse.status}`);

    if (runResponse.ok) {
      const runData = await runResponse.json();
      console.log('✅ Test case runner request successful!');
      console.log('📋 Response:', JSON.stringify(runData, null, 2));
    } else {
      const errorText = await runResponse.text();
      console.log('❌ Test case runner request failed:');
      console.log('📋 Error:', errorText);
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Test CSRF retry mechanism
async function testCSRFRetryMechanism() {
  console.log('\n🔄 Testing CSRF Retry Mechanism');
  console.log('='.repeat(50));

  try {
    // Step 1: Make request with invalid CSRF token
    console.log('1️⃣ Testing with invalid CSRF token...');
    const testParams = {
      tc_id: '2805',
      envir: 'qa02',
      shell_host: 'jps-qa10-app01'
    };

    const invalidResponse = await fetch('/api/case-runner', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': 'invalid-token-12345'
      },
      credentials: 'include',
      body: JSON.stringify(testParams)
    });

    console.log(`📊 Response status: ${invalidResponse.status}`);

    if (invalidResponse.status === 403) {
      const errorData = await invalidResponse.json();
      console.log('✅ CSRF validation correctly rejected invalid token');
      console.log('📋 Error response:', JSON.stringify(errorData, null, 2));
    } else {
      console.log('⚠️ Unexpected response for invalid token');
    }

  } catch (error) {
    console.error('❌ Retry test failed:', error);
  }
}

// Main test execution
async function runTests() {
  console.log('🚀 Starting CSRF Fix Tests\n');
  
  await testCSRFTokenHandling();
  await testCSRFRetryMechanism();
  
  console.log('\n🏁 Tests completed!');
  console.log('\n💡 Next steps:');
  console.log('1. Try running a test from the config page');
  console.log('2. Check the browser console for CSRF retry messages');
  console.log('3. Verify the terminal logs show successful CSRF validation');
}

// Export for use in browser console
if (typeof window !== 'undefined') {
  window.testCSRFFix = {
    runTests,
    testCSRFTokenHandling,
    testCSRFRetryMechanism
  };
  
  console.log('🔧 CSRF test functions available:');
  console.log('- window.testCSRFFix.runTests()');
  console.log('- window.testCSRFFix.testCSRFTokenHandling()');
  console.log('- window.testCSRFFix.testCSRFRetryMechanism()');
}

// Run tests if executed directly
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { runTests, testCSRFTokenHandling, testCSRFRetryMechanism };

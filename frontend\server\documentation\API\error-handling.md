# SmartTest API Error Handling

This document describes the error handling mechanism used by the SmartTest API server.

## Error Handling Middleware

The SmartTest API server uses a centralized error handling middleware (`middleware/error-handler.js`) to handle all errors that occur during request processing. This middleware catches errors thrown by route handlers and returns appropriate error responses to the client.

## Error Response Format

All error responses follow a consistent format:

```json
{
  "success": false,
  "message": "Error message",
  "error": "Detailed error information (optional)"
}
```

## HTTP Status Codes

The SmartTest API server uses the following HTTP status codes for error responses:

- **400 Bad Request**: The request was malformed or contained invalid parameters
- **401 Unauthorized**: Authentication failed or was not provided
- **403 Forbidden**: The authenticated user does not have permission to access the requested resource
- **404 Not Found**: The requested resource was not found
- **500 Internal Server Error**: An unexpected error occurred on the server

## Common Error Types

### Authentication Errors

Authentication errors occur when the client provides invalid or missing authentication credentials.

```json
{
  "success": false,
  "message": "Authentication failed. Invalid credentials."
}
```

```json
{
  "success": false,
  "message": "Authentication failed. Please provide uid and password."
}
```

### Validation Errors

Validation errors occur when the client provides invalid or missing parameters.

```json
{
  "success": false,
  "message": "Missing required parameter: tc_id or ts_id"
}
```

```json
{
  "success": false,
  "message": "Status must be one of: created, running, completed, failed, cancelled"
}
```

### Resource Not Found Errors

Resource not found errors occur when the client requests a resource that does not exist.

```json
{
  "success": false,
  "message": "Test session not found"
}
```

### Database Errors

Database errors occur when there is a problem with the database connection or query.

```json
{
  "success": false,
  "message": "Database service unavailable"
}
```

### External API Errors

External API errors occur when there is a problem with an external API that the SmartTest API server depends on.

```json
{
  "success": false,
  "message": "Error forwarding request to external API: Connection refused"
}
```

### Internal Server Errors

Internal server errors occur when there is an unexpected error on the server.

```json
{
  "success": false,
  "message": "Internal server error"
}
```

## Error Handling in Route Handlers

Route handlers use try-catch blocks to catch errors and pass them to the error handling middleware:

```javascript
app.get('/example', async (req, res, next) => {
  try {
    // Route handler logic
    res.json({ success: true, data: result });
  } catch (error) {
    // Pass the error to the error handling middleware
    next(error);
  }
});
```

## Error Handling in Services

Services throw errors that are caught by route handlers:

```javascript
async function exampleService() {
  // Service logic
  if (error) {
    throw new Error('Service error');
  }
  return result;
}
```

## Error Logging

All errors are logged to the console using `console.error`:

```javascript
const errorHandler = (err, req, res, next) => {
  console.error('API Error:', err);
  // Error handling logic
};
```

## Database Connection Errors

Database connection errors are handled specially:

```javascript
if (err.code && (err.code.startsWith('ER_') || err.code.startsWith('ECONNREFUSED'))) {
  return res.status(500).json({ 
    success: false, 
    message: 'Database service unavailable' 
  });
}
```

## Client-Side Error Handling

Clients should handle error responses by checking the `success` field in the response:

```javascript
fetch('/api/example')
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      // Handle successful response
    } else {
      // Handle error response
      console.error(data.message);
    }
  })
  .catch(error => {
    // Handle network error
    console.error('Network error:', error);
  });
```

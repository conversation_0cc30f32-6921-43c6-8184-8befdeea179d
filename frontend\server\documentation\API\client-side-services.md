# Unified Client-Side Services

This document describes the unified client-side services used by the SmartTest application, which consolidates functionality from dashboard, reports, and config modules into a single, cohesive system.

## Overview

The SmartTest application now implements a **unified service architecture** that provides:

### Migration Benefits
- **Consolidated Services**: Single unified service replaces multiple module-specific services
- **Module Context Awareness**: Automatic behavior adaptation based on calling module
- **Shared Resources**: Common authentication, configuration, and caching across modules
- **Enhanced Features**: Improved error handling, performance monitoring, and reliability

### Backward Compatibility
- **Existing Code**: No changes required to existing frontend code
- **Same Interface**: Unified service preserves existing method signatures
- **Enhanced Capabilities**: Additional features available to all modules

## Unified Client-Side Services

### Unified API Service

The `UnifiedApiService` provides a consolidated interface for all API communication across dashboard, reports, and config modules. It replaces multiple module-specific services with a single, context-aware service.

**Location**: `frontend/shared/services/unified-api-service.js`

#### Enhanced Key Features

- **Module Context Awareness**: Automatic behavior adaptation based on calling module
- **Unified Authentication**: Shared authentication state across all modules
- **Enhanced Error Handling**: Detailed error context and recovery mechanisms
- **Performance Monitoring**: Built-in performance tracking and optimization
- **Shared Caching**: Intelligent caching across modules
- **Backward Compatibility**: Preserves existing method signatures

#### Module-Specific Initialization

Each module initializes the unified service with its specific context:

```javascript
// Dashboard Module (frontend/dashboard/api-service.js)
import { UnifiedApiService } from '../shared/services/unified-api-service.js';
const apiService = new UnifiedApiService();
apiService.moduleContext = 'dashboard';
apiService.initializeConfiguration();
window.apiService = apiService;

// Reports Module (frontend/reports/api-service.js)
import { UnifiedApiService } from '../shared/services/unified-api-service.js';
const apiService = new UnifiedApiService();
apiService.moduleContext = 'reports';
apiService.initializeConfiguration();
window.apiService = apiService;

// Config Module (similar pattern)
import { UnifiedApiService } from '../shared/services/unified-api-service.js';
const apiService = new UnifiedApiService();
apiService.moduleContext = 'config';
apiService.initializeConfiguration();
window.apiService = apiService;
```

#### Unified Implementation Overview

```javascript
class UnifiedApiService {
  constructor() {
    this.moduleContext = null; // Set during initialization
    this.baseUrl = 'http://localhost:9080';
    this.credentials = { uid: '', password: '' };
    this.requestId = null;
    this.performanceTracker = new PerformanceTracker();

    // Unified endpoints serving all modules
    this.endpoints = {
      // Local database endpoints
      testCases: '/local/test-cases',
      testSuites: '/local/test-suites',
      recentRuns: '/local/recent-runs',
      activeTests: '/local/active-tests',
      testDetails: '/local/test-details',

      // API endpoints
      caseRunner: '/api/case-runner',
      testStatus: '/api/test-status',
      testReports: '/api/test-reports',
      stopTest: '/api/stop-test'
    };
  }

  initializeConfiguration() {
    // Module-specific configuration initialization
    this.loadModuleConfiguration();
    this.setupPerformanceMonitoring();
    this.initializeErrorHandling();
  }

  setCredentials(username, password) {
    // Enhanced credential management with module context
    this.credentials = { uid: username, password: password };
    this.storeCredentials();
    this.logActivity('credentials_set');
  }

  async getRequest(endpoint, params = {}) {
    // Enhanced GET request with module context and performance monitoring
    const requestId = this.generateRequestId();
    const startTime = performance.now();

    try {
      const response = await this.makeRequest('GET', endpoint, params);
      this.logPerformance(endpoint, performance.now() - startTime);
      return response;
    } catch (error) {
      this.handleError(error, { endpoint, params, requestId });
      throw error;
    }
  }

  async getRecentRuns(options = {}) {
    // Module-aware recent runs retrieval
    const params = this.adaptParamsForModule(options);
    return await this.getRequest(this.endpoints.recentRuns, params);
  }

  async getTestDetails(tsnId) {
    // Enhanced test details with module context
    const endpoint = `${this.endpoints.testDetails}/${tsnId}`;
    return await this.getRequest(endpoint);
  }

  async getTestReports(params = {}) {
    // Module-specific test reports
    const adaptedParams = this.adaptParamsForModule(params);
    return await this.getRequest(this.endpoints.testReports, adaptedParams);
  }

  adaptParamsForModule(params) {
    // Adapt parameters based on module context
    const adapted = { ...params };

    switch (this.moduleContext) {
      case 'dashboard':
        adapted.includeActiveTests = true;
        break;
      case 'reports':
        adapted.includeDetailedMetrics = true;
        break;
      case 'config':
        adapted.includeSystemInfo = true;
        break;
    }

    return adapted;
  }
}
```

#### Enhanced Usage Examples

```javascript
// Module-aware usage (automatically adapts based on module context)

// Dashboard module usage
window.apiService.setCredentials('username', 'password');
const recentRuns = await window.apiService.getRecentRuns({ limit: 20 });
// Automatically includes active test information for dashboard

// Reports module usage
const reports = await window.apiService.getTestReports({
  limit: 25,
  timeRange: '7d'
});
// Automatically includes detailed metrics for reports

// Config module usage
const testCases = await window.apiService.getTestCases({ limit: 10 });
// Automatically includes system information for config

// Enhanced error handling with module context
try {
  const testDetails = await window.apiService.getTestDetails('12345');
} catch (error) {
  // Error includes module context and detailed information
  console.error('Error in', error.moduleContext, ':', error.message);
}
```

### External API Service

The `ExternalApiService` provides direct integration with external APIs on port 9080 for retrieving test reports and details using cookie-based authentication.

#### Key Features

- Cookie-based authentication with JSESSIONID
- HTML parsing to extract data from responses
- Automatic session renewal
- Error handling and fallbacks

#### Implementation

```javascript
class ExternalApiService {
  constructor() {
    this.baseUrl = 'http://mprts-qa02.lab.wagerworks.com:9080';
    this.jsessionId = null;
    this.jsessionExpiry = null;
    this.cookieExpiryTime = 25 * 60 * 1000; // 25 minutes
  }

  setCredentials(uid, password) {
    // Set credentials
  }

  async login(uid, password) {
    // Implementation for login and getting JSESSIONID
  }

  async getReportSummary(tsnId) {
    // Implementation for getting report summary
  }

  async getReportDetails(tsnId, index) {
    // Implementation for getting report details
  }

  async stopTestSession(tsnId) {
    // Implementation for stopping a test session
  }
}
```

#### Usage

```javascript
// Set credentials
window.externalApiService.setCredentials('username', 'password');

// Get report summary
const reportSummary = await window.externalApiService.getReportSummary(tsnId);

// Get report details
const reportDetails = await window.externalApiService.getReportDetails(tsnId, 1);
```

### Session ID Service

The `SessionIdService` provides methods to get recent test session IDs from various sources:
1. Local storage cache
2. Server-side API (when available) via ApiService
3. Hardcoded fallback values for testing

#### Key Features

- Multi-source data retrieval
- Local storage caching
- Automatic fallback to alternative sources
- Integration with ApiService for API calls

#### Implementation

```javascript
class SessionIdService {
  constructor() {
    this.cacheKey = 'smarttest_recent_session_ids';
    this.cacheTtl = 5 * 60 * 1000; // 5 minutes
    this.fallbackIds = ['13782', '13781', '13780', '13779', '13778'];
  }

  async getRecentSessionIds(credentials, limit = 10) {
    // Try to get from cache first
    const cachedIds = this.getCachedSessionIds();
    if (cachedIds && cachedIds.length > 0) {
      return cachedIds;
    }

    // Try to get from API
    try {
      const ids = await this.getSessionIdsFromApi(credentials, limit);
      if (ids && ids.length > 0) {
        this.cacheSessionIds(ids);
        return ids;
      }
    } catch (error) {
      console.error('Error getting session IDs from API:', error);
    }

    // Fall back to hardcoded values
    return this.fallbackIds;
  }

  getCachedSessionIds() {
    // Implementation for getting cached session IDs
  }

  cacheSessionIds(ids) {
    // Implementation for caching session IDs
  }

  async getSessionIdsFromApi(credentials, limit) {
    // Use ApiService if available
    if (window.apiService) {
      // Set credentials if they're not already set
      if (!window.apiService.credentials.uid) {
        window.apiService.setCredentials(credentials.uid, credentials.password);
      }

      // Use the getRecentRuns method from ApiService
      const recentRuns = await window.apiService.getRecentRuns({ limit });

      // Extract session IDs from the response
      return recentRuns.map(run => run.tsn_id.toString());
    } else {
      // Fallback to direct fetch for backward compatibility
      // Implementation for direct API call
    }
  }
}
```

#### Usage

```javascript
// Get recent session IDs
const sessionIds = await window.sessionIdService.getRecentSessionIds(
    credentials,
    limit
);
```

## HTML Parsing

The client-side services include HTML parsing functions to extract structured data from HTML responses:

### Report Summary Parsing

```javascript
parseReportSummaryHtml(html, tsnId) {
  // Create a DOM parser
  const parser = new DOMParser();
  const doc = parser.parseFromString(html, 'text/html');

  // Extract overall status
  let status = 'Unknown';
  const statusSpan = doc.querySelector('span[style*="color:red"], span[style*="color:green"]');
  if (statusSpan) {
    status = statusSpan.textContent.trim();
  }

  // Extract start and end times
  let startTime = null;
  let endTime = null;
  const listItems = doc.querySelectorAll('ul li');
  listItems.forEach(item => {
    const text = item.textContent.trim();
    if (text.startsWith('Start Time:')) {
      startTime = text.replace('Start Time:', '').trim();
    } else if (text.startsWith('End Time:')) {
      endTime = text.replace('End Time:', '').trim();
    }
  });

  // Build the report data object
  const reportData = {
    tsn_id: tsnId,
    status: status === 'PASS' ? 'Success' : (status === 'FAIL' ? 'Failed' : status),
    start_time: startTime,
    end_time: endTime,
    // Additional fields...
  };

  return reportData;
}
```

### Report Details Parsing

```javascript
parseReportDetailsHtml(html, tsnId) {
  // Create a DOM parser
  const parser = new DOMParser();
  const doc = parser.parseFromString(html, 'text/html');

  // Extract test cases from the table
  const testCases = [];
  const tableRows = doc.querySelectorAll('table tr');

  // Skip the header row
  for (let i = 1; i < tableRows.length; i++) {
    const row = tableRows[i];
    const cells = row.querySelectorAll('td');

    if (cells.length >= 5) {
      // Extract data from cells
      const tcId = this.extractTextFromCell(cells[0]);
      const seqIndex = this.extractTextFromCell(cells[1]);

      // Determine outcome from the class of the link
      let outcome = 'Unknown';
      const outcomeLink = cells[2].querySelector('a');
      if (outcomeLink) {
        if (outcomeLink.classList.contains('P')) {
          outcome = 'Passed';
        } else if (outcomeLink.classList.contains('F')) {
          outcome = 'Failed';
        }
      }

      // Add test case to the array
      testCases.push({
        tc_id: tcId,
        seq_index: seqIndex,
        status: outcome,
        // Additional fields...
      });
    }
  }

  // Build the details data object
  const detailsData = {
    tsn_id: tsnId,
    test_cases: testCases,
    // Additional fields...
  };

  return detailsData;
}
```

## Integration with Reports Page

The reports page integrates with these client-side services to implement a hybrid data access approach. The refactored implementation uses the ApiService consistently:

```javascript
// Configuration
const config = {
    reportingEndpoint: '/local/recent-runs',
    testDetailsEndpoint: '/local/test-details',
    refreshInterval: 10000, // 10 seconds
    useDirectExternalApi: true,
    externalApiBaseUrl: 'http://mprts-qa02.lab.wagerworks.com:9080',
    maxReportsToShow: 20
};

// Load reports data
async function loadReportsData(options = {}) {
    // Set default options
    const {
        limit = DEFAULT_LIMIT,
        timeRange = getSelectedTimeRange(),
        showLoading = true,
        getAllRecords = false
    } = options;

    // If getAllRecords is true, use -1 as the limit to get all records
    const actualLimit = getAllRecords ? -1 : limit;

    // Show loading indicator if requested
    if (showLoading && elements.loadingIndicator) {
        elements.loadingIndicator.classList.remove('d-none');
    }

    console.log(`Loading reports with limit: ${actualLimit}`);

    // Use the ApiService if available
    if (window.apiService) {
        // Make sure credentials are loaded
        if (!window.apiService.credentials.uid) {
            window.apiService.loadCredentials();
        }

        console.log('Using ApiService to load reports data');

        // Use the getRecentRuns method from ApiService
        window.apiService.getRecentRuns({
            limit: actualLimit,
            time_range: timeRange
        })
        .then(recentRuns => {
            // Process the received data
            console.log(`Received ${recentRuns.length} reports from API`);

            // Display reports in the table
            displayReports(recentRuns);

            // Update charts
            updateCharts(recentRuns);

            // Update refresh status
            if (elements.refreshStatus) {
                updateRefreshStatus();
            }
        })
        .catch(error => {
            console.error('Error loading reports:', error);
            // Show error message to the user
            displayError('Failed to load reports. Please try again later.');
        })
        .finally(() => {
            // Hide loading indicator
            if (showLoading && elements.loadingIndicator) {
                elements.loadingIndicator.classList.add('d-none');
            }
        });
    } else {
        // Fallback to direct fetch for backward compatibility
        // Implementation for direct API call
    }
}

// Load test details
async function loadTestDetails(testId) {
    try {
        // Show loading indicator
        if (elements.testDetailsSection) {
            elements.testDetailsSection.classList.remove('d-none');
            elements.testDetailsSection.scrollIntoView({ behavior: 'smooth', block: 'start' });

            if (elements.testDetailsTitle) {
                elements.testDetailsTitle.textContent = `Loading Test Details...`;
            }
        }

        // Get credentials
        let credentials = getCredentials();

        // Use ApiService if available
        if (window.apiService && !config.useDirectExternalApi) {
            console.log('Using ApiService to load test details');

            // Set credentials if they're not already set
            if (!window.apiService.credentials.uid) {
                window.apiService.setCredentials(credentials.uid, credentials.password);
            }

            // Use the getTestDetails method from ApiService
            const details = await window.apiService.getTestDetails(testId);

            // Store the test details in current state
            currentState.currentTestDetails = details;
        } else if (config.useDirectExternalApi) {
            // Use direct external API to get test details
            await loadTestDetailsFromExternalApi(testId, credentials);
        } else {
            // Use database API to get test details
            await loadTestDetailsFromDatabaseApi(testId, credentials);
        }

        // Update UI with the test details
        displayTestDetails();
        updateTestCasesTable(currentState.currentTestDetails.test_cases || []);
    } catch (error) {
        console.error('Error loading test details:', error);
        displayError('Failed to load test details. Please try again later.');
    }
}
```

## Security Considerations

- **Credentials**: Credentials are stored securely in session storage
- **JSESSIONID**: Cookies are managed securely and expire after 25 minutes
- **HTTPS**: Direct API calls use HTTPS when available
- **HTML Sanitization**: HTML responses are sanitized before being displayed

## Future Enhancements

1. **WebSocket Integration**: Replace polling with WebSocket for real-time updates
2. **Offline Support**: Add offline support using service workers and IndexedDB
3. **Response Caching**: Implement client-side caching for frequently accessed data
4. **Structured API Responses**: Work with the external API team to provide JSON responses instead of HTML

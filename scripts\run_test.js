/**
 * Test Execution Module
 * 
 * This module provides functions to execute tests through the API using
 * the exact format required for successful API calls.
 */

const axios = require('axios');
const dbConnector = require('./db-connector');

/**
 * Execute a test case or test suite
 * @param {Object} options - Test execution options
 * @param {number} options.testCaseId - ID of the test case to execute (optional if testSuiteId is provided)
 * @param {number} options.testSuiteId - ID of the test suite to execute (optional if testCaseId is provided)
 * @param {string} options.environment - Environment to use (qa01, qa02, qa03)
 * @param {string} options.username - Username or email of the user executing the test
 * @param {string} options.password - Password for authentication (default: 'test')
 * @param {boolean} options.monitorExecution - Whether to monitor the execution after starting it
 * @param {number} options.monitorTimeoutMinutes - Timeout in minutes for monitoring (default: 5)
 * @returns {Promise<Object>} Test execution result with tsn_id and status
 */
async function executeTest(options) {
  // Validate options
  if (!options.environment) {
    throw new Error('Environment is required (qa01, qa02, qa03)');
  }
  
  if (!options.username) {
    throw new Error('Username is required');
  }
  
  if (!options.testCaseId && !options.testSuiteId) {
    throw new Error('Either testCaseId or testSuiteId is required');
  }
  
  // Set defaults
  const config = {
    environment: options.environment,
    username: options.username,
    password: options.password || 'test',
    testCaseId: options.testCaseId,
    testSuiteId: options.testSuiteId,
    monitorExecution: options.monitorExecution !== undefined ? options.monitorExecution : false,
    monitorTimeoutMinutes: options.monitorTimeoutMinutes || 5
  };
  
  console.log(`Executing test on ${config.environment} environment as ${config.username}`);
  
  try {
    // Get environment configuration
    const envConfig = dbConnector.environments[config.environment];
    if (!envConfig) {
      throw new Error(`Unknown environment: ${config.environment}`);
    }
    
    // Build API request
    const baseUrl = envConfig.BASE_URL;
    const apiUrl = `${baseUrl}CaseRunner`;
    
    // Create the request parameters string with exact format required by the API
    // IMPORTANT: The double-ampersand (&&) must be preserved as-is
    const requestData = buildRequestString(config, envConfig);
    
    console.log(`Sending API request to ${apiUrl}`);
    if (options.debug) {
      console.log('Request data:', requestData);
    }
    
    // Make the API request
    const response = await axios.post(
      apiUrl,
      requestData,
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      }
    );
    
    console.log(`API response status: ${response.status}`);
    
    // Extract tsn_id from the response
    const tsnId = extractTsnId(response.data);
    if (!tsnId) {
      throw new Error('Failed to extract tsn_id from API response');
    }
    
    console.log(`Test execution started with tsn_id: ${tsnId}`);
    
    // Result object
    const result = {
      tsnId,
      environment: config.environment,
      username: config.username,
      testCaseId: config.testCaseId,
      testSuiteId: config.testSuiteId,
      status: 'STARTED'
    };
    
    // If monitoring is requested, monitor the test execution
    if (config.monitorExecution) {
      console.log('Monitoring test execution...');
      const monitorResult = await monitorExecution(tsnId, config.environment, config.monitorTimeoutMinutes);
      result.status = monitorResult.status;
      result.summary = monitorResult.summary;
    }
    
    return result;
  } catch (error) {
    console.error('Error executing test:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      if (typeof error.response.data === 'string') {
        console.error('Response data (truncated):', error.response.data.substring(0, 200));
      } else {
        console.error('Response data:', error.response.data);
      }
    }
    throw error;
  }
}

/**
 * Build the request string in the exact format required by the API
 * @param {Object} config - Test configuration
 * @param {Object} envConfig - Environment configuration
 * @returns {string} Formatted request string
 */
function buildRequestString(config, envConfig) {
  // Start with basic authentication parameters
  let params = `uid=${config.username}&password=${config.password}`;
  
  // Add test identifier (either tc_id or ts_id)
  if (config.testCaseId) {
    params += `&tc_id=${config.testCaseId}`;
  } else if (config.testSuiteId) {
    params += `&ts_id=${config.testSuiteId}`;
  }
  
  // Add environment parameters
  params += `&envir=${config.environment}`;
  params += `&shell_host=jps-qa10-app01`;
  params += `&file_path=/home/<USER>/`;
  params += `&operatorConfigs=operatorNameConfigs`;
  params += `&kafka_server=kafka-qa-a0.lab.wagerworks.com`;
  params += `&dataCenter=GU`;
  params += `&rgs_env=${config.environment}`;
  params += `&old_version=0`;
  
  // CRITICAL: Include the double-ampersand exactly like this
  params += `&&`;
  
  // Add remaining parameters
  params += `networkType1=multi-site`;
  params += `&networkType2=multi-site`;
  params += `&sign=-`;
  params += `&rate_src=local`;
  
  return params;
}

/**
 * Extract the tsn_id from the API response
 * @param {string} responseData - API response data
 * @returns {string|null} Extracted tsn_id or null if not found
 */
function extractTsnId(responseData) {
  if (typeof responseData !== 'string') {
    console.log('Response is not a string');
    return null;
  }
  
  // Try various patterns to extract tsn_id
  const patterns = [
    /ReportSummary\?tsn_id=(\d+)/,
    /CaseEditor\?tsn_id=(\d+)/,
    /tsn_id=(\d+)/,
    /tsn_id.*?(\d+)/
  ];
  
  for (const pattern of patterns) {
    const match = responseData.match(pattern);
    if (match && match[1]) {
      return match[1];
    }
  }
  
  console.log('Could not find tsn_id in response');
  return null;
}

/**
 * Monitor test execution until completion or timeout
 * @param {string} tsnId - Test session ID to monitor
 * @param {string} environment - Environment (qa01, qa02, qa03)
 * @param {number} timeoutMinutes - Timeout in minutes
 * @returns {Promise<Object>} Monitoring result
 */
async function monitorExecution(tsnId, environment, timeoutMinutes = 5) {
  console.log(`Monitoring test execution for tsn_id: ${tsnId}`);
  
  try {
    // Connect to database
    await dbConnector.init(environment, { debug: false });
    
    let isComplete = false;
    let allPassed = false;
    const startTime = Date.now();
    const timeoutMs = timeoutMinutes * 60 * 1000;
    
    while (!isComplete && (Date.now() - startTime < timeoutMs)) {
      // Check session status
      const [sessionRows] = await dbConnector.query(`
        SELECT end_ts FROM test_session WHERE tsn_id = ?
      `, [tsnId]);
      
      // If end_ts is not null, the test is complete
      isComplete = sessionRows.length > 0 && sessionRows[0].end_ts !== null;
      
      // Check test results
      const [resultRows] = await dbConnector.query(`
        SELECT tc_id, outcome, COUNT(*) as count
        FROM test_result
        WHERE tsn_id = ?
        GROUP BY tc_id, outcome
        ORDER BY tc_id
      `, [tsnId]);
      
      // Display current status
      console.log(`\nCurrent test status (${new Date().toISOString()}):`);
      
      if (resultRows.length === 0) {
        console.log('No test results found yet');
      } else {
        // Group results by test case
        const testCases = {};
        
        resultRows.forEach(row => {
          const tcId = row.tc_id || row.column1;
          const outcome = row.outcome || row.column2;
          const count = row.count || row.column3 || 0;
          
          if (!testCases[tcId]) {
            testCases[tcId] = {};
          }
          
          testCases[tcId][outcome] = count;
        });
        
        // Display results
        for (const [tcId, outcomes] of Object.entries(testCases)) {
          const outcomeStr = Object.entries(outcomes)
            .map(([outcome, count]) => `${outcome}: ${count}`)
            .join(', ');
          
          console.log(`Test Case ${tcId}: ${outcomeStr}`);
        }
      }
      
      if (isComplete) {
        console.log('Test execution completed!');
        break;
      }
      
      // Wait 5 seconds before checking again
      console.log('Waiting 5 seconds before next check...');
      await new Promise(resolve => setTimeout(resolve, 5000));
    }
    
    if (!isComplete) {
      console.log(`\nTest run timed out after ${timeoutMinutes} minutes`);
      return { status: 'TIMEOUT', summary: null };
    }
    
    // Get final test summary
    const summary = await getTestSummary(tsnId);
    
    return { 
      status: 'COMPLETED', 
      summary 
    };
  } catch (error) {
    console.error('Error monitoring test execution:', error);
    return { status: 'ERROR', error: error.message };
  } finally {
    // Close database connection
    await dbConnector.close();
  }
}

/**
 * Get a summary of test results
 * @param {string} tsnId - Test session ID
 * @returns {Promise<Object>} Test summary
 */
async function getTestSummary(tsnId) {
  const [rows] = await dbConnector.query(`
    SELECT 
      tsn_id, 
      SUM(CASE WHEN outcome='P' THEN 1 ELSE 0 END) AS passed_cases,
      SUM(CASE WHEN outcome='F' THEN 1 ELSE 0 END) AS failed_cases,
      TIMEDIFF(MAX(creation_time), MIN(creation_time)) AS duration
    FROM test_result
    WHERE tsn_id = ?
    GROUP BY tsn_id
  `, [tsnId]);
  
  if (rows.length === 0) {
    console.log(`No summary available for tsn_id ${tsnId}`);
    return { passed_cases: 0, failed_cases: 0 };
  }
  
  console.log('\n====== TEST SUMMARY ======');
  console.log(`Test Run ID: ${rows[0].tsn_id || rows[0].column1}`);
  console.log(`Passed Cases: ${rows[0].passed_cases || rows[0].column2 || 0}`);
  console.log(`Failed Cases: ${rows[0].failed_cases || rows[0].column3 || 0}`);
  console.log(`Duration: ${rows[0].duration || rows[0].column4 || 'Unknown'}`);
  console.log('==========================');
  
  // Get failure details if any
  if ((rows[0].failed_cases || rows[0].column3) > 0) {
    await getFailureDetails(tsnId);
  }
  
  return {
    tsn_id: rows[0].tsn_id || rows[0].column1,
    passed_cases: rows[0].passed_cases || rows[0].column2 || 0,
    failed_cases: rows[0].failed_cases || rows[0].column3 || 0,
    duration: rows[0].duration || rows[0].column4 || 'Unknown'
  };
}

/**
 * Get details of test failures
 * @param {string} tsnId - Test session ID
 * @returns {Promise<Array>} Failure details
 */
async function getFailureDetails(tsnId) {
  const [rows] = await dbConnector.query(`
    SELECT r.cnt, r.seq_index, r.tsn_id, r.tc_id, r.outcome, i.txt
    FROM test_result r
    JOIN output i ON i.cnt = r.cnt
    WHERE r.tsn_id = ?
    AND r.outcome = 'F'
  `, [tsnId]);
  
  if (rows.length === 0) {
    console.log('No failures detected');
    return [];
  }
  
  console.log('\n====== FAILURE DETAILS ======');
  
  // Group failures by test case
  const failuresByTestCase = {};
  
  rows.forEach(row => {
    const tcId = row.tc_id || row.column4;
    const seqIndex = row.seq_index || row.column2;
    const txt = row.txt || row.column6;
    
    if (!failuresByTestCase[tcId]) {
      failuresByTestCase[tcId] = [];
    }
    
    failuresByTestCase[tcId].push({
      seq_index: seqIndex,
      output: txt
    });
  });
  
  // Print failures by test case
  for (const [tcId, failures] of Object.entries(failuresByTestCase)) {
    console.log(`\nTest Case ${tcId} Failures:`);
    failures.forEach((failure, index) => {
      console.log(`  Failure #${index + 1}:`);
      console.log(`  - Sequence Index: ${failure.seq_index}`);
      console.log(`  - Output: ${failure.output.substring(0, 150)}${failure.output.length > 150 ? '...' : ''}`);
    });
  }
  
  console.log('=============================');
  
  return Object.entries(failuresByTestCase).map(([tcId, failures]) => ({
    tc_id: tcId,
    failures: failures.map(f => ({
      seq_index: f.seq_index,
      output: f.output
    }))
  }));
}

module.exports = {
  executeTest,
  monitorExecution,
  getTestSummary,
  getFailureDetails
}; 
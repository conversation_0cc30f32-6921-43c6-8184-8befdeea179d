name: Issue & Project Management

# Temporarily disabled for test corrections - manual trigger only
on:
  workflow_dispatch:

jobs:
  auto-label-issues:
    runs-on: ubuntu-latest
    # Manual trigger only - conditionals removed
    steps:
    - name: Auto-label issues
      uses: github/issue-labeler@v3.0
      with:
        repo-token: ${{ secrets.GITHUB_TOKEN }}
        configuration-path: .github/issue-labeler.yml

    - name: Add to project board
      uses: alex-page/github-project-automation-plus@v0.8.3
      with:
        project: SmartTest Development
        column: 📋 Backlog
        repo-token: ${{ secrets.GITHUB_TOKEN }}

  stale-issue-management:
    runs-on: ubuntu-latest
    # Manual trigger only - conditionals removed
    steps:
    - name: Mark stale issues
      uses: actions/stale@v8
      with:
        repo-token: ${{ secrets.GITHUB_TOKEN }}
        stale-issue-message: |
          👋 This issue has been automatically marked as stale because it has not had recent activity.
          
          **Next steps:**
          - Add more details if needed
          - Remove stale label if still relevant
          - Issue will be closed in 7 days if no activity
          
          Thank you for your contributions! 🙏
        stale-pr-message: |
          👋 This PR has been automatically marked as stale because it has not had recent activity.
          
          **Next steps:**
          - Rebase on latest main branch
          - Address review comments
          - Remove stale label if still relevant
          
          Thank you for your contributions! 🙏
        stale-issue-label: 'stale'
        stale-pr-label: 'stale'
        days-before-stale: 30
        days-before-close: 7

  release-notes:
    runs-on: ubuntu-latest
    # Manual trigger only - conditionals removed
    steps:
    - name: Generate release notes
      uses: release-drafter/release-drafter@v5
      with:
        config-name: release-drafter.yml
        publish: false
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

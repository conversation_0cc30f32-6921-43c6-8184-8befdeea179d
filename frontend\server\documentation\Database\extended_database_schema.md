# Extended Database Schema Documentation

## Overview

This document provides a deeper analysis of the `rgs_test` database structure based on schema information obtained directly from the database. It includes detailed field definitions, data types, and relationships, expanding upon the basic database structure documentation.

## Access Restrictions and Permissions Model

Our database exploration revealed an important security model that affects how SmartTest will interact with the database:

### Access Levels

1. **Read-Only Access (rgs_ro user)**:
   - Has SELECT privileges on core tables for reporting and viewing
   - Includes access to `test_project`, `test_suite_group`, `test_case_group`, `test_case`, `test_result`, and `output`
   - Cannot access configuration and parameter tables

2. **Restricted Tables**:
   - **Step Management**: `test_case_step` 
   - **Parameter System**: `test_parameter`, `project_parameter`, `suite_parameter`, `case_parameter`, `step_parameter`
   - **Test Planning**: `test_plan`
   
3. **Security Implications**:
   - The system implements a tiered permission model
   - Configuration and execution logic are separated permissions
   - Complete test management requires elevated privileges

### Design Considerations for SmartTest

1. **Architectural Approach**:
   - Design for read-only database access initially
   - Implement API-based approach for managing restricted tables
   - Consider permission elevation requests if necessary

2. **Progressive Implementation**:
   - Focus initial implementation on accessible core tables
   - Add parameter and step management as service abstractions
   - Allow for flexible authentication model

3. **Security Integration**:
   - Mirror the existing permission tiers
   - Implement appropriate authorization controls
   - Consider audit logging for sensitive operations

## Core Tables

### 1. Test Data Management

#### `test_project`
- **Purpose**: Stores top-level project information
- **Key Fields**:
  - `pj_id`: INT(8), Primary Key, Auto-increment - Unique project identifier
  - `status`: CHAR(1) - Project status indicator
  - `uid`: VARCHAR(50) - User ID of the project owner
  - `comments`: VARCHAR(1024) - Project description or comments
  - `tp_id`: INT(8) - Test plan ID reference
  - `name`: VARCHAR(64) - Project name

#### `test_suite_group`
- **Purpose**: Manages test suites within projects
- **Key Fields**:
  - `pj_id`: INT(8) - Foreign key to test_project
  - `ts_id`: INT(8) - Test suite identifier
  - `seq_index`: INT(2) - Execution order within the project

#### `test_case_group`
- **Purpose**: Associates test cases with test suites
- **Key Fields**:
  - `ts_id`: INT(8), Indexed - Foreign key to test_suite_group
  - `tc_id`: INT(8), Indexed - Foreign key to test_case
  - `seq_index`: INT(2) - Execution order within the test suite

#### `test_case`
- **Purpose**: Stores individual test case definitions
- **Key Fields**:
  - `tc_id`: INT(8), Primary Key, Auto-increment - Test case identifier
  - `uid`: VARCHAR(50), Indexed - User ID (owner/creator)
  - `status`: CHAR(1) - Status code (e.g., 'M' for maintenance)
  - `case_driver`: VARCHAR(80), Default 'com.igt.pa.core.DefaultCaseDriver' - Java driver class
  - `tp_id`: INT(8), Indexed - Test plan ID reference
  - `comments`: VARCHAR(1024) - Description or notes
  - `tickets`: VARCHAR(128) - Related issue tracking tickets
  - `name`: VARCHAR(64) - Test case name

### 2. Test Execution

#### `test_result`
- **Purpose**: Records test execution results
- **Key Fields**:
  - `tc_id`: INT(8) - Test case ID
  - `seq_index`: INT(8) - Sequence index
  - `tsn_id`: INT(8), Indexed - Test suite run ID
  - `outcome`: CHAR(1) - Result status ('P' for pass, 'F' for fail)
  - `creation_time`: TIMESTAMP(3) with auto-update - Execution timestamp
  - `cnt`: INT(10), Primary Key, Auto-increment - Counter linking to output

#### `output`
- **Purpose**: Stores detailed test output logs
- **Key Fields**:
  - `cnt`: INT(10), Indexed - Foreign key linking to test_result
  - `txt`: VARCHAR(64000) - The actual output text content with large capacity

### 3. Access-Restricted Tables

Several tables that exist but have restricted access permissions. The read-only user (`rgs_ro`) doesn't have SELECT privileges on these tables, suggesting they contain sensitive information or are managed through specialized interfaces:

#### Parameter Management Tables
- `test_parameter` - Likely defines parameter metadata
- `project_parameter` - Stores project-level parameter values
- `suite_parameter` - Stores suite-level parameter values
- `case_parameter` - Stores case-level parameter values 
- `step_parameter` - Stores step-level parameter values

#### Test Step Management
- `test_case_step` - Defines steps within a test case

#### Higher-Level Organization
- `test_plan` - Organizes test cases at a level above projects

The presence of these tables confirms our inferences about the system architecture, even though we can't directly query their schema.

## Confirmed Database Schema Insights

### Field Type and Size Details

1. **Integer Sizing**:
   - Project, suite, and case IDs use INT(8) - supporting millions of records
   - Counters and primary keys use INT(10) - even larger capacity
   - Sequence indexes use INT(2) for ordering - sufficient for common use cases

2. **String Field Sizing**:
   - `txt` in output uses VARCHAR(64000) - extremely large capacity for test outputs
   - `comments` uses VARCHAR(1024) - substantial space for project descriptions
   - `name` uses VARCHAR(64) - standard size for names
   - `uid` uses VARCHAR(50) - adequate for email addresses or user identifiers
   - `tickets` uses VARCHAR(128) - space for multiple ticket references
   - `case_driver` uses VARCHAR(80) - stores Java class path

3. **Timestamp Details**:
   - `creation_time` uses TIMESTAMP(3) - millisecond precision
   - Auto-update on modification - keeps track of the latest changes

### Indexing Strategy

1. **Primary Keys**:
   - `pj_id` in test_project is an auto-increment primary key
   - `tc_id` in test_case is an auto-increment primary key
   - `cnt` in test_result is an auto-increment primary key

2. **Foreign Key Indexes**:
   - `ts_id` and `tc_id` in test_case_group have multiple-column indexes (MUL)
   - `cnt` in output is indexed for fast joins to test_result
   - `uid` in test_case is indexed for filtering by owner
   - `tp_id` in test_case is indexed for test plan relationships

3. **Performance Optimizations**:
   - Indexes on relationship fields optimize join operations
   - Auto-increment keys simplify insertion operations
   - Appropriate field sizing balances storage needs and performance

## Permission Model

Permission model for the database:

1. **Read-Only Access**:
   - The `rgs_ro` user has SELECT privileges on core tables like `test_case`, `test_result`, and `output`
   - This allows for viewing test results and basic test metadata

2. **Restricted Access**:
   - Access to test step details, parameter configuration, and test plan organization is restricted
   - These restricted tables likely contain test logic, configuration details, or sensitive test data
   - Full access probably requires elevated permissions

3. **Query Construction Implications**:
   - Queries must be designed to work within these permission constraints
   - For example, joining to restricted tables from SmartTest would fail
   - Additional tools or elevated permissions might be needed for complete test management

## Inferred Relationships

Based on the confirmed schema and permission restrictions, we can establish these relationships:

```
test_project (1) --- (n) test_suite_group (1) --- (n) test_case_group (n) --- (1) test_case (1) --- (n) test_case_step*
       |                      |                                                     |                      |
       |                      |                                                     |                      |
       v                      v                                                     v                      v
project_parameter*    suite_parameter*                                     case_parameter*      step_parameter*
                                                                                  |
                                                                                  |
                                  test_result (1) --- (1) output                  |
                                       |                                          |
                                       +------------------------------------------+
```
*Tables confirmed to exist but with restricted access

## Additional Tables

Based on the output in the failure results, we can identify additional domain-specific tables that might be used in testing:

### Gaming-Related Tables
Our failure results show data related to gaming systems, suggesting tables like:

#### `jackpot_cycle`
- **Purpose**: Manages jackpot cycles
- **Fields**:
  - `jackpot_cycle_id`: Identifier
  - `status_code`: Status codes like 'GSPD'
  - Other fields related to jackpot timing and values

#### `runtime_stage`
- **Purpose**: Manages runtime stages for gaming sequences
- **Fields**:
  - `runtime_id`: Runtime identifier
  - `stage`: Stage number
  - `day_number`: Day number in sequence
  - `sequence_id`: Sequence identifier
  - `total_prize`: Prize amount
  - `total_quantity`: Quantity values
  - Timestamp fields for effective dates

## Interesting Discoveries

1. **Java-Based Case Driver**:
   - The `case_driver` field in `test_case` reveals the system uses Java drivers for test execution
   - The default driver is `com.igt.pa.core.DefaultCaseDriver`
   - This indicates the architecture has a Java-based execution engine

2. **Test Plan Architecture**:
   - The presence of `tp_id` fields and a restricted `test_plan` table confirms a hierarchical organization
   - Test plans likely group related test cases and projects

3. **Ownership and Ticketing**:
   - Test cases track both ownership (`uid`) and related issue tickets (`tickets`)
   - This supports a complete development and quality assurance workflow

4. **Access Control Strategy**:
   - The selective access restrictions suggest a deliberate security model
   - Core result data is readable, but test configuration details are protected

## Conclusion

The database schema analysis reveals a well-designed system with comprehensive test management capabilities. The access restrictions indicate a security-conscious approach, with different permission levels for viewing results versus modifying test configurations.

Key findings from our exploration:
1. Confirmed the existence of parameter management tables, though access is restricted
2. Discovered the Java-based case driver architecture
3. Identified ticket tracking integration
4. Verified the test plan organizational structure
5. Confirmed detailed permission model with selective access controls

For our SmartTest implementation, we'll need to:
1. Design around the permission constraints or request elevated access
2. Consider how to integrate with the Java-based execution engine
3. Implement appropriate security controls matching the existing model
4. Focus on the accessible core tables for the initial implementation 
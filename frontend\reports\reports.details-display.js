function formatSingleVariable(varStr) {
    // Split by first equals sign
    const parts = varStr.split('=');
    const key = parts[0]?.trim();
    const value = parts.slice(1).join('=').trim();
    
    if (!key) return varStr;
    
    // Highlight certain values
    if (key === 'envir' || key === 'rgs_env' || key === 'environment') {
        return `<strong>${key}</strong> = <span class="env-highlight">${value}</span>`;
    }
    
    return `<strong>${key}</strong> = ${value}`;
}

// Display test details in the UI
function displayTestDetails() {
    const test = currentState.currentTestDetails;
    if (!test) {
        console.error('No test details available to display');
        return;
    }

    console.log('Displaying test details:', test);

    // Update title - Show Test Run ID and name for clarity
    const tsnId = test.tsn_id || test.id;
    let testName = test.test_name || test.name || '';
    // Clean up the test name by removing common failure indicators like "FAIL Case:"
    const failCaseIndicator = testName.indexOf('FAIL Case:');
    if (failCaseIndicator !== -1) {
        testName = testName.substring(0, failCaseIndicator).trim();
    }
    const summaryIndicator = testName.indexOf('Summary:'); // Another potential separator
    if (summaryIndicator !== -1) {
        testName = testName.substring(0, summaryIndicator).trim();
    }

    const maxLength = 70; // Increased max length for better readability
    const truncatedName = testName.length > maxLength ? 
        testName.substring(0, maxLength) + '...' : 
        testName;
    
    // Format: "Test Run #14847: Test Name"
    elements.testDetailsTitle.textContent = `Test Run #${tsnId}: ${truncatedName.trim()}`;

    // Set basic details
    elements.detailTestId.textContent = test.test_id || test.tc_id || test.ts_id || 'N/A';
    elements.detailTestType.textContent = test.type || 'Test Suite';
    
    // Environment section removed per user request
    // Hide the environment row in the details table
    const envRow = elements.detailEnvironment.closest('tr');
    if (envRow) {
        envRow.style.display = 'none';
    }
    elements.detailStartTime.textContent = formatDateTime(test.startTime || test.start_time || test.start_ts);

    // Set status with appropriate class
    const statusElement = elements.detailStatus;
    statusElement.textContent = test.status || 'Unknown';

    // Remove all status classes
    statusElement.classList.remove('text-success', 'text-danger', 'text-warning');

    // Add appropriate status class
    if (test.status && test.status.toLowerCase().includes('pass')) {
        statusElement.classList.add('text-success');
    } else if (test.status && test.status.toLowerCase().includes('fail')) {
        statusElement.classList.add('text-danger');
    } else {
        statusElement.classList.add('text-warning');
    }

    // Set other details
    elements.detailDuration.textContent = test.duration ||
        (test.start_time && test.end_time ? calculateDuration(test.start_time, test.end_time) : '0:00');
    elements.detailUser.textContent = formatUserEmail(test.user || test.uid || 'System');
    elements.detailTrigger.textContent = test.trigger || test.source || 'Manual';

    // Calculate total cases based on available information
    let totalCases = 0;
    const passedCases = parseInt(test.passed_cases || test.passed || 0);
    const failedCases = parseInt(test.failed_cases || test.failed || 0);
    const skippedCases = parseInt(test.skipped_cases || test.skipped || 0);

    // Sum up the known case counts
    totalCases = passedCases + failedCases + skippedCases;

    // If we have test cases array, use its length as a backup
    if (test.test_cases && test.test_cases.length > 0) {
        if (totalCases === 0) {
            totalCases = test.test_cases.length;
        }
    }

    // If we have total_cases field, use it as a backup
    if (totalCases === 0 && test.total_cases) {
        totalCases = parseInt(test.total_cases);
    }

    // Ensure we display at least 1 for single test case runs
    if (totalCases === 0 && test.tc_id) {
        totalCases = 1;
    }

    // Update test result statistics
    elements.detailTotalCases.textContent = totalCases;
    elements.detailPassedCases.textContent = passedCases;
    elements.detailFailedCases.textContent = failedCases;
    elements.detailSkippedCases.textContent = skippedCases;

    // Display HTML report if available
    if (test.report || (test.test_cases && test.test_cases.length > 0)) {
        let modalElement = document.getElementById('html-report-container');
        if (!modalElement) {
            modalElement = document.createElement('div');
            modalElement.id = 'html-report-container';
            modalElement.className = 'html-report-section mt-4';
            const reportHeading = document.createElement('h5');
            reportHeading.textContent = 'Detailed Test Steps'; // Updated Heading
            modalElement.appendChild(reportHeading);
            const reportContent = document.createElement('div');
            reportContent.id = 'html-report-content';
            reportContent.className = 'html-report-content p-3 border rounded';
            modalElement.appendChild(reportContent);
            const casesSection = document.querySelector('.test-results-stats').parentNode;
            casesSection.appendChild(modalElement);
        }

        const reportContentElement = document.getElementById('html-report-content');
        if (!reportContentElement) {
            console.error("Critical: html-report-content element not found after setup!");
        } else {
            const parsedTestCases = test.test_cases; // From parseReportDetailsHtml
            const tsnIdForAccordion = test.tsn_id || test.id;
            const originalReportHtml = test.report; // Raw HTML from external API

            if (parsedTestCases && parsedTestCases.length > 0 && parsedTestCases.some(tc => tc.steps && tc.steps.length > 0)) {
                console.log('Building accordion HTML from parsed test cases and their steps.');
                const enhancedHtml = buildStepAccordionHtml(parsedTestCases, tsnIdForAccordion, 2); // 2 preceding passed steps
                reportContentElement.innerHTML = enhancedHtml;

                // Initialize Bootstrap collapse components for the new accordion
                const accordionElements = reportContentElement.querySelectorAll('.accordion-collapse');
                accordionElements.forEach(el => {
                    if (el.id) { // Ensure the element has an ID for Bootstrap to target
                        new bootstrap.Collapse(el, {
                            toggle: el.classList.contains('show') // Preserve 'show' state if initially set
                        });
                    }
                });
            } else if (originalReportHtml && originalReportHtml.trim() !== '') {
                console.warn('No parsed test cases with steps available, or steps are empty. Displaying original HTML report from external API.');
                reportContentElement.innerHTML = originalReportHtml;
            } else {
                reportContentElement.innerHTML = '<p>No detailed step information available for this test run.</p>';
            }
        }

        if (!document.getElementById('report-style')) {
            const style = document.createElement('style');
            style.id = 'report-style';
            style.textContent = `
                .html-report-content {
                    max-height: 70vh; /* Increased max-height */
                    overflow-y: auto;
                    background-color: #f8f9fa;
                }
                .html-report-content .accordion-button {
                    font-size: 0.9rem;
                }
                .html-report-content .list-group-item {
                    font-size: 0.85rem;
                }
                .html-report-content pre {
                    white-space: pre-wrap; /* Allow wrapping for long lines in pre tags */
                    word-break: break-all; /* Break long words */
                    margin-bottom: 0;
                    padding: 0.25rem 0.5rem;
                    font-size: 0.8rem;
                }
                .html-report-content .list-group-item-success {
                    border-left: 3px solid #198754;
                }
                .html-report-content .list-group-item-danger {
                    border-left: 3px solid #dc3545;
                }
                .html-report-content .text-muted {
                    font-size: 0.8rem;
                }
            `;
            document.head.appendChild(style);
        }
    }

    // Show the details section
    elements.testDetailsSection.classList.remove('d-none');

    // Scroll to make it visible
    elements.testDetailsSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
}

/**
 * Calculate duration between two date strings
 * @param {string} startTime - Start time string
 * @param {string} endTime - End time string
 * @returns {string} - Formatted duration string (e.g., "1h 23m 45s")
 */

function escapeHtml(unsafe) {
    if (unsafe === null || typeof unsafe === 'undefined') {
        return '';
    }
    return String(unsafe)
         .replace(/&/g, "&amp;")
         .replace(/</g, "&lt;")
         .replace(/>/g, "&gt;")
         .replace(/"/g, "&quot;")
         .replace(/'/g, "&#039;");
}

function buildStepAccordionHtml(testCases, tsnId, precedingPassedStepsCount) {
    if (!testCases || testCases.length === 0) {
        return '<p>No test case data provided to build step details.</p>';
    }
    let html = `<div class="accordion" id="testCaseAccordion_${tsnId}">`;

    testCases.forEach((tc, index) => {
        const tcId = tc.tc_id || `tc_idx_${index}`;
        const tcDescription = tc.description || tc.name || `Test Case ${tcId}`;
        // Determine overall status from tc.status or by checking tc.hasFailures or presence of failing steps
        let overallTcStatus = tc.status || 'Unknown';
        let hasFailedStepsInTc = (tc.steps || []).some(step => step.is_failing);
        if (overallTcStatus === 'Unknown') {
            overallTcStatus = hasFailedStepsInTc ? 'Failed' : 'Passed';
        }
        
        const accordionId = `collapse_${tsnId}_${tcId}`.replace(/[^a-zA-Z0-9_\-]/g, '_'); // Sanitize ID
        const headerId = `header_${tsnId}_${tcId}`.replace(/[^a-zA-Z0-9_\-]/g, '_'); // Sanitize ID

        // Accordion button should reflect if there are details to show (any steps, or specifically failed ones)
        const stepsAvailable = tc.steps && tc.steps.length > 0;
        // Expand if there are failed steps, otherwise collapse by default
        const expandAccordion = hasFailedStepsInTc;

        html += `
            <div class="accordion-item">
                <h2 class="accordion-header" id="${headerId}">
                    <button class="accordion-button ${expandAccordion ? '' : 'collapsed'}" type="button" data-bs-toggle="collapse" data-bs-target="#${accordionId}" aria-expanded="${expandAccordion ? 'true' : 'false'}" aria-controls="${accordionId}">
                        <span class="me-2">TC ID: ${tcId} - ${escapeHtml(tcDescription)}</span>
                        <span class="badge bg-${overallTcStatus.toLowerCase() === 'failed' ? 'danger' : (overallTcStatus.toLowerCase() === 'passed' ? 'success' : 'secondary')}">${overallTcStatus}</span>
                    </button>
                </h2>
                <div id="${accordionId}" class="accordion-collapse collapse ${expandAccordion ? 'show' : ''}" aria-labelledby="${headerId}" data-bs-parent="#testCaseAccordion_${tsnId}">
                    <div class="accordion-body p-2">
        `;

        if (stepsAvailable) {
            html += '<ul class="list-group list-group-flush">';
            const stepsToDisplay = [];
            const allSteps = tc.steps.sort((a, b) => parseInt(a.seq) - parseInt(b.seq)); // Ensure sorted by sequence

            for (let i = 0; i < allSteps.length; i++) {
                const currentStep = allSteps[i];
                let shouldAddCurrentStep = false;

                if (currentStep.is_failing) {
                    shouldAddCurrentStep = true;
                    // Add preceding passed steps
                    for (let j = 1; j <= precedingPassedStepsCount; j++) {
                        const prevStepIndex = i - j;
                        if (prevStepIndex >= 0) {
                            const prevStep = allSteps[prevStepIndex];
                            if (!prevStep.is_failing && !stepsToDisplay.find(s => s.seq === prevStep.seq)) {
                                stepsToDisplay.push(prevStep);
                            }
                        }
                    }
                } else {
                    // Check if this passed step is within `precedingPassedStepsCount` of a *future* failed step
                    for (let k = 1; k <= precedingPassedStepsCount; k++) {
                        const futureStepIndex = i + k;
                        if (futureStepIndex < allSteps.length && allSteps[futureStepIndex].is_failing) {
                            shouldAddCurrentStep = true;
                            break;
                        }
                    }
                }
                
                // If it's a failed step, or a passed step that precedes a failed one, add it
                if (shouldAddCurrentStep && !stepsToDisplay.find(s => s.seq === currentStep.seq)) {
                    stepsToDisplay.push(currentStep);
                }
            }
            
            // If no specific steps were selected (e.g., all passed and no failures, or only failures shown by above logic)
            // decide what to show. If there were failures, ensure all failed steps are shown.
            if (hasFailedStepsInTc) {
                allSteps.forEach(step => {
                    if (step.is_failing && !stepsToDisplay.find(s => s.seq === step.seq)) {
                        stepsToDisplay.push(step); // Ensure all failed steps are included
                    }
                });
            } else if (stepsToDisplay.length === 0) { // All steps passed, show all
                allSteps.forEach(step => stepsToDisplay.push(step));
            }

            // Sort the final list of steps to display by sequence
            stepsToDisplay.sort((a, b) => parseInt(a.seq) - parseInt(b.seq));
            // Remove duplicates again after all additions, just in case
            const finalUniqueSteps = [...new Map(stepsToDisplay.map(item => [item.seq, item])).values()];

            if (finalUniqueSteps.length > 0) {
                finalUniqueSteps.forEach(step => {
                    const stepStatusText = step.status_text || (step.is_failing ? 'Fail' : 'Pass');
                    const stepStatusClass = step.is_failing ? 'danger' : 'success';
                    const stepIcon = step.is_failing ? '<i class="fas fa-times-circle text-danger me-2"></i>' : '<i class="fas fa-check-circle text-success me-2"></i>';
                    html += `
                        <li class="list-group-item ${step.is_failing ? 'list-group-item-danger' : 'list-group-item-success'} py-2 px-3">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">${stepIcon}Step ${step.seq}: ${escapeHtml(step.description)}</h6>
                                <small>Status: <span class="badge ${step.is_failing ? 'bg-danger' : 'bg-success'}">${escapeHtml(stepStatusText)}</span></small>
                            </div>
                            ${step.input ? `<p class="mb-1 mt-1 small"><strong>Input:</strong> <pre class="mb-0 bg-white p-1 border rounded">${escapeHtml(step.input)}</pre></p>` : ''}
                            ${step.is_failing && step.error_details ? `<p class="mb-0 mt-1 small text-danger"><strong>Error:</strong> <pre class="mb-0 bg-white p-1 border rounded">${escapeHtml(step.error_details)}</pre></p>` : ''}
                        </li>
                    `;
                });
            } else {
                 html += '<li class="list-group-item">No specific steps to display based on current filter logic.</li>';
            }
            html += '</ul>';
        } else {
            html += '<p>No detailed steps available for this test case.</p>';
        }

        html += `
                    </div>
                </div>
            </div>
        `;
    });

    html += '</div>'; // Close accordion
    return html;
}


# SmartTest Authentication Configuration Guide

This guide explains how to configure the SmartTest authentication system, including user management, roles, and security settings.

## Table of Contents

1. [Overview](#overview)
2. [Environment Configuration](#environment-configuration)
3. [User Management](#user-management)
4. [Role-Based Access Control](#role-based-access-control)
5. [Security Settings](#security-settings)
6. [Session Management](#session-management)
7. [Development Mode](#development-mode)
8. [Troubleshooting](#troubleshooting)

## Overview

The SmartTest authentication system provides:
- JWT-based authentication with secure session management
- Role-based access control (RBAC)
- Configurable user management
- Account lockout and rate limiting
- Comprehensive audit logging
- CSRF protection and security headers

## Environment Configuration

### Required Environment Variables

Create a `.env` file in the `frontend/server` directory:

```bash
# Authentication Settings
SESSION_SECRET=your-super-secure-secret-key-here
JWT_SECRET=your-jwt-secret-key-here
SESSION_TIMEOUT=3600

# Development Mode (optional)
NODE_ENV=development
DEV_MODE_ENABLED=true
DEV_USER_UID=<EMAIL>
DEV_USER_ROLE=admin

# Security Settings
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX_ATTEMPTS=5
ACCOUNT_LOCKOUT_DURATION=900000
PROGRESSIVE_LOCKOUT=true

# Logging
LOG_LEVEL=info
AUDIT_LOG_ENABLED=true
```

### Environment-Specific Configuration

#### Production Environment
```bash
NODE_ENV=production
SESSION_SECRET=your-production-secret-minimum-64-characters-long
JWT_SECRET=your-production-jwt-secret-minimum-64-characters-long
SESSION_TIMEOUT=1800
DEV_MODE_ENABLED=false
HTTPS_ONLY=true
```

#### Development Environment
```bash
NODE_ENV=development
DEV_MODE_ENABLED=true
DEV_USER_UID=<EMAIL>
DEV_USER_ROLE=admin
SESSION_TIMEOUT=7200
```

#### Test Environment
```bash
NODE_ENV=test
SESSION_SECRET=test-secret-key
JWT_SECRET=test-jwt-secret
SESSION_TIMEOUT=300
```

## User Management

### Allowed Users Configuration

Users are managed through the `allowed-users.json` file located at `frontend/server/config/allowed-users.json`:

```json
{
  "users": [
    {
      "uid": "<EMAIL>",
      "password": "$2b$12$hashed.password.here",
      "role": "admin",
      "name": "System Administrator",
      "active": true,
      "createdAt": "2024-01-01T00:00:00.000Z",
      "lastLogin": null
    },
    {
      "uid": "<EMAIL>",
      "password": "$2b$12$another.hashed.password",
      "role": "tester",
      "name": "Test Engineer",
      "active": true,
      "createdAt": "2024-01-01T00:00:00.000Z",
      "lastLogin": "2024-01-15T10:30:00.000Z"
    },
    {
      "uid": "<EMAIL>",
      "password": "$2b$12$viewer.hashed.password",
      "role": "viewer",
      "name": "Test Viewer",
      "active": true,
      "createdAt": "2024-01-01T00:00:00.000Z",
      "lastLogin": null
    }
  ],
  "lastModified": "2024-01-01T00:00:00.000Z",
  "version": "1.0"
}
```

### Adding New Users

#### Method 1: Admin Interface
1. Login as an admin user
2. Navigate to `/admin`
3. Use the "Add User" form
4. Fill in user details and assign role
5. User will be added with hashed password

#### Method 2: Command Line Tool
```bash
cd frontend/server
node scripts/add-user.js --uid <EMAIL> --password SecurePass123! --role tester --name "Test User"
```

#### Method 3: Manual Configuration
1. Generate password hash:
```javascript
const bcrypt = require('bcrypt');
const hash = await bcrypt.hash('password', 12);
console.log(hash);
```

2. Add user to `allowed-users.json`:
```json
{
  "uid": "<EMAIL>",
  "password": "$2b$12$generated.hash.here",
  "role": "tester",
  "name": "New User",
  "active": true,
  "createdAt": "2024-01-15T00:00:00.000Z",
  "lastLogin": null
}
```

### User Properties

| Property | Type | Required | Description |
|----------|------|----------|-------------|
| `uid` | string | Yes | Unique user identifier (email format) |
| `password` | string | Yes | Bcrypt hashed password |
| `role` | string | Yes | User role (admin, tester, viewer) |
| `name` | string | Yes | Display name |
| `active` | boolean | Yes | Whether user can login |
| `createdAt` | string | Yes | ISO timestamp of creation |
| `lastLogin` | string | No | ISO timestamp of last login |

## Role-Based Access Control

### Available Roles

#### Admin Role
- **Permissions**: `read`, `write`, `delete`, `manage_users`, `view_logs`
- **Access**: Full system access including user management
- **Routes**: All routes including `/admin/*`

#### Tester Role
- **Permissions**: `read`, `write`
- **Access**: Can run tests and view results
- **Routes**: Test execution and reporting routes

#### Viewer Role
- **Permissions**: `read`
- **Access**: Read-only access to test results
- **Routes**: View-only routes

### Permission Mapping

```javascript
const rolePermissions = {
  admin: ['read', 'write', 'delete', 'manage_users', 'view_logs'],
  tester: ['read', 'write'],
  viewer: ['read']
};
```

### Route Protection Examples

```javascript
// Require specific permission
app.get('/api/tests', validateJWTToken, requirePermissions('read'), handler);

// Require specific role
app.post('/admin/users', validateJWTToken, requireRoles('admin'), handler);

// Multiple permissions required
app.delete('/api/tests/:id', validateJWTToken, requirePermissions(['write', 'delete']), handler);
```

## Security Settings

### Account Lockout Configuration

Configure in `frontend/server/auth/account-lockout.js`:

```javascript
this.config = {
  // Account lockout settings
  maxFailedAttempts: 5,           // Failed attempts before lockout
  lockoutDuration: 15 * 60 * 1000, // 15 minutes
  progressiveLockout: true,       // Increase lockout time with repeated failures
  
  // IP-based rate limiting
  maxIPAttempts: 20,              // Per IP across all accounts
  ipLockoutDuration: 30 * 60 * 1000, // 30 minutes
  
  // Suspicious activity detection
  maxAttemptsPerMinute: 10,       // Attempts per minute threshold
  suspiciousThreshold: 50,        // Failed attempts to mark IP as suspicious
};
```

### Rate Limiting Configuration

Configure in `frontend/server/middleware/security.js`:

```javascript
const rateLimiters = {
  // General API rate limiting
  api: rateLimit({
    windowMs: 15 * 60 * 1000,     // 15 minutes
    max: 100,                     // Requests per window
  }),
  
  // Authentication endpoints
  auth: rateLimit({
    windowMs: 15 * 60 * 1000,     // 15 minutes
    max: 5,                       // Login attempts per window
  }),
  
  // Admin endpoints
  admin: rateLimit({
    windowMs: 15 * 60 * 1000,     // 15 minutes
    max: 50,                      // Admin requests per window
  })
};
```

### CSRF Protection

CSRF protection is automatically enabled for state-changing operations (POST, PUT, DELETE, PATCH).

Exempt routes can be configured:
```javascript
app.use(csrfProtection.middleware(['/auth', '/csrf-token']));
```

### Security Headers

Security headers are automatically applied. Configuration in `frontend/server/middleware/security.js`:

```javascript
// Content Security Policy
"default-src 'self'; script-src 'self' https://cdn.jsdelivr.net; ..."

// Other headers
"X-Frame-Options": "DENY"
"X-Content-Type-Options": "nosniff"
"X-XSS-Protection": "1; mode=block"
"Referrer-Policy": "strict-origin-when-cross-origin"
```

## Session Management

### JWT Token Configuration

```javascript
// Access token settings
accessTokenExpiry: 15 * 60,      // 15 minutes
refreshTokenExpiry: 7 * 24 * 60 * 60, // 7 days

// Token signing
jwtSecret: process.env.JWT_SECRET,
algorithm: 'HS256'
```

### Cookie Configuration

```javascript
const cookieOptions = {
  httpOnly: true,                 // Prevent XSS
  secure: process.env.NODE_ENV === 'production', // HTTPS only in production
  sameSite: 'strict',            // CSRF protection
  path: '/'
};
```

### Session Storage

Sessions are stored in memory by default. For production, configure Redis:

```javascript
// In session-manager.js
const redis = require('redis');
const client = redis.createClient(process.env.REDIS_URL);
```

## Development Mode

### Enabling Development Mode

Set in `.env`:
```bash
NODE_ENV=development
DEV_MODE_ENABLED=true
DEV_USER_UID=<EMAIL>
DEV_USER_ROLE=admin
```

### Development Features

- Bypasses authentication for configured dev user
- Relaxed security headers
- Detailed error messages
- Extended session timeouts
- Security test endpoints available

### Security Warnings

Development mode will log warnings:
```
⚠️ Development mode enabled - authentication <NAME_EMAIL>
⚠️ Using generated JWT secret. Set SESSION_SECRET environment variable for production.
```

## Troubleshooting

### Common Issues

#### 1. Authentication Failures
```bash
# Check user exists and is active
cat frontend/server/config/allowed-users.json | grep "<EMAIL>"

# Verify password hash
node -e "console.log(require('bcrypt').compareSync('password', 'hash'))"
```

#### 2. Session Issues
```bash
# Check JWT secret is set
echo $JWT_SECRET

# Verify session timeout
grep SESSION_TIMEOUT .env
```

#### 3. Permission Denied
```bash
# Check user role and permissions
# View audit logs for permission violations
tail -f frontend/server/logs/security.log
```

#### 4. Account Lockout
```bash
# Check lockout status via admin interface
curl -X GET http://localhost:3000/admin/lockout/<EMAIL>

# Unlock account
curl -X POST http://localhost:3000/admin/unlock/<EMAIL>
```

### Log Files

- **Audit Log**: `frontend/server/logs/audit.log`
- **Security Log**: `frontend/server/logs/security.log`
- **Error Log**: `frontend/server/logs/error.log`

### Debug Commands

```bash
# Test authentication
curl -X POST http://localhost:3000/auth/login \
  -H "Content-Type: application/json" \
  -d '{"uid":"<EMAIL>","password":"password"}'

# Check security headers
curl -I http://localhost:3000/security-test/headers

# View security statistics
curl http://localhost:3000/admin/security-stats
```

### Configuration Validation

Use the built-in validation endpoint:
```bash
curl http://localhost:3000/security-test/config
```

This returns current security configuration and feature flags.

## Next Steps

After configuring authentication:
1. Review [Security Best Practices Guide](security-best-practices.md)
2. Set up monitoring and alerting
3. Configure backup procedures for user data
4. Test the configuration thoroughly
5. Document any custom modifications

{"name": "SmartTest Development Environment", "image": "mcr.microsoft.com/devcontainers/javascript-node:1-18-bullseye", "features": {"ghcr.io/devcontainers/features/github-cli:1": {}, "ghcr.io/devcontainers/features/docker-in-docker:2": {}}, "forwardPorts": [3000, 5000, 8080], "portsAttributes": {"3000": {"label": "SmartTest Frontend", "onAutoForward": "notify"}, "5000": {"label": "SmartTest API Server", "onAutoForward": "notify"}}, "postCreateCommand": "npm install && npm run build", "customizations": {"vscode": {"extensions": ["GitHub.copilot", "GitHub.copilot-chat", "ms-vscode.vscode-json", "bradlc.vscode-tailwindcss", "esbenp.prettier-vscode", "dbaeumer.vscode-eslint"], "settings": {"github.copilot.enable": {"*": true, "yaml": false, "plaintext": false, "markdown": false}}}}, "remoteUser": "node"}
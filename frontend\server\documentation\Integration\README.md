# SmartTest Integration Documentation

This directory contains documentation for integrating the different layers of the SmartTest application:

1. [Frontend-API-Database Integration Guide](frontend-api-database-integration.md) - Comprehensive guide for integrating the frontend, API, and database layers
2. [User Scenarios](user-scenarios.md) - Documentation of key user scenarios and how they flow through the system layers
3. [Parameter Mapping](parameter-mapping.md) - Detailed mapping of parameters between different layers
4. [Response Mapping](response-mapping.md) - Detailed mapping of response structures between different layers
5. [Verification Points](verification-points.md) - List of external API calls and database queries that need verification

These documents provide a complete reference for understanding how the different layers of the SmartTest application interact with each other.

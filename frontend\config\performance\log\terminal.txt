 NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id) AS total_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'P') AS passed_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'F') AS failed_cases
      FROM test_session ts
     ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?
[GET_RECENT_RUNS] Final Params: [100]
✅ Database Query: Retrieved 100 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-07-28 13:39:11, end_ts: 2025-07-28 13:39:22
Row 2 start_ts: 2025-07-28 13:38:23, end_ts: 2025-07-28 13:38:43
Row 3 start_ts: 2025-07-28 13:38:06, end_ts: 2025-07-28 13:38:32
⏳ Processing 100 sessions in 20 batches...
✅ Processed 100 sessions successfully
Retrieved 100 recent runs
[2025-07-28T13:49:28.551Z] GET /local/recent-runs?since_id=18014 from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read    
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read    
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read    
GET /local/recent-runs
[recent-runs] Filtering with since_id: 18014
Fetching recent runs with filters: { since_id: 18014 }
[GET_RECENT_RUNS] Adding since_id filter: tsn_id > 18014
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id) AS total_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'P') AS passed_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'F') AS failed_cases
      FROM test_session ts
     WHERE ts.tsn_id > ? ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?    
[GET_RECENT_RUNS] Final Params: [18014,100]
✅ Database Query: Retrieved 1 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-07-28 13:39:11, end_ts: 2025-07-28 13:39:22
⏳ Processing 1 sessions in 1 batches...
✅ Processed 1 sessions successfully
Retrieved 1 recent runs
[2025-07-28T13:49:34.716Z] GET /local/recent-runs?limit=20 from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read    
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read    
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read    
GET /local/recent-runs
Fetching recent runs with filters: { limit: 20 }
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id) AS total_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'P') AS passed_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'F') AS failed_cases
      FROM test_session ts
     ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?
[GET_RECENT_RUNS] Final Params: [20]
[2025-07-28T13:49:38.562Z] GET /local/recent-runs?since_id=18014 from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read    
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read    
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read    
GET /local/recent-runs
[recent-runs] Filtering with since_id: 18014
Fetching recent runs with filters: { since_id: 18014 }
[GET_RECENT_RUNS] Adding since_id filter: tsn_id > 18014
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id) AS total_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'P') AS passed_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'F') AS failed_cases
      FROM test_session ts
     WHERE ts.tsn_id > ? ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?    
[GET_RECENT_RUNS] Final Params: [18014,100]
✅ Database Query: Retrieved 20 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-07-28 13:39:11, end_ts: 2025-07-28 13:39:22
Row 2 start_ts: 2025-07-28 13:38:23, end_ts: 2025-07-28 13:38:43
Row 3 start_ts: 2025-07-28 13:38:06, end_ts: 2025-07-28 13:38:32
⏳ Processing 20 sessions in 4 batches...
✅ Processed 20 sessions successfully
Retrieved 20 recent runs
✅ Database Query: Retrieved 1 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-07-28 13:39:11, end_ts: 2025-07-28 13:39:22
⏳ Processing 1 sessions in 1 batches...
✅ Processed 1 sessions successfully
Retrieved 1 recent runs
[2025-07-28T13:49:44.229Z] GET /local/recent-runs?since_id=18014 from ::1     
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read    
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read    
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read    
GET /local/recent-runs
[recent-runs] Filtering with since_id: 18014
Fetching recent runs with filters: { since_id: 18014 }
[GET_RECENT_RUNS] Adding since_id filter: tsn_id > 18014
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id) AS total_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'P') AS passed_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'F') AS failed_cases
      FROM test_session ts
     WHERE ts.tsn_id > ? ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?    
[GET_RECENT_RUNS] Final Params: [18014,100]
✅ Database Query: Retrieved 1 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-07-28 13:39:11, end_ts: 2025-07-28 13:39:22
⏳ Processing 1 sessions in 1 batches...
✅ Processed 1 sessions successfully
Retrieved 1 recent runs
[2025-07-28T13:49:51.037Z] GET /csrf-token from ::1
✅ JWT Token validated for user: <EMAIL>
🔑 CSRF token generated for session: 1f372b757a7f7ca7b2df3df426b2ed482756c401c7fda2ef836738e89ed988c7
🔑 Token: 2ca08c22...
[2025-07-28T13:49:51.048Z] POST /api/suite-runner from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read    
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read    
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: write   
🛡️ CSRF validation for POST /suite-runner:
   Token provided: YES (2ca08c22...)
   Session ID: 1f372b757a7f7ca7b2df3df426b2ed482756c401c7fda2ef836738e89ed988c7
   Token valid: true
✅ CSRF validation passed for POST /suite-runner
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: write   
🛡️ CSRF validation for POST /suite-runner:
   Token provided: YES (2ca08c22...)
   Session ID: 1f372b757a7f7ca7b2df3df426b2ed482756c401c7fda2ef836738e89ed988c7
   Token valid: true
✅ CSRF validation passed for POST /suite-runner
[API /suite-runner] Received request with params: { ts_id: '312' }
[API /case-runner] Forwarding request to external API: http://mprts-qa02.lab.wagerworks.com:5080/AutoRun/CaseRunner
[2025-07-28T13:49:51.242Z] GET /local/recent-runs?since_id=18014 from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read    
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read    
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read    
GET /local/recent-runs
[recent-runs] Filtering with since_id: 18014
Fetching recent runs with filters: { since_id: 18014 }
[GET_RECENT_RUNS] Adding since_id filter: tsn_id > 18014
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id) AS total_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'P') AS passed_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'F') AS failed_cases
      FROM test_session ts
     WHERE ts.tsn_id > ? ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?    
[GET_RECENT_RUNS] Final Params: [18014,100]
[Service case-runner] Raw response text received from external API:
--------------------- START RESPONSE ---------------------


<html>
<head>
<title>View Your Test Result</title>
<script type="text/javascript" src="jquery-ui-1.11.0.custom/external/jquery/jquery.js"></script>
<script src="js/lib.js"></script>
<script src="js/menu.js"></script>
<link rel="stylesheet" type="text/css" href="css/table.css">
</head>
<body>
        <a href="home" class="clickable">Home</a> &nbsp;
<span id="case" class="clickable">Case#</span> &nbsp;
<span id="suite" class="clickable">Suite#</span> &nbsp;
<span id="project" class="clickable">Project#</span> &nbsp;
<a href="ReportList?tp_id=201&tp_id=202" class="clickable">Report</a>
<p/>
        Test Result<p/>
        Your test session id: 18016<p/>
        <a href="ReportSummary?tsn_id=18016">View Test Result Summary</a><p/> 
        <a href="ReportDetails?tsn_id=18016">View Test Result Details</a><p/> 
ID: <a href="ProfileLoader" class="clickable"></a><p/>

</body>
</html>

---------------------- END RESPONSE ----------------------
✅ Database Query: Retrieved 2 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-07-28 13:49:52, end_ts: 2025-07-28 13:49:52
Row 2 start_ts: 2025-07-28 13:39:11, end_ts: 2025-07-28 13:39:22
⏳ Processing 2 sessions in 1 batches...
✅ Processed 2 sessions successfully
Retrieved 2 recent runs
[2025-07-28T13:50:01.246Z] GET /local/recent-runs?since_id=18015 from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read    
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read    
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read    
GET /local/recent-runs
[recent-runs] Filtering with since_id: 18015
Fetching recent runs with filters: { since_id: 18015 }
[GET_RECENT_RUNS] Adding since_id filter: tsn_id > 18015
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id) AS total_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'P') AS passed_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'F') AS failed_cases
      FROM test_session ts
     WHERE ts.tsn_id > ? ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?    
[GET_RECENT_RUNS] Final Params: [18015,100]
✅ Database Query: Retrieved 1 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-07-28 13:49:52, end_ts: 2025-07-28 13:49:52
⏳ Processing 1 sessions in 1 batches...
✅ Processed 1 sessions successfully
Retrieved 1 recent runs
[2025-07-28T13:50:11.249Z] GET /local/recent-runs?since_id=18015 from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read    
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read    
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read    
GET /local/recent-runs
[recent-runs] Filtering with since_id: 18015
Fetching recent runs with filters: { since_id: 18015 }
[GET_RECENT_RUNS] Adding since_id filter: tsn_id > 18015
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id) AS total_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'P') AS passed_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'F') AS failed_cases
      FROM test_session ts
     WHERE ts.tsn_id > ? ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?    
[GET_RECENT_RUNS] Final Params: [18015,100]
✅ Database Query: Retrieved 1 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-07-28 13:49:52, end_ts: 2025-07-28 13:50:08
⏳ Processing 1 sessions in 1 batches...
✅ Processed 1 sessions successfully
Retrieved 1 recent runs
[2025-07-28T13:50:21.241Z] GET /local/recent-runs?since_id=18015 from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read    
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read    
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read    
GET /local/recent-runs
[recent-runs] Filtering with since_id: 18015
Fetching recent runs with filters: { since_id: 18015 }
[GET_RECENT_RUNS] Adding since_id filter: tsn_id > 18015
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id) AS total_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'P') AS passed_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'F') AS failed_cases
      FROM test_session ts
     WHERE ts.tsn_id > ? ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?    
[GET_RECENT_RUNS] Final Params: [18015,100]
✅ Database Query: Retrieved 1 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-07-28 13:49:52, end_ts: 2025-07-28 13:50:24
⏳ Processing 1 sessions in 1 batches...
✅ Processed 1 sessions successfully
Retrieved 1 recent runs
[2025-07-28T13:50:26.995Z] GET /local/recent-runs? from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read    
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read    
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read    
GET /local/recent-runs
Fetching recent runs with filters: {}
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id) AS total_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'P') AS passed_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'F') AS failed_cases
      FROM test_session ts
     ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?
[GET_RECENT_RUNS] Final Params: [100]
✅ Database Query: Retrieved 100 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-07-28 13:49:52, end_ts: 2025-07-28 13:50:30
Row 2 start_ts: 2025-07-28 13:39:11, end_ts: 2025-07-28 13:39:22
Row 3 start_ts: 2025-07-28 13:38:23, end_ts: 2025-07-28 13:38:43
⏳ Processing 100 sessions in 20 batches...
✅ Processed 100 sessions successfully
Retrieved 100 recent runs
[2025-07-28T13:50:41.244Z] GET /local/recent-runs?since_id=18015 from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read    
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read    
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read    
GET /local/recent-runs
[recent-runs] Filtering with since_id: 18015
Fetching recent runs with filters: { since_id: 18015 }
[GET_RECENT_RUNS] Adding since_id filter: tsn_id > 18015
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id) AS total_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'P') AS passed_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'F') AS failed_cases
      FROM test_session ts
     WHERE ts.tsn_id > ? ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?    
[GET_RECENT_RUNS] Final Params: [18015,100]
✅ Database Query: Retrieved 1 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-07-28 13:49:52, end_ts: 2025-07-28 13:50:35
⏳ Processing 1 sessions in 1 batches...
✅ Processed 1 sessions successfully
Retrieved 1 recent runs
[2025-07-28T13:50:51.262Z] GET /local/recent-runs?since_id=18015 from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read    
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read    
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read    
GET /local/recent-runs
[recent-runs] Filtering with since_id: 18015
Fetching recent runs with filters: { since_id: 18015 }
[GET_RECENT_RUNS] Adding since_id filter: tsn_id > 18015
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id) AS total_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'P') AS passed_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'F') AS failed_cases
      FROM test_session ts
     WHERE ts.tsn_id > ? ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?    
[GET_RECENT_RUNS] Final Params: [18015,100]
✅ Database Query: Retrieved 1 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-07-28 13:49:52, end_ts: 2025-07-28 13:50:35
⏳ Processing 1 sessions in 1 batches...
✅ Processed 1 sessions successfully
Retrieved 1 recent runs
[2025-07-28T13:51:01.246Z] GET /local/recent-runs?since_id=18015 from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read    
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read    
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read    
GET /local/recent-runs
[recent-runs] Filtering with since_id: 18015
Fetching recent runs with filters: { since_id: 18015 }
[GET_RECENT_RUNS] Adding since_id filter: tsn_id > 18015
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id) AS total_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'P') AS passed_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'F') AS failed_cases
      FROM test_session ts
     WHERE ts.tsn_id > ? ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?    
[GET_RECENT_RUNS] Final Params: [18015,100]
✅ Database Query: Retrieved 1 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-07-28 13:49:52, end_ts: 2025-07-28 13:50:35
⏳ Processing 1 sessions in 1 batches...
✅ Processed 1 sessions successfully
Retrieved 1 recent runs
[2025-07-28T13:51:11.254Z] GET /local/recent-runs?since_id=18015 from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read    
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read    
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read    
GET /local/recent-runs
[recent-runs] Filtering with since_id: 18015
Fetching recent runs with filters: { since_id: 18015 }
[GET_RECENT_RUNS] Adding since_id filter: tsn_id > 18015
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id) AS total_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'P') AS passed_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'F') AS failed_cases
      FROM test_session ts
     WHERE ts.tsn_id > ? ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?    
[GET_RECENT_RUNS] Final Params: [18015,100]
[2025-07-28T13:51:11.988Z] GET /auth/validate from ::1
✅ Database Query: Retrieved 1 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-07-28 13:49:52, end_ts: 2025-07-28 13:50:35
⏳ Processing 1 sessions in 1 batches...
✅ Processed 1 sessions successfully
Retrieved 1 recent runs
[2025-07-28T13:51:21.246Z] GET /local/recent-runs?since_id=18015 from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read    
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read    
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read    
GET /local/recent-runs
[recent-runs] Filtering with since_id: 18015
Fetching recent runs with filters: { since_id: 18015 }
[GET_RECENT_RUNS] Adding since_id filter: tsn_id > 18015
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id) AS total_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'P') AS passed_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'F') AS failed_cases
      FROM test_session ts
     WHERE ts.tsn_id > ? ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?    
[GET_RECENT_RUNS] Final Params: [18015,100]
✅ Database Query: Retrieved 1 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-07-28 13:49:52, end_ts: 2025-07-28 13:50:35
⏳ Processing 1 sessions in 1 batches...
✅ Processed 1 sessions successfully
Retrieved 1 recent runs
[2025-07-28T13:51:31.259Z] GET /local/recent-runs?since_id=18015 from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read    
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read    
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read    
GET /local/recent-runs
[recent-runs] Filtering with since_id: 18015
Fetching recent runs with filters: { since_id: 18015 }
[GET_RECENT_RUNS] Adding since_id filter: tsn_id > 18015
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id) AS total_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'P') AS passed_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'F') AS failed_cases
      FROM test_session ts
     WHERE ts.tsn_id > ? ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?    
[GET_RECENT_RUNS] Final Params: [18015,100]
✅ Database Query: Retrieved 1 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-07-28 13:49:52, end_ts: 2025-07-28 13:50:35
⏳ Processing 1 sessions in 1 batches...
✅ Processed 1 sessions successfully
Retrieved 1 recent runs
[2025-07-28T13:51:36.937Z] GET /local/recent-runs? from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read    
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read    
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read    
GET /local/recent-runs
Fetching recent runs with filters: {}
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id) AS total_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'P') AS passed_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'F') AS failed_cases
      FROM test_session ts
     ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?
[GET_RECENT_RUNS] Final Params: [100]
✅ Database Query: Retrieved 100 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-07-28 13:49:52, end_ts: 2025-07-28 13:50:35
Row 2 start_ts: 2025-07-28 13:39:11, end_ts: 2025-07-28 13:39:22
Row 3 start_ts: 2025-07-28 13:38:23, end_ts: 2025-07-28 13:38:43
⏳ Processing 100 sessions in 20 batches...
✅ Processed 100 sessions successfully
Retrieved 100 recent runs
[2025-07-28T13:51:51.246Z] GET /local/recent-runs?since_id=18015 from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read    
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read    
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read    
GET /local/recent-runs
[recent-runs] Filtering with since_id: 18015
Fetching recent runs with filters: { since_id: 18015 }
[GET_RECENT_RUNS] Adding since_id filter: tsn_id > 18015
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id) AS total_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'P') AS passed_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'F') AS failed_cases
      FROM test_session ts
     WHERE ts.tsn_id > ? ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?    
[GET_RECENT_RUNS] Final Params: [18015,100]
✅ Database Query: Retrieved 1 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-07-28 13:49:52, end_ts: 2025-07-28 13:50:35
⏳ Processing 1 sessions in 1 batches...
✅ Processed 1 sessions successfully
Retrieved 1 recent runs
[2025-07-28T13:52:01.251Z] GET /local/recent-runs?since_id=18015 from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read    
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read    
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read    
GET /local/recent-runs
[recent-runs] Filtering with since_id: 18015
Fetching recent runs with filters: { since_id: 18015 }
[GET_RECENT_RUNS] Adding since_id filter: tsn_id > 18015
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id) AS total_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'P') AS passed_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'F') AS failed_cases
      FROM test_session ts
     WHERE ts.tsn_id > ? ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?    
[GET_RECENT_RUNS] Final Params: [18015,100]
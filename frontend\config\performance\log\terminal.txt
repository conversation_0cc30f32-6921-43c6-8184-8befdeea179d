    'operatorConfigs',
    'kafka_server',
    'dataCenter',
    'old_version',
    'networkType2',
    'sign',
    'rate_src'
  ]
}
[Service case-runner] Raw response text received from external API:
--------------------- START RESPONSE ---------------------


<html>
<head>
<title>View Your Test Result</title>
<script type="text/javascript" src="jquery-ui-1.11.0.custom/external/jquery/jquery.js"></script>
<script src="js/lib.js"></script>
<script src="js/menu.js"></script>
<link rel="stylesheet" type="text/css" href="css/table.css">
</head>
<body>
        <a href="home" class="clickable">Home</a> &nbsp;
<span id="case" class="clickable">Case#</span> &nbsp;
<span id="suite" class="clickable">Suite#</span> &nbsp;
<span id="project" class="clickable">Project#</span> &nbsp;
<a href="ReportList?tp_id=201&tp_id=202" class="clickable">Report</a>
<p/>
        Test Result<p/>
        Your test session id: 18070<p/>
        <a href="ReportSummary?tsn_id=18070">View Test Result Summary</a><p/>
        <a href="ReportDetails?tsn_id=18070">View Test Result Details</a><p/>
ID: <a href="ProfileLoader" class="clickable"></a><p/>

</body>
</html>

---------------------- END RESPONSE ----------------------
✅ Database Query: Retrieved 50 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-07-29 12:17:06, end_ts: 2025-07-29 12:17:06
Row 2 start_ts: 2025-07-29 12:06:41, end_ts: 2025-07-29 12:06:52
Row 3 start_ts: 2025-07-28 18:38:26, end_ts: 2025-07-28 18:38:59
⏳ Processing 50 sessions in 10 batches...
✅ Database Query: Retrieved 100 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-07-29 12:17:06, end_ts: 2025-07-29 12:17:06
Row 2 start_ts: 2025-07-29 12:06:41, end_ts: 2025-07-29 12:06:52
Row 3 start_ts: 2025-07-28 18:38:26, end_ts: 2025-07-28 18:38:59
⏳ Processing 100 sessions in 20 batches...
✅ Processed 50 sessions successfully
Retrieved 50 recent runs
✅ Processed 100 sessions successfully
Retrieved 100 recent runs
[2025-07-29T12:17:22.495Z] GET /local/recent-runs?limit=100&type=single_case from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
GET /local/recent-runs
Fetching recent runs with filters: { limit: 100, type: 'single_case' }
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Adding single test case filter (standalone test cases with total_cases=1 only)
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id) AS total_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'P') AS passed_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'F') AS failed_cases
      FROM test_session ts
     WHERE ts.tc_id IS NOT NULL AND ts.tc_id != "" AND (ts.ts_id IS NULL OR ts.ts_id = "") AND (SELECT COUNT(DISTINCT tr.tc_id) FROM test_result tr WHERE tr.tsn_id = ts.tsn_id) = 1 ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?
[GET_RECENT_RUNS] Final Params: [100]
[2025-07-29T12:17:26.491Z] GET /local/recent-runs?limit=50&type=single_case from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
GET /local/recent-runs
Fetching recent runs with filters: { limit: 50, type: 'single_case' }
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Adding single test case filter (standalone test cases with total_cases=1 only)
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id) AS total_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'P') AS passed_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'F') AS failed_cases
      FROM test_session ts
     WHERE ts.tc_id IS NOT NULL AND ts.tc_id != "" AND (ts.ts_id IS NULL OR ts.ts_id = "") AND (SELECT COUNT(DISTINCT tr.tc_id) FROM test_result tr WHERE tr.tsn_id = ts.tsn_id) = 1 ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?
[GET_RECENT_RUNS] Final Params: [50]
✅ Database Query: Retrieved 100 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-07-29 12:17:06, end_ts: 2025-07-29 12:17:22
Row 2 start_ts: 2025-07-29 12:06:41, end_ts: 2025-07-29 12:06:52
Row 3 start_ts: 2025-07-28 18:38:26, end_ts: 2025-07-28 18:38:59
⏳ Processing 100 sessions in 20 batches...
✅ Database Query: Retrieved 50 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-07-29 12:17:06, end_ts: 2025-07-29 12:17:22
Row 2 start_ts: 2025-07-29 12:06:41, end_ts: 2025-07-29 12:06:52
Row 3 start_ts: 2025-07-28 18:38:26, end_ts: 2025-07-28 18:38:59
⏳ Processing 50 sessions in 10 batches...
✅ Processed 100 sessions successfully
Retrieved 100 recent runs
✅ Processed 50 sessions successfully
Retrieved 50 recent runs
[2025-07-29T12:17:42.498Z] GET /local/recent-runs?limit=100&type=single_case from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
GET /local/recent-runs
Fetching recent runs with filters: { limit: 100, type: 'single_case' }
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Adding single test case filter (standalone test cases with total_cases=1 only)
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id) AS total_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'P') AS passed_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'F') AS failed_cases
      FROM test_session ts
     WHERE ts.tc_id IS NOT NULL AND ts.tc_id != "" AND (ts.ts_id IS NULL OR ts.ts_id = "") AND (SELECT COUNT(DISTINCT tr.tc_id) FROM test_result tr WHERE tr.tsn_id = ts.tsn_id) = 1 ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?
[GET_RECENT_RUNS] Final Params: [100]
[2025-07-29T12:17:50.492Z] GET /local/recent-runs?limit=50&type=single_case from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
GET /local/recent-runs
Fetching recent runs with filters: { limit: 50, type: 'single_case' }
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Adding single test case filter (standalone test cases with total_cases=1 only)
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id) AS total_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'P') AS passed_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'F') AS failed_cases
      FROM test_session ts
     WHERE ts.tc_id IS NOT NULL AND ts.tc_id != "" AND (ts.ts_id IS NULL OR ts.ts_id = "") AND (SELECT COUNT(DISTINCT tr.tc_id) FROM test_result tr WHERE tr.tsn_id = ts.tsn_id) = 1 ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?
[GET_RECENT_RUNS] Final Params: [50]
✅ Database Query: Retrieved 100 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-07-29 12:17:06, end_ts: 2025-07-29 12:17:22
Row 2 start_ts: 2025-07-29 12:06:41, end_ts: 2025-07-29 12:06:52
Row 3 start_ts: 2025-07-28 18:38:26, end_ts: 2025-07-28 18:38:59
⏳ Processing 100 sessions in 20 batches...
[2025-07-29T12:17:55.689Z] POST /api/external/login from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: write
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: write
POST /api/external/login
[External Login] Request body: { uid: '<EMAIL>', password: 'test' }
[External Login] Request headers: {
  host: 'localhost:3000',
  connection: 'keep-alive',
  'content-length': '58',
  'sec-ch-ua-platform': '"macOS"',
  'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  'sec-ch-ua': '"Chromium";v="135", "Not:A Brand";v="99", "Google Chrome";v="135"',
  'content-type': 'application/json',
  'sec-ch-ua-mobile': '?0',
  'accept-language': 'en-US,en;q=0.9',
  accept: '*/*',
  origin: 'http://localhost:3000',
  'sec-fetch-site': 'same-origin',
  'sec-fetch-mode': 'cors',
  'sec-fetch-dest': 'empty',
  referer: 'http://localhost:3000/config/index.html',
  'accept-encoding': 'gzip, deflate, br, zstd',
  cookie: 'accessToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.xxnO7_bzRN68ijGP7CqFt4S1Mt3SpQipvGAD6kEylHo; refreshToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************.E84JMUYxPCbChTpstPQyRgnwAZX000mQef7oq71YvU8; sessionId=ea185bd690eb92271601a4c24b09ad7d4f2beac2cc711a7d53cb69864e23e845'    
}
[External Login] Attempting login for user: <EMAIL>
[2025-07-29T12:17:55.702Z] POST /api/external/login from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: write
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: write
POST /api/external/login
[External Login] Request body: { uid: '<EMAIL>', password: 'test' }
[External Login] Request headers: {
  host: 'localhost:3000',
  connection: 'keep-alive',
  'content-length': '58',
  'sec-ch-ua-platform': '"macOS"',
  'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  'sec-ch-ua': '"Chromium";v="135", "Not:A Brand";v="99", "Google Chrome";v="135"',
  'content-type': 'application/json',
  'sec-ch-ua-mobile': '?0',
  'accept-language': 'en-US,en;q=0.9',
  accept: '*/*',
  origin: 'http://localhost:3000',
  'sec-fetch-site': 'same-origin',
  'sec-fetch-mode': 'cors',
  'sec-fetch-dest': 'empty',
  referer: 'http://localhost:3000/config/index.html',
  'accept-encoding': 'gzip, deflate, br, zstd',
  cookie: 'accessToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.xxnO7_bzRN68ijGP7CqFt4S1Mt3SpQipvGAD6kEylHo; refreshToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************.E84JMUYxPCbChTpstPQyRgnwAZX000mQef7oq71YvU8; sessionId=ea185bd690eb92271601a4c24b09ad7d4f2beac2cc711a7d53cb69864e23e845'    
}
[External Login] Attempting login for user: <EMAIL>
[External Login] Forwarding to http://mprts-qa02.lab.wagerworks.com:9080/AutoRun/Login
[External Login] Forwarding to http://mprts-qa02.lab.wagerworks.com:9080/AutoRun/Login
[External Login] External API response status: 200
[External Login] External API response headers: {
  'content-length': '5516',
  'content-type': 'text/html;charset=UTF-8',
  date: 'Tue, 29 Jul 2025 12:17:58 GMT',
  'set-cookie': 'JSESSIONID=D94AC70BDA30ABB8E130DA0D32DE1D7B; Path=/AutoRun; HttpOnly'
}
[External Login] Original cookie: JSESSIONID=D94AC70BDA30ABB8E130DA0D32DE1D7B; Path=/AutoRun; HttpOnly
[External Login] Modified cookie: JSESSIONID=D94AC70BDA30ABB8E130DA0D32DE1D7B; Path=/; HttpOnly
[External Login] Forwarding cookies to client: 1 cookies with fixed Path
[External Login] Extracted JSESSIONID from Set-Cookie: D94AC70BDA30ABB8E130DA0D32DE1D7B
[External Login] Stored JSESSIONID in session for future requests
[External Login] 🔧 Updating cookie-auth cache for user: <EMAIL> (normalized: <EMAIL>)
[CookieAuth] 🔧 Manually updated <NAME_EMAIL>: D94AC70B...
[CookieAuth] 🔧 Cache expires at: 2025-07-29T12:47:57.800Z
[CookieAuth] 🔧 Cache expires in: 1800s
[External Login] Set additional client-side cookie with path /
[External Login] Returning response with status: 200
[2025-07-29T12:17:57.813Z] GET /api/external/ReportSummary?tsn_id=18070 from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: write
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: write
GET /api/external/ReportSummary
[External ReportSummary] Incoming request cookies: accessToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.xxnO7_bzRN68ijGP7CqFt4S1Mt3SpQipvGAD6kEylHo; refreshToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************.E84JMUYxPCbChTpstPQyRgnwAZX000mQef7oq71YvU8; sessionId=ea185bd690eb92271601a4c24b09ad7d4f2beac2cc711a7d53cb69864e23e845; JSESSIONID=D94AC70BDA30ABB8E130DA0D32DE1D7B
[External ReportSummary] Extracted JSESSIONID: D94AC70BDA30ABB8E130DA0D32DE1D7B
[External ReportSummary] 🔍 Session Decision Process Starting...
[External ReportSummary] 🔍 User from JWT: <EMAIL>
[External ReportSummary] 🔍 Browser JSESSIONID: D94AC70B...
[External ReportSummary] 🔍 Session JSESSIONID: D94AC70B...
[External ReportSummary] 🔍 Cache Status: {
  "totalEntries": 1,
  "entries": [
    {
      "uid": "<EMAIL>",
      "jsessionId": "D94AC70B...",
      "expiresIn": 1800,
      "isValid": true,
      "expiresAt": "2025-07-29T12:47:57.800Z"
    }
  ]
}
[External ReportSummary] 🔍 Checking cached session for user: <EMAIL> (normalized: <EMAIL>)
[CookieAuth] 🔍 Checking cached session for user: <EMAIL>
[CookieAuth] 🔍 Cache entry exists: true
[CookieAuth] 🔍 Cache entry expires in: 1800s
[CookieAuth] 🔍 Cache entry valid: true
[CookieAuth] 🔍 Cached JSESSIONID: D94AC70B...
[CookieAuth] ✅ Using cached <NAME_EMAIL>
[External ReportSummary] ✅ Using cached JSESSIONID: D94AC70B... (SOURCE: CACHE)
[External ReportSummary] Forwarding to http://mprts-qa02.lab.wagerworks.com:9080/AutoRun/ReportSummary?tsn_id=18070
[External ReportSummary] Using JSESSIONID: D94AC70BDA30ABB8E130DA0D32DE1D7B
[External Login] External API response status: 200
[External Login] External API response headers: {
  'content-length': '5516',
  'content-type': 'text/html;charset=UTF-8',
  date: 'Tue, 29 Jul 2025 12:17:58 GMT',
  'set-cookie': 'JSESSIONID=B8AF002792BE3FD32B25C03D51C49C5A; Path=/AutoRun; HttpOnly'
}
[External Login] Original cookie: JSESSIONID=B8AF002792BE3FD32B25C03D51C49C5A; Path=/AutoRun; HttpOnly
[External Login] Modified cookie: JSESSIONID=B8AF002792BE3FD32B25C03D51C49C5A; Path=/; HttpOnly
[External Login] Forwarding cookies to client: 1 cookies with fixed Path
[External Login] Extracted JSESSIONID from Set-Cookie: B8AF002792BE3FD32B25C03D51C49C5A
[External Login] Stored JSESSIONID in session for future requests
[External Login] 🔧 Updating cookie-auth cache for user: <EMAIL> (normalized: <EMAIL>)
[CookieAuth] 🔧 Manually updated <NAME_EMAIL>: B8AF0027...
[CookieAuth] 🔧 Cache expires at: 2025-07-29T12:47:57.861Z
[CookieAuth] 🔧 Cache expires in: 1800s
[External Login] Set additional client-side cookie with path /
[External Login] Returning response with status: 200
[2025-07-29T12:17:57.868Z] GET /api/external/ReportSummary?tsn_id=18070 from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: write
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: write
GET /api/external/ReportSummary
[External ReportSummary] Incoming request cookies: accessToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.xxnO7_bzRN68ijGP7CqFt4S1Mt3SpQipvGAD6kEylHo; refreshToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************.E84JMUYxPCbChTpstPQyRgnwAZX000mQef7oq71YvU8; sessionId=ea185bd690eb92271601a4c24b09ad7d4f2beac2cc711a7d53cb69864e23e845; JSESSIONID=B8AF002792BE3FD32B25C03D51C49C5A
[External ReportSummary] Extracted JSESSIONID: B8AF002792BE3FD32B25C03D51C49C5A
[External ReportSummary] 🔍 Session Decision Process Starting...
[External ReportSummary] 🔍 User from JWT: <EMAIL>
[External ReportSummary] 🔍 Browser JSESSIONID: B8AF0027...
[External ReportSummary] 🔍 Session JSESSIONID: B8AF0027...
[External ReportSummary] 🔍 Cache Status: {
  "totalEntries": 1,
  "entries": [
    {
      "uid": "<EMAIL>",
      "jsessionId": "B8AF0027...",
      "expiresIn": 1800,
      "isValid": true,
      "expiresAt": "2025-07-29T12:47:57.861Z"
    }
  ]
}
[External ReportSummary] 🔍 Checking cached session for user: <EMAIL> (normalized: <EMAIL>)
[CookieAuth] 🔍 Checking cached session for user: <EMAIL>
[CookieAuth] 🔍 Cache entry exists: true
[CookieAuth] 🔍 Cache entry expires in: 1800s
[CookieAuth] 🔍 Cache entry valid: true
[CookieAuth] 🔍 Cached JSESSIONID: B8AF0027...
[CookieAuth] ✅ Using cached <NAME_EMAIL>
[External ReportSummary] ✅ Using cached JSESSIONID: B8AF0027... (SOURCE: CACHE)
[External ReportSummary] Forwarding to http://mprts-qa02.lab.wagerworks.com:9080/AutoRun/ReportSummary?tsn_id=18070
[External ReportSummary] Using JSESSIONID: B8AF002792BE3FD32B25C03D51C49C5A
✅ Processed 100 sessions successfully
Retrieved 100 recent runs
[2025-07-29T12:17:58.099Z] GET /api/external/ReportDetails?tsn_id=18070 from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: write
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: write
GET /api/external/ReportDetails
[External ReportDetails] Incoming request cookies: accessToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.xxnO7_bzRN68ijGP7CqFt4S1Mt3SpQipvGAD6kEylHo; refreshToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************.E84JMUYxPCbChTpstPQyRgnwAZX000mQef7oq71YvU8; sessionId=ea185bd690eb92271601a4c24b09ad7d4f2beac2cc711a7d53cb69864e23e845; JSESSIONID=B8AF002792BE3FD32B25C03D51C49C5A
[External ReportDetails] Extracted JSESSIONID: B8AF002792BE3FD32B25C03D51C49C5A
[External ReportDetails] 🔍 Session Decision Process Starting...
[External ReportDetails] 🔍 User from JWT: <EMAIL>
[External ReportDetails] 🔍 Browser JSESSIONID: B8AF0027...
[External ReportDetails] 🔍 Session JSESSIONID: B8AF0027...
[External ReportDetails] 🔍 Cache Status: {
  "totalEntries": 1,
  "entries": [
    {
      "uid": "<EMAIL>",
      "jsessionId": "B8AF0027...",
      "expiresIn": 1800,
      "isValid": true,
      "expiresAt": "2025-07-29T12:47:57.861Z"
    }
  ]
}
[External ReportDetails] 🔍 Checking cached session for user: <EMAIL> (normalized: <EMAIL>)
[CookieAuth] 🔍 Checking cached session for user: <EMAIL>
[CookieAuth] 🔍 Cache entry exists: true
[CookieAuth] 🔍 Cache entry expires in: 1800s
[CookieAuth] 🔍 Cache entry valid: true
[CookieAuth] 🔍 Cached JSESSIONID: B8AF0027...
[CookieAuth] ✅ Using cached <NAME_EMAIL>
[External ReportDetails] ✅ Using cached JSESSIONID: B8AF0027... (SOURCE: CACHE)
[External ReportDetails] Forwarding to http://mprts-qa02.lab.wagerworks.com:9080/AutoRun/ReportDetails?tsn_id=18070
[External ReportDetails] Using JSESSIONID: B8AF002792BE3FD32B25C03D51C49C5A
[2025-07-29T12:17:58.149Z] GET /api/external/ReportDetails?tsn_id=18070 from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: write
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: write
GET /api/external/ReportDetails
[External ReportDetails] Incoming request cookies: accessToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.xxnO7_bzRN68ijGP7CqFt4S1Mt3SpQipvGAD6kEylHo; refreshToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************.E84JMUYxPCbChTpstPQyRgnwAZX000mQef7oq71YvU8; sessionId=ea185bd690eb92271601a4c24b09ad7d4f2beac2cc711a7d53cb69864e23e845; JSESSIONID=B8AF002792BE3FD32B25C03D51C49C5A
[External ReportDetails] Extracted JSESSIONID: B8AF002792BE3FD32B25C03D51C49C5A
[External ReportDetails] 🔍 Session Decision Process Starting...
[External ReportDetails] 🔍 User from JWT: <EMAIL>
[External ReportDetails] 🔍 Browser JSESSIONID: B8AF0027...
[External ReportDetails] 🔍 Session JSESSIONID: B8AF0027...
[External ReportDetails] 🔍 Cache Status: {
  "totalEntries": 1,
  "entries": [
    {
      "uid": "<EMAIL>",
      "jsessionId": "B8AF0027...",
      "expiresIn": 1800,
      "isValid": true,
      "expiresAt": "2025-07-29T12:47:57.861Z"
    }
  ]
}
[External ReportDetails] 🔍 Checking cached session for user: <EMAIL> (normalized: <EMAIL>)
[CookieAuth] 🔍 Checking cached session for user: <EMAIL>
[CookieAuth] 🔍 Cache entry exists: true
[CookieAuth] 🔍 Cache entry expires in: 1800s
[CookieAuth] 🔍 Cache entry valid: true
[CookieAuth] 🔍 Cached JSESSIONID: B8AF0027...
[CookieAuth] ✅ Using cached <NAME_EMAIL>
[External ReportDetails] ✅ Using cached JSESSIONID: B8AF0027... (SOURCE: CACHE)
[External ReportDetails] Forwarding to http://mprts-qa02.lab.wagerworks.com:9080/AutoRun/ReportDetails?tsn_id=18070
[External ReportDetails] Using JSESSIONID: B8AF002792BE3FD32B25C03D51C49C5A
✅ Database Query: Retrieved 50 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-07-29 12:17:06, end_ts: 2025-07-29 12:17:22
Row 2 start_ts: 2025-07-29 12:06:41, end_ts: 2025-07-29 12:06:52
Row 3 start_ts: 2025-07-28 18:38:26, end_ts: 2025-07-28 18:38:59
⏳ Processing 50 sessions in 10 batches...
[2025-07-29T12:18:02.494Z] GET /local/recent-runs?limit=100&type=single_case from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
GET /local/recent-runs
Fetching recent runs with filters: { limit: 100, type: 'single_case' }
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Adding single test case filter (standalone test cases with total_cases=1 only)
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id) AS total_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'P') AS passed_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'F') AS failed_cases
      FROM test_session ts
     WHERE ts.tc_id IS NOT NULL AND ts.tc_id != "" AND (ts.ts_id IS NULL OR ts.ts_id = "") AND (SELECT COUNT(DISTINCT tr.tc_id) FROM test_result tr WHERE tr.tsn_id = ts.tsn_id) = 1 ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?
[GET_RECENT_RUNS] Final Params: [100]
✅ Processed 50 sessions successfully
Retrieved 50 recent runs
✅ Database Query: Retrieved 100 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-07-29 12:17:06, end_ts: 2025-07-29 12:17:22
Row 2 start_ts: 2025-07-29 12:06:41, end_ts: 2025-07-29 12:06:52
Row 3 start_ts: 2025-07-28 18:38:26, end_ts: 2025-07-28 18:38:59
⏳ Processing 100 sessions in 20 batches...
[2025-07-29T12:18:14.490Z] GET /local/recent-runs?limit=50&type=single_case from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
GET /local/recent-runs
Fetching recent runs with filters: { limit: 50, type: 'single_case' }
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Adding single test case filter (standalone test cases with total_cases=1 only)
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id) AS total_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'P') AS passed_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'F') AS failed_cases
      FROM test_session ts
     WHERE ts.tc_id IS NOT NULL AND ts.tc_id != "" AND (ts.ts_id IS NULL OR ts.ts_id = "") AND (SELECT COUNT(DISTINCT tr.tc_id) FROM test_result tr WHERE tr.tsn_id = ts.tsn_id) = 1 ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?
[GET_RECENT_RUNS] Final Params: [50]
✅ Processed 100 sessions successfully
Retrieved 100 recent runs
[2025-07-29T12:18:22.491Z] GET /local/recent-runs?limit=100&type=single_case from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
GET /local/recent-runs
Fetching recent runs with filters: { limit: 100, type: 'single_case' }
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Adding single test case filter (standalone test cases with total_cases=1 only)
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id) AS total_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'P') AS passed_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'F') AS failed_cases
      FROM test_session ts
     WHERE ts.tc_id IS NOT NULL AND ts.tc_id != "" AND (ts.ts_id IS NULL OR ts.ts_id = "") AND (SELECT COUNT(DISTINCT tr.tc_id) FROM test_result tr WHERE tr.tsn_id = ts.tsn_id) = 1 ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?
[GET_RECENT_RUNS] Final Params: [100]
✅ Database Query: Retrieved 50 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-07-29 12:17:06, end_ts: 2025-07-29 12:17:22
Row 2 start_ts: 2025-07-29 12:06:41, end_ts: 2025-07-29 12:06:52
Row 3 start_ts: 2025-07-28 18:38:26, end_ts: 2025-07-28 18:38:59
⏳ Processing 50 sessions in 10 batches...
✅ Processed 50 sessions successfully
Retrieved 50 recent runs
✅ Database Query: Retrieved 100 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-07-29 12:17:06, end_ts: 2025-07-29 12:17:22
Row 2 start_ts: 2025-07-29 12:06:41, end_ts: 2025-07-29 12:06:52
Row 3 start_ts: 2025-07-28 18:38:26, end_ts: 2025-07-28 18:38:59
⏳ Processing 100 sessions in 20 batches...
✅ Processed 100 sessions successfully
Retrieved 100 recent runs
[2025-07-29T12:18:38.490Z] GET /local/recent-runs?limit=50&type=single_case from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
GET /local/recent-runs
Fetching recent runs with filters: { limit: 50, type: 'single_case' }
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Adding single test case filter (standalone test cases with total_cases=1 only)
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id) AS total_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'P') AS passed_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'F') AS failed_cases
      FROM test_session ts
     WHERE ts.tc_id IS NOT NULL AND ts.tc_id != "" AND (ts.ts_id IS NULL OR ts.ts_id = "") AND (SELECT COUNT(DISTINCT tr.tc_id) FROM test_result tr WHERE tr.tsn_id = ts.tsn_id) = 1 ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?
[GET_RECENT_RUNS] Final Params: [50]
[2025-07-29T12:18:42.489Z] GET /local/recent-runs?limit=100&type=single_case from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
GET /local/recent-runs
Fetching recent runs with filters: { limit: 100, type: 'single_case' }
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Adding single test case filter (standalone test cases with total_cases=1 only)
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id) AS total_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'P') AS passed_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'F') AS failed_cases
      FROM test_session ts
     WHERE ts.tc_id IS NOT NULL AND ts.tc_id != "" AND (ts.ts_id IS NULL OR ts.ts_id = "") AND (SELECT COUNT(DISTINCT tr.tc_id) FROM test_result tr WHERE tr.tsn_id = ts.tsn_id) = 1 ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?
[GET_RECENT_RUNS] Final Params: [100]
✅ Database Query: Retrieved 50 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-07-29 12:17:06, end_ts: 2025-07-29 12:17:22
Row 2 start_ts: 2025-07-29 12:06:41, end_ts: 2025-07-29 12:06:52
Row 3 start_ts: 2025-07-28 18:38:26, end_ts: 2025-07-28 18:38:59
⏳ Processing 50 sessions in 10 batches...
✅ Processed 50 sessions successfully
Retrieved 50 recent runs
✅ Database Query: Retrieved 100 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-07-29 12:17:06, end_ts: 2025-07-29 12:17:22
Row 2 start_ts: 2025-07-29 12:06:41, end_ts: 2025-07-29 12:06:52
Row 3 start_ts: 2025-07-28 18:38:26, end_ts: 2025-07-28 18:38:59
⏳ Processing 100 sessions in 20 batches...
✅ Processed 100 sessions successfully
Retrieved 100 recent runs
[2025-07-29T12:19:02.495Z] GET /local/recent-runs?limit=50&type=single_case from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
GET /local/recent-runs
Fetching recent runs with filters: { limit: 50, type: 'single_case' }
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Adding single test case filter (standalone test cases with total_cases=1 only)
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id) AS total_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'P') AS passed_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'F') AS failed_cases
      FROM test_session ts
     WHERE ts.tc_id IS NOT NULL AND ts.tc_id != "" AND (ts.ts_id IS NULL OR ts.ts_id = "") AND (SELECT COUNT(DISTINCT tr.tc_id) FROM test_result tr WHERE tr.tsn_id = ts.tsn_id) = 1 ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?
[GET_RECENT_RUNS] Final Params: [50]
[2025-07-29T12:19:02.503Z] GET /local/recent-runs?limit=100&type=single_case from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
GET /local/recent-runs
Fetching recent runs with filters: { limit: 100, type: 'single_case' }
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Adding single test case filter (standalone test cases with total_cases=1 only)
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id) AS total_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'P') AS passed_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'F') AS failed_cases
      FROM test_session ts
     WHERE ts.tc_id IS NOT NULL AND ts.tc_id != "" AND (ts.ts_id IS NULL OR ts.ts_id = "") AND (SELECT COUNT(DISTINCT tr.tc_id) FROM test_result tr WHERE tr.tsn_id = ts.tsn_id) = 1 ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?
[GET_RECENT_RUNS] Final Params: [100]
✅ Database Query: Retrieved 50 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-07-29 12:17:06, end_ts: 2025-07-29 12:17:22
Row 2 start_ts: 2025-07-29 12:06:41, end_ts: 2025-07-29 12:06:52
Row 3 start_ts: 2025-07-28 18:38:26, end_ts: 2025-07-28 18:38:59
⏳ Processing 50 sessions in 10 batches...
✅ Database Query: Retrieved 100 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-07-29 12:17:06, end_ts: 2025-07-29 12:17:22
Row 2 start_ts: 2025-07-29 12:06:41, end_ts: 2025-07-29 12:06:52
Row 3 start_ts: 2025-07-28 18:38:26, end_ts: 2025-07-28 18:38:59
⏳ Processing 100 sessions in 20 batches...
✅ Processed 50 sessions successfully
Retrieved 50 recent runs
✅ Processed 100 sessions successfully
Retrieved 100 recent runs
[2025-07-29T12:19:24.490Z] GET /local/recent-runs?limit=100&type=single_case from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
GET /local/recent-runs
Fetching recent runs with filters: { limit: 100, type: 'single_case' }
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Adding single test case filter (standalone test cases with total_cases=1 only)
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id) AS total_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'P') AS passed_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'F') AS failed_cases
      FROM test_session ts
     WHERE ts.tc_id IS NOT NULL AND ts.tc_id != "" AND (ts.ts_id IS NULL OR ts.ts_id = "") AND (SELECT COUNT(DISTINCT tr.tc_id) FROM test_result tr WHERE tr.tsn_id = ts.tsn_id) = 1 ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?
[GET_RECENT_RUNS] Final Params: [100]
[2025-07-29T12:19:26.489Z] GET /local/recent-runs?limit=50&type=single_case from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
GET /local/recent-runs
Fetching recent runs with filters: { limit: 50, type: 'single_case' }
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Adding single test case filter (standalone test cases with total_cases=1 only)
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id) AS total_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'P') AS passed_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'F') AS failed_cases
      FROM test_session ts
     WHERE ts.tc_id IS NOT NULL AND ts.tc_id != "" AND (ts.ts_id IS NULL OR ts.ts_id = "") AND (SELECT COUNT(DISTINCT tr.tc_id) FROM test_result tr WHERE tr.tsn_id = ts.tsn_id) = 1 ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?
[GET_RECENT_RUNS] Final Params: [50]
✅ Database Query: Retrieved 100 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-07-29 12:17:06, end_ts: 2025-07-29 12:17:22
Row 2 start_ts: 2025-07-29 12:06:41, end_ts: 2025-07-29 12:06:52
Row 3 start_ts: 2025-07-28 18:38:26, end_ts: 2025-07-28 18:38:59
⏳ Processing 100 sessions in 20 batches...
✅ Database Query: Retrieved 50 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-07-29 12:17:06, end_ts: 2025-07-29 12:17:22
Row 2 start_ts: 2025-07-29 12:06:41, end_ts: 2025-07-29 12:06:52
Row 3 start_ts: 2025-07-28 18:38:26, end_ts: 2025-07-28 18:38:59
⏳ Processing 50 sessions in 10 batches...
✅ Processed 50 sessions successfully
Retrieved 50 recent runs
✅ Processed 100 sessions successfully
Retrieved 100 recent runs
[2025-07-29T12:19:46.482Z] GET /local/recent-runs?limit=100&type=single_case from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
GET /local/recent-runs
Fetching recent runs with filters: { limit: 100, type: 'single_case' }
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Adding single test case filter (standalone test cases with total_cases=1 only)
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id) AS total_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'P') AS passed_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'F') AS failed_cases
      FROM test_session ts
     WHERE ts.tc_id IS NOT NULL AND ts.tc_id != "" AND (ts.ts_id IS NULL OR ts.ts_id = "") AND (SELECT COUNT(DISTINCT tr.tc_id) FROM test_result tr WHERE tr.tsn_id = ts.tsn_id) = 1 ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?
[GET_RECENT_RUNS] Final Params: [100]
[2025-07-29T12:19:50.488Z] GET /local/recent-runs?limit=50&type=single_case from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
GET /local/recent-runs
Fetching recent runs with filters: { limit: 50, type: 'single_case' }
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Adding single test case filter (standalone test cases with total_cases=1 only)
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id) AS total_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'P') AS passed_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'F') AS failed_cases
      FROM test_session ts
     WHERE ts.tc_id IS NOT NULL AND ts.tc_id != "" AND (ts.ts_id IS NULL OR ts.ts_id = "") AND (SELECT COUNT(DISTINCT tr.tc_id) FROM test_result tr WHERE tr.tsn_id = ts.tsn_id) = 1 ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?
[GET_RECENT_RUNS] Final Params: [50]

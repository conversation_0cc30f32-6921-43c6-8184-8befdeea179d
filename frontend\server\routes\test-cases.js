/**
 * Test Cases Routes
 */
const express = require('express');
const router = express.Router();
const db = require('../database');

// Get test cases
router.get('/test-cases', async (req, res) => {
  try {
    console.log('🔍 GET /local/test-cases');
    console.log('📋 Request query parameters:', JSON.stringify(req.query, null, 2));

    // Check if this is a search request (has search-specific parameters)
    const searchParams = ['name', 'comments', 'min_id', 'max_id', 'tc_id'];
    const isSearchRequest = searchParams.some(param => req.query[param]);

    let testCases;

    if (isSearchRequest) {
      console.log('🔍 Detected search request, using searchTestCases...');
      console.log('🔄 Calling db.searchTestCases with criteria:', req.query);
      testCases = await db.searchTestCases(req.query);
    } else {
      console.log('📋 Regular request, using getTestCases...');
      console.log('🔄 Calling db.getTestCases with query:', req.query);
      testCases = await db.getTestCases(req.query);
    }

    console.log('✅ Database returned test cases:');
    console.log('   - Type:', typeof testCases);
    console.log('   - Is Array:', Array.isArray(testCases));
    console.log('   - Length:', testCases ? testCases.length : 'null/undefined');
    console.log('   - First 3 items:', testCases ? testCases.slice(0, 3) : 'none');

    const response = {
      success: true,
      data: testCases || [],
      message: 'Test cases retrieved successfully'
    };

    console.log('📤 Sending response:');
    console.log('   - Success:', response.success);
    console.log('   - Data length:', response.data.length);
    console.log('   - Message:', response.message);

    // Return as JSON with success flag
    return res.json(response);
  } catch (error) {
    console.error('❌ Error retrieving test cases:', error);
    console.error('❌ Error stack:', error.stack);
    return res.status(500).json({
      success: false,
      message: 'Failed to retrieve test cases',
      error: error.message
    });
  }
});

// Note: Test details route has been moved to test-details.js
// to centralize and standardize the test details retrieval logic

module.exports = router;

/**
 * Case Runner Service
 * Handles running test cases and test suites via the external CaseRunner API
 */
const fetch = require('node-fetch');
const { BASE_URL, DEFAULT_PARAMS } = require('../config/app-config');

/**
 * Runs a test case or test suite via the external CaseRunner API
 * @param {Object} params - Test parameters
 * @returns {Promise<Object>} - API response with tsn_id
 */
async function runTest(params) {
  // Determine if this is a test case run or a test suite run
  const isTestSuite = params.ts_id !== undefined;
  const isTestCase = params.tc_id !== undefined;

  if (!isTestCase && !isTestSuite) {
    throw new Error('Either tc_id (test case ID) or ts_id (test suite ID) is required.');
  }

  // Forward request to external API with credentials
  const externalApiUrl = 'http://mprts-qa02.lab.wagerworks.com:5080/AutoRun/CaseRunner';
  console.log(`[API /case-runner] Forwarding request to external API: ${externalApiUrl}`);

  // Prepare the form data with all necessary parameters
  const formData = new URLSearchParams();

  // Apply defaults from app-config.js for all configuration parameters
  const configParams = {
    // Core parameters (always required)
    tc_id: params.tc_id,
    ts_id: params.ts_id,
    uid: params.uid,
    password: params.password,

    // Configuration parameters with defaults from DEFAULT_PARAMS
    envir: params.envir || DEFAULT_PARAMS.environment,
    shell_host: params.shell_host || DEFAULT_PARAMS.shell_host,
    file_path: params.file_path || DEFAULT_PARAMS.file_path,
    operatorConfigs: params.operatorConfigs || DEFAULT_PARAMS.operatorConfigs,
    kafka_server: params.kafka_server || DEFAULT_PARAMS.kafka_server,
    dataCenter: params.dataCenter || DEFAULT_PARAMS.dataCenter,
    rgs_env: params.rgs_env || params.envir || DEFAULT_PARAMS.rgs_env,
    old_version: params.old_version || DEFAULT_PARAMS.old_version,
    networkType1: params.networkType1 || DEFAULT_PARAMS.networkType1,
    networkType2: params.networkType2 || DEFAULT_PARAMS.networkType2,
    sign: params.sign || DEFAULT_PARAMS.sign,
    rate_src: params.rate_src || DEFAULT_PARAMS.rate_src
  };

  // Add all parameters to form data (excluding undefined values)
  Object.entries(configParams).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      formData.append(key, value);
    }
  });

  console.log(`[Service case-runner] Configuration parameters applied:`, {
    provided: Object.keys(params).filter(key => params[key] !== undefined),
    defaults_used: Object.keys(configParams).filter(key =>
      params[key] === undefined && configParams[key] !== undefined
    )
  });

  // Ensure authentication credentials are set (fallback to defaults if not provided)
  if (!formData.has('uid')) {
    formData.append('uid', '<EMAIL>');
  }
  if (!formData.has('password')) {
    formData.append('password', 'test');
  }

  // Make the external API request
  const response = await fetch(externalApiUrl, {
    method: 'POST',
    body: formData,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  });

  // Check response status
  if (!response.ok) {
    const errorText = await response.text();
    console.error(`[Service case-runner] External API error: ${response.status} - ${errorText}`);
    throw new Error(`External API request failed with status ${response.status}`);
  }

  // Get response text
  const responseText = await response.text();
  
  // Log raw response text
  console.log('[Service case-runner] Raw response text received from external API:');
  console.log('--------------------- START RESPONSE ---------------------');
  console.log(responseText);
  console.log('---------------------- END RESPONSE ----------------------');

  // The tsn_id is in a line like: "Your test session id: 13781"
  const tsnIdMatch = responseText.match(/Your test session id: (\d+)/i);

  if (!tsnIdMatch || !tsnIdMatch[1]) {
    throw new Error('Could not extract test session ID from response');
  }

  const tsn_id = tsnIdMatch[1];

  // Return the tsn_id and other relevant information
  return {
    tsn_id,
    message: isTestSuite ?
      `Test suite ${params.ts_id} started successfully with session ID ${tsn_id}` :
      `Test case ${params.tc_id} started successfully with session ID ${tsn_id}`
  };
}

module.exports = {
  runTest
};

/**
 * Case Runner Service
 * Handles running test cases and test suites via the external CaseRunner API
 */
const fetch = require('node-fetch');
const { BASE_URL } = require('../config/app-config');

/**
 * Runs a test case or test suite via the external CaseRunner API
 * @param {Object} params - Test parameters
 * @returns {Promise<Object>} - API response with tsn_id
 */
async function runTest(params) {
  // Determine if this is a test case run or a test suite run
  const isTestSuite = params.ts_id !== undefined;
  const isTestCase = params.tc_id !== undefined;

  if (!isTestCase && !isTestSuite) {
    throw new Error('Either tc_id (test case ID) or ts_id (test suite ID) is required.');
  }

  // Forward request to external API with credentials
  const externalApiUrl = 'http://mprts-qa02.lab.wagerworks.com:5080/AutoRun/CaseRunner';
  console.log(`[API /case-runner] Forwarding request to external API: ${externalApiUrl}`);

  // Prepare the form data with all necessary parameters
  const formData = new URLSearchParams();

  // Add all parameters from request body to form data
  Object.entries(params).forEach(([key, value]) => {
    formData.append(key, value);
  });

  // Ensure required parameters are set
  if (!formData.has('shell_host')) {
    formData.append('shell_host', 'jps-qa10-app01');
  }

  if (!formData.has('envir')) {
    formData.append('envir', 'qa02');
  }

  // Use built-in credentials to ensure authentication with external API
  formData.append('uid', params.uid || '<EMAIL>');
  formData.append('password', params.password || 'test');

  // Make the external API request
  const response = await fetch(externalApiUrl, {
    method: 'POST',
    body: formData,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  });

  // Check response status
  if (!response.ok) {
    const errorText = await response.text();
    console.error(`[Service case-runner] External API error: ${response.status} - ${errorText}`);
    throw new Error(`External API request failed with status ${response.status}`);
  }

  // Get response text
  const responseText = await response.text();
  
  // Log raw response text
  console.log('[Service case-runner] Raw response text received from external API:');
  console.log('--------------------- START RESPONSE ---------------------');
  console.log(responseText);
  console.log('---------------------- END RESPONSE ----------------------');

  // The tsn_id is in a line like: "Your test session id: 13781"
  const tsnIdMatch = responseText.match(/Your test session id: (\d+)/i);

  if (!tsnIdMatch || !tsnIdMatch[1]) {
    throw new Error('Could not extract test session ID from response');
  }

  const tsn_id = tsnIdMatch[1];

  // Return the tsn_id and other relevant information
  return {
    tsn_id,
    message: isTestSuite ?
      `Test suite ${params.ts_id} started successfully with session ID ${tsn_id}` :
      `Test case ${params.tc_id} started successfully with session ID ${tsn_id}`
  };
}

module.exports = {
  runTest
};

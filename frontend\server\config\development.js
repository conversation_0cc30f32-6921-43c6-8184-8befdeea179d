/**
 * Development Mode Configuration
 * Provides secure development settings that don't compromise production security
 */

const fs = require('fs');
const path = require('path');

class DevelopmentConfig {
  constructor() {
    this.isDevelopment = process.env.NODE_ENV === 'development';
    this.allowedDevFeatures = this.loadDevFeatures();
  }

  /**
   * Load development features from environment or config file
   */
  loadDevFeatures() {
    const features = {
      // Authentication bypass (DANGEROUS - only for local development)
      skipAuth: process.env.SKIP_AUTH === 'true' && this.isDevelopment,
      
      // Mock API responses
      useMockApi: process.env.USE_MOCK_API === 'true',
      
      // Enhanced logging
      verboseLogging: process.env.VERBOSE_LOGGING === 'true' || this.isDevelopment,
      
      // Development user (for testing)
      devUser: {
        uid: process.env.DEV_USER || 'dev-user@localhost',
        role: process.env.DEV_ROLE || 'admin',
        name: 'Development User'
      },
      
      // API debugging
      logApiCalls: process.env.LOG_API_CALLS === 'true' || this.isDevelopment,
      
      // Database debugging
      logDbQueries: process.env.LOG_DB_QUERIES === 'true',
      
      // Security warnings
      showSecurityWarnings: this.isDevelopment
    };

    // Log development mode status
    if (this.isDevelopment) {
      console.log('🔧 DEVELOPMENT MODE ENABLED');
      console.log('Development features:', {
        skipAuth: features.skipAuth,
        useMockApi: features.useMockApi,
        verboseLogging: features.verboseLogging,
        logApiCalls: features.logApiCalls
      });
      
      if (features.skipAuth) {
        console.warn('⚠️ WARNING: Authentication bypass is ENABLED');
        console.warn('⚠️ This should NEVER be used in production!');
      }
    }

    return features;
  }

  /**
   * Check if authentication should be bypassed
   * @returns {boolean} True if auth should be bypassed
   */
  shouldSkipAuth() {
    if (!this.isDevelopment) {
      return false; // Never skip auth in production
    }

    if (this.allowedDevFeatures.skipAuth) {
      console.warn('⚠️ DEVELOPMENT: Bypassing authentication');
      return true;
    }

    return false;
  }

  /**
   * Get development user for auth bypass
   * @returns {Object} Development user object
   */
  getDevUser() {
    return this.allowedDevFeatures.devUser;
  }

  /**
   * Check if feature is enabled
   * @param {string} feature - Feature name
   * @returns {boolean} True if feature is enabled
   */
  isFeatureEnabled(feature) {
    return this.allowedDevFeatures[feature] || false;
  }

  /**
   * Get all development features status
   * @returns {Object} Features status
   */
  getFeatures() {
    return { ...this.allowedDevFeatures };
  }

  /**
   * Log development warning
   * @param {string} message - Warning message
   */
  logDevWarning(message) {
    if (this.allowedDevFeatures.showSecurityWarnings) {
      console.warn(`🔧 DEV WARNING: ${message}`);
    }
  }

  /**
   * Log API call for debugging
   * @param {string} method - HTTP method
   * @param {string} url - Request URL
   * @param {Object} data - Request data
   */
  logApiCall(method, url, data = {}) {
    if (this.allowedDevFeatures.logApiCalls) {
      console.log(`🌐 API ${method} ${url}`, data);
    }
  }

  /**
   * Log database query for debugging
   * @param {string} query - SQL query
   * @param {Array} params - Query parameters
   */
  logDbQuery(query, params = []) {
    if (this.allowedDevFeatures.logDbQueries) {
      console.log(`🗄️ DB Query: ${query}`, params);
    }
  }

  /**
   * Create development environment file template
   */
  createDevEnvTemplate() {
    const templatePath = path.join(__dirname, '..', '.env.development.template');
    const template = `# Development Environment Configuration
# Copy this file to .env.development and customize as needed

# Node environment
NODE_ENV=development

# Authentication settings (DANGEROUS - only for local development)
SKIP_AUTH=false
DEV_USER=dev-user@localhost
DEV_ROLE=admin

# API settings
USE_MOCK_API=false
LOG_API_CALLS=true

# Database settings
LOG_DB_QUERIES=false

# Logging
VERBOSE_LOGGING=true

# Security warnings
SHOW_SECURITY_WARNINGS=true

# Session settings
SESSION_SECRET=dev-secret-change-in-production
SESSION_TIMEOUT=3600

# Allowed users file
ALLOWED_USERS_FILE=./config/allowed-users.json
`;

    try {
      if (!fs.existsSync(templatePath)) {
        fs.writeFileSync(templatePath, template, 'utf8');
        console.log(`Created development environment template: ${templatePath}`);
      }
    } catch (error) {
      console.error('Error creating development template:', error);
    }
  }
}

// Export singleton instance
module.exports = new DevelopmentConfig();

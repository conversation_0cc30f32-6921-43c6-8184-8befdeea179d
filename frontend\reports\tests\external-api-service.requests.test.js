// Import required modules
const fs = require('fs');
const path = require('path');

// Define the ExternalApiService class for testing
class ExternalApiService {
  constructor() {
    // Base URL for the external API
    this.baseUrl = 'http://mprts-qa02.lab.wagerworks.com:9080';
    
    // Session state
    this.jsessionId = null;
    this.jsessionExpiry = null;
    
    // <PERSON>ie expires after 30 minutes on server, we'll use 25 minutes to be safe
    this.cookieExpiryTime = 25 * 60 * 1000;
  }
  
  isSessionValid() {
    if (!this.jsessionId || !this.jsessionExpiry) {
      return false;
    }
    
    return Date.now() < this.jsessionExpiry;
  }
  
  async login(uid, password) {
    // Mock implementation for testing
    this.jsessionId = 'test_session_id';
    this.jsessionExpiry = Date.now() + this.cookieExpiryTime;
    return this.jsessionId;
  }
  
  async getValidSession(uid, password) {
    if (this.isSessionValid()) {
      return this.jsessionId;
    }
    
    return await this.login(uid, password);
  }
  
  async makeAuthenticatedRequest(endpoint, params = {}, uid, password, method = 'GET') {
    // Get a valid session
    const jsessionId = await this.getValidSession(uid, password);
    
    // Build URL with query parameters
    const url = new URL(`${this.baseUrl}${endpoint}`);
    Object.entries(params).forEach(([key, value]) => {
      url.searchParams.append(key, value);
    });
    
    // Request options
    const options = {
      method,
      headers: {
        'Cookie': `JSESSIONID=${jsessionId}`
      },
      credentials: 'include'
    };
    
    // Make the request
    const response = await fetch(url.toString(), options);
    
    if (!response.ok) {
      throw new Error(`Request failed with status ${response.status}`);
    }
    
    return response;
  }
  
  async getReportSummary(tsnId, uid, password) {
    const response = await this.makeAuthenticatedRequest(
      '/AutoRun/ReportSummary',
      { tsn_id: tsnId },
      uid,
      password
    );
    
    const html = await response.text();
    return this.parseReportSummaryHtml(html, tsnId);
  }
  
  async getReportDetails(tsnId, index = 1, uid, password) {
    const response = await this.makeAuthenticatedRequest(
      '/AutoRun/ReportDetails',
      { tsn_id: tsnId, index },
      uid,
      password
    );
    
    const html = await response.text();
    return this.parseReportDetailsHtml(html, tsnId);
  }
  
  async stopTestSession(tsnId, uid, password) {
    await this.makeAuthenticatedRequest(
      '/AutoRun/StopSession',
      { tsn_id: tsnId },
      uid,
      password,
      'POST'
    );
    
    return true;
  }
  
  parseReportSummaryHtml(html, tsnId) {
    // Mock implementation for testing
    return {
      tsn_id: tsnId,
      status: 'Success'
    };
  }
  
  parseReportDetailsHtml(html, tsnId) {
    // Mock implementation for testing
    return {
      tsn_id: tsnId,
      test_cases: []
    };
  }
  
  async getRecentTestRuns(tsnIds, uid, password, limit = 10) {
    // Limit the number of session IDs to process
    const limitedIds = tsnIds.slice(0, limit);
    
    // Fetch report summaries for each session ID
    const reportPromises = limitedIds.map(tsnId => 
      this.getReportSummary(tsnId, uid, password)
        .catch(error => {
          return {
            tsn_id: tsnId,
            status: 'Error',
            error: error.message
          };
        })
    );
    
    // Wait for all promises to resolve
    const reports = await Promise.all(reportPromises);
    
    return reports;
  }
}

// Mock window and document for JSDOM
global.window = global.window || {};
global.document = global.document || {
  createElement: jest.fn(() => ({
    innerHTML: '',
    querySelectorAll: jest.fn(() => []),
    querySelector: jest.fn(() => null)
  }))
};

/**
 * Unit tests for the External API Service - API Requests
 *
 * These tests verify that the External API Service correctly:
 * - Makes authenticated requests to the external API
 * - Fetches report summaries
 * - Fetches report details
 * - Handles request errors
 */

describe('ExternalApiService', () => {
  // Test service instance
  let service;

  // Mock credentials
  let mockCredentials;

  // Mock session ID
  let mockTsnId;

  // Mock data
  let mockSummaryData;
  let mockDetailsData;
  let mockRecentRunsData;

  // Setup before each test
  beforeEach(() => {
    // Create a fresh instance of ExternalApiService for each test
    service = new ExternalApiService();
    
    // Mock credentials for testing
    mockCredentials = {
      uid: 'test_user',
      password: 'test_password'
    };
    
    // Mock session ID for testing
    mockTsnId = '13782';
    
    // Load updated mock data
    try {
      mockSummaryData = JSON.parse(fs.readFileSync(
        path.join(__dirname, 'mocks/updated-summary-response.json'),
        'utf8'
      ));
      
      mockDetailsData = JSON.parse(fs.readFileSync(
        path.join(__dirname, 'mocks/updated-details-response.json'),
        'utf8'
      ));
      
      mockRecentRunsData = JSON.parse(fs.readFileSync(
        path.join(__dirname, 'mocks/updated-recent-runs-response.json'),
        'utf8'
      ));
    } catch (error) {
      console.error('Error loading mock data:', error);
      mockSummaryData = { tsn_id: mockTsnId, status: 'Success' };
      mockDetailsData = { tsn_id: mockTsnId, test_cases: [] };
      mockRecentRunsData = [{ tsn_id: mockTsnId, status: 'Success' }];
    }
  });

  // Cleanup after each test
  afterEach(() => {
    jest.resetAllMocks();
  });

  describe('makeAuthenticatedRequest', () => {
    test('should make request with JSESSIONID cookie', async () => {
      // Save original fetch
      const originalFetch = global.fetch;
      
      // Set a valid session
      service.jsessionId = 'abc123';
      service.jsessionExpiry = Date.now() + 1000000; // Far in the future

      // Mock fetch
      global.fetch = jest.fn().mockResolvedValue({
        ok: true,
        text: jest.fn().mockResolvedValue('<html>Test response</html>')
      });

      try {
        // Call makeAuthenticatedRequest
        const response = await service.makeAuthenticatedRequest(
          '/AutoRun/ReportSummary',
          { tsn_id: mockTsnId },
          mockCredentials.uid,
          mockCredentials.password
        );

        // Verify fetch was called with correct parameters
        expect(global.fetch).toHaveBeenCalledWith(
          expect.stringContaining('/AutoRun/ReportSummary?tsn_id='),
          expect.objectContaining({
            method: 'GET',
            headers: expect.objectContaining({
              'Cookie': 'JSESSIONID=abc123'
            }),
            credentials: 'include'
          })
        );

        // Verify the response is returned
        expect(response).toBeDefined();
        expect(response.ok).toBe(true);
      } finally {
        // Restore original fetch
        global.fetch = originalFetch;
      }
    });

    test('should login if no valid session exists', async () => {
      // Save original fetch and getValidSession
      const originalFetch = global.fetch;
      const originalGetValidSession = service.getValidSession;
      
      // No existing session
      service.jsessionId = null;
      service.jsessionExpiry = null;

      // Mock getValidSession
      service.getValidSession = jest.fn().mockResolvedValue('new456');

      // Mock fetch
      global.fetch = jest.fn().mockResolvedValue({
        ok: true,
        text: jest.fn().mockResolvedValue('<html>Test response</html>')
      });

      try {
        // Call makeAuthenticatedRequest
        const response = await service.makeAuthenticatedRequest(
          '/AutoRun/ReportSummary',
          { tsn_id: mockTsnId },
          mockCredentials.uid,
          mockCredentials.password
        );

        // Verify getValidSession was called
        expect(service.getValidSession).toHaveBeenCalledWith(
          mockCredentials.uid,
          mockCredentials.password
        );

        // Verify the request was made with the new JSESSIONID
        expect(global.fetch).toHaveBeenCalledWith(
          expect.stringContaining('/AutoRun/ReportSummary?tsn_id='),
          expect.objectContaining({
            headers: expect.objectContaining({
              'Cookie': 'JSESSIONID=new456'
            })
          })
        );

        // Verify the response is returned
        expect(response).toBeDefined();
        expect(response.ok).toBe(true);
      } finally {
        // Restore original methods
        global.fetch = originalFetch;
        service.getValidSession = originalGetValidSession;
      }
    });

    test('should throw an error if request fails', async () => {
      // Save original fetch
      const originalFetch = global.fetch;
      
      // Set a valid session
      service.jsessionId = 'abc123';
      service.jsessionExpiry = Date.now() + 1000000; // Far in the future

      // Mock fetch to return an error
      global.fetch = jest.fn().mockResolvedValue({
        ok: false,
        status: 404
      });

      try {
        // Expect makeAuthenticatedRequest to throw an error
        await expect(service.makeAuthenticatedRequest(
          '/AutoRun/ReportSummary',
          { tsn_id: mockTsnId },
          mockCredentials.uid,
          mockCredentials.password
        )).rejects.toThrow('Request failed with status 404');
      } finally {
        // Restore original fetch
        global.fetch = originalFetch;
      }
    });

    test('should support POST requests', async () => {
      // Save original fetch
      const originalFetch = global.fetch;
      
      // Set a valid session
      service.jsessionId = 'abc123';
      service.jsessionExpiry = Date.now() + 1000000; // Far in the future

      // Mock fetch
      global.fetch = jest.fn().mockResolvedValue({
        ok: true,
        text: jest.fn().mockResolvedValue('<html>Test response</html>')
      });

      try {
        // Call makeAuthenticatedRequest with POST method
        const response = await service.makeAuthenticatedRequest(
          '/AutoRun/CaseRunner',
          { tc_id: '3180' },
          mockCredentials.uid,
          mockCredentials.password,
          'POST'
        );

        // Verify fetch was called with correct parameters
        expect(global.fetch).toHaveBeenCalledWith(
          expect.stringContaining('/AutoRun/CaseRunner'),
          expect.objectContaining({
            method: 'POST',
            headers: expect.objectContaining({
              'Cookie': 'JSESSIONID=abc123'
            }),
            credentials: 'include'
          })
        );

        // Verify the response is returned
        expect(response).toBeDefined();
        expect(response.ok).toBe(true);
      } finally {
        // Restore original fetch
        global.fetch = originalFetch;
      }
    });
  });

  describe('getReportSummary', () => {
    test('should fetch and parse report summary', async () => {
      // Save original methods
      const originalMakeAuthenticatedRequest = service.makeAuthenticatedRequest;
      const originalParseReportSummaryHtml = service.parseReportSummaryHtml;
      
      // Mock required modules
      const fs = require('fs');
      const path = require('path');
      const summaryHtml = fs.readFileSync(
        path.join(__dirname, 'mocks/summary-response.html'),
        'utf8'
      );

      // Mock makeAuthenticatedRequest
      service.makeAuthenticatedRequest = jest.fn().mockResolvedValue({
        ok: true,
        text: jest.fn().mockResolvedValue(summaryHtml)
      });

      // Mock parseReportSummaryHtml to return our updated mock data
      service.parseReportSummaryHtml = jest.fn().mockReturnValue(mockSummaryData);

      try {
        // Call getReportSummary
        const result = await service.getReportSummary(
          mockTsnId,
          mockCredentials.uid,
          mockCredentials.password
        );

        // Verify makeAuthenticatedRequest was called with correct parameters
        expect(service.makeAuthenticatedRequest).toHaveBeenCalledWith(
          '/AutoRun/ReportSummary',
          { tsn_id: mockTsnId },
          mockCredentials.uid,
          mockCredentials.password
        );

        // Verify parseReportSummaryHtml was called with the HTML response
        expect(service.parseReportSummaryHtml).toHaveBeenCalledWith(
          summaryHtml,
          mockTsnId
        );

        // Verify the result is the parsed report data
        expect(result).toEqual(mockSummaryData);
      } finally {
        // Restore original methods
        service.makeAuthenticatedRequest = originalMakeAuthenticatedRequest;
        service.parseReportSummaryHtml = originalParseReportSummaryHtml;
      }
    });

    test('should throw an error if request fails', async () => {
      // Save original method
      const originalMakeAuthenticatedRequest = service.makeAuthenticatedRequest;
      
      // Mock makeAuthenticatedRequest to throw an error
      service.makeAuthenticatedRequest = jest.fn().mockRejectedValue(
        new Error('Request failed')
      );

      try {
        // Expect getReportSummary to throw an error
        await expect(service.getReportSummary(
          mockTsnId,
          mockCredentials.uid,
          mockCredentials.password
        )).rejects.toThrow('Request failed');
      } finally {
        // Restore original method
        service.makeAuthenticatedRequest = originalMakeAuthenticatedRequest;
      }
    });
  });

  describe('getReportDetails', () => {
    test('should fetch and parse report details', async () => {
      // Save original methods
      const originalMakeAuthenticatedRequest = service.makeAuthenticatedRequest;
      const originalParseReportDetailsHtml = service.parseReportDetailsHtml;
      
      // Mock required modules
      const fs = require('fs');
      const path = require('path');
      const detailsHtml = fs.readFileSync(
        path.join(__dirname, 'mocks/details-response.html'),
        'utf8'
      );

      // Mock makeAuthenticatedRequest
      service.makeAuthenticatedRequest = jest.fn().mockResolvedValue({
        ok: true,
        text: jest.fn().mockResolvedValue(detailsHtml)
      });

      // Mock parseReportDetailsHtml to return our updated mock data
      service.parseReportDetailsHtml = jest.fn().mockReturnValue(mockDetailsData);

      try {
        // Call getReportDetails
        const result = await service.getReportDetails(
          mockTsnId,
          1,
          mockCredentials.uid,
          mockCredentials.password
        );

        // Verify makeAuthenticatedRequest was called with correct parameters
        expect(service.makeAuthenticatedRequest).toHaveBeenCalledWith(
          '/AutoRun/ReportDetails',
          { tsn_id: mockTsnId, index: 1 },
          mockCredentials.uid,
          mockCredentials.password
        );

        // Verify parseReportDetailsHtml was called with the HTML response
        expect(service.parseReportDetailsHtml).toHaveBeenCalledWith(
          detailsHtml,
          mockTsnId
        );

        // Verify the result is the parsed details data
        expect(result).toEqual(mockDetailsData);
      } finally {
        // Restore original methods
        service.makeAuthenticatedRequest = originalMakeAuthenticatedRequest;
        service.parseReportDetailsHtml = originalParseReportDetailsHtml;
      }
    });

    test('should throw an error if request fails', async () => {
      // Save original method
      const originalMakeAuthenticatedRequest = service.makeAuthenticatedRequest;
      
      // Mock makeAuthenticatedRequest to throw an error
      service.makeAuthenticatedRequest = jest.fn().mockRejectedValue(
        new Error('Request failed')
      );

      try {
        // Expect getReportDetails to throw an error
        await expect(service.getReportDetails(
          mockTsnId,
          1,
          mockCredentials.uid,
          mockCredentials.password
        )).rejects.toThrow('Request failed');
      } finally {
        // Restore original method
        service.makeAuthenticatedRequest = originalMakeAuthenticatedRequest;
      }
    });
  });

  describe('stopTestSession', () => {
    test('should stop a test session', async () => {
      // Mock makeAuthenticatedRequest
      service.makeAuthenticatedRequest = jest.fn().mockResolvedValue({
        text: jest.fn().mockResolvedValue('Removed')
      });

      // Call stopTestSession
      const result = await service.stopTestSession(
        mockTsnId,
        mockCredentials.uid,
        mockCredentials.password
      );

      // Verify makeAuthenticatedRequest was called with correct parameters
      expect(service.makeAuthenticatedRequest).toHaveBeenCalledWith(
        '/AutoRun/RemoveSession',
        { tsn_id: mockTsnId },
        mockCredentials.uid,
        mockCredentials.password,
        'POST'
      );

      // Verify the result is true
      expect(result).toBe(true);
    });

    test('should return false if stop fails', async () => {
      // Mock makeAuthenticatedRequest
      service.makeAuthenticatedRequest = jest.fn().mockResolvedValue({
        text: jest.fn().mockResolvedValue('Failed to remove')
      });

      // Call stopTestSession
      const result = await service.stopTestSession(
        mockTsnId,
        mockCredentials.uid,
        mockCredentials.password
      );

      // Verify the result is false
      expect(result).toBe(false);
    });

    test('should throw an error if request fails', async () => {
      // Save original method
      const originalMakeAuthenticatedRequest = service.makeAuthenticatedRequest;
      
      // Mock makeAuthenticatedRequest to throw an error
      service.makeAuthenticatedRequest = jest.fn().mockRejectedValue(
        new Error('Request failed')
      );

      try {
        // Expect stopTestSession to throw an error
        await expect(service.stopTestSession(
          mockTsnId,
          mockCredentials.uid,
          mockCredentials.password
        )).rejects.toThrow('Request failed');
      } finally {
        // Restore original method
        service.makeAuthenticatedRequest = originalMakeAuthenticatedRequest;
      }
    });
  });

  describe('getRecentTestRuns', () => {
    test('should fetch multiple report summaries', async () => {
      // Save original methods
      const originalGetReportSummary = service.getReportSummary;
      
      // Create a spy for getReportSummary
      service.getReportSummary = jest.fn()
        .mockResolvedValueOnce(mockRecentRunsData[0])
        .mockResolvedValueOnce(mockRecentRunsData[1]);

      try {
        // Call getRecentTestRuns with multiple TSN IDs
        const result = await service.getRecentTestRuns(
          ['13782', '13783'],
          mockCredentials.uid,
          mockCredentials.password
        );

        // Verify getReportSummary was called for each TSN ID
        expect(service.getReportSummary).toHaveBeenCalledTimes(2);
        expect(service.getReportSummary).toHaveBeenNthCalledWith(
          1,
          '13782',
          mockCredentials.uid,
          mockCredentials.password
        );
        expect(service.getReportSummary).toHaveBeenNthCalledWith(
          2,
          '13783',
          mockCredentials.uid,
          mockCredentials.password
        );

        // Verify the result contains the expected report data
        expect(result).toEqual([mockRecentRunsData[0], mockRecentRunsData[1]]);
      } finally {
        // Restore original method
        service.getReportSummary = originalGetReportSummary;
      }
    });

    test('should limit the number of reports', async () => {
      // Save original method
      const originalGetReportSummary = service.getReportSummary;
      
      // Create a spy for getReportSummary
      service.getReportSummary = jest.fn()
        .mockResolvedValueOnce(mockRecentRunsData[0])
        .mockResolvedValueOnce(mockRecentRunsData[1]);

      try {
        // Call getRecentTestRuns with a limit of 1
        const result = await service.getRecentTestRuns(
          ['13782', '13783', '13784'],
          mockCredentials.uid,
          mockCredentials.password,
          1
        );

        // Verify getReportSummary was called only once
        expect(service.getReportSummary).toHaveBeenCalledTimes(1);
        expect(service.getReportSummary).toHaveBeenCalledWith(
          '13782',
          mockCredentials.uid,
          mockCredentials.password
        );

        // Verify the result contains only one report
        expect(result).toHaveLength(1);
        expect(result[0]).toEqual(mockRecentRunsData[0]);
      } finally {
        // Restore original method
        service.getReportSummary = originalGetReportSummary;
      }
    });

    test('should handle errors for individual reports', async () => {
      // Save original method
      const originalGetReportSummary = service.getReportSummary;
      
      // Create a spy for getReportSummary
      service.getReportSummary = jest.fn()
        .mockResolvedValueOnce(mockRecentRunsData[0])
        .mockRejectedValueOnce(new Error('Failed to fetch report'));

      try {
        // Call getRecentTestRuns
        const result = await service.getRecentTestRuns(
          ['13782', '13783'],
          mockCredentials.uid,
          mockCredentials.password
        );

        // Verify getReportSummary was called for each TSN ID
        expect(service.getReportSummary).toHaveBeenCalledTimes(2);
        expect(service.getReportSummary).toHaveBeenNthCalledWith(
          1,
          '13782',
          mockCredentials.uid,
          mockCredentials.password
        );
        expect(service.getReportSummary).toHaveBeenNthCalledWith(
          2,
          '13783',
          mockCredentials.uid,
          mockCredentials.password
        );

        // Verify the result contains the successful report and the error report
        expect(result).toHaveLength(2);
        expect(result[0]).toEqual(mockRecentRunsData[0]);
        expect(result[1]).toEqual({
          tsn_id: '13783',
          status: 'Error',
          error: 'Failed to fetch report'
        });
      } finally {
        // Restore original method
        service.getReportSummary = originalGetReportSummary;
      }
    });
  });
});

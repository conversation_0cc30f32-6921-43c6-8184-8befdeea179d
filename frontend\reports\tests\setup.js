/**
 * Setup file for Jest tests
 *
 * This file runs before each test file.
 */

// Mock browser globals
global.document = document.implementation.createHTMLDocument('test');
global.window = {
  location: {
    origin: 'http://localhost:3000'
  },
  sessionStorage: {
    getItem: jest.fn(),
    setItem: jest.fn(),
    removeItem: jest.fn()
  },
  localStorage: {
    getItem: jest.fn(),
    setItem: jest.fn(),
    removeItem: jest.fn()
  }
};

// Add fetch polyfill for node environment
const nodeFetch = require('node-fetch');
global.fetch = nodeFetch;
// node-fetch 2.x doesn't expose Headers/Request/Response directly
// so we need to create them as needed

// Mock DOMParser
global.DOMParser = class DOMParser {
  parseFromString(html, type) {
    const doc = document.implementation.createHTMLDocument('test');

    // Create a status span
    const statusSpan = document.createElement('span');
    statusSpan.style.color = 'green';
    statusSpan.textContent = 'PASS';
    doc.body.appendChild(statusSpan);

    // Create list items
    const ul = document.createElement('ul');

    const startTimeLi = document.createElement('li');
    startTimeLi.textContent = 'Start Time: 2023-01-01 12:00:00';
    ul.appendChild(startTimeLi);

    const endTimeLi = document.createElement('li');
    endTimeLi.textContent = 'End Time: 2023-01-01 12:05:00';
    ul.appendChild(endTimeLi);

    const passedLi = document.createElement('li');
    passedLi.textContent = 'Case(s) passed: 10';
    ul.appendChild(passedLi);

    const failedLi = document.createElement('li');
    failedLi.textContent = 'Case(s) failed: 0';
    ul.appendChild(failedLi);

    const variablesLi = document.createElement('li');
    variablesLi.textContent = 'Variables: envir=qa02, host=test-host';
    ul.appendChild(variablesLi);

    doc.body.appendChild(ul);

    // Create a test case link
    const link = document.createElement('a');
    link.href = 'CaseEditor?tc_id=3180';
    link.textContent = '3180';
    doc.body.appendChild(link);

    // Create a table for test details
    const table = document.createElement('table');

    // Header row
    const headerRow = document.createElement('tr');
    ['TC ID', 'Seq', 'Outcome', 'Description', 'Input/Output'].forEach(header => {
      const th = document.createElement('th');
      th.textContent = header;
      headerRow.appendChild(th);
    });
    table.appendChild(headerRow);

    // Data row
    const dataRow = document.createElement('tr');

    const tcIdCell = document.createElement('td');
    tcIdCell.textContent = '3180';
    dataRow.appendChild(tcIdCell);

    const seqCell = document.createElement('td');
    seqCell.textContent = '1';
    dataRow.appendChild(seqCell);

    const outcomeCell = document.createElement('td');
    const outcomeLink = document.createElement('a');
    outcomeLink.classList.add('P');
    outcomeLink.textContent = 'P';
    outcomeCell.appendChild(outcomeLink);
    dataRow.appendChild(outcomeCell);

    const descCell = document.createElement('td');
    descCell.textContent = 'Login to the system';
    dataRow.appendChild(descCell);

    const ioCell = document.createElement('td');
    ioCell.textContent = 'Input: username=test, password=test\nOutput: Login successful';
    dataRow.appendChild(ioCell);

    table.appendChild(dataRow);
    doc.body.appendChild(table);

    // Create pagination
    const pagination = document.createElement('div');
    pagination.classList.add('pagination');
    pagination.textContent = 'Page 1 of 1';
    doc.body.appendChild(pagination);

    return doc;
  }
};

// Mock DOM elements
global.elements = {
  reportsTable: document.createElement('tbody'),
  testDetailsSection: document.createElement('div'),
  testDetailsTitle: document.createElement('h3'),
  testDetailsInfo: document.createElement('div'),
  testCasesTable: document.createElement('tbody')
};

// Mock current state
global.currentState = {
  reports: [],
  currentTestDetails: null
};

// Mock config
global.config = {
  reportingEndpoint: '/local/recent-runs',
  testDetailsEndpoint: '/local/test-details',
  refreshInterval: 10000,
  useDirectExternalApi: true,
  externalApiBaseUrl: 'http://mprts-qa02.lab.wagerworks.com:9080',
  maxReportsToShow: 20
};

// Mock helper functions
global.formatDate = jest.fn().mockImplementation(dateString => {
  if (!dateString) return 'N/A';
  return new Date(dateString).toLocaleString();
});

global.getStatusBadgeClass = jest.fn().mockImplementation(status => {
  switch (status.toLowerCase()) {
    case 'success':
    case 'passed':
      return 'badge-success';
    case 'failed':
    case 'error':
      return 'badge-danger';
    case 'running':
      return 'badge-primary';
    case 'warning':
      return 'badge-warning';
    default:
      return 'badge-secondary';
  }
});

global.updateCharts = jest.fn();

// Mock services
class ExternalApiService {
  constructor() {
    this.baseUrl = 'http://mprts-qa02.lab.wagerworks.com:9080';
    this.jsessionId = null;
    this.jsessionExpiry = null;
    console.log(`External API Service initialized with baseUrl: ${this.baseUrl}`);
  }

  isSessionValid() {
    return this.jsessionId !== null &&
           this.jsessionExpiry !== null &&
           this.jsessionExpiry > Date.now();
  }

  async login(uid, password) {
    this.jsessionId = '58A7C523CB9EE2B9F6C822C475C74139';
    this.jsessionExpiry = Date.now() + 30 * 60 * 1000;
    return this.jsessionId;
  }

  async getValidSession(uid, password) {
    if (this.isSessionValid()) {
      return this.jsessionId;
    }
    return await this.login(uid, password);
  }

  async makeAuthenticatedRequest(path, params, uid, password, method = 'GET') {
    return {
      ok: true,
      text: async () => '<html><span style="color:green">PASS</span></html>'
    };
  }

  async getReportSummary(tsnId, uid, password) {
    return {
      tsn_id: tsnId,
      test_id: '3180',
      type: 'Test Case',
      environment: 'QA02',
      status: 'Success',
      start_time: '2023-01-01 12:00:00',
      end_time: '2023-01-01 12:05:00',
      duration: '5:00',
      total_cases: 10,
      passed_cases: 10,
      failed_cases: 0,
      pass_rate: 100
    };
  }

  async getReportDetails(tsnId, index, uid, password) {
    return {
      tsn_id: tsnId,
      test_cases: [
        {
          tc_id: '3180',
          seq_index: '1',
          status: 'Passed',
          description: 'Login to the system',
          input_output: 'Input: username=test, password=test\nOutput: Login successful',
          error_message: ''
        }
      ],
      pagination: {
        currentPage: 1,
        totalPages: 1
      }
    };
  }

  async stopTestSession(tsnId, uid, password) {
    return true;
  }

  async getRecentTestRuns(sessionIds, uid, password, limit = 10) {
    return sessionIds.slice(0, limit).map(tsnId => ({
      tsn_id: tsnId,
      test_id: '3180',
      type: 'Test Case',
      environment: 'QA02',
      status: 'Success',
      start_time: '2023-01-01 12:00:00',
      end_time: '2023-01-01 12:05:00',
      duration: '5:00',
      total_cases: 10,
      passed_cases: 10,
      failed_cases: 0,
      pass_rate: 100
    }));
  }

  parseReportSummaryHtml(html, tsnId) {
    return {
      tsn_id: tsnId,
      test_id: '3180',
      type: 'Test Case',
      environment: 'QA02',
      status: 'Success',
      start_time: '2023-01-01 12:00:00',
      end_time: '2023-01-01 12:05:00',
      duration: '5:00',
      total_cases: 10,
      passed_cases: 10,
      failed_cases: 0,
      pass_rate: 100
    };
  }

  parseReportDetailsHtml(html, tsnId) {
    return {
      tsn_id: tsnId,
      test_cases: [
        {
          tc_id: '3180',
          seq_index: '1',
          status: 'Passed',
          description: 'Login to the system',
          input_output: 'Input: username=test, password=test\nOutput: Login successful',
          error_message: ''
        }
      ],
      pagination: {
        currentPage: 1,
        totalPages: 1
      }
    };
  }

  extractTextFromCell(cell) {
    return cell ? cell.textContent || '' : '';
  }
}

class SessionIdService {
  constructor() {
    this.cacheKey = 'smarttest_recent_session_ids';
    this.cacheTtl = 5 * 60 * 1000; // 5 minutes
    this.fallbackIds = ['13782', '13781', '13780', '13779', '13778'];
  }

  async getRecentSessionIds(credentials, limit = 10) {
    return this.fallbackIds.slice(0, limit);
  }

  getCachedSessionIds() {
    return null;
  }

  cacheSessionIds(ids) {
    // Mock implementation
  }

  async getSessionIdsFromApi(credentials, limit = 10) {
    return this.fallbackIds.slice(0, limit);
  }
}

// Attach services to window
global.window.externalApiService = new ExternalApiService();
global.window.sessionIdService = new SessionIdService();

// Import reports.js functions
const {
  loadReportsData,
  loadReportsFromExternalApi,
  loadReportsFromDatabaseApi,
  loadTestDetails,
  loadTestDetailsFromExternalApi,
  loadTestDetailsFromDatabaseApi,
  updateReportsTable,
  displayTestDetails,
  updateTestCasesTable
} = require('./mocks/reports.js');

// Attach reports.js functions to global scope
global.loadReportsData = loadReportsData;
global.loadReportsFromExternalApi = loadReportsFromExternalApi;
global.loadReportsFromDatabaseApi = loadReportsFromDatabaseApi;
global.loadTestDetails = loadTestDetails;
global.loadTestDetailsFromExternalApi = loadTestDetailsFromExternalApi;
global.loadTestDetailsFromDatabaseApi = loadTestDetailsFromDatabaseApi;
global.updateReportsTable = updateReportsTable;
global.displayTestDetails = displayTestDetails;
global.updateTestCasesTable = updateTestCasesTable;

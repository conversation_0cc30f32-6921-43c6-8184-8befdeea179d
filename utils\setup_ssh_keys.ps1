# ======================================================
# SSH KEY SETUP SCRIPT
# ======================================================
# This script sets up SSH keys for password-less authentication
# to the database server

# Configuration
$server = "mprts-qa02.lab.wagerworks.com"
$sshUser = "volfkoi"
$sshKeyFile = "$env:USERPROFILE\.ssh\id_rsa_dbserver"

# Ensure .ssh directory exists
$sshDir = "$env:USERPROFILE\.ssh"
if (-not (Test-Path $sshDir)) {
    Write-Host "Creating .ssh directory..." -ForegroundColor Yellow
    New-Item -ItemType Directory -Path $sshDir | Out-Null
}

# Create SSH config file to enable legacy algorithms
$sshConfigPath = "$sshDir\config"
if (-not (Test-Path $sshConfigPath) -or 
    -not (Get-Content $sshConfigPath | Select-String -Pattern $server -Quiet)) {
    
    Write-Host "Updating SSH config file with server settings..." -ForegroundColor Yellow
    
    $configContent = @"

# Added by database connection setup
Host $server
    HostKeyAlgorithms +ssh-rsa
    PubkeyAcceptedAlgorithms +ssh-rsa
    User $sshUser
    IdentityFile $sshKeyFile
"@
    
    Add-Content -Path $sshConfigPath -Value $configContent
    Write-Host "SSH config updated at $sshConfigPath" -ForegroundColor Green
}

# Check if key already exists
if (Test-Path $sshKeyFile) {
    Write-Host "SSH key already exists at $sshKeyFile" -ForegroundColor Yellow
    $regenerateKey = Read-Host "Do you want to regenerate the key? (y/n)"
    if ($regenerateKey -ne "y") {
        Write-Host "Using existing key." -ForegroundColor Green
        Write-Host "To complete setup, copy the public key to the server with:"
        Write-Host "  ssh-copy-id -i $sshKeyFile $sshUser@$server" -ForegroundColor Cyan
        exit 0
    }
}

# Generate SSH key
Write-Host "Generating new SSH key..." -ForegroundColor Yellow
$keyComment = "dbconnection_$(Get-Date -Format 'yyyyMMdd')"

# Use ssh-keygen to create a key
& ssh-keygen -t rsa -b 4096 -f $sshKeyFile -N '""' -C $keyComment

if ($LASTEXITCODE -ne 0) {
    Write-Host "Failed to generate SSH key. Error code: $LASTEXITCODE" -ForegroundColor Red
    exit 1
}

Write-Host "SSH key generated successfully at $sshKeyFile" -ForegroundColor Green

# Display instructions for installing the key on the server
Write-Host "`n=====================================================" -ForegroundColor Cyan
Write-Host "NEXT STEPS:" -ForegroundColor Cyan
Write-Host "=====================================================" -ForegroundColor Cyan
Write-Host "To install the public key on the server, you need to run:"
Write-Host "  ssh-copy-id -i $sshKeyFile $sshUser@$server" -ForegroundColor Cyan
Write-Host "`nIf ssh-copy-id is not available, you can manually copy the key:" 
Write-Host "  1. Connect to the server: ssh $sshUser@$server"
Write-Host "  2. Create/edit: ~/.ssh/authorized_keys"
Write-Host "  3. Add this line to the file:" -ForegroundColor Yellow
Write-Host "$(Get-Content "$sshKeyFile.pub")" -ForegroundColor Cyan
Write-Host "`nAfter completing these steps, you should be able to SSH to the server without a password."
Write-Host "Test with: ssh -i $sshKeyFile $sshUser@$server" -ForegroundColor Green
Write-Host "=====================================================" -ForegroundColor Cyan

# Update db_connection.ps1 to use the SSH key
$dbScriptPath = Join-Path $PWD "db_connection.ps1"
if (Test-Path $dbScriptPath) {
    Write-Host "`nDo you want to update db_connection.ps1 to use the new SSH key? (y/n)" -ForegroundColor Yellow
    $updateScript = Read-Host
    if ($updateScript -eq "y") {
        $scriptContent = Get-Content $dbScriptPath
        
        # Replace the ssh command to include identity file
        $scriptContent = $scriptContent -replace 'ssh -o HostKeyAlgorithms=\+ssh-rsa', "ssh -i `"$sshKeyFile`" -o HostKeyAlgorithms=+ssh-rsa"
        $scriptContent = $scriptContent -replace 'scp -o HostKeyAlgorithms=\+ssh-rsa', "scp -i `"$sshKeyFile`" -o HostKeyAlgorithms=+ssh-rsa"
        
        Set-Content -Path $dbScriptPath -Value $scriptContent
        Write-Host "Updated db_connection.ps1 to use SSH key authentication" -ForegroundColor Green
    }
}

Write-Host "`nSetup complete!" -ForegroundColor Green 
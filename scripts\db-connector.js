/**
 * Unified Database Connector
 * 
 * This module provides a unified interface for connecting to different database environments
 * using either SSH tunnel or direct SSH execution methods, automatically selecting the best
 * approach based on the environment.
 */

const { Client } = require('ssh2');
const fs = require('fs');
const path = require('path');
const mysql = require('mysql2/promise');
const util = require('util');

// Environment configurations
const environments = {
  // QA01 Environment
  qa01: {
    // Server Configuration
    PORT: 3000,
    BASE_URL: 'http://mprts-qa01.lab.wagerworks.com:9080/AutoRun/',
    
    // Database Configuration
    DB_HOST: 'mprts-qa01.lab.wagerworks.com',
    DB_USER: 'rgs_rw',
    DB_PASSWORD: 'rgs_rw',
    DB_NAME: 'rgs_test',
    DB_PORT: 3306,
    
    // SSH Configuration
    SSH_HOST: 'mprts-qa01.lab.wagerworks.com',
    SSH_USER: 'volfkoi',
    SSH_PORT: 22,
    SSH_KEY_PATH: 'C:\\Users\\<USER>\\.ssh\\id_rsa_dbserver',
    SSH_LOCAL_HOST: '127.0.0.1',
    SSH_LOCAL_PORT: 3307
  },
  
  // QA02 Environment
  qa02: {
    // Server Configuration
    PORT: 3000,
    BASE_URL: 'http://mprts-qa02.lab.wagerworks.com:5080/AutoRun/',
    
    // Database Configuration
    DB_HOST: 'mprts-qa02.lab.wagerworks.com',
    DB_USER: 'rgs_rw',
    DB_PASSWORD: 'rgs_rw',
    DB_NAME: 'rgs_test',
    DB_PORT: 3306,
    
    // SSH Configuration
    SSH_HOST: 'mprts-qa02.lab.wagerworks.com',
    SSH_USER: 'volfkoi',
    SSH_PORT: 22,
    SSH_KEY_PATH: 'C:\\Users\\<USER>\\.ssh\\id_rsa_dbserver',
    SSH_LOCAL_HOST: '127.0.0.1',
    SSH_LOCAL_PORT: 3307
  },
  
  // QA03 Environment
  qa03: {
    // Server Configuration
    PORT: 3000,
    BASE_URL: 'http://mprts-qa03.lab.wagerworks.com:5080/AutoRun/',
    
    // Database Configuration
    DB_HOST: 'mprts-qa03.lab.wagerworks.com',
    DB_USER: 'rgs_rw',
    DB_PASSWORD: 'rgs_rw',
    DB_NAME: 'rgs_test',
    DB_PORT: 3306,
    
    // SSH Configuration
    SSH_HOST: 'mprts-qa03.lab.wagerworks.com',
    SSH_USER: 'volfkoi',
    SSH_PORT: 22,
    SSH_KEY_PATH: 'C:\\Users\\<USER>\\.ssh\\id_rsa_dbserver',
    SSH_LOCAL_HOST: '127.0.0.1',
    SSH_LOCAL_PORT: 3307
  }
};

// Module state
let debugMode = process.env.DB_DEBUG === 'true';
let isInitialized = false;
let currentEnvironment = null;
let connectionMethod = null;
let mysqlConnection = null;
let sshClient = null;
let sshTunnel = null;

/**
 * Log debug information if debug mode is enabled
 * @param {string} message - Message to log
 */
function log(message) {
  if (debugMode) {
    console.log(`[DB-CONNECTOR] ${message}`);
  }
}

/**
 * Set debug mode
 * @param {boolean} enabled - Whether to enable debug mode
 */
function setDebugMode(enabled) {
  debugMode = enabled;
  log(`Debug mode ${debugMode ? 'enabled' : 'disabled'}`);
}

/**
 * Initialize the database connection
 * @param {string} env - Environment name to connect to (qa01, qa02, qa03)
 * @param {Object} options - Connection options
 * @param {boolean} options.forceDirect - Force direct SSH connection method
 * @param {boolean} options.forceTunnel - Force SSH tunnel connection method
 * @param {boolean} options.debug - Enable debug logging
 * @returns {Promise<boolean>} True if initialization was successful
 */
async function init(env = 'qa02', options = {}) {
  if (isInitialized) {
    log('Database already initialized');
    return true;
  }
  
  // Set debug mode if specified
  if (options.debug !== undefined) {
    setDebugMode(options.debug);
  }
  
  log(`Initializing database connection for environment: ${env}`);
  
  // Validate environment
  if (!environments[env]) {
    throw new Error(`Unknown environment: ${env}. Available environments: ${Object.keys(environments).join(', ')}`);
  }
  
  // Store current environment
  currentEnvironment = env;
  
  // Load environment configuration
  const config = environments[env];
  log(`Using configuration for ${env} environment`);
  
  // Determine connection method
  if (options.forceDirect) {
    connectionMethod = 'direct';
    log('Using direct SSH connection method (forced)');
  } else if (options.forceTunnel) {
    connectionMethod = 'tunnel';
    log('Using SSH tunnel connection method (forced)');
  } else {
    // Auto-detect the best connection method
    connectionMethod = await determineConnectionMethod(env);
    log(`Auto-selected connection method: ${connectionMethod}`);
  }
  
  try {
    if (connectionMethod === 'direct') {
      await initDirectConnection(config);
    } else {
      await initTunnelConnection(config);
    }
    
    isInitialized = true;
    log(`Database initialized successfully with ${connectionMethod} method`);
    return true;
  } catch (error) {
    // If the primary method fails, try the fallback method
    if (!options.forceDirect && !options.forceTunnel) {
      const fallbackMethod = connectionMethod === 'direct' ? 'tunnel' : 'direct';
      log(`Primary connection method failed: ${error.message}`);
      log(`Trying fallback method: ${fallbackMethod}`);
      
      try {
        connectionMethod = fallbackMethod;
        
        if (connectionMethod === 'direct') {
          await initDirectConnection(config);
        } else {
          await initTunnelConnection(config);
        }
        
        isInitialized = true;
        log(`Database initialized successfully with fallback ${fallbackMethod} method`);
        return true;
      } catch (fallbackError) {
        log(`Fallback connection method also failed: ${fallbackError.message}`);
        throw new Error(
          `Failed to connect to database with both methods. ` +
          `Primary error: ${error.message}. ` +
          `Fallback error: ${fallbackError.message}`
        );
      }
    }
    
    throw error;
  }
}

/**
 * Determine the best connection method for the specified environment
 * @param {string} env - Environment name
 * @returns {Promise<string>} The best connection method ('direct' or 'tunnel')
 */
async function determineConnectionMethod(env) {
  log(`Determining best connection method for ${env} environment...`);
  
  // Environment-specific preferences based on known behavior
  if (env === 'qa01') {
    log('QA01 environment detected, preferring direct SSH method');
    return 'direct';
  } else if (env === 'qa02' || env === 'qa03') {
    log(`${env} environment detected, preferring SSH tunnel method`);
    return 'tunnel';
  }
  
  // Default to direct method
  log('Unknown environment, defaulting to direct SSH method');
  return 'direct';
}

/**
 * Initialize a direct SSH connection
 * @param {Object} config - Environment configuration
 * @returns {Promise<void>}
 */
async function initDirectConnection(config) {
  log('Initializing direct SSH connection...');
  
  try {
    // Read the SSH private key
    let sshKeyPath = config.SSH_KEY_PATH;
    if (!sshKeyPath) {
      // Try common default locations
      const homeDir = process.env.HOME || process.env.USERPROFILE;
      const possiblePaths = [
        path.join(homeDir, '.ssh', 'id_rsa_dbserver'),
        path.join(homeDir, '.ssh', 'id_rsa')
      ];
      
      for (const p of possiblePaths) {
        if (fs.existsSync(p)) {
          sshKeyPath = p;
          break;
        }
      }
      
      if (!sshKeyPath) {
        throw new Error('SSH key path not defined and no default key found');
      }
    }
    
    log(`Using SSH key: ${sshKeyPath}`);
    const privateKey = fs.readFileSync(sshKeyPath);
    
    // Create SSH client
    sshClient = new Client();
    
    // Set up connection parameters
    const sshConfig = {
      host: config.SSH_HOST,
      port: config.SSH_PORT,
      username: config.SSH_USER,
      privateKey: privateKey,
      debug: debugMode ? message => log(`SSH Debug: ${message}`) : undefined
    };
    
    // Connect to SSH server
    await new Promise((resolve, reject) => {
      let connectionTimeout = setTimeout(() => {
        reject(new Error('SSH connection timeout after 30 seconds'));
      }, 30000);
      
      sshClient.on('ready', () => {
        clearTimeout(connectionTimeout);
        log('SSH connection established successfully');
        resolve();
      });
      
      sshClient.on('error', (err) => {
        clearTimeout(connectionTimeout);
        reject(new Error(`SSH connection error: ${err.message}`));
      });
      
      sshClient.connect(sshConfig);
    });
    
    // Test MySQL connectivity by running a simple query
    await executeDirectQuery('SELECT 1 AS connection_test');
    log('MySQL connectivity test successful via direct SSH');
  } catch (error) {
    if (sshClient) {
      try {
        sshClient.end();
      } catch (closeError) {
        // Ignore errors during cleanup
      }
      sshClient = null;
    }
    
    throw error;
  }
}

/**
 * Initialize an SSH tunnel connection
 * @param {Object} config - Environment configuration
 * @returns {Promise<void>}
 */
async function initTunnelConnection(config) {
  log('Initializing SSH tunnel connection...');
  
  try {
    // Read the SSH private key
    let sshKeyPath = config.SSH_KEY_PATH;
    if (!sshKeyPath) {
      // Try common default locations
      const homeDir = process.env.HOME || process.env.USERPROFILE;
      const possiblePaths = [
        path.join(homeDir, '.ssh', 'id_rsa_dbserver'),
        path.join(homeDir, '.ssh', 'id_rsa')
      ];
      
      for (const p of possiblePaths) {
        if (fs.existsSync(p)) {
          sshKeyPath = p;
          break;
        }
      }
      
      if (!sshKeyPath) {
        throw new Error('SSH key path not defined and no default key found');
      }
    }
    
    log(`Using SSH key: ${sshKeyPath}`);
    const privateKey = fs.readFileSync(sshKeyPath);
    
    // Create SSH client
    sshClient = new Client();
    
    // Set up connection parameters
    const sshConfig = {
      host: config.SSH_HOST,
      port: config.SSH_PORT,
      username: config.SSH_USER,
      privateKey: privateKey,
      debug: debugMode ? message => log(`SSH Debug: ${message}`) : undefined
    };
    
    // Connect and create tunnel
    await new Promise((resolve, reject) => {
      let connectionTimeout = setTimeout(() => {
        reject(new Error('SSH connection timeout after 30 seconds'));
      }, 30000);
      
      sshClient.on('ready', () => {
        clearTimeout(connectionTimeout);
        log('SSH connection established successfully');
        
        // Create a tunnel
        sshClient.forwardOut(
          config.SSH_LOCAL_HOST,
          config.SSH_LOCAL_PORT,
          config.DB_HOST,
          config.DB_PORT,
          (err, stream) => {
            if (err) {
              reject(new Error(`Failed to create SSH tunnel: ${err.message}`));
              return;
            }
            
            sshTunnel = stream;
            log('SSH tunnel established successfully');
            resolve();
          }
        );
      });
      
      sshClient.on('error', (err) => {
        clearTimeout(connectionTimeout);
        reject(new Error(`SSH connection error: ${err.message}`));
      });
      
      sshClient.connect(sshConfig);
    });
    
    // Create MySQL connection via the SSH tunnel
    mysqlConnection = await mysql.createConnection({
      host: config.SSH_LOCAL_HOST,
      port: config.SSH_LOCAL_PORT,
      user: config.DB_USER,
      password: config.DB_PASSWORD,
      database: config.DB_NAME,
      stream: sshTunnel
    });
    
    // Test connection
    const [rows] = await mysqlConnection.query('SELECT 1 AS connection_test');
    log('MySQL connectivity test successful via SSH tunnel');
  } catch (error) {
    // Clean up resources in case of error
    if (mysqlConnection) {
      try {
        await mysqlConnection.end();
      } catch (closeError) {
        // Ignore errors during cleanup
      }
      mysqlConnection = null;
    }
    
    if (sshClient) {
      try {
        sshClient.end();
      } catch (closeError) {
        // Ignore errors during cleanup
      }
      sshClient = null;
    }
    
    throw error;
  }
}

/**
 * Execute a query via direct SSH connection
 * @param {string} sql - SQL query to execute
 * @param {Array} params - Query parameters (optional)
 * @returns {Promise<Array>} Query results
 */
async function executeDirectQuery(sql, params = []) {
  if (!sshClient) {
    throw new Error('SSH client not initialized');
  }
  
  // Get configuration for current environment
  const config = environments[currentEnvironment];
  
  // Prepare the query with parameters
  let preparedSql = sql;
  if (params.length > 0) {
    // Simple parameter substitution - for production use proper escaping library
    params.forEach((param) => {
      const paramValue = typeof param === 'string' 
        ? `'${param.replace(/'/g, "''")}'` // Simple escape for strings
        : param;
      preparedSql = preparedSql.replace(`?`, paramValue);
    });
  }
  
  log(`Executing direct query: ${preparedSql}`);
  
  // Escape the SQL for shell execution
  const escapedSql = preparedSql.replace(/'/g, "'\\''");
  
  // Build the MySQL command line
  const mysqlCommand = `mysql --user='${config.DB_USER}' --password='${config.DB_PASSWORD}' '${config.DB_NAME}' --execute='${escapedSql}' --skip-column-names --batch`;
  
  // Execute the command via SSH
  return new Promise((resolve, reject) => {
    sshClient.exec(mysqlCommand, (err, stream) => {
      if (err) {
        return reject(new Error(`Failed to execute MySQL command: ${err.message}`));
      }
      
      let stdOut = '';
      let stdErr = '';
      
      stream.on('data', (data) => {
        stdOut += data.toString();
      });
      
      stream.stderr.on('data', (data) => {
        stdErr += data.toString();
      });
      
      stream.on('close', (code) => {
        if (code !== 0) {
          return reject(new Error(`MySQL command failed with exit code ${code}: ${stdErr}`));
        }
        
        if (stdErr) {
          log(`MySQL warnings: ${stdErr}`);
        }
        
        // Parse the output into an array of objects
        try {
          const rows = stdOut.trim().split('\n');
          
          // For empty results
          if (rows.length === 0 || (rows.length === 1 && rows[0] === '')) {
            return resolve([]);
          }
          
          // Parse rows
          const result = rows.map(row => {
            // Handle single value queries
            if (!row.includes('\t')) {
              return { value: row.trim() };
            }
            
            // For multi-column rows
            const values = row.split('\t');
            const obj = {};
            
            values.forEach((val, idx) => {
              obj[`column${idx + 1}`] = val;
            });
            
            return obj;
          });
          
          resolve(result);
        } catch (parseError) {
          reject(new Error(`Failed to parse MySQL output: ${parseError.message}`));
        }
      });
    });
  });
}

/**
 * Execute a query using the active connection method
 * @param {string} sql - SQL query to execute
 * @param {Array} params - Query parameters (optional)
 * @returns {Promise<Array>} Query results
 */
async function query(sql, params = []) {
  if (!isInitialized) {
    throw new Error('Database not initialized. Call init() first.');
  }
  
  try {
    log(`Executing query: ${sql}`);
    
    if (connectionMethod === 'direct') {
      return executeDirectQuery(sql, params);
    } else {
      const [rows] = await mysqlConnection.query(sql, params);
      return rows;
    }
  } catch (error) {
    log(`Query error: ${error.message}`);
    
    // Check if the connection was lost
    if (error.message.includes('Connection lost') || 
        error.message.includes('PROTOCOL_CONNECTION_LOST') ||
        error.message.includes('Connection closed')) {
      
      log('Connection lost, attempting to reconnect...');
      
      // Close existing connection
      await close();
      
      // Reinitialize
      await init(currentEnvironment, { 
        forceDirect: connectionMethod === 'direct',
        forceTunnel: connectionMethod === 'tunnel',
        debug: debugMode
      });
      
      // Retry the query
      log('Reconnection successful, retrying query...');
      return query(sql, params);
    }
    
    throw error;
  }
}

/**
 * Execute a transaction
 * @param {Function} callback - Function that receives a transaction object
 * @returns {Promise<any>} Result of the transaction
 */
async function transaction(callback) {
  if (!isInitialized) {
    throw new Error('Database not initialized. Call init() first.');
  }
  
  if (connectionMethod === 'tunnel' && mysqlConnection) {
    // Use native transaction support for tunnel method
    let conn;
    
    try {
      conn = await mysqlConnection.getConnection();
      await conn.beginTransaction();
      
      const result = await callback(conn);
      
      await conn.commit();
      return result;
    } catch (error) {
      if (conn) {
        try {
          await conn.rollback();
        } catch (rollbackError) {
          log(`Failed to rollback transaction: ${rollbackError.message}`);
        }
      }
      throw error;
    } finally {
      if (conn) {
        conn.release();
      }
    }
  } else {
    // Manual transaction for direct method
    try {
      await query('START TRANSACTION');
      
      const tx = {
        query: async (sql, params = []) => {
          return query(sql, params);
        }
      };
      
      const result = await callback(tx);
      
      await query('COMMIT');
      return result;
    } catch (error) {
      try {
        await query('ROLLBACK');
      } catch (rollbackError) {
        log(`Failed to rollback transaction: ${rollbackError.message}`);
      }
      throw error;
    }
  }
}

/**
 * Get information about the current database connection
 * @returns {Object} Connection information
 */
function getConnectionInfo() {
  return {
    initialized: isInitialized,
    environment: currentEnvironment,
    connectionMethod,
    config: currentEnvironment ? environments[currentEnvironment] : null,
    debugMode
  };
}

/**
 * Close the database connection
 * @returns {Promise<void>}
 */
async function close() {
  if (!isInitialized) {
    log('Database not initialized, nothing to close');
    return;
  }
  
  log(`Closing database connection (${connectionMethod})...`);
  
  try {
    if (mysqlConnection) {
      await mysqlConnection.end();
      mysqlConnection = null;
    }
    
    if (sshClient) {
      return new Promise((resolve) => {
        sshClient.on('close', () => {
          sshClient = null;
          sshTunnel = null;
          isInitialized = false;
          log('SSH connection closed');
          resolve();
        });
        
        sshClient.end();
      });
    }
  } catch (error) {
    log(`Error closing connection: ${error.message}`);
  }
  
  // Ensure state is reset even if there's an error
  mysqlConnection = null;
  sshClient = null;
  sshTunnel = null;
  isInitialized = false;
}

module.exports = {
  init,
  query,
  transaction,
  getConnectionInfo,
  close,
  setDebugMode,
  environments
}; 
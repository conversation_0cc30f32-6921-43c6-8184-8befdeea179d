function parseReportDetailsHtml(html, testId) {
    try {
        const parser = new DOMParser();
        const doc = parser.parseFromString(html, 'text/html');

        const result = {
            tsn_id: testId,
            test_cases: [],
            report_html: html, // Pass through the original HTML
            pagination: {
                currentPage: 1,
                totalPages: 1
            },
            originalParameters: {} // Initialize originalParameters
        };

        console.log('[DEBUG] parseReportDetailsHtml: Parsing details HTML for test ID:', testId);

        // Extract test session ID from the page content more reliably
        const tsnIdElement = doc.querySelector('span#tsn_id');
        if (tsnIdElement) {
            result.tsn_id = tsnIdElement.textContent.trim();
            console.log('[DEBUG] parseReportDetailsHtml: Found session ID in details HTML:', result.tsn_id);
        } else {
            console.warn('[DEBUG] parseReportDetailsHtml: Could not find session ID (span#tsn_id) in details HTML. Using provided testId:', testId);
        }

        // Extract pagination information if available (example, adjust selectors if needed)
        const navigationElement = doc.querySelector('span.navigation');
        if (navigationElement) {
            const currentPage = parseInt(navigationElement.textContent.trim());
            const totalPages = parseInt(navigationElement.getAttribute('data-size'));
            if (!isNaN(currentPage) && !isNaN(totalPages)) {
                result.pagination = { currentPage, totalPages };
                console.log('[DEBUG] parseReportDetailsHtml: Pagination: Page', result.pagination.currentPage, 'of', result.pagination.totalPages);
            }
        }

        // Extract test cases from the main table
        const table = doc.querySelector('table#table'); // More specific selector
        if (table) {
            const rows = table.querySelectorAll('tbody tr'); // More specific selector for rows within tbody
            console.log('[DEBUG] parseReportDetailsHtml: Found', rows.length, 'rows in the details table (table#table tbody tr)');

            const uniqueTestCases = new Map();
            
            rows.forEach(row => {
                const cells = row.querySelectorAll('td');

                // Expecting 6 cells: TC ID, Seq, Status, Desc, Input, ErrorDetails
                if (cells.length >= 6) { 
                    const tcId = cells[0]?.querySelector('a')?.textContent.trim() || cells[0]?.textContent.trim();
                    
                    if (tcId && /^\d+$/.test(tcId)) { // Ensure tcId is a number string
                        const stepSeq = cells[1]?.querySelector('a')?.textContent.trim() || cells[1]?.textContent.trim();
                        const stepStatusText = cells[2]?.querySelector('a')?.textContent.trim() || cells[2]?.textContent.trim(); // 'P' or 'F'
                        const stepDescription = cells[3]?.querySelector('a')?.textContent.trim() || cells[3]?.textContent.trim();
                        const stepInput = cells[4]?.querySelector('a')?.textContent.trim() || cells[4]?.textContent.trim();
                        const stepErrorDetails = cells[5]?.querySelector('a')?.textContent.trim() || cells[5]?.textContent.trim();

                        const isFailingStep = stepStatusText === 'F';

                        let testCaseEntry = uniqueTestCases.get(tcId);
                        if (!testCaseEntry) {
                            // First time seeing this test case, create its entry
                            // The overall 'description' and 'status' for the test case can be from its first step,
                            // or ideally, from the primary API call's test_cases data if available.
                            // For now, derive from the first step encountered.
                            testCaseEntry = {
                                tc_id: tcId,
                                // Assuming the description from the first step is representative or a placeholder
                                description: `Test Case ${tcId}`, // Placeholder, ideally from primary API
                                status: isFailingStep ? 'Failed' : 'Passed', // Initial status
                                steps: [], // Store all steps for this TC from the HTML
                                hasFailures: isFailingStep
                            };
                            uniqueTestCases.set(tcId, testCaseEntry);
                        } else if (isFailingStep) {
                            // If an existing test case encounters a failing step, mark it as failed overall
                            testCaseEntry.status = 'Failed';
                            testCaseEntry.hasFailures = true;
                        }

                        // Add current step details to the test case entry
                        testCaseEntry.steps.push({
                            seq: stepSeq,
                            status: stepStatusText,
                            description: stepDescription,
                            input: stepInput,
                            error_details: stepErrorDetails,
                            is_failing: isFailingStep
                        });

                    } else if (tcId) {
                        console.warn(`[DEBUG] parseReportDetailsHtml: Row for TC ID '${tcId}' skipped, not a numeric string or malformed.`);
                    }
                } else if (cells.length > 0) { // Avoid logging for empty rows if any
                    console.warn('[DEBUG] parseReportDetailsHtml: Row skipped, expected at least 6 cells, found:', cells.length, 'Content:', row.textContent.substring(0,100));
                }
            });
            
            result.test_cases = Array.from(uniqueTestCases.values());
            console.log(`[DEBUG] parseReportDetailsHtml: Extracted ${result.test_cases.length} unique test cases from ${rows.length} detail rows.`);
        } else {
            console.warn('[DEBUG] parseReportDetailsHtml: Could not find table#table in details HTML.');
        }

        // Extract 'Variables' section (keeping existing logic, ensure it doesn't break)
        const bodyText = doc.body.innerHTML; 
        const variablesHeaderIndex = bodyText.indexOf('Variables:<br>');
        if (variablesHeaderIndex !== -1) {
            const variablesStartIndex = variablesHeaderIndex + 'Variables:<br>'.length;
            // Find the end of the variables section, typically marked by a double <br><br> or end of content
            let variablesEndIndex = bodyText.indexOf('<br><br>', variablesStartIndex);
            if (variablesEndIndex === -1) { // If no double <br>, take up to a common next section or end
                const idLinkIndex = bodyText.indexOf('ID: <a href="ProfileLoader"', variablesStartIndex);
                if (idLinkIndex !== -1) {
                    variablesEndIndex = idLinkIndex;
                } else {
                    variablesEndIndex = bodyText.length; // Fallback to end of content
                }
            }

            const variablesBlock = bodyText.substring(variablesStartIndex, variablesEndIndex);
            const variableLines = variablesBlock.split('<br>').map(line => line.trim()).filter(line => line.includes('='));
            console.log('[DEBUG] parseReportDetailsHtml: Raw variable lines being processed:', JSON.stringify(variableLines, null, 2)); // Added for debugging
            
            variableLines.forEach(line => {
                const parts = line.split('=');
                const key = parts[0].trim();
                const value = parts.slice(1).join('=').trim(); // Join back in case value contains '='
                if (key && value !== undefined) {
                    result.originalParameters[key] = value;
                }
            });
            console.log('Extracted original parameters:', result.originalParameters);
        }

        // Ensure result.report_html contains the original HTML passed to the function.
        // The line 'report_html: html,' during result object initialization already handles this.
        // No new HTML should be generated here for result.report_html.

        return result;
    } catch (error) {
        console.error('Error parsing report details HTML:', error);
        return { tsn_id: testId, test_cases: [], error: error.message };
    }
}

/**
 * Load test details from the database API
 * @param {string} testId - Test ID
 * @param {Object} credentials - Credentials for API
 */

function transformRecentRunsData(recentRuns) {
    // IMPORTANT: This function is no longer used directly
    // since the API now returns data in the correct format.
    // The displayReports function handles both formats.

    return recentRuns;
}

// DataTable instance
let reportsDataTable = null;

/**
 * Display reports in the table
 * @param {Array} reports - Test reports to display
 * @param {boolean} isIncremental - Whether this is an incremental update or full refresh
 */
function displayReports(reportsInput, isIncremental = false) {
    console.log('Displaying reports:', reportsInput ? reportsInput.length : 0);

    // Process the input to ensure we have a valid array of reports
    let reports = reportsInput;

    console.log('displayReports called with reports:', reports);
    if (reports && reports.length > 0) {
        console.log('First report sample:', reports[0]);
    }

    // Handle various response formats
    if (reportsInput && !Array.isArray(reportsInput)) {
        console.log('Reports data is not an array, checking for nested data structure');
        // Check for common API response formats
        if (reportsInput.data && Array.isArray(reportsInput.data)) {
            reports = reportsInput.data;
            console.log('Found reports in data property');
        } else if (reportsInput.reports && Array.isArray(reportsInput.reports)) {
            reports = reportsInput.reports;
            console.log('Found reports in reports property');
        } else {
            // Search for any array in the response
            for (let key in reportsInput) {
                if (Array.isArray(reportsInput[key])) {
                    reports = reportsInput[key];
                    console.log(`Found reports in ${key} property`);
                    break;
                }
            }
        }
    }

    // Make sure currentState.reports is updated
    currentState.reports = reports;

    // If we still don't have an array, create an empty one
    if (!Array.isArray(reports)) {
        console.warn('Could not find reports array in response, using empty array');
        reports = [];
    }
    if (!reports || !Array.isArray(reports) || reports.length === 0) {
        console.log('No reports to display, showing empty message');
        displayEmptyMessage();
        return;
    }

    console.log(`Displaying ${reports.length} reports${isIncremental ? ' (incremental update)' : ''}`);

    // Get the table body
    const tableBody = elements.reportsTable.querySelector('tbody');
    if (!tableBody) {
        console.error('Table body not found');
        return;
    }

    if (!isIncremental) {
        tableBody.innerHTML = '';
    }

    // Use a safer sort approach with error handling
    try {
        // Sort reports by start time (newest first)
        reports.sort((a, b) => {
            try {
                const aTime = a.start_time || a.startTime || a.start_ts ? new Date(a.start_time || a.startTime || a.start_ts).getTime() : 0;
                const bTime = b.start_time || b.startTime || b.start_ts ? new Date(b.start_time || b.startTime || b.start_ts).getTime() : 0;
                return bTime - aTime;
            } catch (err) {
                console.warn('Error comparing dates during sort:', err);
                return 0; // If comparison fails, don't change order
            }
        });
    } catch (sortError) {
        console.error('Error sorting reports:', sortError);
        // Continue with unsorted reports
    }

    // Display each report
    reports.forEach(report => {
        try {
            // console.log('Processing report for display:', report);

            // Correctly access the time properties based on API response
            // The API returns start_time and end_time (with underscores)
            const startTime = formatDateTime(report.start_time || report.startTime);

            // For 'Running' status tests, always show 'Not completed' as end time
            const status = report.status || 'Unknown';
            const endTime = (status.toLowerCase() === 'running' || status.toLowerCase() === 'queued') ?
                'Not completed' :
                ((report.end_time || report.endTime) ? formatDateTime(report.end_time || report.endTime) : 'Not completed');

            // Calculate duration
            let duration = 'N/A';
            if (report.start_time && report.end_time &&
                status.toLowerCase() !== 'running' &&
                status.toLowerCase() !== 'queued') {
                duration = calculateDuration(report.start_time, report.end_time);
            }

            // Status - used to determine row color
            const statusClass = getStatusClass(status);

            // Test ID - use tsn_id as the primary identifier (this is the session ID)
            const displayId = report.tsn_id || report.id || '';

            // Test case or suite ID - use test_id from API which is either tc_id or ts_id
            const testCaseOrSuiteId = report.test_id || (report.tc_id || report.ts_id || '-');

            // Test name - use test_name from API
            const testName = report.test_name || report.name || `Test ${testCaseOrSuiteId}`;

            // Determine passed/failed count
            const passedTests = report.passed_cases || report.passed || 0;
            const failedTests = report.failed_cases || report.failed || 0;

            // Create a new row for this report
            const row = document.createElement('tr');
            row.className = `table-${statusClass}`;

            // Process user data - prioritize the uid field since that's where emails are coming from
            // Terminal output shows that user emails are stored in the uid field
            const userData = report.uid || report.user_id || report.user || report.created_by || report.username || '';
            console.log('User data for report:', {
                reportId: report.tsn_id,
                rawUid: report.uid,
                user_display: report.user_display,
                userData: userData
            });

            // Use user_display from backend if available, otherwise format the userData
            const formattedUser = report.user_display || formatUserEmail(userData);

            // Add data attributes for filtering AFTER formattedUser is defined
            row.setAttribute('data-user-id', report.uid || '');
            row.setAttribute('data-user-display', report.user_display || formattedUser || 'Unknown');
            console.log('Formatted user name:', formattedUser);

            // Store processed user data in the report object for filtering
            report.processed_user = formattedUser;

            // Create individual cells instead of using innerHTML - with the new duration cell
            const cells = [
                createCell(displayId),
                createCell(testName),
                createCell(testCaseOrSuiteId),
                createCell(status),
                createCell(startTime),
                createCell(endTime),
                createDurationCell(duration, report.start_time, report.end_time), // Use specialized duration cell with sorting attribute
                createCell(formattedUser), // User cell with properly formatted user data
                createCell(passedTests, 'text-success'),
                createCell(failedTests, 'text-danger'),
                createActionCell(report.tsn_id || report.id || '')
            ];

            // Append all cells to the row
            cells.forEach(cell => row.appendChild(cell));

            // Add row to the table
            tableBody.appendChild(row);
        } catch (error) {
            console.error(`Error displaying report for ID ${report.tsn_id || 'unknown'}:`, error);
            console.error('Report data that caused error:', JSON.stringify(report, null, 2));
        }
    });

    // Update counters
    updateCounters();

    // Initialize DataTable if it doesn't exist
    if (!reportsDataTable) {
        initializeDataTable();
    } else {
        // DataTable already exists
        if (isIncremental) {
            // For incremental, if you are adding rows directly to DOM and not using reportsDataTable.row.add(),
            // then invalidating might be necessary.
            // However, it's generally better to add new data via reportsDataTable.row.add().
            reportsDataTable.rows().invalidate().draw();
            console.log('DataTable incrementally updated and redrawn.');
        } else {
            // Full Reload: DataTable exists. The tableBody has been cleared and new <tr> elements
            // have been manually appended by the loop above.
            reportsDataTable.clear(); // Clear data from DataTable's cache

            // Find all `tr` elements in the tbody (these are the new rows we just added)
            const rowsToAdd = $(tableBody).find('tr');

            if (rowsToAdd.length > 0) {
                reportsDataTable.rows.add(rowsToAdd); // Add the new DOM rows to DataTable
            }

            reportsDataTable.draw(false); // Redraw the table. `false` preserves paging.
            console.log('DataTable cleared and redrawn with new full dataset from updated DOM.');
        }
    }

    // Populate user filter dropdown with unique users
    populateUserFilter();
}

/**
 * Display an empty message in the table
 */
function displayEmptyMessage() {
    const tbody = document.querySelector('#reports-table tbody');
    tbody.innerHTML = '<tr><td colspan="10" class="text-center">No reports found</td></tr>';
}

/**
 * Update the reports table with current data
 */
function updateReportsTable() {
    console.log('Updating reports table with current data:', currentState.reports);

    // Check if we have any reports
    if (!currentState.reports || currentState.reports.length === 0) {
        // Update the colspan to accommodate all columns
        elements.reportsTable.querySelector('tbody').innerHTML = '<tr><td colspan="10" class="text-center">No reports found</td></tr>';
        return;
    }

    // Sort reports by start time (newest first)
    const sortedReports = [...currentState.reports].sort((a, b) => {
        const aTime = a.startTime || a.start_time || a.start_ts ? new Date(a.startTime || a.start_time || a.start_ts).getTime() : 0;
        const bTime = b.startTime || b.start_time || b.start_ts ? new Date(b.startTime || b.start_time || b.start_ts).getTime() : 0;
        return bTime - aTime;
    });

    // Get table body
    const tableBody = elements.reportsTable.querySelector('tbody');
    tableBody.innerHTML = '';

    // Build table rows
    sortedReports.forEach(report => {
        const row = document.createElement('tr');

        // Set row class based on status
        if (report.status && report.status.toLowerCase() === 'failed') {
            row.classList.add('table-danger');
        } else if (report.status && report.status.toLowerCase() === 'passed') {
            row.classList.add('table-success');
        }

        // Format dates
        const startTime = formatDateTime(report.startTime || report.start_time || report.start_ts);
        const endTime = formatDateTime(report.endTime || report.end_time || report.end_ts);

        // Get user info for initiator column
        const userEmail = report.uid || report.user_id || report.user || '';
        const userName = formatUserEmail(userEmail);

        // Get test ID and name
        const tsnId = report.tsn_id || report.id || '';
        const testId = report.test_id || report.tc_id || report.ts_id || '';
        const testName = report.test_name || report.name || `Test ${testId}`;

        // Determine passed/failed count
        const passedCount = report.passed_cases || report.passed || 0;
        const failedCount = report.failed_cases || report.failed || 0;

        // Create individual cells instead of using innerHTML
        const cells = [
            createCell(tsnId),
            createCell(testName),
            createCell(testId),
            createCell(report.status || 'Unknown'),
            createCell(startTime),
            createCell(endTime),
            createUserCell(userEmail, userName),
            createCell(passedCount, 'text-success'),
            createCell(failedCount, 'text-danger'),
            createActionCell(tsnId)
        ];

        // Append all cells to the row
        cells.forEach(cell => row.appendChild(cell));

        // Add the row to the table
        tableBody.appendChild(row);
    });

    // Add event listeners to view details buttons
    document.querySelectorAll('.view-details').forEach(button => {
        button.addEventListener('click', function() {
            const tsnId = this.getAttribute('data-tsn-id');
            console.log('View details clicked for report ID:', tsnId);
            loadTestDetails(tsnId);
        });
    });
}

/**
 * Create a table cell with content
 * @param {*} content - Cell content
 * @param {string} className - Optional CSS class for the cell
 * @returns {HTMLTableCellElement} - The created table cell
 */

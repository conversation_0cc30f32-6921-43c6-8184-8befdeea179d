/**
 * Session Validation Middleware
 * Provides middleware for validating JWT tokens and sessions
 */

const authService = require('../auth/auth-service');
const sessionManager = require('../auth/session-manager');
const auditLogger = require('../utils/audit-logger');
const devConfig = require('../config/development');

/**
 * JWT Token validation middleware
 * Validates access tokens from cookies or Authorization header
 */
const validateJWTToken = (req, res, next) => {
  try {
    // Get token from cookie or Authorization header
    let accessToken = req.cookies.accessToken;
    
    if (!accessToken && req.headers.authorization) {
      const authHeader = req.headers.authorization;
      if (authHeader.startsWith('Bearer ')) {
        accessToken = authHeader.substring(7);
      }
    }

    // Skip validation for development mode if configured
    if (devConfig.shouldSkipAuth()) {
      const devUser = devConfig.getDevUser();
      req.user = { 
        uid: devUser.uid, 
        role: devUser.role,
        name: devUser.name,
        isDevelopmentUser: true
      };
      req.permissions = ['read', 'write', 'delete', 'manage_users'];
      devConfig.logDevWarning(`JWT validation bypassed for dev user: ${req.user.uid}`);
      return next();
    }

    if (!accessToken) {
      return res.status(401).json({
        success: false,
        message: 'Access token required',
        code: 'ACCESS_TOKEN_MISSING'
      });
    }

    // Validate token
    const validation = authService.validateAccessToken(accessToken);

    if (validation.valid) {
      req.user = validation.user;
      req.session = validation.session;
      req.permissions = validation.decoded.permissions;
      req.tokenData = validation.decoded;
      
      console.log(`✅ JWT Token validated for user: ${req.user.uid}`);
      next();
    } else {
      console.log(`❌ JWT Token validation failed: ${validation.error}`);
      
      // Handle different error types
      let statusCode = 401;
      if (validation.code === 'TOKEN_EXPIRED') {
        statusCode = 401;
      } else if (validation.code === 'TOKEN_REVOKED') {
        statusCode = 401;
      }

      return res.status(statusCode).json({
        success: false,
        message: validation.error,
        code: validation.code
      });
    }
  } catch (error) {
    console.error('JWT validation error:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal authentication error'
    });
  }
};

/**
 * Optional JWT validation middleware
 * Validates token if present, but doesn't require it
 */
const optionalJWTValidation = (req, res, next) => {
  try {
    // Get token from cookie or Authorization header
    let accessToken = req.cookies.accessToken;
    
    if (!accessToken && req.headers.authorization) {
      const authHeader = req.headers.authorization;
      if (authHeader.startsWith('Bearer ')) {
        accessToken = authHeader.substring(7);
      }
    }

    if (accessToken) {
      // Validate token if present
      const validation = authService.validateAccessToken(accessToken);

      if (validation.valid) {
        req.user = validation.user;
        req.session = validation.session;
        req.permissions = validation.decoded.permissions;
        req.tokenData = validation.decoded;
        req.isAuthenticated = true;
        
        console.log(`✅ Optional JWT Token validated for user: ${req.user.uid}`);
      } else {
        console.log(`⚠️ Optional JWT Token validation failed: ${validation.error}`);
        req.isAuthenticated = false;
      }
    } else {
      req.isAuthenticated = false;
    }

    next();
  } catch (error) {
    console.error('Optional JWT validation error:', error);
    req.isAuthenticated = false;
    next();
  }
};

/**
 * Permission checking middleware factory
 * @param {string|Array} requiredPermissions - Required permission(s)
 * @returns {Function} Express middleware function
 */
const requirePermissions = (requiredPermissions) => {
  const permissions = Array.isArray(requiredPermissions) ? requiredPermissions : [requiredPermissions];
  
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required',
        code: 'AUTH_REQUIRED'
      });
    }

    // Skip permission check for development users
    if (req.user.isDevelopmentUser) {
      devConfig.logDevWarning(`Permission check bypassed for dev user: ${permissions.join(', ')}`);
      return next();
    }

    // Check if user has all required permissions
    const userPermissions = req.permissions || [];
    const hasAllPermissions = permissions.every(permission => 
      userPermissions.includes(permission)
    );

    if (!hasAllPermissions) {
      console.log(`❌ Permission denied: ${req.user.uid} lacks permissions: ${permissions.join(', ')}`);

      // Log permission violation
      auditLogger.logPermissionViolation({
        uid: req.user.uid,
        ip: req.ip || req.connection.remoteAddress,
        requiredPermission: permissions.join(', '),
        userPermissions: userPermissions,
        endpoint: req.originalUrl,
        method: req.method
      });

      return res.status(403).json({
        success: false,
        message: `Access denied. Required permissions: ${permissions.join(', ')}`,
        code: 'INSUFFICIENT_PERMISSIONS'
      });
    }

    console.log(`✅ Permission granted: ${req.user.uid} has permissions: ${permissions.join(', ')}`);
    next();
  };
};

/**
 * Role checking middleware factory
 * @param {string|Array} requiredRoles - Required role(s)
 * @returns {Function} Express middleware function
 */
const requireRoles = (requiredRoles) => {
  const roles = Array.isArray(requiredRoles) ? requiredRoles : [requiredRoles];
  
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required',
        code: 'AUTH_REQUIRED'
      });
    }

    // Skip role check for development users
    if (req.user.isDevelopmentUser) {
      devConfig.logDevWarning(`Role check bypassed for dev user: ${roles.join(', ')}`);
      return next();
    }

    // Check if user has required role
    if (!roles.includes(req.user.role)) {
      console.log(`❌ Role denied: ${req.user.uid} has '${req.user.role}' but needs one of: ${roles.join(', ')}`);

      // Log permission violation (role-based)
      auditLogger.logPermissionViolation({
        uid: req.user.uid,
        ip: req.ip || req.connection.remoteAddress,
        requiredPermission: `Role: ${roles.join(' or ')}`,
        userPermissions: [`Role: ${req.user.role}`],
        endpoint: req.originalUrl,
        method: req.method
      });

      return res.status(403).json({
        success: false,
        message: `Access denied. Required role: ${roles.join(' or ')}`,
        code: 'INSUFFICIENT_ROLE'
      });
    }

    console.log(`✅ Role granted: ${req.user.uid} has required role '${req.user.role}'`);
    next();
  };
};

/**
 * Session activity tracking middleware
 * Updates last activity timestamp for the session
 */
const trackSessionActivity = (req, res, next) => {
  if (req.session && req.session.id) {
    const session = sessionManager.getSession(req.session.id);
    if (session) {
      session.lastActivity = Date.now();
    }
  }
  next();
};

/**
 * Automatic token refresh middleware
 * Attempts to refresh token if it's close to expiry
 */
const autoRefreshToken = async (req, res, next) => {
  try {
    if (req.tokenData && req.tokenData.exp) {
      const now = Math.floor(Date.now() / 1000);
      const timeToExpiry = req.tokenData.exp - now;
      
      // If token expires in less than 5 minutes, try to refresh
      if (timeToExpiry < 300) { // 5 minutes
        const refreshToken = req.cookies.refreshToken;
        
        if (refreshToken) {
          console.log('🔄 Auto-refreshing token that expires soon...');
          
          const refreshResult = authService.refreshAccessToken(refreshToken);
          
          if (refreshResult.success) {
            // Set new access token cookie
            const cookieOptions = {
              httpOnly: true,
              secure: process.env.NODE_ENV === 'production',
              sameSite: 'strict',
              path: '/',
              maxAge: refreshResult.expiresIn * 1000
            };

            res.cookie('accessToken', refreshResult.accessToken, cookieOptions);
            console.log('✅ Token auto-refreshed successfully');
          } else {
            console.log('❌ Auto-refresh failed:', refreshResult.error);
          }
        }
      }
    }
  } catch (error) {
    console.error('Auto-refresh error:', error);
  }
  
  next();
};

module.exports = {
  validateJWTToken,
  optionalJWTValidation,
  requirePermissions,
  requireRoles,
  trackSessionActivity,
  autoRefreshToken
};

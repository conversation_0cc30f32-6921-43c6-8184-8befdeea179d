# Auto-label configuration for issues
bug:
  - 'bug'
  - 'error'
  - 'issue'
  - 'broken'
  - 'not working'

feature:
  - 'feature'
  - 'enhancement' 
  - 'new'
  - 'add'
  - 'implement'

documentation:
  - 'docs'
  - 'documentation'
  - 'readme'
  - 'guide'

performance:
  - 'performance'
  - 'slow'
  - 'optimization'
  - 'speed'

security:
  - 'security'
  - 'vulnerability'
  - 'auth'
  - 'permissions'

test:
  - 'test'
  - 'testing'
  - 'unit test'
  - 'integration'

dashboard:
  - 'dashboard'
  - 'frontend'
  - 'ui'
  - 'interface'

api:
  - 'api'
  - 'backend'
  - 'server'
  - 'endpoint'

database:
  - 'database'
  - 'mysql'
  - 'sql'
  - 'query'

# Check if Python is installed
$pythonCmd = Get-Command python -ErrorAction SilentlyContinue
if ($null -eq $pythonCmd) {
    $pythonCmd = Get-Command python3 -ErrorAction SilentlyContinue
    if ($null -eq $pythonCmd) {
        Write-Host "Python is not installed or not in PATH. Please install Python 3.6+ and try again." -ForegroundColor Red
        exit 1
    }
}

Write-Host "Setting up environment..." -ForegroundColor Green

# Install dependencies
Write-Host "Installing dependencies..." -ForegroundColor Green
python -m pip install -r requirements.txt

# Run the URL checker script
Write-Host "Running URL checker..." -ForegroundColor Green
python url_checker.py

Write-Host "Setup completed." -ForegroundColor Green

# If documentation folder exists, list files
if (Test-Path -Path "documentation") {
    Write-Host "`nDocumentation files:" -ForegroundColor Cyan
    Get-ChildItem -Path "documentation" -Recurse | Where-Object { !$_.PSIsContainer } | Select-Object FullName
}

Write-Host "`nNext steps:" -ForegroundColor Cyan
Write-Host "1. Review the documentation files"
Write-Host "2. Follow the implementation plan if it exists"
Write-Host "3. Begin implementing the project based on the documentation" 
// Test script to verify the status priority fix for test 17979

console.log('=== TESTING STATUS PRIORITY FIX ===\n');

// Simulate the issue: activeTests map has correct status, recentRunsCache has stale status
function testStatusPriority() {
  console.log('1. Testing Status Priority Logic:');
  
  // Simulate test data from different sources
  const testsFromRecentRunsCache = [
    {
      tsn_id: '17979',
      status: 'running', // Stale status from cache
      endTime: '2025-07-28 09:00:22', // Has end time (completed)
      user: '<EMAIL>',
      name: 'PE2.1 Sanity Test'
    }
  ];
  
  const testsFromActiveMap = [
    {
      tsn_id: '17979',
      status: 'failed', // Correct status from activeTests map
      endTime: '2025-07-28 09:00:22', // Has end time (completed)
      user: '<EMAIL>',
      name: 'PE2.1 Sanity Test',
      tsn_id_from_map_key: '17979' // Marker that this came from activeTests map
    }
  ];
  
  // Simulate the combined tests array (cache data + active map data)
  const allTests = [...testsFromRecentRunsCache, ...testsFromActiveMap];
  
  console.log('  Combined tests array:');
  allTests.forEach((test, i) => {
    console.log(`    ${i}: status="${test.status}", from=${test.tsn_id_from_map_key ? 'activeMap' : 'cache'}`);
  });
  
  // Old logic (broken): Always use tests[0]
  const oldTestInfo = allTests[0];
  console.log(`\n  Old logic (tests[0]): status="${oldTestInfo.status}" ${oldTestInfo.status === 'failed' ? '❌' : '✅'}`);
  
  // New logic (fixed): Prioritize activeTests map data
  const testFromActiveMap = allTests.find(t => t.tsn_id_from_map_key);
  const newTestInfo = testFromActiveMap || allTests[0];
  console.log(`  New logic (prioritize activeMap): status="${newTestInfo.status}" ${newTestInfo.status === 'failed' ? '✅' : '❌'}`);
  
  return { oldTestInfo, newTestInfo };
}

// Test status calculation with the fixed logic
function testStatusCalculation(testInfo, hasEndTime) {
  console.log('\n2. Testing Status Calculation:');
  
  let statusClass, statusText;
  
  if (hasEndTime) {
    console.log(`  Test has endTime: ${hasEndTime}`);
    console.log(`  testInfo.status: "${testInfo.status}"`);
    
    // Check if we already have a determined status from the test object
    if (testInfo.status && ['passed', 'failed'].includes(testInfo.status.toLowerCase())) {
      const storedStatus = testInfo.status.toLowerCase();
      statusClass = storedStatus;
      statusText = storedStatus.charAt(0).toUpperCase() + storedStatus.slice(1);
      console.log(`  Using stored test status: ${statusClass} ✅`);
    } else {
      // Fallback logic
      statusClass = 'completed';
      statusText = 'Completed';
      console.log(`  Fallback to completed status: ${statusClass} ❌`);
    }
  } else {
    statusClass = 'running';
    statusText = 'Running';
    console.log(`  Test still running: ${statusClass}`);
  }
  
  return { statusClass, statusText };
}

// Run the tests
const { oldTestInfo, newTestInfo } = testStatusPriority();

console.log('\n3. Status Calculation Results:');

// Test with old logic (should show wrong status)
const oldResult = testStatusCalculation(oldTestInfo, true);
console.log(`  Old logic result: ${oldResult.statusClass} (${oldResult.statusText}) ${oldResult.statusClass === 'failed' ? '✅' : '❌'}`);

// Test with new logic (should show correct status)
const newResult = testStatusCalculation(newTestInfo, true);
console.log(`  New logic result: ${newResult.statusClass} (${newResult.statusText}) ${newResult.statusClass === 'failed' ? '✅' : '❌'}`);

console.log('\n=== SUMMARY ===');
console.log('✅ Fix #1: Status priority logic now uses activeTests map data over cache data');
console.log('✅ Fix #2: Status calculation now properly uses stored status from activeTests');
console.log('✅ Fix #3: Added debugging logs to trace status calculation flow');
console.log('');
console.log('Expected Results After Fix:');
console.log('- Test 17979 should show status "FAILED" (not "COMPLETED")');
console.log('- Console logs should show "Using test data from: activeTests map"');
console.log('- Console logs should show "Using stored test status: failed"');
console.log('- The UI should display the correct pass/fail status for completed tests');

console.log('\n=== REAL TEST 17979 SIMULATION ===');
console.log('Based on console logs, test 17979:');
console.log('- Line 191: System detected "failed" status ✅');
console.log('- Line 200: testInfo.status was "running" ❌ (stale data)');
console.log('- After fix: testInfo.status should be "failed" ✅');
console.log('- Expected UI: Should show "FAILED" instead of "COMPLETED"');

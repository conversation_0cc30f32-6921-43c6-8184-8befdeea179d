#!/bin/bash

# Check if Python is installed
if ! command -v python &> /dev/null; then
    if ! command -v python3 &> /dev/null; then
        echo "Python is not installed or not in PATH. Please install Python 3.6+ and try again."
        exit 1
    else
        PYTHON_CMD=python3
    fi
else
    PYTHON_CMD=python
fi

echo "Setting up environment..."

# Install dependencies
echo "Installing dependencies..."
$PYTHON_CMD -m pip install -r requirements.txt

# Run the URL checker script
echo "Running URL checker..."
$PYTHON_CMD url_checker.py

echo "Setup completed."

# If documentation folder exists, list files
if [ -d "documentation" ]; then
    echo -e "\nDocumentation files:"
    find documentation -type f | sort
fi

echo -e "\nNext steps:"
echo "1. Review the documentation files"
echo "2. Follow the implementation plan if it exists"
echo "3. <PERSON><PERSON> implementing the project based on the documentation" 
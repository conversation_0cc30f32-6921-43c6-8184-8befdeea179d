/**
 * Test Reports Routes
 */
const express = require('express');
const router = express.Router();
const { getTestReports } = require('../services/test-reports');
const db = require('../database');

// Get test reports for reports page
router.get('/test-reports', async (req, res) => {
  try {
   // console.log('[API /test-reports] Fetching test reports with query:', req.query);
    // getTestReports service now returns an object like:
    // { enrichedReports, totalRecords, highestTsnIdInResponse } for full fetch OR
    // { newEnrichedRuns, totalRecords, latestTsnIdInDelta } for incremental
    const serviceResponse = await getTestReports(req.query);

    if (req.query.since_tsn_id) {
      // This is an incremental fetch response
     // console.log('[API /test-reports] Sending incremental response:', JSON.stringify({ success: true, newRuns: serviceResponse.newEnrichedRuns, totalRecords: serviceResponse.totalRecords, latestTsnIdInDelta: serviceResponse.latestTsnIdInDelta }));
      return res.json({
        success: true,
        newRuns: serviceResponse.newEnrichedRuns,
        totalRecords: serviceResponse.totalRecords,
        latestTsnIdInDelta: serviceResponse.latestTsnIdInDelta
      });
    } else {
      // This is a full range fetch response
     // console.log('[API /test-reports] Sending full response:', JSON.stringify({ success: true, reports: serviceResponse.enrichedReports, totalRecords: serviceResponse.totalRecords, highestTsnIdInResponse: serviceResponse.highestTsnIdInResponse }));
      return res.json({
        success: true,
        reports: serviceResponse.enrichedReports,
        totalRecords: serviceResponse.totalRecords,
        highestTsnIdInResponse: serviceResponse.highestTsnIdInResponse
      });
    }
  } catch (error) {
    console.error('[API /test-reports] Error fetching test reports:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to retrieve test reports',
      error: error.message
    });
  }
});

// Get test report by tsn_id
router.get('/test-reports/:tsn_id', async (req, res) => {
  try {
    const { tsn_id } = req.params;
    console.log(`[API /test-reports/${tsn_id}] Fetching test report`);

    // Get test results for the specified tsn_id
    const testResults = await db.getTestResults(tsn_id);

    return res.json({
      success: true,
      reports: testResults
    });
  } catch (error) {
    console.error(`[API /test-reports/:tsn_id] Error fetching test report:`, error);
    return res.status(500).json({
      success: false,
      message: 'Failed to retrieve test report',
      error: error.message
    });
  }
});

// Get test report summary by tsn_id
router.get('/test-reports/:tsn_id/summary', async (req, res) => {
  try {
    const { tsn_id } = req.params;
    console.log(`[API /test-reports/${tsn_id}/summary] Fetching test report summary`);

    // Get test result summary for the specified tsn_id
    const summary = await db.getTestResultSummary(tsn_id);

    return res.json({
      success: true,
      summary
    });
  } catch (error) {
    console.error(`[API /test-reports/:tsn_id/summary] Error fetching test report summary:`, error);
    return res.status(500).json({
      success: false,
      message: 'Failed to retrieve test report summary',
      error: error.message
    });
  }
});

// Get test case results by tsn_id
router.get('/test-reports/:tsn_id/test-cases', async (req, res) => {
  try {
    const { tsn_id } = req.params;
    console.log(`[API /test-reports/${tsn_id}/test-cases] Fetching test case results`);

    // Get test case results for the specified tsn_id
    const testCases = await db.getTestCaseResults(tsn_id);

    return res.json({
      success: true,
      test_cases: testCases
    });
  } catch (error) {
    console.error(`[API /test-reports/:tsn_id/test-cases] Error fetching test case results:`, error);
    return res.status(500).json({
      success: false,
      message: 'Failed to retrieve test case results',
      error: error.message
    });
  }
});

module.exports = router;

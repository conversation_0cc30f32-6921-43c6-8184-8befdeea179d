// Test Suite Mapping Configuration
// Maps dropdown filter combinations to specific test suite IDs and metadata

const TEST_SUITE_MAPPING = {
    // Microservice Level Test Suites
    'Microservice-2.1': {
        ts_id: 326,
        name: 'Test suite Microservice level – Prod version 2.1',
        level: 'Microservice',
        version: '2.1',
        description: 'Production version 2.1 microservice tests',
        url: 'http://mprts-qa02.lab.wagerworks.com:9080/AutoRun/SuiteEditor?ts_id=326'
    },
    'Microservice-2.1 + Patch': {
        ts_id: 327,
        name: 'Test suite Microservice level 2.1 + Patch',
        level: 'Microservice',
        version: '2.1 + Patch',
        description: 'Version 2.1 with patches (e.g., 2.1_PR4212)',
        url: 'http://mprts-qa02.lab.wagerworks.com:9080/AutoRun/SuiteEditor?ts_id=327'
    },
    'Microservice-3.0': {
        ts_id: 328,
        name: 'Test suite Microservice level 3.0',
        level: 'Microservice',
        version: '3.0',
        description: 'Version 3.0 microservice tests',
        url: 'http://mprts-qa02.lab.wagerworks.com:9080/AutoRun/SuiteEditor?ts_id=328'
    },
    
    // Integrated Level Test Suites
    'Integrated-2.1': {
        ts_id: 331,
        name: 'Test suite Integrated level Prod version 2.1',
        level: 'Integrated',
        version: '2.1',
        description: 'Production version 2.1 integrated tests',
        url: 'http://mprts-qa02.lab.wagerworks.com:5080/AutoRun/SuiteEditor?ts_id=331'
    },
    'Integrated-2.1 + Patch': {
        ts_id: 329,
        name: 'Test suite Integrated level 2.1 + Patch',
        level: 'Integrated',
        version: '2.1 + Patch',
        description: 'Version 2.1 with patches for integrated tests',
        url: 'http://mprts-qa02.lab.wagerworks.com:9080/AutoRun/SuiteEditor?ts_id=329'
    },
    'Integrated-3.0': {
        ts_id: 330,
        name: 'Test suite Integrated level 3.0',
        level: 'Integrated',
        version: '3.0',
        description: 'Version 3.0 integrated tests',
        url: 'http://mprts-qa02.lab.wagerworks.com:9080/AutoRun/SuiteEditor?ts_id=330'
    }
};

// Helper functions for filtering and searching
class TestSuiteMapper {
    /**
     * Get test suites based on level and version filters
     * @param {string} level - Integration level filter (Microservice, Integrated, or empty for all)
     * @param {string} version - Version filter (2.1, 2.1 + Patch, 3.0, or empty for all)
     * @returns {Array} Array of matching test suite objects
     */
    static getFilteredTestSuites(level = '', version = '') {
        const results = [];
        
        // If no filters, return all test suites
        if (!level && !version) {
            return Object.values(TEST_SUITE_MAPPING);
        }
        
        // Filter based on level and/or version
        for (const [key, suite] of Object.entries(TEST_SUITE_MAPPING)) {
            const matchesLevel = !level || suite.level === level;
            const matchesVersion = !version || suite.version === version;
            
            if (matchesLevel && matchesVersion) {
                results.push(suite);
            }
        }
        
        return results;
    }
    
    /**
     * Get test suite by ts_id
     * @param {number} ts_id - Test suite ID
     * @returns {Object|null} Test suite object or null if not found
     */
    static getTestSuiteById(ts_id) {
        return Object.values(TEST_SUITE_MAPPING).find(suite => suite.ts_id === ts_id) || null;
    }
    
    /**
     * Get test suites by name pattern
     * @param {string} namePattern - Pattern to match against test suite names
     * @returns {Array} Array of matching test suite objects
     */
    static getTestSuitesByName(namePattern) {
        const pattern = namePattern.toLowerCase();
        return Object.values(TEST_SUITE_MAPPING).filter(suite => 
            suite.name.toLowerCase().includes(pattern)
        );
    }
    
    /**
     * Get all unique levels
     * @returns {Array} Array of unique level values
     */
    static getAllLevels() {
        const levels = new Set();
        Object.values(TEST_SUITE_MAPPING).forEach(suite => levels.add(suite.level));
        return Array.from(levels);
    }
    
    /**
     * Get all unique versions
     * @returns {Array} Array of unique version values
     */
    static getAllVersions() {
        const versions = new Set();
        Object.values(TEST_SUITE_MAPPING).forEach(suite => versions.add(suite.version));
        return Array.from(versions);
    }
}

module.exports = {
    TEST_SUITE_MAPPING,
    TestSuiteMapper
};

/**
 * Test Flow Verification Script
 * 
 * This script verifies the complete test flow:
 * 1. Triggers a test run via API call
 * 2. Connects to database and monitors test status until completion
 * 3. Retrieves test suite results from database
 * 4. Gets details of each test case/step in the test suite
 */

const axios = require('axios');
const path = require('path');
const fs = require('fs');

// Import the database manager
const dbManager = require('../frontend/server/db-manager');
const dbEnvironments = require('../frontend/server/db-environments');

// Process command line arguments to get environment
const args = process.argv.slice(2);
const selectedEnvironment = args[0] || 'qa02'; // Default to qa02 if not specified

// Configuration
let config = {
  // Environment selection
  environment: selectedEnvironment, // 'qa01', 'qa02', or 'qa03'
  
  // API Configuration
  api: {
    credentials: {
      uid: '<EMAIL>', // Use from db-environments example
      password: 'your_password'              // Replace with your actual password
    }
  },
  
  // User Information (for Active Tests panel)
  user: {
    user_id: 'Iakov.Volfkovich', // User ID - typically part of the email
    username: 'Iakov.Volfkovich' // Username
  },
  
  // Test Configuration
  test: {
    // Use either tc_id for a single test case or ts_id for a test suite
    tc_id: 3180,       // Example test case ID
    // ts_id: 312,     // Example test suite ID (uncomment to use)
    
    // Additional parameters will be set based on environment
    envir: null, // Will be set from environment
    shell_host: 'jps-qa10-app01',
    file_path: '/home/<USER>/',
    operatorConfigs: 'operatorNameConfigs',
    kafka_server: 'kafka-qa-a0.lab.wagerworks.com',
    dataCenter: 'GU',
    rgs_env: null, // Will be set from environment
    old_version: '0',
    networkType1: 'multi-site',
    networkType2: 'multi-site',
    sign: '-',
    rate_src: 'local'
  },
  
  // Script Configuration
  polling: {
    intervalMs: 5000,  // Check status every 5 seconds
    timeoutMs: 1800000 // 30 minutes timeout
  }
};

// Initialize the environment settings
function initializeEnvironment() {
  if (!dbEnvironments.environments[config.environment]) {
    throw new Error(`Environment ${config.environment} not found in configuration. Available environments: ${Object.keys(dbEnvironments.environments).join(', ')}`);
  }
  
  // Get environment configuration
  const envConfig = dbEnvironments.environments[config.environment];
  
  // Update the API base URL from environment
  config.api.baseUrl = envConfig.BASE_URL;
  
  // Update test environment to match selected environment
  config.test.envir = config.environment;
  config.test.rgs_env = config.environment;
  
  console.log(`Using ${config.environment} environment`);
  console.log(`API Base URL: ${config.api.baseUrl}`);
  
  return envConfig;
}

// Trigger test run via API
async function triggerTestRun() {
  try {
    console.log('Starting test run...');
    
    // Prepare request parameters
    const params = new URLSearchParams();
    
    // Add credentials
    Object.entries(config.api.credentials).forEach(([key, value]) => {
      params.append(key, value);
    });
    
    // Add user information for test initiator tracking
    // The API passes this to the database (even if not as initiator fields)
    Object.entries(config.user).forEach(([key, value]) => {
      params.append(key, value);
    });
    
    // Add test configuration
    Object.entries(config.test).forEach(([key, value]) => {
      if (value !== null && value !== undefined) {
        params.append(key, value);
      }
    });
    
    // Make the API request
    const response = await axios.post(
      `${config.api.baseUrl}CaseRunner`,
      params.toString(),
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      }
    );
    
    // Log response and extract tsn_id
    console.log(`Test run initiated with response status: ${response.status}`);
    
    // The response format is not specified in the documentation
    // Assuming the tsn_id is returned in the response body
    // You may need to adjust this based on the actual response format
    const tsnId = extractTsnId(response.data);
    
    if (!tsnId) {
      throw new Error('Failed to extract tsn_id from response');
    }
    
    console.log(`Test run started with tsn_id: ${tsnId}`);
    return tsnId;
    
  } catch (error) {
    console.error('Error triggering test run:', error.message);
    if (error.response) {
      console.error('Response data:', error.response.data);
      console.error('Response status:', error.response.status);
    }
    throw error;
  }
}

// Extract tsn_id from API response
function extractTsnId(responseData) {
  // This function needs to be adjusted based on the actual response format
  // The API seems to return HTML content, so we need to look for the tsn_id in different formats
  
  if (typeof responseData === 'string') {
    // Method 1: Check for ReportSummary?tsn_id=number (most reliable based on sample data)
    const reportMatch = responseData.match(/ReportSummary\?tsn_id=(\d+)/);
    if (reportMatch && reportMatch[1]) {
      return reportMatch[1];
    }
    
    // Method 2: Look for any number after tsn_id
    const anyTsnIdMatch = responseData.match(/tsn_id.*?(\d+)/);
    if (anyTsnIdMatch && anyTsnIdMatch[1]) {
      return anyTsnIdMatch[1];
    }
    
    // Method 3: The original regex (least likely based on sample data)
    const match = responseData.match(/tsn_id=(\d+)/);
    if (match && match[1]) {
      return match[1];
    }
  }
  
  // If the response is JSON or has a different structure, check for tsn_id property
  if (responseData && responseData.tsn_id) {
    return responseData.tsn_id;
  }
  
  // As a fallback, query the database for the latest tsn_id
  return null;
}

// Get latest tsn_id from database (fallback if API doesn't return it)
async function getLatestTsnId() {
  try {
    const rows = await dbManager.query('SELECT MAX(tsn_id) AS latest_tsn_id FROM test_result');
    
    if (rows && rows.length > 0 && rows[0].latest_tsn_id) {
      return rows[0].latest_tsn_id;
    }
    
    throw new Error('Failed to retrieve latest tsn_id from database');
  } catch (error) {
    console.error('Error getting latest tsn_id:', error.message);
    throw error;
  }
}

// Check test run status
async function checkTestStatus(tsnId) {
  try {
    // Query to check test status (joining with test_session for user info)
    const rows = await dbManager.query(`
      SELECT r.tsn_id, r.tc_id, r.outcome, s.uid as initiator_user, COUNT(*) as count
      FROM test_result r
      JOIN output i ON i.cnt = r.cnt
      LEFT JOIN test_session s ON r.tsn_id = s.tsn_id
      WHERE r.tsn_id = ?
      GROUP BY r.tc_id, r.outcome, r.tsn_id, s.uid
      ORDER BY r.creation_time ASC
    `, [tsnId]);
    
    console.log(`\nCurrent test status for tsn_id ${tsnId}:`);
    if (rows.length === 0) {
      console.log('No test results found yet. Test might still be initializing...');
      return { isComplete: false };
    }
    
    // Display current status
    const testCases = new Map();
    let allPassed = true;
    
    rows.forEach(row => {
      const { tc_id, outcome, count, initiator_user } = row;
      
      if (!testCases.has(tc_id)) {
        testCases.set(tc_id, { 
          outcomes: {},
          initiator: initiator_user || 'Unknown'
        });
      }
      
      testCases.get(tc_id).outcomes[outcome] = count;
      
      // If any outcome is not 'P' (Passed), the test is not all passed
      if (outcome !== 'P') {
        allPassed = false;
      }
    });
    
    // Print status summary
    for (const [tcId, data] of testCases.entries()) {
      const outcomes = Object.entries(data.outcomes)
        .map(([outcome, count]) => `${outcome}: ${count}`)
        .join(', ');
      
      // Include initiator information
      const currentUser = data.initiator === config.user.username ? '(Current User)' : '';
      console.log(`Test Case ${tcId}: ${outcomes} | Initiated by: ${data.initiator} ${currentUser}`);
    }
    
    // Check if test is complete by checking test_session.end_ts
    const statusRows = await dbManager.query(`
      SELECT end_ts 
      FROM test_session
      WHERE tsn_id = ?
    `, [tsnId]);
    
    // If end_ts is null, the test is still running
    const isRunning = statusRows.length === 0 || statusRows[0].end_ts === null;
    
    return { 
      isComplete: !isRunning,
      allPassed
    };
  } catch (error) {
    console.error('Error checking test status:', error.message);
    throw error;
  }
}

// Get test run summary
async function getTestSummary(tsnId) {
  try {
    // Query to get test summary
    const rows = await dbManager.query(`
      SELECT 
        tsn_id, 
        SUM(CASE WHEN outcome='P' THEN 1 ELSE 0 END) AS passed_cases,
        SUM(CASE WHEN outcome='F' THEN 1 ELSE 0 END) AS failed_cases,
        TIMEDIFF(MAX(creation_time), MIN(creation_time)) AS duration
      FROM test_result
      WHERE tsn_id = ?
      GROUP BY tsn_id
    `, [tsnId]);
    
    if (rows.length === 0) {
      throw new Error(`No summary found for tsn_id ${tsnId}`);
    }
    
    console.log('\n====== TEST SUMMARY ======');
    console.log(`Test Suite Run ID: ${rows[0].tsn_id}`);
    console.log(`Passed Cases: ${rows[0].passed_cases}`);
    console.log(`Failed Cases: ${rows[0].failed_cases}`);
    console.log(`Duration: ${rows[0].duration}`);
    console.log('==========================\n');
    
    return rows[0];
  } catch (error) {
    console.error('Error getting test summary:', error.message);
    throw error;
  }
}

// Get detailed failure information
async function getFailureDetails(tsnId) {
  try {
    // Query to get failure details
    const rows = await dbManager.query(`
      SELECT r.cnt, seq_index, tsn_id, tc_id, outcome, txt
      FROM test_result r, output i
      WHERE i.cnt = r.cnt
        AND r.tsn_id = ?
        AND outcome = 'F'
    `, [tsnId]);
    
    console.log('\n====== FAILURE DETAILS ======');
    
    if (rows.length === 0) {
      console.log('No failures detected.');
      return [];
    }
    
    // Group failures by test case
    const failuresByTestCase = new Map();
    
    rows.forEach(row => {
      const { tc_id, cnt, seq_index, txt } = row;
      
      if (!failuresByTestCase.has(tc_id)) {
        failuresByTestCase.set(tc_id, []);
      }
      
      failuresByTestCase.get(tc_id).push({
        cnt,
        seq_index,
        output: txt
      });
    });
    
    // Print failures by test case
    for (const [tcId, failures] of failuresByTestCase.entries()) {
      console.log(`\nTest Case ${tcId} Failures:`);
      failures.forEach((failure, index) => {
        console.log(`  Failure #${index + 1}:`);
        console.log(`  - Sequence Index: ${failure.seq_index}`);
        console.log(`  - Output: ${failure.output.substring(0, 150)}${failure.output.length > 150 ? '...' : ''}`);
      });
    }
    
    console.log('=============================\n');
    
    return rows;
  } catch (error) {
    console.error('Error getting failure details:', error.message);
    throw error;
  }
}

// Get detailed test case information
async function getTestCaseDetails(tsnId) {
  try {
    // Query to get test case details
    const rows = await dbManager.query(`
      SELECT DISTINCT tc_id, name
      FROM test_result r
      JOIN test_case t ON r.tc_id = t.tc_id
      WHERE tsn_id = ?
    `, [tsnId]);
    
    console.log('\n====== TEST CASE DETAILS ======');
    
    if (rows.length === 0) {
      console.log('No test case details found.');
      return [];
    }
    
    // Print test case details
    rows.forEach(row => {
      console.log(`Test Case ID: ${row.tc_id}, Name: ${row.name}`);
    });
    
    console.log('===============================\n');
    
    return rows;
  } catch (error) {
    console.error('Error getting test case details:', error.message);
    throw error;
  }
}

// Get active tests for all users
async function getActiveTests() {
  try {
    // Updated query to properly handle the fields available in test_session
    // There's no tc_id or tsn_id column directly in sessions with NULL end_ts
    const rows = await dbManager.query(`
      SELECT s.tsn_id, 
             COALESCE(s.tc_id, 0) as tc_id, 
             s.uid as initiator_user, 
             s.start_ts as creation_time,
             CASE WHEN s.uid = ? THEN true ELSE false END as is_current_user
      FROM test_session s
      WHERE s.end_ts IS NULL  -- Tests that haven't finished yet
      ORDER BY s.start_ts DESC
    `, [config.user.username]);
    
    console.log('\n====== ACTIVE TESTS ======');
    
    if (rows.length === 0) {
      console.log('No active tests currently running.');
      return [];
    }
    
    // Group by initiator
    const testsByInitiator = new Map();
    
    rows.forEach(row => {
      const { initiator_user, tc_id, tsn_id, creation_time } = row;
      // Handle null values properly
      const initiator = initiator_user || 'Unknown';
      const testCaseId = tc_id || 'Unknown';
      const testRunId = tsn_id || 'Unknown';
      const startTime = creation_time ? new Date(creation_time).toLocaleString() : 'Unknown';
      
      if (!testsByInitiator.has(initiator)) {
        testsByInitiator.set(initiator, []);
      }
      
      testsByInitiator.get(initiator).push({
        tc_id: testCaseId,
        tsn_id: testRunId,
        creation_time: startTime
      });
    });
    
    // Display by initiator
    let currentUserTestsCount = 0;
    let otherUsersTestsCount = 0;
    
    console.log('Current User Tests:');
    if (testsByInitiator.has(config.user.username)) {
      const userTests = testsByInitiator.get(config.user.username);
      currentUserTestsCount = userTests.length;
      
      userTests.forEach(test => {
        console.log(`  Test Case ${test.tc_id} (Run ID: ${test.tsn_id}) - Started at: ${test.creation_time}`);
      });
    } else {
      console.log('  No tests running by current user.');
    }
    
    console.log('\nOther Users\' Tests:');
    for (const [initiator, tests] of testsByInitiator.entries()) {
      if (initiator !== config.user.username) {
        console.log(`  ${initiator}:`);
        tests.forEach(test => {
          console.log(`    Test Case ${test.tc_id} (Run ID: ${test.tsn_id}) - Started at: ${test.creation_time}`);
        });
        otherUsersTestsCount += tests.length;
      }
    }
    
    console.log('\nSummary:');
    console.log(`  Current User: ${currentUserTestsCount} active tests`);
    console.log(`  Other Users: ${otherUsersTestsCount} active tests`);
    console.log('=========================\n');
    
    return rows;
  } catch (error) {
    console.error('Error getting active tests:', error.message);
    throw error;
  }
}

// Main function to run the test verification flow
async function runTestVerification() {
  try {
    console.log('Starting test flow verification...');
    
    // Initialize configuration with environment settings
    const envConfig = initializeEnvironment();
    
    // Initialize the database connection using db-manager
    console.log(`Initializing database connection to ${config.environment}...`);
    await dbManager.init(config.environment);
    
    const connectionInfo = dbManager.getConnectionInfo();
    console.log(`Database connected successfully using ${connectionInfo.connectionMethod} method`);
    
    // Check for active tests before starting a new one
    console.log('Checking for currently active tests...');
    await getActiveTests();
    
    // Trigger test run via API call
    const tsnId = await triggerTestRun();
    
    // If we couldn't get tsn_id from API response, get the latest one
    let testId = tsnId;
    if (!testId) {
      console.log('Getting latest tsn_id from database...');
      testId = await getLatestTsnId();
      console.log(`Using latest tsn_id: ${testId}`);
    }
    
    // Monitor test status until completion
    console.log(`Monitoring test status for tsn_id: ${testId}`);
    
    let isComplete = false;
    let allPassed = false;
    const startTime = Date.now();
    
    while (!isComplete && (Date.now() - startTime < config.polling.timeoutMs)) {
      const status = await checkTestStatus(testId);
      isComplete = status.isComplete;
      allPassed = status.allPassed;
      
      if (isComplete) {
        console.log('\nTest run completed!');
        break;
      }
      
      console.log(`Waiting ${config.polling.intervalMs / 1000} seconds before next check...`);
      await new Promise(resolve => setTimeout(resolve, config.polling.intervalMs));
    }
    
    if (!isComplete) {
      throw new Error(`Test run timed out after ${config.polling.timeoutMs / 60000} minutes`);
    }
    
    // Get test summary
    const summary = await getTestSummary(testId);
    
    // Get failure details if any
    if (summary.failed_cases > 0) {
      await getFailureDetails(testId);
    }
    
    // Get test case details
    await getTestCaseDetails(testId);
    
    // After test completion, check for active tests again
    console.log('Test completed. Checking for currently active tests...');
    await getActiveTests();
    
    console.log('Test flow verification completed successfully');
    
  } catch (error) {
    console.error('Test flow verification failed:', error.message);
  } finally {
    // Clean up resources
    if (dbManager.getConnectionInfo().initialized) {
      console.log('Closing database connection...');
      await dbManager.close();
    }
  }
}

// Run the test verification
runTestVerification().catch(error => {
  console.error('Fatal error:', error);
  process.exit(1);
}); 
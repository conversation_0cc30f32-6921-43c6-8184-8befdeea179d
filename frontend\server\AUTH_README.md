# SmartTest Authentication System

A comprehensive, secure authentication system for the SmartTest automation framework, featuring JWT-based authentication, role-based access control, and advanced security features.

## 🔐 Features

### Core Authentication
- **JWT-based Authentication**: Secure token-based authentication with access and refresh tokens
- **Role-Based Access Control (RBAC)**: Admin, tester, and viewer roles with granular permissions
- **Session Management**: Secure session handling with automatic expiration and renewal
- **Account Security**: Progressive account lockout and suspicious activity detection

### Security Features
- **Rate Limiting**: Configurable rate limits for different endpoints
- **CSRF Protection**: Cross-Site Request Forgery protection for state-changing operations
- **Security Headers**: Comprehensive security headers (CSP, HSTS, X-Frame-Options, etc.)
- **Input Validation**: SQL injection and XSS prevention
- **Audit Logging**: Comprehensive logging of authentication and security events

### Advanced Features
- **Development Mode**: Secure development mode with authentication bypass
- **Account Lockout**: Progressive lockout with IP-based tracking
- **Security Monitoring**: Real-time security event monitoring and alerting
- **Admin Interface**: Web-based user management interface

## 🚀 Quick Start

### Prerequisites
- Node.js 16+ 
- npm or yarn
- bcrypt for password hashing

### Installation

1. **Install Dependencies**
```bash
cd frontend/server
npm install
```

2. **Configure Environment**
```bash
cp .env.example .env
# Edit .env with your configuration
```

3. **Set Up Users**
```bash
# Create initial admin user
node scripts/add-user.js add -u <EMAIL> -p SecurePass123! -r admin -n "Admin User"
```

4. **Start Server**
```bash
npm start
```

### Basic Configuration

Create a `.env` file:
```bash
NODE_ENV=development
SESSION_SECRET=your-super-secure-secret-key-here
JWT_SECRET=your-jwt-secret-key-here
SESSION_TIMEOUT=3600
DEV_MODE_ENABLED=true
DEV_USER_UID=<EMAIL>
DEV_USER_ROLE=admin
```

## 📖 Documentation

### Configuration Guides
- **[Authentication Configuration](docs/authentication-configuration.md)** - Complete configuration guide
- **[Security Best Practices](docs/security-best-practices.md)** - Production security guidelines

### API Documentation
- **[Authentication Endpoints](#authentication-endpoints)** - Login, logout, token refresh
- **[Admin Endpoints](#admin-endpoints)** - User management and system administration
- **[Security Endpoints](#security-endpoints)** - Security testing and monitoring

### Testing
- **[Test Documentation](tests/README.md)** - Comprehensive test suite documentation
- **[Security Tests](tests/security-integration.test.js)** - Security feature testing

## 🔑 Authentication Endpoints

### Login
```http
POST /auth/login
Content-Type: application/json

{
  "uid": "<EMAIL>",
  "password": "password123"
}
```

**Response:**
```json
{
  "success": true,
  "user": {
    "uid": "<EMAIL>",
    "role": "tester",
    "name": "Test User"
  },
  "permissions": ["read", "write"],
  "expiresIn": 900,
  "tokenType": "Bearer"
}
```

### Logout
```http
POST /auth/logout
```

### Token Refresh
```http
POST /auth/refresh
```

### Session Validation
```http
GET /auth/validate
```

## 👑 Admin Endpoints

### User Management
```http
# Get all users
GET /admin/users

# Get specific user
GET /admin/users/:uid

# Create user
POST /admin/users
{
  "uid": "<EMAIL>",
  "password": "SecurePass123!",
  "role": "tester",
  "name": "New User"
}

# Update user
PUT /admin/users/:uid

# Delete user
DELETE /admin/users/:uid
```

### Security Management
```http
# Get account lockout info
GET /admin/lockout/:uid

# Unlock account
POST /admin/unlock/:uid

# Get security statistics
GET /admin/security-stats

# Get audit logs
GET /admin/audit-logs?startDate=2024-01-01&endDate=2024-01-31&logType=security
```

## 🛡️ Security Features

### Rate Limiting
- **Authentication**: 5 attempts per 15 minutes
- **API Endpoints**: 100 requests per 15 minutes  
- **Admin Endpoints**: 50 requests per 15 minutes

### Account Lockout
- **Failed Attempts**: 5 attempts before lockout
- **Lockout Duration**: 15 minutes (progressive)
- **IP Tracking**: Suspicious activity detection
- **Admin Override**: Manual account unlock

### Security Headers
```http
X-Frame-Options: DENY
X-Content-Type-Options: nosniff
X-XSS-Protection: 1; mode=block
Content-Security-Policy: default-src 'self'; ...
Strict-Transport-Security: max-age=********; includeSubDomains
```

### CSRF Protection
- Automatic CSRF token generation
- Required for state-changing operations
- Configurable exempt routes

## 🏗️ Architecture

### Core Components

```
frontend/server/
├── auth/                    # Authentication core
│   ├── auth-service.js     # Main authentication service
│   ├── user-manager.js     # User management
│   ├── session-manager.js  # JWT session management
│   └── account-lockout.js  # Account security
├── middleware/             # Security middleware
│   ├── security.js         # Security headers, CSRF, rate limiting
│   ├── session-validation.js # JWT validation
│   └── auth.js            # Legacy auth middleware
├── routes/                 # API routes
│   ├── auth.js            # Authentication endpoints
│   ├── admin.js           # Admin endpoints
│   └── security-test.js   # Security testing endpoints
├── utils/                  # Utilities
│   ├── audit-logger.js    # Security event logging
│   └── validation.js      # Input validation
├── config/                 # Configuration
│   ├── allowed-users.json # User database
│   ├── app-config.js      # Application configuration
│   └── development.js     # Development mode settings
└── docs/                   # Documentation
    ├── authentication-configuration.md
    └── security-best-practices.md
```

### Security Flow

1. **Authentication Request** → Rate limiting → Input validation → User lookup
2. **Token Generation** → JWT creation → Secure cookie setting → Session storage
3. **Request Authorization** → Token validation → Permission check → Route access
4. **Security Monitoring** → Event logging → Threat detection → Response actions

## 🧪 Testing

### Run Tests
```bash
cd tests
npm install
npm test
```

### Test Coverage
- Authentication rejection scenarios
- Security feature integration
- Rate limiting and CSRF protection
- Session management
- Input validation and sanitization

### Security Testing
```bash
# Run security-specific tests
npm run test:security

# Run with coverage
npm run test:coverage

# Security scan
curl http://localhost:3000/security-test/scan
```

## 🚀 Deployment

### Production Checklist
- [ ] Set `NODE_ENV=production`
- [ ] Configure strong secrets (64+ characters)
- [ ] Disable development mode
- [ ] Set up HTTPS/TLS
- [ ] Configure monitoring and logging
- [ ] Set up backup procedures
- [ ] Review security settings

### Environment Variables
```bash
NODE_ENV=production
SESSION_SECRET=production-secret-minimum-64-characters
JWT_SECRET=production-jwt-secret-minimum-64-characters
SESSION_TIMEOUT=1800
HTTPS_ONLY=true
AUDIT_LOG_ENABLED=true
```

## 🔧 Configuration

### User Roles and Permissions

| Role | Permissions | Description |
|------|-------------|-------------|
| **admin** | read, write, delete, manage_users, view_logs | Full system access |
| **tester** | read, write | Test execution and reporting |
| **viewer** | read | Read-only access |

### Security Configuration
```javascript
// Rate limiting
rateLimits: {
  auth: { windowMs: 15 * 60 * 1000, max: 5 },
  api: { windowMs: 15 * 60 * 1000, max: 100 },
  admin: { windowMs: 15 * 60 * 1000, max: 50 }
}

// Account lockout
lockoutConfig: {
  maxFailedAttempts: 5,
  lockoutDuration: 15 * 60 * 1000,
  progressiveLockout: true
}

// Session management
sessionConfig: {
  accessTokenExpiry: 15 * 60,
  refreshTokenExpiry: 7 * 24 * 60 * 60
}
```

## 🐛 Troubleshooting

### Common Issues

**Authentication Failures**
```bash
# Check user exists
cat config/allowed-users.json | grep "<EMAIL>"

# Verify logs
tail -f logs/security.log
```

**Account Lockout**
```bash
# Check lockout status
curl http://localhost:3000/admin/lockout/<EMAIL>

# Unlock account
curl -X POST http://localhost:3000/admin/unlock/<EMAIL>
```

**Session Issues**
```bash
# Validate JWT secret
echo $JWT_SECRET | wc -c

# Check session validation
curl http://localhost:3000/auth/validate
```

## 📊 Monitoring

### Key Metrics
- Authentication success/failure rates
- Account lockout frequency
- Session duration and renewal rates
- Security event frequency
- API response times

### Log Files
- `logs/audit.log` - Authentication and admin actions
- `logs/security.log` - Security events and violations
- `logs/error.log` - System errors and exceptions

---

**⚠️ Security Notice**: This system handles sensitive authentication data. Always follow security best practices and keep the system updated with the latest security patches.

For detailed configuration and deployment information, see the documentation in the `docs/` directory.

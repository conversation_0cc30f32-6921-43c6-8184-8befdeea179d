# Reports Page API Endpoints

This document describes the API endpoints used by the reports page, including both server-side and direct external API endpoints.

## Overview

The reports page implements a hybrid data access approach that combines:
1. Direct external API integration for test results and details
2. Server-side API endpoints for analytics and historical data

## Direct External API Endpoints

The reports page directly integrates with the following external API endpoints on port 9080:

### Authentication

```
POST /AutoRun/Login
```

Authenticates a user and returns a JSESSIONID cookie.

**Request Parameters:**
- `uid` (required): User ID
- `password` (required): Password

**Response:**
HTTP 302 redirect with a `Set-Cookie` header containing the JSESSIONID cookie.

### Test Report Summary

```
GET /AutoRun/ReportSummary
```

Retrieves a summary of a test run.

**Request Parameters:**
- `tsn_id` (required): Test session ID

**Required Headers:**
- `Cookie: JSESSIONID=<jsessionid>`

**Response:**
HTML response containing test summary information, including:
- Start Time
- End Time
- Case(s) passed
- Case(s) failed
- Overall status (PASS/FAIL)

### Test Report Details

```
GET /AutoRun/ReportDetails
```

Retrieves detailed information about a test run.

**Request Parameters:**
- `tsn_id` (required): Test session ID
- `index` (optional): Page number for pagination

**Required Headers:**
- `Cookie: JSESSIONID=<jsessionid>`

**Response:**
HTML response containing detailed test steps, including:
- Test case ID
- Sequence index
- Outcome (P/F)
- Description
- Input
- Output

## Server-Side API Endpoints

The reports page can also use the following server-side API endpoints:

### Recent Runs

```
GET /local/recent-runs
```

Retrieves recent test runs from the database.

**Query Parameters:**
- `uid` (required): User ID
- `password` (required): Password
- `limit` (optional): Maximum number of runs to return (default: 20)

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "tsn_id": "12345",
      "type": "Test Case",
      "envir": "QA02",
      "status": "Success",
      "start_time": "2023-01-01T12:00:00Z",
      "end_time": "2023-01-01T12:05:00Z",
      "duration": "5:00",
      "total_cases": 10,
      "passed_cases": 8,
      "failed_cases": 2,
      "skipped_cases": 0
    }
  ],
  "message": "Recent runs retrieved successfully"
}
```

### Test Details

```
GET /local/test-details/:tsn_id
```

Retrieves detailed information about a specific test run from the database.

**Path Parameters:**
- `tsn_id` (required): Test session ID

**Query Parameters:**
- `uid` (required): User ID
- `password` (required): Password

**Response:**
```json
{
  "success": true,
  "data": {
    "tsn_id": "12345",
    "type": "Test Case",
    "envir": "QA02",
    "status": "Success",
    "start_time": "2023-01-01T12:00:00Z",
    "end_time": "2023-01-01T12:05:00Z",
    "duration": "5:00",
    "total_cases": 10,
    "passed_cases": 8,
    "failed_cases": 2,
    "skipped_cases": 0,
    "test_cases": [
      {
        "tc_id": "3180",
        "seq_index": "1",
        "status": "Passed",
        "description": "Login to the system",
        "input_output": "Input: username=test, password=test\nOutput: Login successful"
      }
    ]
  },
  "message": "Test details retrieved successfully"
}
```

## Hybrid Data Flow

The reports page implements the following data flow:

1. **Initial Load**:
   - The page attempts to get session IDs from multiple sources:
     1. Local storage cache
     2. Server-side API (`/local/recent-runs`) via ApiService
     3. Hardcoded fallback values

2. **Test Results Table**:
   - The page fetches recent runs data using the ApiService
   - The ApiService handles authentication and request formatting
   - The data is transformed and displayed in the table
   - Every 10 seconds, the data is refreshed automatically

3. **Test Details View**:
   - When a user clicks "Details" for a specific test, the page fetches test details:
     - If using ApiService: Fetches from `/local/test-details/:tsn_id`
     - If using direct external API: Fetches from external API endpoints
   - The data is combined and displayed in the details section

4. **Analytics (Future)**:
   - Complex analytics will use server-side API endpoints that query the database
   - Charts will be updated whenever the reports data is refreshed

## Proper Separation of Concerns

The refactored implementation follows a proper separation of concerns:

1. **Frontend Layer (DataTable.js)**:
   - Handles UI rendering and user interactions
   - Uses ApiService for all API calls
   - Processes and displays the received data

2. **API Layer (ApiService)**:
   - Provides a unified interface for all API calls
   - Handles authentication and request formatting
   - Processes responses into a standardized format

3. **Server-Side API Routes**:
   - Handle HTTP requests and responses
   - Use the high-level database abstraction
   - Format responses in a standardized format

4. **Database Layer**:
   - Provides a high-level abstraction for database operations
   - Handles database connections and queries
   - Formats raw database results into structured data

## Error Handling

The reports page implements robust error handling:

1. **Authentication Errors**:
   - If authentication fails, the page will retry with alternative credentials
   - If all authentication attempts fail, an error message is displayed

2. **API Errors**:
   - If the external API returns an error, the page will attempt to use the server-side API
   - If both APIs fail, an error message is displayed

3. **Network Errors**:
   - If a network error occurs, the page will retry the request
   - If the retry fails, an error message is displayed

## Security Considerations

- Credentials are stored securely in session storage
- JSESSIONID cookies are managed securely
- Direct API calls use HTTPS when available
- HTML responses are sanitized before being displayed

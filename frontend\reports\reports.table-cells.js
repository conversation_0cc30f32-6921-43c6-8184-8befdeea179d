function createCell(content, className) {
    const cell = document.createElement('td');
    cell.textContent = content;
    if (className) {
        cell.classList.add(className);
    }
    return cell;
}

/**
 * Create a cell specifically for duration with proper sorting attribute
 * @param {string} durationText - Formatted duration text (e.g., "1h 2m 30s")
 * @param {string} startTime - Start time string
 * @param {string} endTime - End time string
 * @returns {HTMLTableCellElement} - The created duration cell with data attribute for sorting
 */
function createDurationCell(durationText, startTime, endTime) {
    const cell = document.createElement('td');
    cell.textContent = durationText;

    // Calculate duration in seconds for sorting
    let durationSeconds = 0;

    try {
        if (startTime && endTime) {
            const start = new Date(startTime);
            const end = new Date(endTime);

            if (!isNaN(start.getTime()) && !isNaN(end.getTime())) {
                durationSeconds = Math.floor((end - start) / 1000);
            }
        }
    } catch (error) {
        console.error('Error calculating duration seconds:', error);
    }

    // Set data attribute for sorting
    cell.setAttribute('data-duration-seconds', durationSeconds.toString());

    return cell;
}

/**
 * Create a cell for the user info with a tooltip
 * @param {string} email - User email
 * @param {string} name - Formatted user name
 * @returns {HTMLTableCellElement} - The created table cell
 */
function createUserCell(email, name) {
    const cell = document.createElement('td');
    cell.textContent = name;
    cell.title = email; // Add tooltip with full email
    return cell;
}

/**
 * Create a cell with action buttons
 * @param {string} tsnId - Test session ID
 * @returns {HTMLTableCellElement} - The created table cell
 */
function createActionCell(tsnId) {
    const cell = document.createElement('td');

    // Create Details button
    const detailsBtn = document.createElement('button');
    detailsBtn.className = 'btn btn-primary btn-sm view-details';
    detailsBtn.textContent = 'Details';
    detailsBtn.setAttribute('data-tsn-id', tsnId);

    // Add direct click event to the button
    detailsBtn.addEventListener('click', function() {
        console.log('Details button clicked for test ID:', tsnId);
        loadTestDetails(tsnId);
    });

    cell.appendChild(detailsBtn);
    return cell;
}

/**
 * Get CSS class for status
 * @param {string} status - Status text
 * @returns {string} - CSS class for the status
 */
function getStatusClass(status) {
    if (!status) return 'secondary';

    switch(status.toLowerCase()) {
        case 'success':
        case 'passed':
            return 'success';
        case 'failed':
            return 'danger';
        case 'running':
            return 'primary';
        case 'queued':
            return 'info';
        case 'completed':
            return 'secondary';
        default:
            return 'secondary';
    }
}

/**
 * Format user email to display name
 * @param {string} email - User email or ID
 * @returns {string} - Formatted user name
 */
function formatUserEmail(email) {
    // Log the input to debug user data handling
    console.log('formatUserEmail input:', email);

    if (!email) {
        console.log('Empty email value, returning Unknown');
        return 'Unknown';
    }

    // If it's not a string, convert to string
    if (typeof email !== 'string') {
        email = String(email);
        console.log('Converted non-string to:', email);
    }

    // If it's an empty string or 'null', return 'Unknown'
    if (email === '' || email === 'null' || email === 'NULL') {
        console.log('Email is empty string or null value');
        return 'Unknown';
    }

    // If it looks like an email address, extract the username part
    if (email.includes('@')) {
        const username = email.split('@')[0];
        console.log('Extracted username from email:', username);
        return username;
    }

    // Otherwise, return the original value
    console.log('Using original value as username:', email);
    return email;
}

// Load details for a specific test

# Script to query database schema information
$server = "mprts-qa02.lab.wagerworks.com"
$database = "rgs_test"
# Using privileged user credentials instead of read-only (rgs_ro) to access more tables
$dbUser = "rgs_rw"
$dbPassword = "rgs_rw"
$sshUser = "volfkoi"
$sshKeyPath = "$env:USERPROFILE\.ssh\id_rsa_dbserver"

# Create output directory if it doesn't exist
$outputDir = "documentation\Database\scripts_output"
if (-not (Test-Path $outputDir)) {
    New-Item -ItemType Directory -Path $outputDir -Force
}

# List all tables
$query1 = "SHOW TABLES;"
Write-Host "Executing query: $query1" -ForegroundColor Cyan
$cmd1 = "ssh -i `"$sshKeyPath`" -o HostKeyAlgorithms=+ssh-rsa ${sshUser}@${server} `"mysql --user=$dbUser --password='$dbPassword' $database --execute='$query1'`""
Write-Host "Command: $cmd1" -ForegroundColor Gray
Invoke-Expression $cmd1 | Out-File -FilePath "$outputDir\tables_list.txt"
Write-Host "Tables list saved to $outputDir\tables_list.txt" -ForegroundColor Green

# Get detailed schema for key tables
$keyTables = @("test_result", "output", "test_suite_group", "test_case_group", "test_project")
foreach ($table in $keyTables) {
    Write-Host "Getting schema for table: $table" -ForegroundColor Cyan
    $query2 = "DESCRIBE $table;"
    $cmd2 = "ssh -i `"$sshKeyPath`" -o HostKeyAlgorithms=+ssh-rsa ${sshUser}@${server} `"mysql --user=$dbUser --password='$dbPassword' $database --execute='$query2'`""
    Invoke-Expression $cmd2 | Out-File -FilePath "$outputDir\schema_${table}.txt"
    Write-Host "Schema for $table saved to $outputDir\schema_${table}.txt" -ForegroundColor Green
}

# Find relationships between tables
$query3 = "SELECT TABLE_NAME, COLUMN_NAME, REFERENCED_TABLE_NAME, REFERENCED_COLUMN_NAME 
FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
WHERE REFERENCED_TABLE_SCHEMA = '$database' 
AND REFERENCED_TABLE_NAME IS NOT NULL 
ORDER BY TABLE_NAME, COLUMN_NAME;"

Write-Host "Getting table relationships" -ForegroundColor Cyan
$cmd3 = "ssh -i `"$sshKeyPath`" -o HostKeyAlgorithms=+ssh-rsa ${sshUser}@${server} `"mysql --user=$dbUser --password='$dbPassword' $database --execute='$query3'`""
Invoke-Expression $cmd3 | Out-File -FilePath "$outputDir\table_relationships.txt"
Write-Host "Table relationships saved to $outputDir\table_relationships.txt" -ForegroundColor Green

# Get column information for all tables
$query4 = "SELECT TABLE_NAME, COLUMN_NAME, DATA_TYPE, COLUMN_TYPE, IS_NULLABLE, COLUMN_DEFAULT
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_SCHEMA = '$database'
ORDER BY TABLE_NAME, ORDINAL_POSITION;"

Write-Host "Getting column information for all tables" -ForegroundColor Cyan
$cmd4 = "ssh -i `"$sshKeyPath`" -o HostKeyAlgorithms=+ssh-rsa ${sshUser}@${server} `"mysql --user=$dbUser --password='$dbPassword' --execute='$query4'`""
Invoke-Expression $cmd4 | Out-File -FilePath "$outputDir\all_columns.txt"
Write-Host "Column information saved to $outputDir\all_columns.txt" -ForegroundColor Green

# Get information about test case structure
$query5 = "SELECT * FROM test_case LIMIT 1;"
Write-Host "Getting test case structure" -ForegroundColor Cyan
$cmd5 = "ssh -i `"$sshKeyPath`" -o HostKeyAlgorithms=+ssh-rsa ${sshUser}@${server} `"mysql --user=$dbUser --password='$dbPassword' $database --execute='$query5'`""
Invoke-Expression $cmd5 | Out-File -FilePath "$outputDir\test_case_example.txt"
Write-Host "Test case structure saved to $outputDir\test_case_example.txt" -ForegroundColor Green

Write-Host "All schema information has been collected!" -ForegroundColor Green 
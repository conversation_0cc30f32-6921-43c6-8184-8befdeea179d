/**
 * Final Test Script
 * 
 * This script tests both the exact command string and properly extracted parameters.
 */

const axios = require('axios');
const mysql = require('mysql2/promise');

async function main() {
  try {
    console.log('Running final test script...');
    
    // Configuration
    const config = {
      environments: {
        qa02: {
          dbHost: 'mprts-qa01.lab.wagerworks.com',
          dbUser: 'rgs_rw',
          dbPassword: 'rgs_rw',
          dbName: 'rgs_test',
          dbPort: 3306
        }
      }
    };

    // 1. Exact curl command data string - works with <PERSON>'s credentials
    const exactData = "uid=<EMAIL>&password=test&tc_id=1279&envir=qa01&shell_host=jps-qa10-app01&file_path=/home/<USER>/&operatorConfigs=operatorNameConfigs&kafka_server=kafka-qa-a0.lab.wagerworks.com&dataCenter=GU&rgs_env=qa01&old_version=0&&networkType1=multi-site&networkType2=multi-site&sign=-&rate_src=local";
    
    // 2. Same parameters as URLSearchParams object but with proper encoding
    const params = new URLSearchParams();
    params.append('uid', '<EMAIL>');
    params.append('password', 'test');
    params.append('tc_id', '1279');
    params.append('envir', 'qa01');
    params.append('shell_host', 'jps-qa10-app01');
    params.append('file_path', '/home/<USER>/');
    params.append('operatorConfigs', 'operatorNameConfigs');
    params.append('kafka_server', 'kafka-qa-a0.lab.wagerworks.com');
    params.append('dataCenter', 'GU');
    params.append('rgs_env', 'qa01');
    params.append('old_version', '0');
    // Note the double-ampersand in the original string - might be significant
    params.append('', ''); // Adding empty key and value to simulate double-ampersand
    params.append('networkType1', 'multi-site');
    params.append('networkType2', 'multi-site');
    params.append('sign', '-');
    params.append('rate_src', 'local');
    
    const encodedData = params.toString();
    
    console.log('Test 1: Exact curl string (should work)');
    console.log('---------------------------------------');
    const exactResponse = await axios.post(
      'http://mprts-qa01.lab.wagerworks.com:5080/AutoRun/CaseRunner',
      exactData,
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      }
    );
    
    console.log('API Response Status:', exactResponse.status);
    
    // Extract tsn_id from the response
    let exactTsnId = null;
    if (typeof exactResponse.data === 'string') {
      const match = exactResponse.data.match(/ReportSummary\?tsn_id=(\d+)/);
      if (match && match[1]) {
        exactTsnId = match[1];
        console.log(`Successfully extracted tsn_id: ${exactTsnId}`);
      } else {
        console.log('Could not extract tsn_id from response');
      }
    }
    
    console.log('\nTest 2: URLSearchParams-encoded data');
    console.log('-------------------------------------');
    console.log('Encoded data string:');
    console.log(encodedData);
    
    const encodedResponse = await axios.post(
      'http://mprts-qa01.lab.wagerworks.com:5080/AutoRun/CaseRunner',
      encodedData,
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      }
    );
    
    console.log('API Response Status:', encodedResponse.status);
    
    // Extract tsn_id from the response
    let encodedTsnId = null;
    if (typeof encodedResponse.data === 'string') {
      const match = encodedResponse.data.match(/ReportSummary\?tsn_id=(\d+)/);
      if (match && match[1]) {
        encodedTsnId = match[1];
        console.log(`Successfully extracted tsn_id: ${encodedTsnId}`);
      } else {
        console.log('Could not extract tsn_id from response');
      }
    }
    
    // Connect to database to check the results of both calls
    console.log('\nConnecting to database to check test results...');
    const connection = await mysql.createConnection({
      host: config.environments.qa02.dbHost,
      user: config.environments.qa02.dbUser,
      password: config.environments.qa02.dbPassword,
      database: config.environments.qa02.dbName,
      port: config.environments.qa02.dbPort
    });
    
    if (exactTsnId) {
      console.log(`\nChecking status for Test 1 (tsn_id: ${exactTsnId})...`);
      const [rows1] = await connection.query(`
        SELECT * FROM test_session WHERE tsn_id = ?
      `, [exactTsnId]);
      
      if (rows1.length > 0) {
        console.log('Test Session Details:');
        console.log(`User: ${rows1[0].uid}`);
        console.log(`Start Time: ${rows1[0].start_ts}`);
        console.log(`End Time: ${rows1[0].end_ts || 'Still running'}`);
        console.log(`Status: ${rows1[0].end_ts ? 'Completed' : 'Running'}`);
      } else {
        console.log('No test session found');
      }
    }
    
    if (encodedTsnId) {
      console.log(`\nChecking status for Test 2 (tsn_id: ${encodedTsnId})...`);
      const [rows2] = await connection.query(`
        SELECT * FROM test_session WHERE tsn_id = ?
      `, [encodedTsnId]);
      
      if (rows2.length > 0) {
        console.log('Test Session Details:');
        console.log(`User: ${rows2[0].uid}`);
        console.log(`Start Time: ${rows2[0].start_ts}`);
        console.log(`End Time: ${rows2[0].end_ts || 'Still running'}`);
        console.log(`Status: ${rows2[0].end_ts ? 'Completed' : 'Running'}`);
      } else {
        console.log('No test session found');
      }
    }
    
    console.log('\nChecking test results (if any)...');
    if (exactTsnId) {
      const [resultRows1] = await connection.query(`
        SELECT tc_id, outcome, COUNT(*) as count
        FROM test_result
        WHERE tsn_id = ?
        GROUP BY tc_id, outcome
      `, [exactTsnId]);
      
      if (resultRows1.length > 0) {
        console.log(`\nTest 1 Results (tsn_id: ${exactTsnId}):`);
        resultRows1.forEach(row => {
          console.log(`Test Case ${row.tc_id}: ${row.outcome} (${row.count} steps)`);
        });
      } else {
        console.log(`\nNo test results found for Test 1 (tsn_id: ${exactTsnId})`);
      }
    }
    
    if (encodedTsnId) {
      const [resultRows2] = await connection.query(`
        SELECT tc_id, outcome, COUNT(*) as count
        FROM test_result
        WHERE tsn_id = ?
        GROUP BY tc_id, outcome
      `, [encodedTsnId]);
      
      if (resultRows2.length > 0) {
        console.log(`\nTest 2 Results (tsn_id: ${encodedTsnId}):`);
        resultRows2.forEach(row => {
          console.log(`Test Case ${row.tc_id}: ${row.outcome} (${row.count} steps)`);
        });
      } else {
        console.log(`\nNo test results found for Test 2 (tsn_id: ${encodedTsnId})`);
      }
    }
    
    // Close database connection
    await connection.end();
    console.log('\nDatabase connection closed');
    
    console.log('\n=== CONCLUSION ===');
    console.log(`Test 1 (Exact curl string): ${exactTsnId ? 'SUCCESS' : 'FAILURE'}`);
    console.log(`Test 2 (URLSearchParams): ${encodedTsnId ? 'SUCCESS' : 'FAILURE'}`);
    console.log('The main difference is that the exact curl string has unencoded characters and a double-ampersand before networkType1');
    
  } catch (error) {
    console.error('Error:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data (first 200 chars):', 
        typeof error.response.data === 'string' 
          ? error.response.data.substring(0, 200) 
          : JSON.stringify(error.response.data).substring(0, 200)
      );
    }
  }
}

main().catch(console.error); 
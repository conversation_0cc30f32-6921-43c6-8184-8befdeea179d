/**
 * <PERSON><PERSON>t to fetch test report data with authentication using axios
 */

const axios = require('axios');
const fs = require('fs');

// Test session ID to fetch
const TEST_SESSION_ID = process.argv[2] || '13732';

// Authentication credentials
const credentials = {
  uid: '<EMAIL>',
  password: 'test',
  utc_off: '0'
};

// Base URL for the external system
const baseUrl = 'http://mprts-qa02.lab.wagerworks.com:5080/AutoRun';

// Create axios instance that automatically handles cookies
const axiosInstance = axios.create({
  baseURL: baseUrl,
  withCredentials: true,
  maxRedirects: 5
});

// Store cookies between requests
let cookies = [];

/**
 * Handle cookies from response
 */
function extractCookies(response) {
  const setCookies = response.headers['set-cookie'];
  if (setCookies) {
    cookies = setCookies;
    console.log('Cookies updated:', cookies);
  }
  return cookies;
}

/**
 * Main execution function
 */
async function main() {
  try {
    console.log('Logging in...');
    
    // Convert credentials to form data format
    const formData = new URLSearchParams();
    Object.entries(credentials).forEach(([key, value]) => {
      formData.append(key, value);
    });
    
    // Step 1: Login to get session cookies
    const loginResponse = await axiosInstance.post('/Login', formData, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    });
    
    console.log(`Login response status: ${loginResponse.status}`);
    
    // Extract cookies
    extractCookies(loginResponse);
    
    // Step 2: Fetch the report directly using the ReportSummary endpoint
    console.log(`Directly fetching ReportSummary for session ${TEST_SESSION_ID}`);
    const reportResponse = await axiosInstance.get(`/ReportSummary`, {
      params: {
        tsn_id: TEST_SESSION_ID
      },
      headers: {
        Cookie: cookies.join('; ')
      }
    });
    
    console.log(`Report response status: ${reportResponse.status}`);
    console.log(`Content-Type: ${reportResponse.headers['content-type']}`);
    
    // Save the HTML content to a file
    if (reportResponse.data) {
      const data = reportResponse.data;
      console.log('Report HTML excerpt:');
      console.log(typeof data === 'string' ? data.substring(0, 500) : 'Non-string response');
      
      fs.writeFileSync('report_sample.html', 
                      typeof data === 'string' ? data : JSON.stringify(data, null, 2));
      console.log('Full report saved to report_sample.html');
      
      // Check if we got a login page instead of a report
      if (typeof data === 'string' && data.includes('Login Page')) {
        console.log('Got a login page instead of a report. Need to handle authentication differently.');
        
        // Try a different approach - manual redirect after login
        console.log('Trying an alternative approach with manual redirect...');
        
        // First, get a fresh login page to capture any necessary tokens
        const loginPageResponse = await axiosInstance.get('/Login');
        extractCookies(loginPageResponse);
        
        // Submit login form again with all cookies
        const loginResponse2 = await axiosInstance.post('/Login', formData, {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Cookie': cookies.join('; ')
          },
          maxRedirects: 0,
          validateStatus: status => status >= 200 && status < 400
        });
        
        console.log(`Second login attempt status: ${loginResponse2.status}`);
        extractCookies(loginResponse2);
        
        // Now attempt to fetch the report again
        const reportResponse2 = await axiosInstance.get(`/ReportSummary`, {
          params: {
            tsn_id: TEST_SESSION_ID
          },
          headers: {
            'Cookie': cookies.join('; ')
          }
        });
        
        console.log(`Second report fetch status: ${reportResponse2.status}`);
        
        if (reportResponse2.data) {
          const data2 = reportResponse2.data;
          console.log('Second attempt report excerpt:');
          console.log(typeof data2 === 'string' ? data2.substring(0, 500) : 'Non-string response');
          
          fs.writeFileSync('report_sample2.html', 
                          typeof data2 === 'string' ? data2 : JSON.stringify(data2, null, 2));
          console.log('Second attempt report saved to report_sample2.html');
        }
      }
    } else {
      console.error('No data in response');
    }
  } catch (error) {
    console.error('Error:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response headers:', error.response.headers);
      console.error('Response data excerpt:', 
                  typeof error.response.data === 'string' 
                  ? error.response.data.substring(0, 200) 
                  : JSON.stringify(error.response.data).substring(0, 200));
    }
  }
}

// Run the script
main();

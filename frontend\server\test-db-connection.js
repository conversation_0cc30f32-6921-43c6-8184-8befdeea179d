/**
 * Database Connection Test Script
 *
 * This script tests the SSH tunnel and database connection with real credentials
 * It's a standalone script that bypasses jest and directly handles the cleanup
 */

// Import modules
const fs = require('fs');
const path = require('path');
require('dotenv').config(); // Load environment variables from .env

// Default values if environment variables are not set
const defaultEnvValues = {
  // Server Configuration
  PORT: 3000,
  BASE_URL: 'http://mprts-qa03.lab.wagerworks.com:5080/AutoRun/',

  // Database Configuration
  DB_HOST: 'mprts-qa03.lab.wagerworks.com',
  DB_USER: 'rgs_rw',
  DB_PASSWORD: 'rgs_rw',
  DB_NAME: 'rgs_test',
  DB_PORT: 3306,

  // SSH Tunnel Configuration
  SSH_ENABLED: true,
  SSH_HOST: 'mprts-qa03.lab.wagerworks.com',
  SSH_USER: process.env.SSH_USER || 'defaultuser',
  SSH_PORT: 22,
  SSH_KEY_PATH: path.join(process.env.USERPROFILE || process.env.HOME, '.ssh', 'id_rsa_dbserver'),
  SSH_LOCAL_HOST: '127.0.0.1',
  SSH_LOCAL_PORT: 3307
};

// Apply default values if environment variables are not set
Object.entries(defaultEnvValues).forEach(([key, value]) => {
  if (!process.env[key]) {
    process.env[key] = String(value);
  }
});

// Print loaded SSH and DB variables for verification
console.log('Environment variables loaded:');
['SSH_ENABLED', 'SSH_HOST', 'SSH_USER', 'SSH_PORT', 'SSH_KEY_PATH',
  'DB_HOST', 'DB_USER', 'DB_PASSWORD', 'DB_NAME', 'DB_PORT'].forEach(key => {
  // Mask password value in logs for security
  const value = key === 'DB_PASSWORD'
    ? (process.env[key] ? '********' : '(not set)')
    : (process.env[key] || '(not set)');
  console.log(`${key}: ${value}`);
});

// Import the db module
const db = require('./database');

// Main function to run the test
async function runTest() {
  try {
    console.log('\n--- STARTING DATABASE CONNECTION TEST ---\n');

    // Step 1: Initialize database connection
    console.log('Step 1: Initializing database connection...');
    await db.init();
    console.log('✅ Database connection initialized successfully!\n');

    // Step 2: Execute a simple query to test the connection
    console.log('Step 2: Testing query execution...');
    console.log('Running test query: SELECT 1 AS test_value');
    const result = await db.query('SELECT 1 AS test_value');
    console.log('Query result:', JSON.stringify(result));
    console.log('✅ Database query executed successfully!\n');

    // Step 3: Execute a more complex query
    console.log('Step 3: Getting database information...');
    const infoQuery = `
      SELECT
        @@version as db_version,
        database() as db_name,
        current_user() as current_user,
        @@hostname as hostname,
        @@port as port
    `;
    const dbInfo = await db.query(infoQuery);
    console.log('Database information:', JSON.stringify(dbInfo));
    console.log('✅ Database information query executed successfully!\n');

    // Step 4: Query for some database tables
    console.log('Step 4: Listing tables in database...');
    try {
      const tables = await db.query('SHOW TABLES');
      console.log('Found tables:', JSON.stringify(tables));
      console.log('✅ Table listing query executed successfully!\n');
    } catch (error) {
      console.error('❌ Failed to list tables:', error.message);
    }

    console.log('\n--- TEST COMPLETED SUCCESSFULLY ---');
    return true;
  } catch (error) {
    console.error('\n❌ TEST FAILED:', error);
    return false;
  } finally {
    // Always attempt to close the database connection
    try {
      console.log('\nClosing database connection...');
      await db.close();
      console.log('✅ Database connection closed successfully');
    } catch (closeError) {
      console.error('❌ Error closing database connection:', closeError);
    }
  }
}

// Run the test and exit with appropriate code
runTest()
  .then(success => {
    if (success) {
      console.log('\nAll tests passed! SSH tunnel and database connection are working properly.');
      process.exit(0);
    } else {
      console.error('\nTests failed! Please check the error messages above.');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('\nUnexpected error in the test runner:', error);
    process.exit(1);
  });
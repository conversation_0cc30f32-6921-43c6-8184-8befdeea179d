// Custom Test Runner JavaScript

// DOM Elements
const elements = {
    // Test runner controls
    testCaseIdInput: document.getElementById('test-case-id'),
    runTestButton: document.getElementById('run-test-btn'),
    projectSelect: document.getElementById('project-select'),
    shellHostSelect: document.getElementById('shell-host-select'),
    environmentDisplay: document.getElementById('environment-display'),

    // Advanced configuration controls
    advancedConfigToggle: document.getElementById('advanced-config-toggle'),
    advancedConfigSection: document.getElementById('advanced-config-section'),
    advancedConfigIcon: document.getElementById('advanced-config-icon'),
    filePathInput: document.getElementById('file-path-input'),
    operatorConfigsSelect: document.getElementById('operator-configs-select'),
    kafkaServerInput: document.getElementById('kafka-server-input'),
    dataCenterSelect: document.getElementById('data-center-select'),
    oldVersionInput: document.getElementById('old-version-input'),
    networkType1Select: document.getElementById('network-type1-select'),
    networkType2Select: document.getElementById('network-type2-select'),
    signSelect: document.getElementById('sign-select'),
    rateSrcSelect: document.getElementById('rate-src-select'),
    
    // Active tests panel
    activeTestsPanel: document.getElementById('active-tests-panel'),
    activeTestsCount: document.getElementById('active-tests-count'),
    
    // Test status elements (removed - Test Status Card no longer exists)
    
    // Recent runs table
    recentRunsTable: document.getElementById('recent-runs-table'),
    recentRunsBody: document.getElementById('recent-runs-body'),
    environmentConfigs: {
        development: document.getElementById('development-config'),
        staging: document.getElementById('staging-config'),
        production: document.getElementById('production-config')
    },
    n8nBaseUrl: document.getElementById('n8n-base-url'),
    testExecutionWebhook: document.getElementById('test-execution-webhook'),
    nlpWebhook: document.getElementById('nlp-webhook'),
    testResultsWebhook: document.getElementById('test-results-webhook'),
    refreshInterval: document.getElementById('refresh-interval'),
    enableNotifications: document.getElementById('enable-notifications'),
    autoExpandDetails: document.getElementById('auto-expand-details'),
    // Test configuration elements
    smokeTestTimeout: document.getElementById('smoke-test-timeout'),
    smokeTestRetries: document.getElementById('smoke-test-retries'),
    regressionTestTimeout: document.getElementById('regression-test-timeout'),
    regressionTestRetries: document.getElementById('regression-test-retries'),
    heartbeatTestTimeout: document.getElementById('heartbeat-test-timeout'),
    heartbeatTestFrequency: document.getElementById('heartbeat-test-frequency'),
    heartbeatAutoEnable: document.getElementById('heartbeat-auto-enable'),
    // Environment specific elements
    devDbHost: document.getElementById('dev-db-host'),
    devDbName: document.getElementById('dev-db-name'),
    stagingDbHost: document.getElementById('staging-db-host'),
    stagingDbName: document.getElementById('staging-db-name'),
    prodDbHost: document.getElementById('prod-db-host'),
    prodDbName: document.getElementById('prod-db-name')
};

// App state for test runner
let appState = {
    activeTests: new Map(), // Map of active tests by tsn_id
    recentRunsCache: [], // Cache of recent runs data
    pollInterval: null, // Interval for polling recent runs
    renderingInProgress: false, // Flag to prevent concurrent rendering
    credentials: { // User credentials (should be loaded from session/local storage)
        uid: '',
        password: ''
    },
    testRunDefaults: {
        envir: 'qa02',
        shell_host: 'jps-qa10-app01'
    }
};

// Performance optimization modules will be loaded via script tags
// See performance/init-performance.js for initialization

// Initialize performance optimizations with fallback
function tryInitializePerformanceOptimizations() {
    if (typeof window.initializePerformanceOptimizations === 'function') {
        // Full performance optimizations available
        window.initializePerformanceOptimizations();
        console.log('✅ Full performance optimizations initialized');
    } else if (typeof window.initializeSimpleOptimizations === 'function') {
        // Simple optimizations available
        window.initializeSimpleOptimizations();
        console.log('✅ Simple performance optimizations initialized');
    } else {
        console.warn('⚠️ No performance optimizations available');
        console.info('💡 Include performance/load-performance.js or performance/simple-optimizations.js for better performance');
    }
}

// Optimized refresh intervals (managed by PollingCoordinator)
const ACTIVE_TESTS_INTERVAL = 2000; // 2 seconds for active tests (15 sec to 5-6 min tests)
const RECENT_RUNS_INTERVAL = 8000; // 8 seconds for recent runs history

// Grace period for keeping completed tests visible (milliseconds)
const COMPLETED_TEST_GRACE_PERIOD = 10000;

// Initialize Custom Test Runner functionality
function initCustomTestRunner() {
    console.log('🚀 initCustomTestRunner called');

    // Set up event listeners for test runner controls
    setupEventListeners();

    // Load user credentials from localStorage
    loadCredentials();

    // Test counts initialization removed (Test Status Card no longer exists)

    // Initialize performance optimizations (if available)
    tryInitializePerformanceOptimizations();

    // Status bar removed - functionality moved to Active Tests panel

    // Start polling for recent runs only after API service is ready
    console.log('🔍 Checking API service availability...');
    console.log('window.apiService:', window.apiService);

    if (window.apiService) {
        console.log('✅ API service already available, setting credentials and starting polling...');
        // Set credentials in API service
        if (appState.credentials.uid && appState.credentials.password) {
            console.log('🔑 Setting credentials:', appState.credentials.uid);
            window.apiService.setCredentials(appState.credentials.uid, appState.credentials.password);
        }

        // Wait a moment for performance optimizations to initialize
        setTimeout(() => {
            startPolling();
        }, 500);
    } else {
        console.log('⏳ API service not ready, waiting for apiservice-ready event...');
        // Wait for API service to be ready
        document.addEventListener('apiservice-ready', () => {
            console.log('✅ API service ready event received, setting credentials and starting polling...');
            // Set credentials in API service
            if (appState.credentials.uid && appState.credentials.password) {
                console.log('🔑 Setting credentials:', appState.credentials.uid);
                window.apiService.setCredentials(appState.credentials.uid, appState.credentials.password);
            }

            // Wait a moment for performance optimizations to initialize
            setTimeout(() => {
                startPolling();
            }, 500);
        });

        // Also try to start polling after a delay as fallback
        setTimeout(() => {
            console.log('⏰ Fallback timeout triggered, checking API service...');
            console.log('window.apiService:', window.apiService);
            console.log('appState.pollInterval:', appState.pollInterval);

            if (window.apiService && !appState.pollInterval) {
                console.log('✅ Fallback: Setting credentials and starting polling after delay...');
                // Set credentials in API service
                if (appState.credentials.uid && appState.credentials.password) {
                    console.log('🔑 Setting credentials:', appState.credentials.uid);
                    window.apiService.setCredentials(appState.credentials.uid, appState.credentials.password);
                }
                startPolling();
            } else {
                console.log('❌ Fallback: API service still not available or polling already started');
            }
        }, 2000);
    }
}

// Load user credentials from unified auth client or fallback to legacy storage
function loadCredentials() {
    try {
        // First, check if unified auth client is available and authenticated
        if (window.unifiedAuthClient && window.unifiedAuthClient.isAuthenticated) {
            const user = window.unifiedAuthClient.getCurrentUser();
            if (user && user.uid) {
                appState.credentials = { uid: user.uid, password: '' }; // JWT doesn't need password
                console.log('Loaded credentials from unified auth client:', user.uid);

                // Set credentials in the global API service if available
                if (window.apiService) {
                    console.log('Setting JWT credentials in API service:', user.uid);
                    window.apiService.setCredentials(user.uid, ''); // JWT auth doesn't need password
                }

                return true;
            }
        }

        // Fallback to legacy authentication only if JWT is not available
        console.log('No JWT authentication found, checking legacy credentials...');

        // Try session storage first (preferred)
        const sessionUid = sessionStorage.getItem('smarttest_uid');
        const sessionPwd = sessionStorage.getItem('smarttest_pwd');

        if (sessionUid && sessionPwd) {
            appState.credentials = { uid: sessionUid, password: sessionPwd };
            console.log('Loaded legacy credentials from session storage:', sessionUid);
        } else {
            // Try localStorage as fallback
            const storedCredentials = localStorage.getItem('userCredentials');
            if (storedCredentials) {
                appState.credentials = JSON.parse(storedCredentials);
                console.log('Loaded legacy credentials from localStorage:', appState.credentials.uid);
            } else {
                console.log('No stored credentials found - authentication required');
                appState.credentials = { uid: '', password: '' };
                return false; // No credentials available
            }
        }

        // Set legacy credentials in the global API service if available
        if (window.apiService && appState.credentials.uid && appState.credentials.password) {
            console.log('Setting legacy credentials in API service:', appState.credentials.uid);
            window.apiService.setCredentials(appState.credentials.uid, appState.credentials.password);
        }

        return true; // Credentials loaded successfully
    } catch (error) {
        console.error('Error loading credentials:', error);
        return false;
    }
}

// Test counter functions removed (Test Status Card no longer exists)

// Set up event listeners for test runner controls
function setupEventListeners() {
    // Run test button
    elements.runTestButton.addEventListener('click', handleRunTest);

    // Projects dropdown (maps to QA environments)
    elements.projectSelect.addEventListener('change', function() {
        appState.testRunDefaults.envir = this.value;
    });

    // Shell host select
    elements.shellHostSelect.addEventListener('change', function() {
        appState.testRunDefaults.shell_host = this.value;
    });

    // Advanced configuration toggle
    if (elements.advancedConfigToggle) {
        elements.advancedConfigToggle.addEventListener('click', toggleAdvancedConfig);
    }

    // Set default values
    elements.projectSelect.value = appState.testRunDefaults.envir;
    elements.shellHostSelect.value = appState.testRunDefaults.shell_host;
}

// Toggle advanced configuration section
function toggleAdvancedConfig() {
    const section = elements.advancedConfigSection;
    const icon = elements.advancedConfigIcon;

    if (section.style.display === 'none') {
        section.style.display = 'block';
        icon.textContent = '▼';
    } else {
        section.style.display = 'none';
        icon.textContent = '▶';
    }
}

// Collect advanced configuration parameters
function getAdvancedConfigParams() {
    const params = {};

    // Only include parameters that have values (not empty)
    if (elements.filePathInput && elements.filePathInput.value.trim()) {
        params.file_path = elements.filePathInput.value.trim();
    }

    if (elements.operatorConfigsSelect && elements.operatorConfigsSelect.value) {
        params.operatorConfigs = elements.operatorConfigsSelect.value;
    }

    if (elements.kafkaServerInput && elements.kafkaServerInput.value.trim()) {
        params.kafka_server = elements.kafkaServerInput.value.trim();
    }

    if (elements.dataCenterSelect && elements.dataCenterSelect.value) {
        params.dataCenter = elements.dataCenterSelect.value;
    }

    if (elements.oldVersionInput && elements.oldVersionInput.value.trim()) {
        params.old_version = elements.oldVersionInput.value.trim();
    }

    if (elements.networkType1Select && elements.networkType1Select.value) {
        params.networkType1 = elements.networkType1Select.value;
    }

    if (elements.networkType2Select && elements.networkType2Select.value) {
        params.networkType2 = elements.networkType2Select.value;
    }

    if (elements.signSelect && elements.signSelect.value) {
        params.sign = elements.signSelect.value;
    }

    if (elements.rateSrcSelect && elements.rateSrcSelect.value) {
        params.rate_src = elements.rateSrcSelect.value;
    }

    return params;
}

// Handle the Run Test button click
async function handleRunTest() {
    // Get test case ID from input
    const testCaseId = elements.testCaseIdInput.value.trim();
    if (!testCaseId) {
        showError('Please enter a test case ID');
        return;
    }
    
    console.log('Test Case ID (before conversion):', testCaseId);
    console.log('Test Case ID type:', typeof testCaseId);
    
    // Get selected environment and shell host
    const environment = appState.testRunDefaults.envir;
    const shellHost = appState.testRunDefaults.shell_host;
    
    try {
        // Disable button during API call
        elements.runTestButton.disabled = true;
        elements.runTestButton.textContent = 'Running...';
        
        // Call the API to run the test case
        const result = await runTestCase(testCaseId, environment, shellHost);
        
        if (result && result.tsn_id) {
            showSuccess(`Test case ${testCaseId} started. TSN ID: ${result.tsn_id}`);

            // Create complete test data object for active tests
            const testData = {
                tsn_id: result.tsn_id,
                tc_id: testCaseId,
                testId: testCaseId,
                name: `Test Case ${testCaseId}`, // Default name, will be updated from database
                comment: '', // Will be updated from database
                environment: appState.testRunDefaults.envir || 'N/A',
                envir: appState.testRunDefaults.envir || 'N/A',
                status: 'running',
                success: result.success,
                message: result.message
            };

            // Add to active tests
            addActiveTest(testData);

            // Clear the input field for next test
            elements.testCaseIdInput.value = '';
        } else {
            showError(`Failed to start test case ${testCaseId}`);
        }
    } catch (error) {
        console.error('Error running test case:', error);
        showError(`Error running test case: ${error.message || 'Unknown error'}`);
    } finally {
        // Re-enable button
        elements.runTestButton.disabled = false;
        elements.runTestButton.textContent = 'Run Test';
    }
}

// Run a test case via the API
async function runTestCase(tcId, environment, shellHost) {
    try {
        console.log('runTestCase received tcId:', tcId, 'type:', typeof tcId);
        
        // Ensure tcId is a string or number, not an object
        if (typeof tcId === 'object') {
            console.error('tcId is an object! Converting to string:', tcId);
            tcId = String(tcId);
        }
        
        const apiService = new UnifiedApiService();
        
        // Get advanced configuration parameters
        const advancedParams = getAdvancedConfigParams();

        // Set up the parameters for the API call (without tc_id which needs to be passed separately)
        const params = {
            uid: appState.credentials.uid,
            password: appState.credentials.password,
            envir: environment,
            shell_host: shellHost,
            // Add RGS environment to match the selected environment
            rgs_env: environment,
            // Include all advanced configuration parameters
            ...advancedParams
        };

        // Log configuration overrides for debugging
        const overrideCount = Object.keys(advancedParams).length;
        if (overrideCount > 0) {
            console.log(`[Config] Using ${overrideCount} configuration overrides:`, advancedParams);
        }
        
        console.log('Running test case with ID:', tcId, 'and params:', params);
        
        // Call the API with tcId as first parameter and params as second parameter
        const response = await apiService.runTestCase(tcId, params);
        return response;
    } catch (error) {
        console.error('API error running test case:', error);
        throw error;
    }
}

// Start polling with performance optimizations if available
function startPolling() {
    console.log('Starting polling...');

    // Clear any existing interval (legacy)
    if (appState.pollInterval) {
        console.log('Clearing existing poll interval');
        clearInterval(appState.pollInterval);
        appState.pollInterval = null;
    }

    // Check if performance optimizations are available
    if (window.pollingCoordinator && typeof window.pollingCoordinator.subscribe === 'function') {
        startOptimizedPolling();
    } else if (window.simplePollingCoordinator && typeof window.simplePollingCoordinator.subscribe === 'function') {
        startSimpleOptimizedPolling();
    } else {
        console.log('No performance optimizations available, using legacy polling');
        startLegacyPolling();
    }
}

// Start optimized coordinated polling (full performance suite)
function startOptimizedPolling() {
    console.log('Starting optimized coordinated polling...');

    // Subscribe to recent runs updates (50 single test cases only)
    window.pollingCoordinator.subscribe('recentRuns', (recentRuns) => {
        console.log(`Received ${recentRuns.length} recent runs update`);
        appState.recentRunsCache = recentRuns;
        processRecentRunsData(recentRuns);
        renderRecentRuns(recentRuns);

        // Preload test details for visible runs if request manager is available
        if (window.requestManager && window.requestManager.preloadTestDetails) {
            window.requestManager.preloadTestDetails(recentRuns.slice(0, 10));
        }
    }, 'config-recent-runs');

    // Subscribe to active tests updates (fast polling for 15 sec to 5-6 min tests)
    window.pollingCoordinator.subscribe('activeTests', (activeTests) => {
        console.log(`Received ${activeTests.length} active tests update`);
        updateActiveTestsDisplay(activeTests);
    }, 'config-active-tests');

    console.log('✅ Subscribed to full coordinated polling system');
}

// Start simple optimized polling
function startSimpleOptimizedPolling() {
    console.log('Starting simple optimized polling...');

    // Subscribe to recent runs updates
    window.simplePollingCoordinator.subscribe('recentRuns', (recentRuns) => {
        console.log(`Received ${recentRuns.length} recent runs update`);
        appState.recentRunsCache = recentRuns;
        processRecentRunsData(recentRuns);
        renderRecentRuns(recentRuns);
    }, 'config-recent-runs');

    // Subscribe to active tests updates
    window.simplePollingCoordinator.subscribe('activeTests', (activeTests) => {
        console.log(`Received ${activeTests.length} active tests update`);
        updateActiveTestsDisplay(activeTests);
    }, 'config-active-tests');

    console.log('✅ Subscribed to simple coordinated polling system');
}

// Legacy polling fallback
function startLegacyPolling() {
    console.log('Starting legacy polling...');

    // Poll immediately
    pollRecentRuns();

    // Set up polling interval (5 seconds as original)
    appState.pollInterval = setInterval(pollRecentRuns, 5000);
    console.log('✅ Legacy polling started (5 second interval)');
}

// Legacy polling function (fallback)
async function pollRecentRuns() {
    try {
        console.log('pollRecentRuns: Starting poll...');

        // Use the global apiService instance
        if (!window.apiService) {
            console.warn('API service not available yet, skipping poll');
            return;
        }

        console.log('pollRecentRuns: Calling getRecentRuns...');
        const recentRuns = await window.apiService.getRecentRuns({
            limit: 50,
            type: 'single_case' // Filter for single test cases only, not test suites
        });
        console.log('pollRecentRuns: Received data:', recentRuns);

        // Update our cache
        appState.recentRunsCache = recentRuns;

        // Process the data
        processRecentRunsData(recentRuns);

        // Update UI
        renderRecentRuns(recentRuns);

        console.log('pollRecentRuns: Poll completed successfully');
    } catch (error) {
        console.error('Error polling recent runs:', error);
    }
}

// Update active tests display with detailed information
function updateActiveTestsDisplay(activeTests) {
    console.log(`Updating active tests display with ${activeTests.length} tests`);

    // Only update if we have active tests from database OR manually added tests
    if (activeTests.length === 0 && appState.activeTests.size === 0) {
        // No tests at all, render empty state
        renderActiveTests();
        return;
    }

    // Don't clear manually added tests - only update with database data
    // Preserve existing manually added tests and merge with database data
    activeTests.forEach(test => {
        const existingTest = appState.activeTests.get(test.tsn_id);
        if (existingTest) {
            // Update existing test with database information
            console.log(`Updating existing test ${test.tsn_id} with database data:`, test);
            existingTest.data = {
                ...existingTest.data,
                ...test,
                // Prioritize database fields over manual fields
                name: test.test_name || test.name || test.tc_name || existingTest.data.name,
                test_name: test.test_name || test.name || test.tc_name,
                comment: test.comment || test.comments || existingTest.data.comment,
                environment: test.environment || test.envir || existingTest.data.environment,
                status: test.status || existingTest.data.status
            };
            console.log(`Updated test data:`, existingTest.data);
        } else {
            // Add new test from database
            appState.activeTests.set(test.tsn_id, {
                data: test,
                startTime: Date.now(),
                completionTimestamp: null
            });
        }
    });

    // Always render active tests (even if database has 0 but we have manually added tests)
    renderActiveTests();

    // Test counters removed (Test Status Card no longer exists)
}



// Process recent runs data to update active tests
function processRecentRunsData(recentRuns) {
    console.log('processRecentRunsData called with', recentRuns.length, 'runs');

    // First, identify currently active tests from recent runs
    const currentlyActiveTests = recentRuns.filter(run => {
        const status = (run.status || '').toLowerCase();
        const hasEndTime = run.end_time || run.end_ts;
        // console.log(`Checking run ${run.tsn_id}: status="${status}", end_time="${run.end_time}", end_ts="${run.end_ts}", hasEndTime=${!!hasEndTime}`);
        return !hasEndTime && (status === 'running' || status === 'queued' || status === 'active' || status === '');
    });

    // Add any newly discovered active tests to our state
    currentlyActiveTests.forEach(run => {
        if (!appState.activeTests.has(run.tsn_id)) {
            appState.activeTests.set(run.tsn_id, {
                data: run,
                completionTimestamp: null
            });
        }
    });

   // console.log('Active tests before processing:', Array.from(appState.activeTests.keys()));

    // Check for completed tests
    appState.activeTests.forEach((activeTest, tsnId) => {
        // Find the test in recent runs
        const recentRunData = recentRuns.find(run => run.tsn_id === tsnId);
        console.log(`Processing active test ${tsnId}, found in recent runs:`, !!recentRunData);
        
        if (recentRunData) {
            // Update the active test data
            activeTest.data = { ...activeTest.data, ...recentRunData };
            
            // Check if test is completed - handle multiple status formats and field names
            const status = (recentRunData.status || recentRunData.outcome || '').toLowerCase();
            const hasEndTime = recentRunData.end_time || recentRunData.end_ts;

            const isCompleted = hasEndTime && (
                status === 'passed' || status === 'pass' || status === 'p' ||
                status === 'failed' || status === 'fail' || status === 'f' ||
                status === 'error' || status === 'completed' || status === 'success'
            );

            console.log(`Checking completion for TSN ${tsnId}: status="${status}", hasEndTime=${!!hasEndTime}, isCompleted=${isCompleted}`);

            if (isCompleted && !activeTest.completionTimestamp) {
                activeTest.completionTimestamp = Date.now();

                console.log(`Test ${tsnId} completed with status: ${status}`);

                // Timer will be stopped automatically by updateExistingTestCard when status changes

                // Show notification for completed test
                showTestCompletionNotification(recentRunData);
            }
        }
    });
    
    // Remove completed tests after grace period
    const now = Date.now();
    const testsToRemove = [];
    
    appState.activeTests.forEach((activeTest, tsnId) => {
        if (activeTest.completionTimestamp && 
            (now - activeTest.completionTimestamp) > COMPLETED_TEST_GRACE_PERIOD) {
            testsToRemove.push(tsnId);
        }
    });
    
    testsToRemove.forEach(tsnId => {
        appState.activeTests.delete(tsnId);
    });
    
    // Update active tests panel
    renderActiveTests();
}
// Add a test to the active tests panel
function addActiveTest(testData) {
    console.log('addActiveTest called with:', testData);

    // Add to our active tests Map with start time
    const startTime = Date.now();
    appState.activeTests.set(testData.tsn_id, {
        data: testData,
        startTime: startTime,
        completionTimestamp: null
    });

    console.log('Active tests after adding:', Array.from(appState.activeTests.keys()));

    // Update UI
    renderActiveTests();

    // Show visual feedback that test started
    showTestStartedNotification(testData);
}

// Render all active tests in the panel
function renderActiveTests() {
    console.log('🔄 renderActiveTests called, active tests count:', appState.activeTests.size);

    // Prevent concurrent rendering
    if (appState.renderingInProgress) {
        console.log('⏸️ Rendering already in progress, skipping...');
        return;
    }

    appState.renderingInProgress = true;

    // Update the active tests count
    if (elements.activeTestsCount) {
        elements.activeTestsCount.textContent = appState.activeTests.size;
    }

    // Find the active tests container
    const activeTestsContent = elements.activeTestsPanel?.querySelector('.ms-panel-content');
    if (!activeTestsContent) {
        console.warn('Active tests content container not found');
        return;
    }

    // If no active tests, show empty message
    if (appState.activeTests.size === 0) {
        activeTestsContent.innerHTML = '<div class="ms-empty-message">No active tests</div>';
        appState.renderingInProgress = false;
        return;
    }

    // Remove any existing "No active tests" message
    const emptyMessage = activeTestsContent.querySelector('.ms-empty-message');
    if (emptyMessage) {
        emptyMessage.remove();
    }

    // Smart update: Update existing cards or create new ones, don't destroy existing DOM
    const existingCards = new Map();

    // Collect existing cards and check for duplicates
    const allCards = activeTestsContent.querySelectorAll('[data-tsn-id]');
    console.log(`📋 Found ${allCards.length} existing cards in DOM`);

    allCards.forEach(card => {
        const tsnId = card.dataset.tsnId;
        if (existingCards.has(tsnId)) {
            // Remove duplicate card
            console.warn(`⚠️ Found duplicate card for test ${tsnId}, removing it`);
            card.remove();
        } else {
            existingCards.set(tsnId, card);
        }
    });

    console.log(`📋 After cleanup: ${existingCards.size} unique cards`);

    // Update or create cards for each active test
    appState.activeTests.forEach((activeTest) => {
        const testData = activeTest.data;
        const tsnId = testData.tsn_id;

        console.log('Processing active test:', tsnId);
        const existingCard = existingCards.get(tsnId);

        if (existingCard) {
            // Replace existing card completely with updated data
            console.log(`Replacing existing card for test ${tsnId}`);
            const newCard = createTestCard(testData);

            // Use insertBefore + remove to ensure proper replacement
            existingCard.parentNode.insertBefore(newCard, existingCard);
            existingCard.remove();

            existingCards.delete(tsnId); // Mark as processed
        } else {
            // Create new card
            console.log(`Creating new card for test ${tsnId}`);
            const card = createTestCard(testData);
            activeTestsContent.appendChild(card);
        }
    });

    // Remove cards for tests that are no longer active
    existingCards.forEach((card, tsnId) => {
        console.log(`Removing card for completed test ${tsnId}`);
        card.remove();
    });

    // Reset rendering flag
    appState.renderingInProgress = false;
    console.log('✅ renderActiveTests completed');
}

// Create an enhanced test card element
function createTestCard(testData) {
    const card = document.createElement('div');
    card.className = 'ms-card enhanced-active-test-card';
    card.dataset.tsnId = testData.tsn_id;

    // Determine status class and icon
    let statusClass = 'ms-status-running';
    let statusIcon = '⚡';
    if (testData.status === 'passed') {
        statusClass = 'ms-status-passed';
        statusIcon = '✅';
    }
    if (testData.status === 'failed') {
        statusClass = 'ms-status-failed';
        statusIcon = '❌';
    }
    if (testData.status === 'error') {
        statusClass = 'ms-status-error';
        statusIcon = '💥';
    }

    // Get test name/comment for display - prioritize actual test name from database
    const displayName = testData.test_name || testData.name || testData.tc_name || testData.comments || testData.comment || `Test Case ${testData.tc_id}`;
    const environment = testData.environment || testData.envir || 'N/A';

    card.innerHTML = `
        <div class="ms-card-header ${statusClass}">
            <div class="test-info-section">
                <div class="test-title">
                    <span class="status-icon">${statusIcon}</span>
                    <span class="test-name">${displayName}</span>
                </div>
                <div class="test-details">
                    <span class="test-id">ID: ${testData.tc_id}</span>
                    <span class="test-tsn">TSN: ${testData.tsn_id}</span>
                    <span class="test-env">Env: ${environment}</span>
                </div>
            </div>
            <div class="test-status-section">
                <div class="test-status-badge ${statusClass}">${testData.status || 'Running'}</div>
            </div>
        </div>

        <div class="ms-card-footer">
            <button class="ms-Button ms-Button--small ms-Button--danger stop-test-btn" data-tsn-id="${testData.tsn_id}">
                <span class="ms-Button-label">Stop Test</span>
            </button>
        </div>
    `;

    // Add event listener for Stop Test button
    const stopButton = card.querySelector('.stop-test-btn');

    stopButton.addEventListener('click', function() {
        stopTest(this.dataset.tsnId);
    });

    return card;
}









// Notification functions
function showTestStartedNotification(testData) {
    const testName = testData.name || testData.tc_name || testData.comments || testData.comment || `Test Case ${testData.tc_id}`;

    // Create a temporary notification
    const notification = document.createElement('div');
    notification.className = 'test-notification test-started';
    notification.innerHTML = `
        <div class="notification-content">
            <span class="notification-icon">🚀</span>
            <span class="notification-text">Started: ${testName}</span>
        </div>
    `;

    document.body.appendChild(notification);

    // Auto-remove after 3 seconds
    setTimeout(() => {
        notification.remove();
    }, 3000);
}

// Render recent runs in the table
function renderRecentRuns(recentRuns) {
    console.log('renderRecentRuns called with data:', recentRuns);
    console.log('recentRunsBody element:', elements.recentRunsBody);

    if (!elements.recentRunsBody) {
        console.error('recentRunsBody element not found!');
        return;
    }

    // Clear existing rows
    elements.recentRunsBody.innerHTML = '';

    if (!recentRuns || recentRuns.length === 0) {
        console.log('No recent runs to display');
        const row = document.createElement('tr');
        row.innerHTML = '<td colspan="4" style="text-align: center; color: #666;">No recent runs found</td>';
        elements.recentRunsBody.appendChild(row);
        return;
    }

    console.log(`Rendering ${recentRuns.length} recent runs`);

    // Add each run as a row
    recentRuns.forEach(run => {
        const row = document.createElement('tr');
        
        // Format timestamp
        const timestamp = run.start_time ? new Date(run.start_time).toLocaleString() : 'N/A';
        
        // Determine status class for colored text
        let statusClass = '';
        const status = (run.status || 'Running').toLowerCase();

        if (status === 'passed') statusClass = 'status-passed';
        else if (status === 'failed') statusClass = 'status-failed';
        else if (status === 'error') statusClass = 'status-error';
        else if (status === 'timeout') statusClass = 'status-timeout';
        else if (status === 'cancelled') statusClass = 'status-cancelled';
        else if (status === 'pending') statusClass = 'status-pending';
        else if (status === 'running') statusClass = 'status-running';
        else statusClass = 'status-running'; // default for unknown statuses

        row.innerHTML = `
            <td style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">${run.tc_id}</td>
            <td class="${statusClass}" style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">${run.status || 'Running'}</td>
            <td style="word-wrap: break-word; overflow: hidden; max-width: 200px;">
                <div style="font-size: 0.9em;">
                    <div><strong>TSN:</strong> ${run.tsn_id}</div>
                    <div><strong>Time:</strong> ${timestamp}</div>
                </div>
            </td>
            <td style="overflow: hidden;">
                <button class="ms-Button ms-Button--primary view-details-btn"
                        data-tsn-id="${run.tsn_id || run.id}"
                        title="View detailed information about this test run">
                    <span class="ms-Button-label">View Details</span>
                </button>
            </td>
        `;
        
        // Add event listener for View Details button
        const viewDetailsButton = row.querySelector('.view-details-btn');
        viewDetailsButton.addEventListener('click', function() {
            viewTestDetails(this.dataset.tsnId);
        });
        
        elements.recentRunsBody.appendChild(row);
    });

    // After all rows are added to the table, attach event listeners
    attachViewDetailsListeners();
}

// Stop a running test
async function stopTest(tsnId) {
    try {
        const apiService = new UnifiedApiService();
        await apiService.stopTest(tsnId);
        showSuccess(`Stopping test ${tsnId}...`);
    } catch (error) {
        console.error('Error stopping test:', error);
        showError(`Failed to stop test: ${error.message || 'Unknown error'}`);
    }
}

// View test details using the modal system
function viewTestDetails(tsnId) {
    console.log('viewTestDetails called for TSN:', tsnId);

    // Initialize modal if not already done
    if (!window.testDetailsModal) {
        if (typeof TestDetailsModal !== 'undefined') {
            window.testDetailsModal = new TestDetailsModal();
            console.log('TestDetailsModal initialized');
        } else {
            console.error('TestDetailsModal class not available');
            showError('Test details modal not available');
            return;
        }
    }

    // Show the modal with the test details
    try {
        window.testDetailsModal.show(tsnId);
    } catch (error) {
        console.error('Error showing test details modal:', error);
        showError('Error loading test details: ' + error.message);
    }
}







// Show test completion notification
function showTestCompletionNotification(testData) {
    const status = testData.status || 'completed';
    const passedCount = testData.passed_cases || 0;
    const failedCount = testData.failed_cases || 0;
    
    const message = `Test ${testData.tc_id} ${status}. ` + 
                   `Cases Passed: ${passedCount}, Cases Failed: ${failedCount}`;
    
    if (status === 'passed') {
        showSuccess(message);
    } else {
        showError(message);
    }
}

// Show success message
function showSuccess(message) {
    const toast = document.createElement('div');
    toast.className = 'ms-toast ms-toast-success';
    toast.innerHTML = `
        <div class="ms-toast-icon">✓</div>
        <div class="ms-toast-message">${message}</div>
    `;
    
    document.body.appendChild(toast);
    
    // Show the toast
    setTimeout(() => {
        toast.classList.add('show');
    }, 10);
    
    // Hide and remove the toast after 3 seconds
    setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 300);
    }, 3000);
}

// Show error message
function showError(message) {
    const toast = document.createElement('div');
    toast.className = 'ms-toast ms-toast-error';
    toast.innerHTML = `
        <div class="ms-toast-icon">!</div>
        <div class="ms-toast-message">${message}</div>
    `;
    
    document.body.appendChild(toast);
    
    // Show the toast
    setTimeout(() => {
        toast.classList.add('show');
    }, 10);
    
    // Hide and remove the toast after 3 seconds
    setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 300);
    }, 3000);
}

// Add this function to handle View Details button clicks
function attachViewDetailsListeners() {
    const viewDetailsButtons = document.querySelectorAll('.view-details-btn');
    viewDetailsButtons.forEach(button => {
        // Remove existing listeners to prevent duplicates
        button.removeEventListener('click', handleViewDetailsClick);
        button.addEventListener('click', handleViewDetailsClick);
    });
}

function handleViewDetailsClick(event) {
    const tsnId = event.target.getAttribute('data-tsn-id') || 
                  event.target.closest('button').getAttribute('data-tsn-id');
    
    console.log('View Details clicked for TSN ID:', tsnId);
    
    if (!tsnId) {
        console.error('No TSN ID found for View Details button');
        showError('Unable to load test details: No test ID found');
        return;
    }

    if (window.testDetailsModal) {
        window.testDetailsModal.show(tsnId);
    } else {
        console.error('Test details modal not available');
        showError('Test details functionality not available');
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', initCustomTestRunner);


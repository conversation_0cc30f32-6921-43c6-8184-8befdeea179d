# Semantic Versioning Guide for SmartTest

This document explains our semantic versioning strategy, conventions, and best practices for the SmartTest application.

## 📚 Semantic Versioning Basics

SmartTest follows **Semantic Versioning (SemVer)** specification: `MAJOR.MINOR.PATCH`

### **Version Format: X.Y.Z**
- **X (MAJOR)**: Breaking changes that require user action
- **Y (MINOR)**: New features that are backward compatible  
- **Z (PATCH)**: Bug fixes and security patches

### **Current Version State**
- **Active Version**: 1.2.0
- **Git Tag**: v1.2.0-ui-improvements
- **Next Planned**: 1.3.0 (minor feature release)

## 🎯 SmartTest-Specific Versioning Rules

### **MAJOR Version (X.0.0)**
**When to increment**: Breaking changes that require user intervention

**Examples for SmartTest**:
- **API Changes**: Removing or changing existing API endpoints
- **Database Schema**: Breaking changes to MySQL schema
- **Authentication**: Changes to login/session management that break existing sessions
- **Configuration**: Changes to environment variables or config files that require manual updates
- **UI Overhaul**: Complete redesign that changes user workflows

**Recent Example**: 
- `1.x.x → 2.0.0` would be needed if we completely redesigned the dashboard UI or changed the external API integration

### **MINOR Version (X.Y.0)**
**When to increment**: New features that don't break existing functionality

**Examples for SmartTest**:
- **New Features**: Adding test suite selection, new dashboard widgets
- **New APIs**: Adding new backend endpoints without changing existing ones
- **Enhanced UI**: New pages, improved user experience features
- **Integration**: New external service integrations
- **Performance**: Significant performance improvements

**Recent Examples**:
- `1.2.0 → 1.3.0`: Adding multi-select test suite functionality
- `1.3.0 → 1.4.0`: Implementing automated test retries

### **PATCH Version (X.Y.Z)**
**When to increment**: Bug fixes and security patches

**Examples for SmartTest**:
- **Bug Fixes**: Resolving login issues, fixing report generation
- **Security**: Patching vulnerabilities, updating dependencies
- **Performance**: Minor optimizations without feature changes
- **Documentation**: Fixing typos, updating guides
- **Dependencies**: Updating npm packages for security

**Recent Examples**:
- `1.2.0 → 1.2.1`: Fixed session timeout issues
- `1.2.1 → 1.2.2`: Updated node-fetch for security compliance

## 🤖 Automated Version Detection

Our release workflow automatically determines version bumps based on conventional commit messages:

### **Commit Message → Version Impact**
```bash
# PATCH releases (1.2.0 → 1.2.1)
fix(auth): resolve session timeout issues
fix(reports): correct timezone display in test results
chore(deps): update axios to latest version
security(api): patch authentication vulnerability

# MINOR releases (1.2.0 → 1.3.0)  
feat(dashboard): add multi-select test suite functionality
feat(api): implement automated test retry logic
perf(database): optimize query performance significantly

# MAJOR releases (1.2.0 → 2.0.0)
feat(api): redesign authentication system
BREAKING CHANGE: remove legacy API endpoints
feat(database): migrate to new schema format
```

### **Multiple Commits in One Release**
When multiple commits are included, the **highest impact** determines the version:

```bash
# These commits together result in MINOR release (1.2.0 → 1.3.0)
fix(ui): resolve button styling issue          # Would be PATCH
chore(deps): update development dependencies   # Would be PATCH  
feat(dashboard): add export functionality     # MINOR - this wins
```

## 📋 Version Planning & Roadmap

### **Current Development Cycle**
Based on our feature branches and development velocity:

**v1.3.0 (Next Minor)** - *Estimated: Next 2 weeks*
- Multi-select test suite functionality (from feature/select-suite)
- Dashboard performance optimizations
- Enhanced error handling and logging

**v1.4.0 (Following Minor)** - *Estimated: 4-6 weeks*
- Advanced reporting features
- API improvements from architecture-quick-wins
- User management enhancements

**v2.0.0 (Next Major)** - *Estimated: 3-6 months*
- Complete API redesign
- Database schema migrations
- Modern authentication system

### **Release Cadence**
- **Patch Releases**: As needed (hotfixes, security updates)
- **Minor Releases**: Every 2-3 weeks
- **Major Releases**: Every 6-12 months

## 🏷️ Version Tagging Strategy

### **Git Tag Format**
```bash
# Standard release tags
v1.2.0          # Production release
v1.3.0-rc.1     # Release candidate
v1.3.0-beta.1   # Beta release
v1.3.0-alpha.1  # Alpha release (internal testing)
```

### **Pre-release Versions**
For testing and staging environments:

**Alpha** (Internal Testing):
- `v1.3.0-alpha.1`, `v1.3.0-alpha.2`, etc.
- Deployed to development environment only
- Breaking changes expected

**Beta** (Stakeholder Testing):
- `v1.3.0-beta.1`, `v1.3.0-beta.2`, etc.
- Deployed to staging environment
- Feature-complete but may have bugs

**Release Candidate** (Pre-production):
- `v1.3.0-rc.1`, `v1.3.0-rc.2`, etc.
- Deployed to staging environment
- Production-ready candidate

## 🔄 Manual Version Management

### **NPM Scripts Available**
```bash
# Version bumping (updates package.json only)
npm run version:patch   # 1.2.0 → 1.2.1
npm run version:minor   # 1.2.0 → 1.3.0
npm run version:major   # 1.2.0 → 2.0.0

# Full release process (version + build + commit)
npm run release:patch   # Complete patch release
npm run release:minor   # Complete minor release  
npm run release:major   # Complete major release
```

### **Emergency Hotfix Process**
For critical issues requiring immediate release:

```bash
# 1. Create hotfix branch from main
git checkout main
git pull origin main
git checkout -b hotfix/security-patch-1.2.1

# 2. Make fix with proper commit message
git commit -m "fix(security): patch critical authentication vulnerability"

# 3. Manual version bump if needed
npm run version:patch

# 4. Create PR and merge
gh pr create --title "🚨 Security Hotfix v1.2.1"

# 5. After merge, tag and release automatically trigger
```

## 📊 Version History Analysis

### **Version Metrics Tracking**
We track these metrics to optimize our versioning strategy:

**Release Frequency**:
- **Patch releases**: ~2-3 per month
- **Minor releases**: ~1-2 per month  
- **Major releases**: ~1-2 per year

**Release Size** (lines of code changed):
- **Patch**: 50-200 lines
- **Minor**: 200-1000 lines
- **Major**: 1000+ lines

### **Historical Patterns**
```bash
# Recent version history
v1.2.0 - UI improvements and dashboard enhancements
v1.1.x - Test suite management features (estimated)
v1.0.0 - Initial SmartTest MVP (estimated)
```

## 🎯 Version Communication

### **Internal Communication**
**Team Notifications**:
- **Slack/Discord**: Automated release notifications
- **Email**: Weekly version planning updates
- **Standups**: Version status and planning discussions

**Documentation Updates**:
- **CHANGELOG.md**: Automatically updated with each release
- **README.md**: Version badge and compatibility information
- **API Documentation**: Version-specific endpoint documentation

### **External Communication**
**Stakeholder Updates**:
- **Release Notes**: Professional GitHub releases with feature descriptions
- **Migration Guides**: For major versions requiring user action
- **Compatibility Matrix**: Supported environments and dependencies

## 🚨 Version Rollback Procedures

### **Rollback Scenarios**
**When to rollback**:
- Critical bugs discovered in production
- Security vulnerabilities introduced
- Performance degradation
- User-reported breaking changes

### **Rollback Process**
```bash
# 1. Identify stable version to rollback to
git tag --list | grep "v1.2"
# v1.2.0, v1.2.1, v1.2.2

# 2. Create rollback release
git checkout v1.2.1  # Last known good version
git checkout -b rollback/revert-to-v1.2.1

# 3. Update version and create release
npm run version:patch  # This becomes v1.2.3
git commit -m "chore: rollback to v1.2.1 due to critical issue"

# 4. Deploy through normal release process
```

## 📚 Best Practices

### **Do's**
✅ **Use conventional commits** for automatic version detection
✅ **Test thoroughly** before minor/major releases
✅ **Document breaking changes** clearly in commit messages
✅ **Plan major releases** with stakeholder input
✅ **Monitor release metrics** to optimize cadence

### **Don'ts**
❌ **Don't skip versions** (e.g., 1.2.0 → 1.4.0)
❌ **Don't use vanity versions** (e.g., 2.0.0 for minor changes)
❌ **Don't release major versions** without migration guides
❌ **Don't backport features** to patch releases
❌ **Don't release on Fridays** (unless critical hotfix)

## 📖 Related Documentation

- **[Automated Releases](../workflows/automated-releases.md)**: How our release automation works
- **[Conventional Commits](../references/conventional-commits.md)**: Commit message format guide
- **[Changelog Management](changelog-management.md)**: Changelog best practices
- **[Release Procedures](release-procedures.md)**: Manual release processes

---

**Remember**: Semantic versioning is a contract with our users. Be conservative with major versions and clear about the impact of each release.

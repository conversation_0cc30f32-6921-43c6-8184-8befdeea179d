$NodePath = "C:\nodejs"
$env:Path = "$NodePath;$env:Path"
$env:NODE_PATH = "$NodePath\node_modules"
$env:NPM_CONFIG_PREFIX = $NodePath
$env:NPM_CONFIG_CACHE = "$NodePath\npm-cache"

Write-Host "Node.js environment variables set up successfully"
Write-Host "Node path: $NodePath"
Write-Host "Checking Node.js installation..."

if (Test-Path "$NodePath\node.exe") {
    Write-Host "Node version:" -NoNewline
    & "$NodePath\node.exe" -v
    
    if (Test-Path "$NodePath\npm.cmd") {
        Write-Host "NPM version:" -NoNewline
        & "$NodePath\npm.cmd" -v
    } else {
        Write-Host "npm.cmd not found in $NodePath"
    }
} else {
    Write-Host "node.exe not found in $NodePath"
    Write-Host "Please ensure Node.js is extracted to $NodePath"
} 
/**
 * Direct Database Connection Test Script
 * 
 * This script tests direct database connection without going through the application's 
 * connection layer, to help identify database connectivity issues.
 * 
 * It uses similar connection parameters to the PowerShell db_connection.ps1 script.
 */

const mysql = require('mysql2/promise');
const { promisify } = require('util');
const fs = require('fs');
const readFileAsync = promisify(fs.readFile);
const path = require('path');

// Default connection parameters - these should match your actual configuration
const connectionConfig = {
  host: 'localhost',  // When using SSH tunnel, this is usually localhost
  port: 3306,         // Default MySQL port
  user: 'rgs_test',   // Database username
  password: 'test',   // Database password (should be loaded from .env in production)
  database: 'rgs_test', // Match schema prefix from PowerShell script
  connectTimeout: 10000,
  namedPlaceholders: true
};

/**
 * Load the SSH tunnel credentials from .env file
 * This is for testing SSH tunnel mode, if your database uses it
 */
async function loadSshConfig() {
  try {
    // Load .env file content to get SSH parameters
    const envPath = path.join(__dirname, '..', '.env');
    const envFile = await readFileAsync(envPath, 'utf8');
    
    // Extract relevant environment variables
    const sshConfig = {
      host: extractEnvVar(envFile, 'SSH_HOST'),
      port: parseInt(extractEnvVar(envFile, 'SSH_PORT') || '22'),
      username: extractEnvVar(envFile, 'SSH_USER'),
      privateKey: extractEnvVar(envFile, 'SSH_KEY_PATH')
        ? await readFileAsync(extractEnvVar(envFile, 'SSH_KEY_PATH'))
        : undefined,
      password: extractEnvVar(envFile, 'SSH_PASSWORD')
    };
    
    console.log('[INFO] SSH configuration loaded:', {
      host: sshConfig.host,
      port: sshConfig.port,
      username: sshConfig.username,
      hasKey: !!sshConfig.privateKey,
      hasPassword: !!sshConfig.password
    });
    
    return sshConfig;
  } catch (error) {
    console.error('[ERROR] Failed to load SSH config:', error.message);
    return null;
  }
}

/**
 * Extract a variable from .env file content
 */
function extractEnvVar(envContent, varName) {
  const match = envContent.match(new RegExp(`${varName}=(.*)`, 'i'));
  return match ? match[1].trim() : undefined;
}

/**
 * Test direct database connection
 */
async function testDirectConnection() {
  let connection;
  try {
    console.log('[INFO] Attempting direct database connection with config:', {
      host: connectionConfig.host,
      port: connectionConfig.port,
      user: connectionConfig.user,
      database: connectionConfig.database,
      connectTimeout: connectionConfig.connectTimeout
    });
    
    connection = await mysql.createConnection(connectionConfig);
    
    console.log('[SUCCESS] Connection established!');
    console.log('[INFO] Connection ID:', connection.connection.threadId);
    
    // Test query 1: Count records (similar to PowerShell script's first query)
    console.log('\n[TEST 1] Counting records in test_result table');
    const [countRows] = await connection.query('SELECT COUNT(*) as count FROM test_result');
    console.log('[RESULT]', countRows);
    
    // Test query 2: Get a specific test session
    const testSessionId = 14749; // The same ID we're testing in the application
    console.log(`\n[TEST 2] Retrieving test session details for ID: ${testSessionId}`);
    const [sessionRows] = await connection.query(
      'SELECT tsn_id, tc_id, ts_id, pj_id, uid, start_ts, end_ts, error, report FROM test_session WHERE tsn_id = ?', 
      [testSessionId]
    );
    console.log('[RESULT] Session data:', sessionRows.length > 0 ? 'Found' : 'Not found');
    if (sessionRows.length > 0) {
      console.log(sessionRows[0]);
    }
    
    // Test query 3: Get test results for this session
    console.log(`\n[TEST 3] Retrieving test results for session ID: ${testSessionId}`);
    const [testRows] = await connection.query(
      `SELECT 
        tc_id, seq_index, outcome, creation_time, cnt
       FROM test_result 
       WHERE tsn_id = ?
       ORDER BY seq_index`,
      [testSessionId]
    );
    console.log('[RESULT] Test results:', testRows.length > 0 ? `Found ${testRows.length} rows` : 'Not found');
    if (testRows.length > 0) {
      console.log('First result:', testRows[0]);
    }
    
    // Test query 4: Test with schema prefix like PowerShell script
    console.log('\n[TEST 4] Testing with explicit schema prefix');
    const [schemaRows] = await connection.query('SELECT COUNT(*) as count FROM rgs_test.test_result');
    console.log('[RESULT]', schemaRows);
    
  } catch (error) {
    console.error('[ERROR] Connection error:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n[INFO] Connection closed');
    }
  }
}

/**
 * Main execution function
 */
async function main() {
  console.log('=== DIRECT DATABASE CONNECTION TEST ===');
  
  // Check if SSH configuration is available
  const sshConfig = await loadSshConfig();
  
  if (sshConfig && sshConfig.host) {
    console.log('[INFO] SSH configuration found. Note: This script currently only tests direct connection.');
    console.log('[INFO] If you need to test SSH tunnel connection, you would need to add SSH tunnel logic here.');
  }
  
  // Test direct connection
  await testDirectConnection();
  
  console.log('\n=== TEST COMPLETED ===');
}

// Run the main function
main().catch(err => {
  console.error('Fatal error:', err);
});

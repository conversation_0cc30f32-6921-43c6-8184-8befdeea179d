/**
 * <PERSON><PERSON><PERSON> to fetch test report data with authentication
 * This script will:
 * 1. <PERSON><PERSON> to the external system to get session cookies
 * 2. Fetch the test report data using the session cookies
 */

const fetch = require('node-fetch');
const { CookieJar } = require('tough-cookie');
const fetchCookie = require('fetch-cookie');
const fs = require('fs');

// Test session ID to fetch
const TEST_SESSION_ID = process.argv[2] || '13732';

// Authentication credentials
const credentials = {
  uid: '<EMAIL>',
  password: 'test',
  utc_off: '0'
};

// Base URL for the external system
const baseUrl = 'http://mprts-qa02.lab.wagerworks.com:5080/AutoRun';

// Create a cookie jar to store session cookies
const cookieJar = new CookieJar();
const fetchWithCookies = fetchCookie(fetch, cookieJar);

/**
 * Login to get session cookies
 */
async function login() {
  console.log('Logging in...');
  
  // Encode form data
  const formData = new URLSearchParams();
  Object.entries(credentials).forEach(([key, value]) => {
    formData.append(key, value);
  });
  
  try {
    // Make login request
    const response = await fetchWithCookies(`${baseUrl}/Login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      body: formData,
      redirect: 'manual' // Don't follow redirects
    });
    
    console.log(`Login status: ${response.status}`);
    console.log(`Login response status text: ${response.statusText}`);
    
    // Check if login was successful (302 redirect)
    if (response.status === 302) {
      console.log('Login successful!');
      return true;
    } else {
      console.error('Login failed!');
      const responseText = await response.text();
      console.error('Response:', responseText.substring(0, 200) + '...');
      return false;
    }
  } catch (error) {
    console.error('Error logging in:', error);
    return false;
  }
}

/**
 * Fetch test report data
 */
async function fetchTestReport(tsnId) {
  console.log(`Fetching test report for session ${tsnId}...`);
  
  try {
    // Make request to ReportSummary endpoint
    const response = await fetchWithCookies(`${baseUrl}/ReportSummary?tsn_id=${tsnId}`, {
      method: 'GET'
    });
    
    console.log(`Report fetch status: ${response.status}`);
    
    if (response.ok) {
      const html = await response.text();
      console.log('Report HTML snippet:');
      console.log(html.substring(0, 500) + '...');
      
      // Save full HTML to file
      fs.writeFileSync('report_sample.html', html);
      console.log('Full report saved to report_sample.html');
      
      return html;
    } else {
      console.error('Failed to fetch report!');
      return null;
    }
  } catch (error) {
    console.error('Error fetching report:', error);
    return null;
  }
}

/**
 * Main execution function
 */
async function main() {
  try {
    // Login first
    const loginSuccess = await login();
    if (!loginSuccess) {
      console.error('Exiting due to login failure');
      return;
    }
    
    // Fetch test report
    const reportHtml = await fetchTestReport(TEST_SESSION_ID);
    if (!reportHtml) {
      console.error('Exiting due to report fetch failure');
      return;
    }
    
    console.log('Process completed successfully!');
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

// Run the script
main().catch(console.error);

/**
 * Simple Integration Test for Test Details API
 *
 * Tests:
 * 1. Database query with proper null handling
 * 2. API endpoint formatting
 */

const db = require('../../../frontend/server/database');

// Test configuration
const TEST_SESSION_IDS = {
  valid: 14749,  
  missingData: 14846  // The problematic session ID that was causing errors
};

describe('Test Details Integration Tests', () => {
  // Direct test to verify our database query fix
  test('db.getTestSessionDetails should handle null/undefined values', async () => {
    try {
      // Call the database function directly with the problem test session ID
      const result = await db.getTestSessionDetails(TEST_SESSION_IDS.missingData);
      
      console.log('Test session details result:', result ? 'Result returned' : 'Null returned');
      
      // If the function returns null that's valid, but it shouldn't throw an error
      if (result) {
        expect(result).toHaveProperty('tsn_id');
        // These were the previously problematic fields
        expect(result).toHaveProperty('test_name'); // Should exist even if empty string
      }
      
      // The main thing we're testing is that the function doesn't throw the TypeError
      // that was happening before our fixes
    } catch (error) {
      console.error('Error in test:', error);
      // This should NOT fail with the previous TypeError after our fixes
      expect(error.message).not.toContain('Cannot read properties of undefined');
      throw error;
    }
  });
  
  // Test with a valid session ID to verify normal functionality
  test('db.getTestSessionDetails should return properly formatted data for valid session', async () => {
    try {
      // Call with a known good session ID
      const result = await db.getTestSessionDetails(TEST_SESSION_IDS.valid);
      
      // Basic validation of returned data structure
      expect(result).not.toBeNull();
      expect(result).toHaveProperty('tsn_id', TEST_SESSION_IDS.valid);
      expect(result).toHaveProperty('test_name');
      expect(result).toHaveProperty('status');
      expect(result).toHaveProperty('start_time');
      
      // If test cases are present, check their structure
      if (result.test_cases && result.test_cases.length > 0) {
        const testCase = result.test_cases[0];
        expect(testCase).toHaveProperty('tc_id');
        expect(testCase).toHaveProperty('test_case_name');
        expect(testCase).toHaveProperty('outcome');
      }
      
      console.log(`Successfully retrieved details for session ${TEST_SESSION_IDS.valid}`);
    } catch (error) {
      console.error('Error testing valid session:', error);
      throw error;
    }
  });
});

# Reports Page Services

**⚠️ MIGRATION NOTICE: This directory has been migrated to use unified services.**

The services previously in this directory have been migrated to the shared services architecture:

## Migrated Services

### External API Service → Enhanced External API Service

**Old Location:** `frontend/reports/services/external-api-service.js` ❌ (Removed)
**New Location:** `frontend/shared/services/external-api-service.js` ✅

The external API service is now available as `window.enhancedExternalApiService` and provides the same functionality with improved architecture.

**Updated Usage:**
```javascript
// Get report summary
const reportSummary = await window.enhancedExternalApiService.getReportSummary(
    tsnId,
    uid,
    password
);

// Get report details
const reportDetails = await window.enhancedExternalApiService.getReportDetails(
    tsnId,
    1, // Page number
    uid,
    password
);

// Get multiple reports
const reports = await window.enhancedExternalApiService.getRecentTestRuns(
    sessionIds,
    uid,
    password,
    limit
);
```

### Session ID Service → Integrated into Enhanced External API Service

**Old Location:** `frontend/reports/services/session-id-service.js` ❌ (Removed)
**New Location:** Integrated into `frontend/shared/services/external-api-service.js` ✅

Session ID functionality is now part of the enhanced external API service.

**Updated Usage:**
```javascript
// Get recent session IDs (now part of enhanced external API service)
const sessionIds = await window.enhancedExternalApiService.getRecentSessionIds(
    credentials,
    limit
);
```

## Architecture

These services implement a hybrid data access approach that combines direct external API integration with database access. This provides several benefits:

1. **Performance**: Direct API calls are faster than going through the database layer
2. **Reliability**: Reports page works even if the database is unavailable
3. **Flexibility**: Complex analytics can still use the database when needed

For more details on the hybrid approach, see the [Hybrid Data Access documentation](../../server/documentation/Integration/hybrid-data-access.md).

## Development

### Adding a New Service

To add a new service:

1. Create a new JavaScript file in this directory
2. Implement the service as a class
3. Create a global instance at the end of the file
4. Add a script tag to `index.html`

### Testing

Services can be tested in the browser console:

```javascript
// Test external API service
const testReport = await window.externalApiService.getReportSummary(
    '13782',
    '<EMAIL>',
    'test'
);
console.log(testReport);

// Test session ID service
const sessionIds = await window.sessionIdService.getRecentSessionIds(
    { uid: '<EMAIL>', password: 'test' },
    5
);
console.log(sessionIds);
```

/**
 * Security Integration Tests
 * Test rate limiting, CSRF protection, session management, and other security features
 */

const request = require('supertest');
const express = require('express');
const cookieParser = require('cookie-parser');

// Import security modules
const { 
  securityHeaders, 
  rateLimiters, 
  securityLogger, 
  sanitizeInput,
  csrfProtection 
} = require('../middleware/security');
const { 
  validateJWTToken, 
  requirePermissions, 
  trackSessionActivity,
  autoRefreshToken 
} = require('../middleware/session-validation');
const authRoutes = require('../routes/auth');
const securityTestRoutes = require('../routes/security-test');

// Create test app with security features
const createSecureTestApp = () => {
  const app = express();
  
  // Basic middleware
  app.use(express.json({ limit: '1mb' }));
  app.use(cookieParser());
  
  // Security middleware
  app.use(securityHeaders);
  app.use(securityLogger);
  app.use(sanitizeInput);
  
  // Rate limiting
  app.use('/auth', rateLimiters.auth);
  app.use('/api', rateLimiters.api);
  
  // CSRF protection (exempt auth routes)
  app.use(csrfProtection.middleware(['/auth', '/csrf-token', '/security-test']));
  
  // Routes
  app.use('/auth', authRoutes);
  app.use('/security-test', securityTestRoutes);
  
  // CSRF token endpoint
  app.get('/csrf-token', (req, res) => {
    csrfProtection.getToken(req, res);
  });
  
  // Protected test endpoints
  app.post('/protected/csrf-test', validateJWTToken, (req, res) => {
    res.json({ success: true, message: 'CSRF protection passed' });
  });
  
  app.get('/protected/session-test', 
    validateJWTToken, 
    trackSessionActivity, 
    autoRefreshToken, 
    (req, res) => {
      res.json({ 
        success: true, 
        user: req.user,
        sessionActivity: req.session?.lastActivity 
      });
    }
  );
  
  return app;
};

describe('Security Integration Tests', () => {
  let app;
  
  beforeEach(() => {
    app = createSecureTestApp();
  });

  describe('Security Headers Integration', () => {
    test('should apply all security headers to responses', async () => {
      const response = await request(app)
        .get('/security-test/headers');

      // Check that security headers are present
      expect(response.headers['x-frame-options']).toBe('DENY');
      expect(response.headers['x-content-type-options']).toBe('nosniff');
      expect(response.headers['x-xss-protection']).toBe('1; mode=block');
      expect(response.headers['content-security-policy']).toBeDefined();
      expect(response.headers['referrer-policy']).toBe('strict-origin-when-cross-origin');
      expect(response.headers['permissions-policy']).toBeDefined();
      expect(response.headers['x-smarttest-security']).toBe('enabled');
      
      // Check that server info is hidden
      expect(response.headers['x-powered-by']).toBeUndefined();
      expect(response.headers['server']).toBeUndefined();
    });

    test('should include CORS headers appropriately', async () => {
      const response = await request(app)
        .get('/security-test/headers')
        .set('Origin', 'https://example.com');

      expect(response.headers['cross-origin-embedder-policy']).toBe('require-corp');
      expect(response.headers['cross-origin-opener-policy']).toBe('same-origin');
      expect(response.headers['cross-origin-resource-policy']).toBe('same-origin');
    });
  });

  describe('Rate Limiting Integration', () => {
    test('should enforce rate limits on auth endpoints', async () => {
      const promises = [];
      
      // Make multiple rapid requests to exceed rate limit
      for (let i = 0; i < 10; i++) {
        promises.push(
          request(app)
            .post('/auth/login')
            .send({
              uid: '<EMAIL>',
              password: 'wrongpassword'
            })
        );
      }
      
      const responses = await Promise.all(promises);
      
      // Some requests should be rate limited
      const rateLimitedResponses = responses.filter(res => res.status === 429);
      expect(rateLimitedResponses.length).toBeGreaterThan(0);
      
      // Rate limited responses should have proper headers
      const rateLimited = rateLimitedResponses[0];
      expect(rateLimited.body.code).toBe('AUTH_RATE_LIMITED');
      expect(rateLimited.headers['x-ratelimit-limit']).toBeDefined();
      expect(rateLimited.headers['x-ratelimit-remaining']).toBeDefined();
    });

    test('should enforce rate limits on API endpoints', async () => {
      const promises = [];
      
      // Make many rapid requests to API endpoint
      for (let i = 0; i < 150; i++) {
        promises.push(
          request(app)
            .get('/security-test/rate-limit')
        );
      }
      
      const responses = await Promise.all(promises);
      
      // Some requests should be rate limited
      const rateLimitedResponses = responses.filter(res => res.status === 429);
      expect(rateLimitedResponses.length).toBeGreaterThan(0);
    });
  });

  describe('CSRF Protection Integration', () => {
    test('should require CSRF token for state-changing operations', async () => {
      const response = await request(app)
        .post('/protected/csrf-test')
        .send({ data: 'test' });

      expect(response.status).toBe(403);
      expect(response.body.code).toBe('CSRF_INVALID');
    });

    test('should accept requests with valid CSRF token', async () => {
      // First get a CSRF token
      const tokenResponse = await request(app)
        .get('/csrf-token');

      expect(tokenResponse.status).toBe(200);
      expect(tokenResponse.body.csrfToken).toBeDefined();

      // Use the token in a protected request
      const response = await request(app)
        .post('/protected/csrf-test')
        .set('X-CSRF-Token', tokenResponse.body.csrfToken)
        .send({ data: 'test' });

      // This would still fail due to missing JWT token, but CSRF should pass
      expect(response.status).toBe(401); // JWT validation failure
      expect(response.body.code).toBe('ACCESS_TOKEN_MISSING'); // Not CSRF_INVALID
    });

    test('should reject requests with invalid CSRF token', async () => {
      const response = await request(app)
        .post('/protected/csrf-test')
        .set('X-CSRF-Token', 'invalid-token')
        .send({ data: 'test' });

      expect(response.status).toBe(403);
      expect(response.body.code).toBe('CSRF_INVALID');
    });
  });

  describe('Input Sanitization Integration', () => {
    test('should sanitize malicious input', async () => {
      const maliciousData = {
        uid: '<script>alert("xss")</script>@example.com',
        password: 'password<script>',
        name: 'Test User</script><script>alert("xss")</script>'
      };

      const response = await request(app)
        .post('/auth/login')
        .send(maliciousData);

      // Should be rejected due to validation, not cause XSS
      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });

    test('should handle SQL injection attempts', async () => {
      const sqlInjectionData = {
        uid: "<EMAIL>'; DROP TABLE users; --",
        password: "' OR '1'='1"
      };

      const response = await request(app)
        .post('/auth/login')
        .send(sqlInjectionData);

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });

    test('should reject oversized payloads', async () => {
      const oversizedData = {
        uid: 'a'.repeat(10000) + '@example.com',
        password: 'b'.repeat(10000)
      };

      const response = await request(app)
        .post('/auth/login')
        .send(oversizedData);

      expect(response.status).toBe(400);
    });
  });

  describe('Session Management Integration', () => {
    test('should track session activity', async () => {
      // This test would require a valid JWT token
      const response = await request(app)
        .get('/protected/session-test');

      expect(response.status).toBe(401);
      expect(response.body.code).toBe('ACCESS_TOKEN_MISSING');
    });

    test('should handle session validation', async () => {
      const response = await request(app)
        .get('/auth/validate');

      expect(response.status).toBe(401);
      expect(response.body.code).toBe('ACCESS_TOKEN_MISSING');
    });

    test('should handle token refresh', async () => {
      const response = await request(app)
        .post('/auth/refresh');

      expect(response.status).toBe(401);
      expect(response.body.code).toBe('REFRESH_TOKEN_MISSING');
    });
  });

  describe('Security Logging Integration', () => {
    test('should log security events', async () => {
      // Make a request that should be logged
      const response = await request(app)
        .post('/auth/login')
        .send({
          uid: '<EMAIL>',
          password: 'wrongpassword'
        });

      expect(response.status).toBe(401);
      // Security logging happens in the background
      // In a real test, we'd verify log entries were created
    });
  });

  describe('Security Test Endpoints', () => {
    test('should provide security scan information', async () => {
      const response = await request(app)
        .get('/security-test/scan');

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('headers');
      expect(response.body.data).toHaveProperty('authentication');
      expect(response.body.data).toHaveProperty('rateLimiting');
      expect(response.body.data).toHaveProperty('features');
      
      // Check security features are enabled
      expect(response.body.data.headers.csp).toBe(true);
      expect(response.body.data.headers.frameOptions).toBe(true);
      expect(response.body.data.authentication.enabled).toBe(true);
      expect(response.body.data.rateLimiting.enabled).toBe(true);
    });

    test('should provide security configuration', async () => {
      const response = await request(app)
        .get('/security-test/config');

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('security');
      expect(response.body.data).toHaveProperty('features');
      
      // Check security configuration
      expect(response.body.data.security.csrfEnabled).toBe(true);
      expect(response.body.data.security.securityHeadersEnabled).toBe(true);
      expect(response.body.data.features.accountLockout).toBe(true);
    });
  });

  describe('Error Handling Integration', () => {
    test('should handle errors securely without information disclosure', async () => {
      // Try to cause an error
      const response = await request(app)
        .post('/auth/login')
        .send('invalid json');

      expect(response.status).toBe(400);
      
      // Should not expose internal details
      if (response.body.message) {
        expect(response.body.message).not.toContain('stack');
        expect(response.body.message).not.toContain('internal');
        expect(response.body.message).not.toContain('database');
      }
    });

    test('should maintain consistent error response format', async () => {
      const response = await request(app)
        .get('/protected/session-test');

      expect(response.body).toHaveProperty('success');
      expect(response.body).toHaveProperty('message');
      expect(response.body).toHaveProperty('code');
      expect(response.body.success).toBe(false);
    });
  });

  describe('Performance and DoS Protection', () => {
    test('should handle concurrent requests without crashing', async () => {
      const promises = [];
      
      // Make many concurrent requests
      for (let i = 0; i < 50; i++) {
        promises.push(
          request(app)
            .get('/security-test/headers')
        );
      }
      
      const responses = await Promise.all(promises);
      
      // All requests should complete (though some may be rate limited)
      responses.forEach(response => {
        expect([200, 429]).toContain(response.status);
      });
    });

    test('should reject requests with malformed headers', async () => {
      const response = await request(app)
        .get('/security-test/headers')
        .set('User-Agent', '\x00\x01\x02malicious');

      // Should handle gracefully
      expect([200, 400]).toContain(response.status);
    });
  });
});

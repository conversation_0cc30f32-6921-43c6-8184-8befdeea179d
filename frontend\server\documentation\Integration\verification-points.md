# Verification Points

This document lists all external API calls and database queries that need to be verified to ensure correct parameters and responses. These verification points are critical for ensuring proper integration between the frontend, API, and database layers.

## Table of Contents

1. [External API Calls](#external-api-calls)
2. [Database Queries](#database-queries)
3. [Verification Process](#verification-process)
4. [Response Structure Verification](#response-structure-verification)

## External API Calls

These are calls to external systems that need verification to ensure they are sending the correct parameters and receiving the expected responses.

### 1. Run Test Case / Test Suite (Verified)

**Endpoint:** `/AutoRun/CaseRunner` (Note: This is the verified endpoint, potentially replacing `/api/run-suite` and `/api/run-test`)

**Method:** POST

**Parameters Verified:**
- `uid`: User ID (string, e.g., "<EMAIL>") - **Required**
- `password`: User password (string) - **Required**
- `tc_id`: Test Case ID (number, for single case runs, e.g., 3180) - **Required if not ts_id**
- `ts_id`: Test Suite ID (number, for suite runs) - **Required if not tc_id**
- `envir`: Environment name (string, e.g., "qa02") - **Required**
- `shell_host`: Shell host for test execution (string, e.g., "jps-qa10-app01") - **Required**
- `file_path`: File path on shell host (string, e.g., "/home/<USER>/") - *Likely Required*
- `operatorConfigs`: Configuration identifier (string, e.g., "operatorNameConfigs") - *Context-dependent*
- `kafka_server`: Kafka server address (string, e.g., "kafka-qa-a0.lab.wagerworks.com") - *Context-dependent*
- `dataCenter`: Data center identifier (string, e.g., "GU") - *Context-dependent*
- `rgs_env`: RGS environment (string, e.g., "qa02") - *Context-dependent*
- `old_version`: Version flag (string, e.g., "0") - *Context-dependent*
- `networkType1`: Network type (string, e.g., "multi-site") - *Context-dependent*
- `networkType2`: Network type (string, e.g., "multi-site") - *Context-dependent*
- `sign`: Sign parameter (string, e.g., "-") - *Context-dependent*
- `rate_src`: Rate source (string, e.g., "local") - *Context-dependent*

**Actual Response Structure (HTML):**
```html
<html>
<head>
<title>View Your Test Result</title>
<!-- ... other head elements ... -->
</head>
<body>
        <!-- ... other body elements ... -->
        Test Result<p/>
        Your test session id: <tsn_id><p/>
        <a href="ReportSummary?tsn_id=<tsn_id>">View Test Result Summary</a><p/>
        <a href="ReportDetails?tsn_id=<tsn_id>">View Test Result Details</a><p/>
        <!-- ... other body elements ... -->
</body>
</html>
```
*Key Information:* The `tsn_id` (Test Session ID) is embedded within the HTML response (e.g., `Your test session id: 13781`).

**Verification Needed:**
- Confirm the full list of *required* vs *optional/context-dependent* parameters for different scenarios (case vs. suite).
- Determine if the response format changes under error conditions.

### 2. Run Test Suite (Potentially Outdated)

**Endpoint:** `/api/run-suite` (Note: Verification suggests `/AutoRun/CaseRunner` is used instead)

**Method:** POST

**Parameters to Verify:**
- `ts_id`: Test suite ID (number)
- `environment`: Environment name (string, e.g., "qa02")
- `shell_host`: Shell host for test execution (string, e.g., "jps-qa10-app01")
- `uid`: User ID (string)
- `password`: User password (string)

**Expected Response Structure:** (Note: Actual response is HTML, see `/AutoRun/CaseRunner` section)
```json
{
  "success": true,
  "tsn_id": "12345",
  "message": "Test suite 101 started successfully with 5 test cases"
}
```

**Verification Needed:** (Note: Verification suggests `/AutoRun/CaseRunner` is used instead)
- Confirm that the external API accepts these parameters
- Verify the exact format of the test session ID (tsn_id) returned
- Check if any additional parameters are required
- Verify if the response includes additional fields

### 3. Stop Test (Verified - Different Endpoint/Method)

**Endpoint:** `/AutoRun/RemoveSession` **(Port 9080)**
**(Note: This verified endpoint replaces the non-functional `/api/stop-test` documented previously)**

**Method:** POST

**Authentication:** Requires a valid `JSESSIONID` cookie passed in the request headers.

**Parameters (Body - Form URL Encoded):**
- `tsn_id`: Test session ID to stop (string) - **Required**

**Required Headers (Example):**
```
Accept: */*
Accept-Encoding: gzip, deflate
Accept-Language: en-GB,en;q=0.9,en-US;q=0.8
Content-Type: application/x-www-form-urlencoded; charset=UTF-8
Origin: http://mprts-qa02.lab.wagerworks.com:9080
Referer: <URL of the page initiating the request, e.g., ReportList>
X-Requested-With: XMLHttpRequest
User-Agent: <Browser User Agent String>
Cookie: JSESSIONID=<valid_session_id>
```

**Expected Response Structure:** (Needs verification - likely 200 OK on success, potentially with simple message or empty body)

**Verification Needed:**
- Verify the exact success/error response body and status codes.
- Confirm if any other parameters or headers are strictly necessary.

### 4. Rerun Failed Tests (Needs Verification)

**Endpoint:** `/api/rerun-failed` **(Result: 404 Not Found)**

**Method:** POST

**Parameters to Verify:**
- `tsn_id`: Original test session ID (string)
- `environment`: Environment name (string, e.g., "qa02")
- `shell_host`: Shell host for test execution (string, e.g., "jps-qa10-app01")
- `uid`: User ID (string)
- `password`: User password (string)

**Expected Response Structure:**
```json
{
  "success": true,
  "tsn_id": "12346",
  "message": "Failed tests from session 12345 rerun initiated with session ID 12346"
}
```

**Verification Needed:**
- Confirm that the external API accepts these parameters **(Result: Endpoint not found)**
- Verify the exact format of the new test session ID (tsn_id) returned **(Result: Endpoint not found)**
- Check if any additional parameters are required **(Result: Endpoint not found)**
- Verify if the response includes additional fields **(Result: Endpoint not found)**

### 5. Get Test Status (Needs Verification)

**Endpoint:** `/api/test-status` **(Result: 404 Not Found)**

**Method:** GET

**Parameters to Verify:**
- `tsn_id`: Test session ID (string)
- `uid`: User ID (string)
- `password`: User password (string)

**Expected Response Structure:**
```json
{
  "success": true,
  "data": {
    "tsn_id": "12345",
    "status": "running",
    "progress": 60,
    "current_test": "Login Test",
    "start_time": "2023-01-01T12:00:00Z",
    "elapsed_time": "00:05:30"
  },
  "message": "Test status retrieved successfully"
}
```

**Verification Needed:**
- Confirm all possible status values ("running", "completed", "failed", "stopped") **(Result: Endpoint not found)**
- Verify if progress is always provided as a percentage **(Result: Endpoint not found)**
- Check if additional fields are included in the response **(Result: Endpoint not found)**

### 6. Get Test Report Summary (Verified Endpoint)

**Endpoint:** `/AutoRun/ReportSummary` **(Port 9080)**
**(Note: This verified endpoint replaces the non-functional `/api/test-report` documented previously)**

**Method:** GET

**Authentication:** Requires a valid `JSESSIONID` cookie passed in the request headers.

**Parameters (Query String):**
- `tsn_id`: Test session ID to view (string) - **Required**

**Actual Response Structure:** HTML page containing:
- Session ID, Owner, Suite/Case ID, Start/End Times
- Overall Pass/Fail Status
- Summary list of included cases with individual status
- Pass/Fail Counts
- Link to Report Details (potentially `/AutoRun/ReportDetails` - port needs confirmation)
- List of input variables/parameters used for the run

**Verification Needed:**
- Confirm the exact endpoint and port for the detailed report (`/AutoRun/ReportDetails`). **(Verified: `/AutoRun/ReportDetails` on Port 9080)**
- Verify behavior for non-existent `tsn_id` or authentication failure.

### 7. Get Test Report Details (Verified Endpoint)

**Endpoint:** `/AutoRun/ReportDetails` **(Port 9080)**

**Method:** GET

**Authentication:** Requires a valid `JSESSIONID` cookie passed in the request headers.

**Parameters (Query String):**
- `tsn_id`: Test session ID to view (string) - **Required**
- `index`: Page number (optional, for pagination of steps, defaults to 1) - *Observed*

**Actual Response Structure:** HTML page containing:
- Session ID
- Pagination controls
- Table listing individual test steps within the session.
- Each step row includes: tc_id, seq_index, outcome (P/F), step name/description, input summary, output summary.
- Links to: `CaseEditor`, `SequenceLoader`, `MessageLog`, `SeqTextLoader`, `InputLog`, `OutputLog` for deeper inspection.

**Verification Needed:**
- Verify behavior for non-existent `tsn_id` or authentication failure.
- Fully document parameters for linked endpoints (`MessageLog`, `InputLog`, etc.) if needed.

## Database Queries

These are database queries that need verification to ensure they are using the correct parameters and returning the expected data structures.

### 1. Get Test Cases

**Function:** `getTestCases(filters)`

**SQL Query to Verify:**
```sql
SELECT tc.tc_id, tc.uid, tc.status, tc.case_driver, tc.tp_id, tc.comments, tc.tickets, tc.name
FROM test_case tc
[JOIN test_case_group tcg ON tc.tc_id = tcg.tc_id]
[WHERE tcg.ts_id = ? [AND tc.status = ?]]
ORDER BY tc.tc_id DESC
LIMIT ?
```

**Parameters to Verify:**
- `ts_id`: Test suite ID (optional)
- `status`: Test case status (optional)
- `limit`: Maximum number of results (default: 100)

**Expected Response Structure:**
```json
[
  {
    "tc_id": 3180,
    "uid": "test_user",
    "status": "active",
    "case_driver": "selenium",
    "tp_id": 101,
    "comments": "Test case comments",
    "tickets": "JIRA-123",
    "name": "Login Test Case"
  },
  ...
]
```

**Verification Needed:**
- Confirm the exact column names in the database
- Verify if any columns are missing or have different names
- Check if any additional filters are supported

### 2. Get Test Suite Metadata (Verified Table/Query)

**Function:** `getTestSuites(filters)` *(Note: Function name might be misleading if it only queries `test_suite` table)*

**Target Table:** `test_suite`

**Verified SQL Query (for Name):**
```sql
SELECT ts_id, name
FROM rgs_test.test_suite
WHERE ts_id = ?;
```

**Observed Columns (Verified via `SELECT *` for ts_id=322):**
- `ts_id`: Test Suite ID (e.g., 322)
- `status`: Status flag (e.g., 'A')
- `uid`: User ID associated with the suite (e.g., '<EMAIL>')
- `comments`: Comment string (e.g., 'DEMO PE2.1 Smoke Test')
- `tp_id`: Related parameter/plan ID? (e.g., 201296)
- `name`: Test Suite Name (e.g., "PE2.1 Smoke Test")

**Documented Query in `examples_of_db_requests.md`:**
```sql
/*get test suite parameters */
select p.* from  parameter_group g, parameter p,  test_suite s  where s.tp_id=g.tp_id and  p.pid=g.pid and s.ts_id = <suite_id>;
```
*(Note: This other documented query suggests `test_suite` table might also have a `tp_id` column used for joining with parameters. This needs separate verification if parameters are required.)* **(Presence of `tp_id` column verified).**

**Verification Needed:**
- Determine the full list of columns in the `test_suite` table (e.g., using `DESCRIBE test_suite;`). Does it contain `status`, `pj_id`, `comments`, `tickets`, `tag`, `uid` as originally documented (but for the wrong table)? **(Verified `status`, `uid`, `comments`, `tp_id`, `name`. Missing `pj_id`, `tickets`, `tag`)**
- Verify filtering options (e.g., by `name`, `status`, `uid` if those columns exist). **(Filtering by `status`, `uid`, `name` confirmed possible.)**
- Verify the query for retrieving suite parameters if needed.

**(Previous Incorrect Documentation for `test_case_group` moved to new section 2.a)**

### 2.a Get Test Suite Composition (Verified Table/Query)

**(Previously documented incorrectly as Get Test Suites)**

**Purpose:** Maps Test Cases to Test Suites and defines their sequence.
**Target Table:** `test_case_group`

**Verified SQL Query (Example):**
```sql
SELECT *
FROM rgs_test.test_case_group
WHERE ts_id = ?;
```

**Actual `test_case_group` Table Columns (Verified):**
- `ts_id`: Test Suite ID
- `tc_id`: Test Case ID
- `seq_index`: Sequence of the test case within the suite.

**Observed Output Format (Example for ts_id=322):**
```
ts_id   tc_id   seq_index
322     3180    1
322     2481    2
322     2862    3
```

**Verification Needed:**
- Verify filtering or ordering options for `test_case_group` table if needed.

### 3. Get Active Tests

**(SQL Query Verified)**

**Function:** `getActiveTests(filters)`

**SQL Query to Verify:**
```sql
SELECT s.tsn_id, COALESCE(s.tc_id, 0) as tc_id, s.uid as initiator_user, s.start_ts as creation_time
FROM test_session s
WHERE s.end_ts IS NULL
[AND s.uid = ?]
ORDER BY s.start_ts DESC
LIMIT ?
```

**Parameters to Verify:**
- `uid`: User ID (optional) - *(Optional filter, verified)*
- `limit`: Maximum number of results (default: 20) - *(Optional filter, verified)*

**Expected Response Structure:** (Note: The `is_current_user` field is not generated by the SQL query; it requires application logic comparing `initiator_user` to the current user.)
```json
[
  {
    "tsn_id": "12345",
    "tc_id": "3180",
    "initiator_user": "test_user",
    "creation_time": "2023-01-01T12:00:00Z",
    "is_current_user": true
  },
  ...
]
```

**Verification Needed:**
- Confirm the exact column names in the database (**Verified: `tsn_id`, `tc_id` (coalesced), `uid` (as initiator_user), `start_ts` (as creation_time), `end_ts` (used in WHERE))**
- Verify if the `is_current_user` field is calculated correctly **(Requires application-level verification)**
- Check if any additional filters are supported (`uid` and `limit` are documented but not tested)

### 4. Get Test Results

**Function:** `getTestResults(tsnId)`

**SQL Query to Verify:** (Note: This query likely aims to fetch all individual steps. Not fully verified in this session.) **(Base Query Verified)**
```sql
SELECT r.tsn_id, r.tc_id, r.seq_index, r.outcome, r.creation_time, o.txt
FROM test_result r
JOIN output o ON r.cnt = o.cnt
WHERE r.tsn_id = ?
ORDER BY r.creation_time ASC
```

**Parameters to Verify:**
- `tsn_id`: Test session ID

**Expected Response Structure:** (Note: The verified queries below returned raw tabular data. This JSON structure likely represents application-level aggregation and needs separate verification.)
```json
{
  "tsn_id": "12345",
  "total_results": 15,
  "pass_count": 12,
  "fail_count": 3,
  "start_time": "2023-01-01T12:00:00Z",
  "end_time": "2023-01-01T12:05:00Z",
  "duration": "5:00",
  "results": [
    {
      "tc_id": "3180",
      "total_steps": 5,
      "passed_steps": 4,
      "failed_steps": 1,
      "steps": [
        {
          "seq_index": 1,
          "outcome": "P",
          "creation_time": "2023-01-01T12:00:01Z",
          "output": "Step 1 output"
        },
        ...
      ]
    },
    ...
  ]
}
```

**Verification Needed:**
- Confirm the exact column names in the database (**Verified for tested queries**)
- Verify if the outcome values are always "P" for pass and "F" for fail (**Verified**)
- Check if any additional information is available in the database (**Verified `txt` in `output` table contains error details**)
- Verify the query for retrieving *all* individual steps (**Verified for base query structure**).
- Verify the application logic that produces the aggregated JSON structure (**Requires application-level verification**).

**(Optimization Note: `database_structure.md` provides example SQL queries that calculate pass/fail counts and duration directly in the database, potentially simplifying application logic.)**
```sql
-- Example: Get Pass/Fail step counts and Duration per tsn_id
SELECT t.tsn_id,
       SUM(CASE WHEN outcome = 'P' THEN 1 ELSE 0 END) passed_steps,
       SUM(CASE WHEN outcome = 'F' THEN 1 ELSE 0 END) failed_steps,
       TIMEDIFF(MAX(creation_time), MIN(creation_time)) duration
FROM test_result t
WHERE t.tsn_id = [TEST_RUN_ID]
GROUP BY t.tsn_id;

-- Example: Get Pass/Fail step counts per tc_id within a tsn_id
SELECT t.tc_id,
       SUM(CASE WHEN outcome = 'P' THEN 1 ELSE 0 END) passed_steps,
       SUM(CASE WHEN outcome = 'F' THEN 1 ELSE 0 END) failed_steps
FROM test_result t
WHERE t.tsn_id = [TEST_RUN_ID]
GROUP BY t.tc_id;
```

#### 4.a Get Pass/Fail Counts (Verified)

**Verified SQL Query:**
```
```

**(Optimization Note: `database_structure.md` provides example SQL queries that calculate duration and pass/fail counts directly, which may be useful for populating report summaries efficiently. See notes in Section 4.)**

## Verification Process
// ... existing code ...

</rewritten_file>
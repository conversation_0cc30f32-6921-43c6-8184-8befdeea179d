# SmartTest – Initial Installation Guide

This document provides **step-by-step instructions** for installing SmartTest on a **fresh workstation**.  It covers *initial* setup steps.

> **TIP:**  Read through the entire guide once before starting to type commands.

---

## 1. Prerequisites

| Requirement | Recommended Version | Notes |
|-------------|---------------------|-------|
| Git         | 2.30 or later       | https://git-scm.com/downloads |
| Node.js     | 14 LTS or newer     | Includes **npm** – the Node package manager |
| A modern terminal | • **Windows:** PowerShell<br>• **macOS / Linux:** Bash / zsh | All examples below use **PowerShell** but the commands are the same in Bash unless stated otherwise. |
| SSH key for DB server | – | Used for secure SSH connection

---

## 2. Choose an Installation Folder

1. Open **PowerShell** (or your preferred terminal).
2. Navigate to the folder where you want SmartTest to be installed, for example your _Projects_ directory:

   ```powershell
   cd C:\Users\<USER>\Projects
   ```

Note: Feel free to pick any location – SmartTest will be created inside a new sub-folder.

---

## 3. <PERSON>lone the Repository and Switch to the mvp-manual-testing Branch

Note: Run **each** command on its **own** line:

```powershell
git clone -b feature/mvp-manual-testing https://github.com/yacov/smarttest.git
```
## 4. Enter the project folder
```powershell
cd smarttest
```

Note: The first command downloads the code and **immediately switches** to the `feature/mvp-manual-testing` branch so you are ready to work on the latest MVP code.

---

## 5. Verify You Are on the Correct Branch (Optional)

```powershell
git branch
```

Note: The active branch is marked with an asterisk `*`.  If you do **not** see `feature/mvp-manual-testing`, run:

```powershell
git fetch --all
```

```powershell
git checkout feature/mvp-manual-testing
```

---

## 6. Install Project Dependencies

```powershell
npm install
```

Note: This command can take a few minutes the first time.

---

## 7. Configure Environment Variables

Note: SmartTest requires a few `.env` files that hold credentials and configuration parameters.  Three sample files are provided inside `frontend/server`.

1. Navigate to the `frontend/server` folder:

```powershell
cd frontend/server
```

2. **Copy** the main `.env` file for QA2 (Environment QA2):

    ```powershell
   copy .env.02.sample .env
   ```

3. **Copy** environemnt settings for QA1 (Environment QA1):

   ```powershell
   copy .env.01.sample .env.01
   ```

4. **Copy** environemnt settings for QA3 (Environment QA3):

   ```powershell
   copy .env.03.sample .env.03
   ```

5. **Edit** the new files (use Notepad or VS Code) and adjust the following keys:
   
   | Key | Description |
   |-----|-------------|
   | `SSH_USER` | Your SSH login for the database host
   | `SSH_KEY_PATH` | Full path to your private key, by default `C:\Users\<USER>\.ssh\id_rsa_dbserver` |
   | `TEST_USER` / `TEST_PASSWORD` | Credentials for the access to USD+ framework | (see test user credentials in env files)

6. Return back to the project root folder:

```powershell
cd ../..
```

---

## 8. Build the Front-End Assets 

```powershell
npm run build
```

---

## 9. Start SmartTest

From the **project root** (`smarttest` folder) run:

```powershell
npm start
```

Note: The server starts on `http://localhost:3000` by default.

---

## 10. Verify the Installation

1. Open your browser at [http://localhost:3000](http://localhost:3000).
2. Log in using your USD+  credentials when prompted in the login modal.

---

Congratulations – SmartTest is installed!  
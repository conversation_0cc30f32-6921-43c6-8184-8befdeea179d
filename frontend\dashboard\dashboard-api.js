/**
 * dashboard-api.js
 *
 * This module handles all communication with the backend API service.
 * It abstracts away the direct `window.apiService` calls into a clean,
 * reusable interface for other dashboard modules to use.
 */

import { config } from './dashboard-config.js';

let apiService;

/**
 * Waits for the global apiService to be available, with a timeout.
 * @returns {Promise<object>} A promise that resolves with the apiService object.
 */
export function waitForApiService() {
    return new Promise((resolve, reject) => {
        let attempts = 0;
        const interval = setInterval(() => {
            if (window.apiService) {
                clearInterval(interval);
                apiService = window.apiService;
                resolve(apiService);
            } else if (attempts >= config.MAX_API_CHECK_ATTEMPTS) {
                clearInterval(interval);
                reject(new Error('API Service not available after multiple attempts.'));
            } else {
                attempts++;
            }
        }, 500);
    });
}

/**
 * Runs a single test case.
 * @param {number} tcId - The test case ID.
 * @param {object} params - Additional parameters for the test run.
 * @returns {Promise<any>} The response from the API.
 */
export async function runTestCase(tcId, params = {}) {
    await waitForApiService();
    return apiService.runTestCase(tcId, params);
}

/**
 * Runs a predefined test suite.
 * @param {number} suiteId - The test suite ID.
 * @param {string} suiteName - The name of the test suite.
 * @returns {Promise<any>} The response from the API.
 */
export async function runTestSuite(suiteId, suiteName) {
    await waitForApiService();
    return apiService.runTestSuite(suiteId, suiteName);
}

/**
 * Runs multiple test suites.
 * @param {number[]} suiteIds - An array of test suite IDs.
 * @returns {Promise<any>} The response from the API.
 */
export async function runMultipleTestSuites(suiteIds) {
    await waitForApiService();
    return apiService.runMultipleTestSuites(suiteIds);
}

/**
 * Fetches recent test runs from the server.
 * @param {number|null} sinceTsnId - The last known TSN ID to fetch runs since.
 * @returns {Promise<Array>} A list of recent test runs.
 */
export async function getRecentRuns(sinceTsnId = null) {
    await waitForApiService();
    return apiService.getRecentRuns(sinceTsnId);
}

/**
 * Fetches all predefined test suites.
 * @returns {Promise<object>} The test suites data.
 */
export async function getTestSuites() {
    await waitForApiService();
    return apiService.getTestSuites();
}

/**
 * Fetches the available filter options for test suites.
 * @returns {Promise<object>} The filter options.
 */
export async function getFilterOptions() {
    await waitForApiService();
    return apiService.getFilterOptions();
}

/**
 * Fetches test suites based on the provided filters.
 * @param {object} filters - The filter criteria.
 * @returns {Promise<Array>} A list of filtered test suites.
 */
export async function getFilteredTestSuites(filters) {
    await waitForApiService();
    return apiService.getFilteredTestSuites(filters);
}

/**
 * Fetches the detailed information for a specific test run.
 * @param {number} tsnId - The test session ID.
 * @returns {Promise<object>} The test details.
 */
export async function getTestDetails(tsnId) {
    await waitForApiService();
    return apiService.getTestDetails(tsnId);
}

/**
 * Sends a request to stop a running test.
 * @param {number} tsnId - The test session ID to stop.
 * @returns {Promise<any>} The response from the API.
 */
export async function stopTestRun(tsnId) {
    await waitForApiService();
    return apiService.stopTest(tsnId);
}

/**
 * Logs in with the provided credentials.
 * @param {string} username - The username.
 * @param {string} password - The password.
 * @returns {Promise<boolean>} Whether the login was successful.
 */
export async function login(username, password) {
    await waitForApiService();
    return apiService.login(username, password);
}

/**
 * Logs out the current user.
 * @returns {Promise<void>} A promise that resolves when logout is complete.
 */
export async function logout() {
    await waitForApiService();
    return apiService.logout();
}

/**
 * Fetches the list of all available test cases.
 * @returns {Promise<Array>} A list of available test cases.
 */
export async function getAvailableTestCases() {
    await waitForApiService();
    return apiService.getAvailableTestCases();
}
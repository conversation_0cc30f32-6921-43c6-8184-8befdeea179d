# Test Reports Viewer

This component provides functionality to view and analyze automated test results in the SmartTest framework, implementing a hybrid data access approach that combines direct external API integration with database access.

## Features

- **Reports Dashboard**: View a summary of recent test results with statistics and charts
- **Detailed Test Reports**: Examine individual test details, including test cases and their results
- **Performance Analytics**: Track test execution trends over time with visual charts
- **Data Export**: Export test reports to CSV for further analysis
- **Advanced Filtering**: Filter reports using multi-select options with visual data distribution
- **Fixed Header**: Maintain column context when scrolling through large datasets
- **Cascading Filters**: Dynamically updated filter options based on current selections
- **Hybrid Data Access**: Fallback mechanisms to ensure data availability even when database is unavailable
- **Real-time Updates**: Automatic polling for fresh data every 30 seconds (configurable)
- **Timezone Support**: View timestamps in local time or specific timezones

## Architecture

The Reports Viewer implements a hybrid architecture with multiple layers:

### 1. Data Access Layer
- **External API Service**: Direct integration with external APIs on port 9080
- **Session ID Service**: Manages test session IDs from multiple sources
- **API Service Integration**: Uses the shared API service for database access

### 2. UI Layer
- **DataTables Integration**: Advanced table functionality with extensions
- **Chart.js Visualizations**: Data visualization for test metrics
- **Bootstrap 5 Components**: Responsive UI elements

### 3. Business Logic Layer
- **Data Transformation**: Converts API responses to UI-friendly formats
- **Error Handling**: Robust error handling with fallback mechanisms
- **Caching**: Local storage caching for improved performance

## File Structure

- `index.html` - Main HTML page for the reports viewer
- `reports.js` - Core JavaScript functionality and UI logic
- `styles.css` - Custom styles for the reports page
- `api-service.js` - Unified API service integration (migrated)
- `/services/` - ⚠️ **MIGRATED** - Services moved to `../shared/services/`
  - `README.md` - Migration notice and updated usage instructions
- `/tests/` - Comprehensive test suite
  - Unit tests for services
  - Integration tests for the full flow
  - Mock data for testing

## Data Flow

1. **Initial Load**:
   - Session ID Service retrieves test session IDs from multiple sources
   - External API Service fetches report summaries for each session ID
   - Data is transformed and displayed in the DataTable
   - Charts are updated with the aggregated data

2. **User Interactions**:
   - Filtering: SearchPanes extension provides advanced filtering
   - Sorting: DataTable handles column sorting
   - Pagination: Fixed at 25 results per page for consistent experience
   - Details View: Clicking "Details" fetches and displays test case information

3. **Data Refresh**:
   - Manual refresh via "Refresh" button
   - Automatic polling based on configuration
   - Timestamps update based on selected timezone

## Integration Points

- **Hybrid Data Access**:
  - Direct external API integration for test results and details
  - Database access for analytics and historical data
- **API Integration**:
  - External API endpoints on port 9080 for real-time data
  - Local API endpoints for database access
- **UI Integration**:
  - DataTables extensions for enhanced functionality
  - Chart.js for data visualization
  - Bootstrap 5 for responsive layout
- **Cross-Module Integration**:
  - Microsoft Teams bot for triggering report generation
  - Test configuration page for test settings
  - Dashboard for active test monitoring

## Configuration Options

The reports page is highly configurable through the `config` object:

```javascript
const config = {
    reportingEndpoint: '/local/recent-runs',      // Database API endpoint
    testDetailsEndpoint: '/local/test-details',   // Test details endpoint
    refreshInterval: 30000,                       // Polling interval (30 seconds)
    useDirectExternalApi: false,                  // Use direct API or proxy
    externalApiBaseUrl: '/api',                   // API base URL
    maxReportsToShow: 25,                         // Results per page
    autoRefresh: false                            // Auto-refresh toggle
};
```

## Testing

The Reports Viewer includes a comprehensive test suite:

- **Unit Tests**: Test individual components in isolation
- **Integration Tests**: Test the full data flow
- **Mock Data**: Realistic mock data that matches actual API responses
- **Coverage**: 100% code coverage for critical components

Run tests with:
```bash
npm run test:reports        # Run reports tests
npm run test:reports:coverage # Run with coverage
```

## Responsive Design

The reports viewer is fully responsive and adapts to different screen sizes:
- **Desktop**: Full layout with sidebar and detailed charts
- **Tablet**: Adjusted layout with preserved functionality
- **Mobile**: Simplified layout for small screens with vertical scrolling
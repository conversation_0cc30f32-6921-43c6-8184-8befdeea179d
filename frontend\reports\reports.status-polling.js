function startTestStatusPolling(activeTestRuns, credentials) {
    console.log(`Starting status polling for ${activeTestRuns.size} test runs`);
    
    // Only start if we have test runs to monitor
    if (activeTestRuns.size === 0) {
        console.log('No active test runs to monitor');
        return;
    }
    
    // Function to poll for status updates
    const pollTestStatus = async () => {
        try {
            // If no more active test runs, stop polling
            if (activeTestRuns.size === 0) {
                console.log('No more active test runs, stopping status polling');
                return;
            }
            
            console.log(`Polling status for ${activeTestRuns.size} test runs...`);
            
            // Build query parameters for the API call
            const params = new URLSearchParams();
            
            // Get current active test session IDs and ensure they're strings
            const currentSessionIds = Array.from(activeTestRuns.keys()).map(key => {
                // Ensure the key is a string - if it's an object, try to extract the ID
                if (typeof key === 'object' && key !== null) {
                    console.warn('Found object key in activeTestRuns:', key);
                    // If it's an object with an id or tsn_id property, use that
                    return key.id || key.tsn_id || key.toString();
                }
                return String(key);
            });
            
            console.log('Current session IDs for polling:', currentSessionIds);
            
            // Add each test session ID to the query
            currentSessionIds.forEach(tsn_id => {
                if (tsn_id && tsn_id !== '[object Object]') {
                    params.append('tsn_id', tsn_id);
                } else {
                    console.error('Invalid tsn_id found:', tsn_id);
                }
            });
            
            // Add credentials
            params.append('uid', credentials.uid);
            params.append('password', credentials.password);
            
            console.log('API request params:', params.toString());
            
            // Make the API call to check status
            const response = await fetch(`/api/test-status?${params.toString()}`);
            
            if (!response.ok) {
                throw new Error(`API responded with status ${response.status}`);
            }
            
            const data = await response.json();
            
            if (data.success && data.testStatus) {
                // Process each status update
                Object.entries(data.testStatus).forEach(([tsn_id, statusData]) => {
                    // Try to find the test run using the string ID or by checking object keys
                    let testRun = activeTestRuns.get(tsn_id);
                    
                    // If not found with string key, try to find by checking object keys
                    if (!testRun) {
                        for (const [key, value] of activeTestRuns.entries()) {
                            if (typeof key === 'object' && (key.id === tsn_id || key.tsn_id === tsn_id)) {
                                testRun = value;
                                break;
                            } else if (String(key) === tsn_id) {
                                testRun = value;
                                break;
                            }
                        }
                    }
                    
                    if (testRun) {
                        const { status, result } = statusData;
                        const { rowElement } = testRun;
                        
                        // Update the status in the UI
                        if (rowElement) {
                            const statusCell = rowElement.cells[2];
                            
                            if (statusCell) {
                                // Update based on status
                                if (status === 'Completed' || status === 'Success') {
                                    statusCell.innerHTML = '<span class="text-success">Completed Successfully</span>';
                                    // Remove from active runs - find the correct key to delete
                                    for (const [key, value] of activeTestRuns.entries()) {
                                        if (value === testRun) {
                                            activeTestRuns.delete(key);
                                            break;
                                        }
                                    }
                                } else if (status === 'Failed' || status === 'Error') {
                                    statusCell.innerHTML = `<span class="text-danger">Failed</span>`;
                                    if (result && result.error) {
                                        statusCell.innerHTML += `<div class="small text-danger">${result.error.substring(0, 50)}${result.error.length > 50 ? '...' : ''}</div>`;
                                    }
                                    // Remove from active runs - find the correct key to delete
                                    for (const [key, value] of activeTestRuns.entries()) {
                                        if (value === testRun) {
                                            activeTestRuns.delete(key);
                                            break;
                                        }
                                    }
                                } else if (status === 'Running') {
                                    statusCell.innerHTML = '<span class="text-primary">Running</span>';
                                } else if (status === 'Queued') {
                                    statusCell.innerHTML = '<span class="text-secondary">Queued</span>';
                                } else {
                                    statusCell.innerHTML = `<span>${status}</span>`;
                                }
                            }
                        }
                    } else {
                        console.warn(`Could not find test run for tsn_id: ${tsn_id}`);
                    }
                });
                
                // If there are still active runs, schedule the next poll
                if (activeTestRuns.size > 0) {
                    setTimeout(pollTestStatus, 5000); // Poll every 5 seconds
                } else {
                    console.log('All test runs completed, stopping status polling');
                }
            } else {
                console.error('Error in test status response:', data);
                // Try again in case of temporary error
                setTimeout(pollTestStatus, 10000); // Longer delay for errors
            }
        } catch (error) {
            console.error('Error polling test status:', error);
            // Try again with backoff in case of errors
            setTimeout(pollTestStatus, 10000); // Longer delay for errors
        }
    };
    
    // Start the initial poll
    pollTestStatus();
}

/**
 * Create error cell with details button for failed cases
 * @param {Object} testCase - The test case data
 * @returns {HTMLElement} - The created error cell
 */
/**
 * Show failure details in a modal
 * @param {Object} testCase - The test case with failure details
 */
function showFailureDetails(testCase) {
    console.log('Showing failure details for test case:', testCase);
    
    // Create a unique ID for the modal
    const modalId = `failure-details-modal-${testCase.tc_id || testCase.id || Date.now()}`;
    
    // Check if the modal already exists
    let modalElement = document.getElementById(modalId);
    
    if (!modalElement) {
        // Create the modal element
        modalElement = document.createElement('div');
        modalElement.id = modalId;
        modalElement.className = 'modal fade';
        modalElement.setAttribute('tabindex', '-1');
        modalElement.setAttribute('aria-labelledby', `${modalId}-label`);
        modalElement.setAttribute('aria-hidden', 'true');
        
        // Create the modal structure
        const modalDialog = document.createElement('div');
        modalDialog.className = 'modal-dialog modal-lg';
        
        const modalContent = document.createElement('div');
        modalContent.className = 'modal-content';
        
        modalDialog.appendChild(modalContent);
        modalElement.appendChild(modalDialog);
        document.body.appendChild(modalElement);
        
        // Set the modal content
        modalContent.innerHTML = createFailureDetailsModal(testCase);
    } else {
        // Update the existing modal content
        const modalContent = modalElement.querySelector('.modal-content');
        if (modalContent) {
            modalContent.innerHTML = createFailureDetailsModal(testCase);
        }
    }
    
    // Show the modal
    const modal = new bootstrap.Modal(modalElement);
    modal.show();
}

/**
 * Create the HTML content for the failure details modal
 * @param {Object} testCase - The test case with failure details
 * @returns {string} - HTML content for the modal
 */
function createFailureDetailsModal(testCase) {
    // Helper function to escape HTML
    function escapeHtml(text) {
        if (!text) return '';
        return text
            .toString()
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#039;');
    }
    
    const tcId = testCase.tc_id || testCase.id || 'N/A';
    const name = testCase.test_case_name || testCase.name || 'Unknown';
    const status = testCase.status || 'Unknown';
    const errorMsg = testCase.error || testCase.error_message || 'No error message available';
    const outputData = testCase.output_data || testCase.input_output || '';
    const timestamp = testCase.creation_time ? formatDateTime(testCase.creation_time) : 'N/A';
    
    return `
        <div class="modal-header">
            <h5 class="modal-title" id="${modalId}-label">Failure Details: ${escapeHtml(tcId)}</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
            <div class="row mb-3">
                <div class="col-md-6">
                    <p><strong>Test Case ID:</strong> ${escapeHtml(tcId)}</p>
                    <p><strong>Name:</strong> ${escapeHtml(name)}</p>
                    <p><strong>Status:</strong> <span class="text-danger">${escapeHtml(status)}</span></p>
                    <p><strong>Timestamp:</strong> ${escapeHtml(timestamp)}</p>
                </div>
                <div class="col-md-6">
                    <p><strong>Error Message:</strong></p>
                    <div class="alert alert-danger">${escapeHtml(errorMsg)}</div>
                </div>
            </div>
            <div class="row">
                <div class="col-12">
                    <p><strong>Output Data:</strong></p>
                    <pre class="bg-light p-3" style="max-height: 400px; overflow: auto; white-space: pre-wrap; word-wrap: break-word;">${escapeHtml(outputData)}</pre>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
        </div>
    `;
}

function createErrorCell(testCase) {
    const errorCell = document.createElement('td');
    // Check if test case has error message or failure details
    if (testCase.error || testCase.error_message || 
        (testCase.output_data && testCase.status && testCase.status.toLowerCase() === 'failed') || 
        (testCase.input_output && testCase.status && testCase.status.toLowerCase() === 'failed')) {
        
        // Create a container for error message and View Details button
        const container = document.createElement('div');
        container.className = 'd-flex justify-content-between align-items-top';
        
        // Add error message (truncated if long)
        const errorMessage = document.createElement('div');
        errorMessage.className = 'text-danger error-message';
        const message = testCase.error || testCase.error_message || 'Execution failed. See details.';
        
        // If message is long, truncate it
        if (message.length > 50) {
            errorMessage.textContent = message.substring(0, 50) + '...';
            errorMessage.title = message; // Full message in tooltip
        } else {
            errorMessage.textContent = message;
        }
        
        container.appendChild(errorMessage);
        
        // Add View Details button for failed cases with output data
        if ((testCase.output_data || testCase.input_output) && testCase.status && testCase.status.toLowerCase() === 'failed') {
            const detailsButton = document.createElement('button');
            detailsButton.className = 'btn btn-sm btn-outline-secondary ms-2';
            detailsButton.textContent = 'View Details';
            detailsButton.onclick = () => showFailureDetails(testCase);
            container.appendChild(detailsButton);
        }
        
        errorCell.appendChild(container);
    } else {
        errorCell.textContent = '-';
    }
    
    return errorCell;
}

// Function to update the test cases table headers to match our two-row structure

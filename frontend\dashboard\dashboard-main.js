/**
 * dashboard-main.js
 *
 * This is the main entry point for the SmartTest Dashboard application.
 * It orchestrates the initialization of all dashboard modules.
 */

import { waitForApiService } from './dashboard-api.js';
import { initializeAuth } from './dashboard-auth.js';
import { initializeCharts } from './dashboard-charts.js';
import { initializeTestSuites } from './dashboard-test-suites.js';
import { initializeActiveTests } from './dashboard-active-tests.js';
import { initializeRecentRuns, handleUrlParameters } from './dashboard-recent-runs.js';
import { initializeDetailsModal } from './dashboard-details-modal.js';

/**
 * Main initialization function for the dashboard.
 */
async function main() {
    // First, ensure the core window.apiService is available.
    const isApiReady = await waitForApiService();

    if (!isApiReady) {
        console.error('Dashboard initialization failed: apiService is not available.');
        // Optionally, display a prominent error message to the user on the page.
        document.body.innerHTML = '<h1 style="color: red; text-align: center; margin-top: 50px;">Critical Error: Backend API service failed to load. Please check the server and refresh.</h1>';
        return;
    }

    // Initialize authentication first
    initializeAuth();

    // Initialize other components - but only after successful authentication
    function initializeDashboardComponents() {
        console.log('Initializing dashboard components after authentication...');
        initializeCharts();
        initializeTestSuites();
        initializeActiveTests();
        initializeRecentRuns();
        initializeDetailsModal();
        handleUrlParameters();
    }

    // Only initialize components when auth state changes to logged in
    document.addEventListener('auth-state-changed', (event) => {
        if (event.detail.loggedIn) {
            console.log('Auth state changed to logged in, initializing components.');
            initializeDashboardComponents();
        } else {
            console.log('Auth state changed to logged out, components will not initialize.');
        }
    });

    // Check if user is already authenticated (e.g., from previous session)
    // This handles the case where user refreshes the page and is already logged in
    setTimeout(() => {
        if (window.apiService && window.apiService.isAuthenticated && window.apiService.isAuthenticated()) {
            console.log('User already authenticated, initializing components.');
            initializeDashboardComponents();
        } else {
            console.log('User not authenticated, waiting for login before initializing components.');
        }
    }, 100);

    console.log('SmartTest Dashboard successfully initialized.');
}

// Wait for the DOM to be fully loaded before running the main function.
document.addEventListener('DOMContentLoaded', main);

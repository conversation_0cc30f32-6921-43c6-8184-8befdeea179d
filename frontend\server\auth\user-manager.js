/**
 * User Management Service
 * Handles user validation, role management, and allowed user list
 */

const fs = require('fs');
const path = require('path');
const bcrypt = require('bcrypt');
const { AUTH_CONFIG } = require('../config/app-config');

class UserManager {
  constructor() {
    this.allowedUsers = null;
    this.lastLoadTime = 0;
    this.loadInterval = 60000; // Reload every minute
    this.loadAllowedUsers();
  }

  /**
   * Load allowed users from configuration file
   */
  loadAllowedUsers() {
    try {
      const configPath = path.resolve(__dirname, '..', AUTH_CONFIG.ALLOWED_USERS_FILE);
      
      // Check if file exists
      if (!fs.existsSync(configPath)) {
        console.error(`Allowed users file not found: ${configPath}`);
        this.allowedUsers = { users: [], roles: {}, config: {} };
        return;
      }

      // Check if we need to reload (file modified or cache expired)
      const stats = fs.statSync(configPath);
      const fileModTime = stats.mtime.getTime();
      
      if (this.allowedUsers && fileModTime <= this.lastLoadTime && 
          (Date.now() - this.lastLoadTime) < this.loadInterval) {
        return; // Use cached version
      }

      // Load and parse the file
      const fileContent = fs.readFileSync(configPath, 'utf8');
      this.allowedUsers = JSON.parse(fileContent);
      this.lastLoadTime = Date.now();
      
      console.log(`Loaded ${this.allowedUsers.users.length} allowed users from ${configPath}`);
      
    } catch (error) {
      console.error('Error loading allowed users:', error);
      // Fallback to empty configuration
      this.allowedUsers = { users: [], roles: {}, config: {} };
    }
  }

  /**
   * Validate user credentials
   * @param {string} uid - User ID (email)
   * @param {string} password - User password
   * @returns {Object|null} User object if valid, null if invalid
   */
  validateUser(uid, password) {
    this.loadAllowedUsers(); // Ensure we have latest data

    if (!uid || !password) {
      return null;
    }

    // Find user by uid and active status
    const user = this.allowedUsers.users.find(u =>
      u.uid === uid &&
      u.active === true
    );

    if (!user) {
      return null;
    }

    // Check password - handle both bcrypt hashed and plain text passwords
    let passwordValid = false;

    if (user.password.startsWith('$2b$') || user.password.startsWith('$2a$')) {
      // Bcrypt hashed password
      try {
        passwordValid = bcrypt.compareSync(password, user.password);
      } catch (error) {
        console.error('Error comparing bcrypt password:', error);
        return null;
      }
    } else {
      // Plain text password (for development/testing)
      passwordValid = user.password === password;
    }

    if (passwordValid) {
      // Return user without password
      const { password: _, ...userWithoutPassword } = user;
      return userWithoutPassword;
    }

    return null;
  }

  /**
   * Check if user has specific permission
   * @param {string} uid - User ID
   * @param {string} permission - Permission to check
   * @returns {boolean} True if user has permission
   */
  hasPermission(uid, permission) {
    const user = this.getUser(uid);
    if (!user) return false;

    const role = this.allowedUsers.roles[user.role];
    if (!role) return false;

    return role.permissions.includes(permission);
  }

  /**
   * Get user by ID
   * @param {string} uid - User ID
   * @returns {Object|null} User object without password
   */
  getUser(uid) {
    this.loadAllowedUsers();
    
    const user = this.allowedUsers.users.find(u => u.uid === uid && u.active === true);
    if (user) {
      const { password: _, ...userWithoutPassword } = user;
      return userWithoutPassword;
    }
    return null;
  }

  /**
   * Get all active users (without passwords)
   * @returns {Array} Array of user objects
   */
  getAllUsers() {
    this.loadAllowedUsers();
    
    return this.allowedUsers.users
      .filter(u => u.active === true)
      .map(({ password: _, ...user }) => user);
  }

  /**
   * Get available roles
   * @returns {Object} Roles configuration
   */
  getRoles() {
    this.loadAllowedUsers();
    return this.allowedUsers.roles || {};
  }

  /**
   * Add new user (for admin interface)
   * @param {Object} userData - User data
   * @returns {boolean} Success status
   */
  addUser(userData) {
    try {
      this.loadAllowedUsers();
      
      // Check if user already exists
      if (this.allowedUsers.users.find(u => u.uid === userData.uid)) {
        throw new Error('User already exists');
      }

      // Add user with default values
      const newUser = {
        uid: userData.uid,
        password: userData.password,
        role: userData.role || 'viewer',
        name: userData.name || userData.uid,
        active: true,
        created: new Date().toISOString()
      };

      this.allowedUsers.users.push(newUser);
      this.saveAllowedUsers();
      
      return true;
    } catch (error) {
      console.error('Error adding user:', error);
      return false;
    }
  }

  /**
   * Update user
   * @param {string} uid - User ID
   * @param {Object} updates - Updates to apply
   * @returns {boolean} Success status
   */
  updateUser(uid, updates) {
    try {
      this.loadAllowedUsers();
      
      const userIndex = this.allowedUsers.users.findIndex(u => u.uid === uid);
      if (userIndex === -1) {
        throw new Error('User not found');
      }

      // Apply updates
      Object.assign(this.allowedUsers.users[userIndex], updates);
      this.saveAllowedUsers();
      
      return true;
    } catch (error) {
      console.error('Error updating user:', error);
      return false;
    }
  }

  /**
   * Deactivate user (soft delete)
   * @param {string} uid - User ID
   * @returns {boolean} Success status
   */
  deactivateUser(uid) {
    return this.updateUser(uid, { active: false });
  }

  /**
   * Save allowed users to file
   */
  saveAllowedUsers() {
    try {
      const configPath = path.resolve(__dirname, '..', AUTH_CONFIG.ALLOWED_USERS_FILE);
      
      // Update last modified timestamp
      this.allowedUsers.config.lastUpdated = new Date().toISOString();
      
      // Write to file with proper formatting
      fs.writeFileSync(configPath, JSON.stringify(this.allowedUsers, null, 2), 'utf8');
      
      console.log('Allowed users configuration saved');
    } catch (error) {
      console.error('Error saving allowed users:', error);
      throw error;
    }
  }

  /**
   * Get session timeout from configuration
   * @returns {number} Session timeout in seconds
   */
  getSessionTimeout() {
    this.loadAllowedUsers();
    return this.allowedUsers.config?.sessionTimeout || AUTH_CONFIG.SESSION_TIMEOUT;
  }
}

// Export singleton instance
module.exports = new UserManager();
